/*
This query is designed to pull the Transfer cohort and prepare it to insert into IA_CW_Cohort
The rules to be in the cohort are:
Student is a full time, registered, Transfer, UG in the FAll term
  or the student is a full time or part time, registered, Transfer, UG in the prior Summer term 
  and the student returned in the Fall term as a full time, registered, continuing, UG, in Fall term
*/
select
sd_pidm CO_PIDM,
'201410' CO_TERM_CODE_KEY, -- fall term code
'Fall 2013' CO_TERM_DESC, -- fall term
student_type_code CO_STUDENT_TYPE_CODE,
student_type_desc CO_STUDNET_TYPE_DESC

from td_student_data td_started
where primary_level_code = 'UG'
and registered_ind = 'Y'
and student_type_code = 'T'
and (
  (
    sd_term_code = '201410'-- fall term code
    and full_part_time_ind_umf = 'F'
  ) or (
    sd_term_code = '201340'--summer term code
    and exists (
      select 'continued in fall'
      from td_student_data td_continued
      where td_started.sd_pidm = td_continued.sd_pidm
      and td_started.sd_term_code + 70 = td_continued.sd_term_code
      and td_continued.registered_ind = 'Y'
      and td_continued.full_part_time_ind_umf = 'F'
      and td_continued.primary_level_code = 'UG'
    )
  )  
)   
order by sd_pidm   