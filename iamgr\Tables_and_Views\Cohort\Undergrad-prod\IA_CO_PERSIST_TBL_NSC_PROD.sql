TRUNCATE TABLE IA_CO_PERSIST_TBL_NSC_PROD_BAC;

INSERT INTO IA_CO_PERSIST_TBL_NSC_PROD_BAC
SELECT * FROM IA_CO_PERSIST_TBL_NSC_PROD;
COMMIT;

SELECT COUNT (*) FROM IA_CO_PERSIST_TBL_NSC_PROD_BAC;
SELECT COUNT (*) FROM IA_CO_PERSIST_TBL_NSC_PROD;

select * from IA_CO_PERSIST_TBL_NSC_PROD;

TRUNCATE TABLE IA_CO_PERSIST_TBL_NSC_PROD;

CREATE TABLE IA_CO_PERSIST_TBL_NSC_PROD AS
--INSERT INTO IA_CO_PERSIST_TBL_NSC_PROD
  (SELECT IA_COHORT_POP_UNDUP_TBL.*,
      ----------------------------FIRST WINTER PERSISTENCE----------------------
      CASE
        WHEN FRST_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRST_WIN_TERM_REG_IND IS NULL
        OR FRST_WIN_TERM_REG_IND      = 'N')
        AND FRST_WIN_TERM_REG_NSC    IS NULL)
        THEN 'LOST'
        WHEN ((FRST_WIN_TERM_REG_IND IS NULL
        OR FRST_WIN_TERM_REG_IND      = 'N')
        AND FRST_WIN_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRST_WIN_TERM_REG_IND IS NULL
        OR FRST_WIN_TERM_REG_IND      = 'N')
        AND FRST_WIN_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRST_WIN_PERSIST,
      ------------------------------FIRST SPRING PERSISTENCE--------------------
      CASE
        WHEN FRST_SPR_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRST_SPR_TERM_REG_IND IS NULL
        OR FRST_SPR_TERM_REG_IND      = 'N')
        AND FRST_SPR_TERM_REG_NSC    IS NULL)
        THEN 'LOST'
        WHEN ((FRST_SPR_TERM_REG_IND IS NULL
        OR FRST_SPR_TERM_REG_IND      = 'N')
        AND FRST_SPR_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRST_SPR_TERM_REG_IND IS NULL
        OR FRST_SPR_TERM_REG_IND      = 'N')
        AND FRST_SPR_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRST_SPR_PERSIST,
      ----------------------------FIRST SUMMER PERSISTENCE----------------------
      CASE
        WHEN FRST_SUM_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRST_SUM_TERM_REG_IND IS NULL
        OR FRST_SUM_TERM_REG_IND      = 'N')
        AND FRST_SUM_TERM_REG_NSC    IS NULL)
        THEN 'LOST'
        WHEN ((FRST_SUM_TERM_REG_IND IS NULL
        OR FRST_SUM_TERM_REG_IND      = 'N')
        AND FRST_SUM_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRST_SUM_TERM_REG_IND IS NULL
        OR FRST_SUM_TERM_REG_IND      = 'N')
        AND FRST_SUM_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRST_SUM_PERSIST,
      ----------------------------SECOND FALL PERSISTENCE-----------------------
      CASE
        WHEN SCND_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC    IS NULL
        AND SCND_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND SCND_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SCND_FALL_PERSIST,
      ----------------------------SECOND WINTER PERSISTENCE---------------------
      CASE
        WHEN SCND_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_TERM_REG_NSC    IS NULL
        AND SCND_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_NSC         = 'Y'
        AND SCND_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_TERM_REG_NSC     = 'Y'
        AND SCND_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_TERM_REG_NSC     = 'Y'
        AND SCND_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SCND_WIN_PERSIST,
      ----------------------------THIRD FALL PERSISTENCE------------------------
      CASE
        WHEN THRD_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC    IS NULL
        AND THRD_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND THRD_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END THRD_FALL_PERSIST,
      ----------------------------THIRD WINTER PERSISTENCE----------------------
      CASE
        WHEN THRD_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_TERM_REG_NSC    IS NULL
        AND THRD_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_NSC         = 'Y'
        AND THRD_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_TERM_REG_NSC     = 'Y'
        AND THRD_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_TERM_REG_NSC     = 'Y'
        AND THRD_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END THRD_WIN_PERSIST,
      ----------------------------FOURTH FALL PERSISTENCE-----------------------
      CASE
        WHEN FRTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC    IS NULL
        AND FRTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRTH_FALL_PERSIST,
      ----------------------------FOURTH WINTER PERSISTENCE---------------------
      CASE
        WHEN FRTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_TERM_REG_NSC    IS NULL
        AND FRTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_NSC         = 'Y'
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_TERM_REG_NSC     = 'Y'
        AND FRTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_TERM_REG_NSC     = 'Y'
        AND FRTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRTH_WIN_PERSIST--,
 
    ---------------------------END SELECT---------------------------------------
    FROM IA_COHORT_TEN_YR_TBL_NSC
    INNER JOIN IA_COHORT_POP_UNDUP_TBL
    ON IA_COHORT_POP_UNDUP_TBL.CO_PIDM = IA_COHORT_TEN_YR_TBL_NSC.CO_PIDM
      --where IA_COHORT_POP_UNDUP_TBL.co_pidm = '72357'
  );
