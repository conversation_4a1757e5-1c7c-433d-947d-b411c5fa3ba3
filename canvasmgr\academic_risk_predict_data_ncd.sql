DROP TABLE ACADEMIC_RISK_PREDICT_DATA_NCD;
create table ACADEMIC_RISK_PREDICT_DATA_NCD as
SELECT --38198
--    v3.person_id udp_person_id,
--    cast(NULL as number) udp_person_id,
    sd.sd_pidm pidm,
--    sd.sd_term_desc term_name,
    sd.sd_term_code term_code,
    dfw.crn,
--    decode(v1.type, 'Laboratory', 1, 'Mixed Mode', 2,
--          'Flex Mode', 3, 'Online/Web Based', 4, 'Lecture/Discussion',
--          5, 'Lecture/Lab', 6, 'Study', 7,
--          'On Campus or Online', 8, 'Performance', 9, 'Field Work',
--          10, 11) type_cat,
--    cast(NULL as number) type_cat,
--    case 
--    when max_assignment_score is null then 1
--    when max_assignment_score = 0 then  1
--    when max_assignment_score > 0 and (assignment_score / max_assignment_score) < .65000 then  0
--    else 1
--    end assign_satisfacotry_bin,
--   cast(NULL as number) assign_satisfacotry_bin,
   cast(NULL as number) satisfactory_unsatisfactory_bin,
   decode(sd.student_type_code, 'C', 1, 'F', 2,
          'R', 3, 'N', 4, 'T',
          5, 6) student_type_cat,
    decode(sd.class_code, 'FR', 1, 'SO', 2,
          'JR', 3, 'SR', 4, 'GR',
          5, 6) class_ord,
    decode(sd.online_courses_only_ind, 'Y', 1, 0) online_only_bin,
    decode(sd.primary_level_code, 'UG', '1', 'U2', '2',
          'U3', '3', 'GR', '4', 'G2',
          '5', 'G3', '6', 'DR', '7',
          '8') primary_level_cat,
    decode(dm.gender, 'M', '1', 'F', '2',
          '3') gender_cat,
    dm.age,
    DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien','0',
      'Hispanic or Latino','1',
      'American Indian or Alaska Native','1',
      'Asian','0',
      'Black or African American','1',
      'Native Hawaiian and Other Pacific Islander','1',
      'White','0',
      'Two or more races','1','0')REPORT_URM_BIN,
    DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien',1,
      'Hispanic or Latino',2,
      'American Indian or Alaska Native',3,
      'Asian',4,
      'Black or African American',5,
      'Native Hawaiian and Other Pacific Islander',6,
      'White',7,
      'Two or more races',8,9)report_ethnicity_CAT,
    decode(dm.FLINT_PROMISE_ELIGIBLE_IND, 'Y', '1', '0') flint_promise_bin,
    decode(dm.FAFSA_PARENT_EDUCATION_DESC, 'Junior High/Middle School', '1', 'High School', '2',
          'College ', '3', '4') parent_edu_cat,
    decode(dm.FIRST_GENERATION_STUDENT_IND, 'Y', 1, 0) first_gen_bin,
    dm.hsch_code hsch_cat,
    dm.hsch_gpa,
    CASE
       WHEN REGEXP_LIKE ( dm.pcol_code_1,
                          '[^0-9]+' ) THEN
           '000000'
       ELSE
           dm.pcol_code_1
    END AS pcol_cat,
    dm.PCOL_GPA_TRANSFERRED_1 pcol_gpa,
    to_number (ts.act_composite) act_composite,
    to_number (ts.SAT_TOTAL_COMBINED_S10) sat_total_combined,
    sd.overall_gpa umf_overall_gpa,
    decode(sd.hold_code_1, 'AS', 1, 'AR', 2,
          'GR', 3, 'SA', 4, 'TR',
          5, 'IC', 6, 'CH', 7,
          'AA', 8, 'CO', 9, 'FA',
          10, 'AD', 11, 'AC', 12,
          'PE', 13, 14) hold_1_cat
          
FROM
    um_student_data sd 
    inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
    inner join um_test_score ts on sd.sd_pidm = ts.pidm
    inner join UMF_CURRENT_COURSES dfw on dfw.pidm = sd.sd_pidm
        and dfw.term = sd.sd_term_code
--        left join v1_student_course_tbl v1  on v1.pidm = dfw.pidm
--            and v1.term_code = dfw.term
--            and v1.course_title = dfw.course_title   
--                left join v3_term_course_activities_tbl v3 on v3.person_id = v1.person_id
--                    and v3.course_offering_id = v1.course_offering_id
where
sd.report_level_code = 'UG'