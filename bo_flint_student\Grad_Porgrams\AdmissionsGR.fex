-DEFAULT &AsOfDate = '4/24/2017'
-DEFAULT &PrimaryTerm = 201810
-DEFAULT &CompareDate = '4/24/2016'
-DEFAULT &CompareTerm = 201710
-DEFAULT &ThirdTermDate = '4/24/2015'
-DEFAULT &ThirdTerm = 201610
-DEFAULT &FourthTermDate = '4/24/2014'
-DEFAULT &FourthTerm = 201510

JOIN
INNER UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.AD_PIDM IN
UM_ADMISSIONS_APPLICANT TO MULTIPLE UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM
IN UM_DEMOGRAPHIC TAG J0 AS J0
END
DEFINE FILE UM_ADMISSIONS_APPLICANT
ReportingMajor/A100=
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'BUS' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'CERG' THEN 'Business Certificate'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'DNP' AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP-NP' OR UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP-NR-NP' ) THEN 'Nursing (current NP)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'DNP' AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP' OR UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP-NR' ) THEN 'Nursing (seeking NP cert.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'MSN' OR UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'MSN-NR') THEN 'Nursing (MSN)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTPP' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG PTPP' THEN 'Physical Therapy (cert.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTPP' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-PTPP RS' THEN 'Physical Therapy (residency)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ANE' AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DAP-CRNA' OR UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNAP-CRNA') THEN 'Anesthesia (DNAP Completion)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'EDU' AND (UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE EQ 'DR'
OR UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE EQ 'D2' OR
UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE EQ 'D3') THEN 'Education (Ed.D.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTP' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'PHD' THEN 'Physical Therapy (PhD)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.SECOND_PROGRAM EQ 'MS-AS-JOINT' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'CSC' THEN 'Computer Sci & Info Systems'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ACTG' THEN 'Accounting (cert.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'FINC' THEN 'Finance (cert.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'MRKT' THEN 'Marketing (cert.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'INTB' THEN 'International Business (cert.)'
ELSE
IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ORGL' THEN 'Org. Leadership (cert.)'
ELSE IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' THEN 'Nursing (cert.)'
ELSE IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ANE' THEN 'Anesthesia (MS)'
ELSE IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'EDU' THEN 'Education (Ed.S.)'
ELSE IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTP' THEN 'Physical Therapy (DPT)'
ELSE IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTPP' THEN 'Physical Therapy (t-DPT)'
ELSE IF UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'EDUT' THEN 'Education (MAC)'
ELSE UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1_DESC;
END
DEFINE FILE TD_ADMISSIONS_APPLICANT
ReportingMajor/A100=
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'BUS' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'CERG' THEN 'Business Certificate'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'DNP' AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP-NP' OR TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP-NR-NP' ) THEN 'Nursing (current NP)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'DNP' AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP' OR TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNP-NR' ) THEN 'Nursing (seeking NP cert.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'MSN' OR TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'MSN-NR') THEN 'Nursing (MSN)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTPP' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG PTPP' THEN 'Physical Therapy (cert.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTPP' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-PTPP RS' THEN 'Physical Therapy (residency)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ANE' AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DAP-CRNA' OR TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'DNAP-CRNA') THEN 'Anesthesia (DNAP Completion)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'EDU' AND (TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE EQ 'DR'
OR TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE EQ 'D2' OR
TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE EQ 'D3') THEN 'Education (Ed.D.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTP' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_DEGREE_CODE EQ 'PHD' THEN 'Physical Therapy (PhD)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.SECOND_PROGRAM EQ 'MS-AS-JOINT' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'CSC' THEN 'Computer Sci & Info Systems'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ACTG' THEN 'Accounting (cert.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'FINC' THEN 'Finance (cert.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'MRKT' THEN 'Marketing (cert.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'INTB' THEN 'International Business (cert.)'
ELSE
IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM EQ 'CERG-SOM' AND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ORGL' THEN 'Org. Leadership (cert.)'
ELSE IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'NUR' THEN 'Nursing (cert.)'
ELSE IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'ANE' THEN 'Anesthesia (MS)'
ELSE IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'EDU' THEN 'Education (Ed.S.)'
ELSE IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTP' THEN 'Physical Therapy (DPT)'
ELSE IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'PTPP' THEN 'Physical Therapy (t-DPT)'
ELSE IF TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 EQ 'EDUT' THEN 'Education (MAC)'
ELSE TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1_DESC;
END
TABLE FILE UM_ADMISSIONS_APPLICANT
PRINT
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ReportingMajor
     J0.UM_DEMOGRAPHIC.UMID
     J0.UM_DEMOGRAPHIC.FIRST_NAME
     J0.UM_DEMOGRAPHIC.LAST_NAME
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.AD_PIDM
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1_DESC
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY_DESC
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APDC_CODE_2
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APDC_CODE_3
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ENROLL_DEPOSIT_IND
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.CONFIRM_ADMISSION_CARD_IND
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ORIENTATION_SESSION_IND
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ENROLL_DEPOSIT_TOTAL
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ADMT_CODE
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ADMT_DESC
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.MATRICULANT_STYP_DESC
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APPL_NO
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.STYP_DESC
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_COLLEGE_DESC
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ENROLLED_IND
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.NATION_DESC_ADMIT
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.REGISTERED_IND
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APST_CODE
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APDC_CODE_1
     UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.HOUSING_APP_STATUS
WHERE ((( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&PrimaryTerm.(FIND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY,UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY_DESC IN um_admissions_applicant).PrimaryTerm.' ) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APPL_DATE LT DT(&AsOfDate         ) ) OR (( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&CompareTerm.(FIND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY,UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY_DESC IN um_admissions_applicant).CompareTerm.' ) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APPL_DATE LT DT(&CompareDate         ) )) OR (( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&ThirdTerm.(FIND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY,UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY_DESC IN um_admissions_applicant).SecondTerm.' ) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APPL_DATE LT DT(&ThirdTermDate         ) )) OR (( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&FourthTerm.(FIND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY,UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY_DESC IN um_admissions_applicant).ThirdTerm.' ) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.APPL_DATE LE DT(&FourthTermDate         ) ))) AND (( NOT UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE IN ('NA','PR','UG','U3','U2') ) OR ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.SECOND_PROGRAM EQ 'MS-AS-JOINT' )) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.ADMT_CODE NE 'RE' ) AND ( NOT UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.STYP_CODE IN ('R','S','C') ) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 NE '0000' )) AND ( UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.INTERNATIONAL_IND EQ &International_Ind.(OR(FIND UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.INTERNATIONAL_IND IN um_admissions_applicant)).International_Ind. );
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS MONTHLYGR FORMAT FOCUS
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
ENDSTYLE
END
TABLE FILE TD_ADMISSIONS_APPLICANT
PRINT
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ReportingMajor
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.AD_PIDM
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1_DESC
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY_DESC
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.APDC_CODE_2
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.APDC_CODE_3
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ENROLL_DEPOSIT_IND
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ENROLL_DEPOSIT_TOTAL
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ADMT_CODE
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ADMT_DESC
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.MATRICULANT_STYP_DESC
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.APPL_NO
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.STYP_DESC
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_COLLEGE_DESC
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ENROLLED_IND
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.NATION_DESC_ADMIT
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.REGISTERED_IND
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.APST_CODE
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.APDC_CODE_1
     TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.HOUSING_APP_STATUS
WHERE ((( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&PrimaryTerm' ) OR ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&CompareTerm' ) OR ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&ThirdTerm' ) OR ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY EQ '&FourthTerm' )) AND (( NOT TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_LEVEL_CODE IN ('NA','PR','UG','U3','U2') ) OR ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.SECOND_PROGRAM EQ 'MS-AS-JOINT' )) AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.ADMT_CODE NE 'RE' ) AND ( NOT TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.STYP_CODE IN ('R','S','C') ) AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1 NE '0000' )) AND ( TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.INTERNATIONAL_IND EQ &International_Ind.(OR(FIND TD_ADMISSIONS_APPLICANT.TD_ADMISSIONS_APPLICANT.INTERNATIONAL_IND IN TD_admissions_applicant)).International_Ind. );
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS TENTHGR FORMAT FOCUS
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
ENDSTYLE
END
JOIN
LEFT_OUTER MONTHLYGR.SEG01.AD_PIDM AND MONTHLYGR.SEG01.TERM_CODE_ENTRY
AND MONTHLYGR.SEG01.APPL_NO IN MONTHLYGR TO UNIQUE
VIEWUMFSUNAPSISADMISSIONS_EXT.VIEWUMFSUNAPSISADMISSIONS_EXT.ASSOCIATEDIDNUMBER
AND VIEWUMFSUNAPSISADMISSIONS_EXT.VIEWUMFSUNAPSISADMISSIONS_EXT.TERM_CODE_ENTRY
AND VIEWUMFSUNAPSISADMISSIONS_EXT.VIEWUMFSUNAPSISADMISSIONS_EXT.APPL_NO
IN VIEWUMFSUNAPSISADMISSIONS_EXT TAG SUNADM AS SUNADM
END
DEFINE FILE MONTHLYGR
ReportCountry/A30=IF MONTHLYGR.SEG01.NATION_DESC_ADMIT EQ '' THEN ' United States' ELSE MONTHLYGR.SEG01.NATION_DESC_ADMIT;
AdmittedCount/I5=IF MONTHLYGR.SEG01.APDC_CODE_1 EQ 'A2' OR MONTHLYGR.SEG01.APDC_CODE_1 EQ 'AD'  OR MONTHLYGR.SEG01.APDC_CODE_2 EQ 'A2' OR MONTHLYGR.SEG01.APDC_CODE_2 EQ 'AD' OR MONTHLYGR.SEG01.APDC_CODE_3 EQ 'A2' OR MONTHLYGR.SEG01.APDC_CODE_3 EQ 'AD'  THEN 1 ELSE 0;
CurrentlyAdmittedCount/I5=IF MONTHLYGR.SEG01.APDC_CODE_1 EQ 'A2' OR MONTHLYGR.SEG01.APDC_CODE_1 EQ 'AD' THEN 1 ELSE 0;
DeniedCount/I5=IF MONTHLYGR.SEG01.APDC_CODE_1 EQ 'DN' THEN 1 ELSE 0;
WaitlistedCount/I5=IF MONTHLYGR.SEG01.APDC_CODE_1 EQ 'WL' THEN 1 ELSE 0;
IncompleteCount/I5=IF (( MONTHLYGR.SEG01.APST_CODE EQ 'I' OR MONTHLYGR.SEG01.APST_CODE EQ 'C' ) AND MONTHLYGR.SEG01.APDC_CODE_1 EQ '') OR (( MONTHLYGR.SEG01.APST_CODE EQ 'D' OR MONTHLYGR.SEG01.APST_CODE EQ 'C' ) AND MONTHLYGR.SEG01.APDC_CODE_1 EQ 'PN')  THEN 1 ELSE 0;
WithdrawnCount/I5=IF ( MONTHLYGR.SEG01.APDC_CODE_1 EQ 'WD' OR MONTHLYGR.SEG01.APDC_CODE_1 EQ 'CN' OR MONTHLYGR.SEG01.APDC_CODE_1 EQ 'CT' OR MONTHLYGR.SEG01.APDC_CODE_1 EQ 'PN' ) AND IncompleteCount EQ 0 THEN 1 ELSE 0;
EnrolledCount/I5=IF MONTHLYGR.SEG01.REGISTERED_IND EQ 'Y' AND WithdrawnCount EQ 0 THEN 1 ELSE 0;
HousingReceivedCount/I5=IF MONTHLYGR.SEG01.HOUSING_APP_STATUS EQ 'AP' THEN 1 ELSE 0;
ReportTermLabel/A30=MONTHLYGR.SEG01.TERM_CODE_ENTRY_DESC;
FlightConfirmedCount/I5=IF SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.FLIGHT_CONFIRMED_IND EQ 'Y' THEN 1 ELSE 0;
SEVISFeePaidCount/I5=IF SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.SEVIS_FEE_PAID_IND EQ 'Y' AND NOT SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.VISA_RECIEVED_IND EQ 'Y' THEN 1 ELSE 0;
I20CreateCount/I5=IF SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.CREATEI20 EQ 'Y' THEN 1 ELSE 0;
VisaReceivedCount/I5=IF SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.VISA_RECIEVED_IND EQ 'Y' THEN 1 ELSE 0;
OrientationCount/I5=IF MONTHLYGR.SEG01.ORIENTATION_SESSION_IND EQ 'Y' THEN 1 ELSE 0;
ConfirmedCount/I5=IF ( VisaReceivedCount GT 0 OR OrientationCount GT 0 OR FlightConfirmedCount GT 0 OR SEVISFeePaidCount GT 0 OR HousingReceivedCount GT 0 OR MONTHLYGR.SEG01.ENROLL_DEPOSIT_TOTAL GT 0 OR MONTHLYGR.SEG01.CONFIRM_ADMISSION_CARD_IND EQ 'Y' ) AND EnrolledCount EQ 0 AND CurrentlyAdmittedCount EQ 1 THEN 1 ELSE 0;
NotConfirmedEnrolledCount/I5=IF ConfirmedCount + EnrolledCount + WithdrawnCount EQ 0 THEN 1 ELSE 0;
END
DEFINE FILE TENTHGR
AdmittedCount/I5=IF TENTHGR.SEG01.APDC_CODE_1 EQ 'A2' OR TENTHGR.SEG01.APDC_CODE_1 EQ 'AD'  OR TENTHGR.SEG01.APDC_CODE_2 EQ 'A2' OR TENTHGR.SEG01.APDC_CODE_2 EQ 'AD' OR TENTHGR.SEG01.APDC_CODE_3 EQ 'A2' OR TENTHGR.SEG01.APDC_CODE_3 EQ 'AD'  THEN 1 ELSE 0;
CurrentlyAdmittedCount/I5=IF TENTHGR.SEG01.APDC_CODE_1 EQ 'A2' OR TENTHGR.SEG01.APDC_CODE_1 EQ 'AD' THEN 1 ELSE 0;
DeniedCount/I5=IF TENTHGR.SEG01.APDC_CODE_1 EQ 'DN' THEN 1 ELSE 0;
WaitlistedCount/I5=IF TENTHGR.SEG01.APDC_CODE_1 EQ 'WL' THEN 1 ELSE 0;
IncompleteCount/I5=IF (( TENTHGR.SEG01.APST_CODE EQ 'I' OR TENTHGR.SEG01.APST_CODE EQ 'C' ) AND TENTHGR.SEG01.APDC_CODE_1 EQ '') OR (( TENTHGR.SEG01.APST_CODE EQ 'D' OR TENTHGR.SEG01.APST_CODE EQ 'C' ) AND TENTHGR.SEG01.APDC_CODE_1 EQ 'PN')  THEN 1 ELSE 0;
WithdrawnCount/I5=IF ( TENTHGR.SEG01.APDC_CODE_1 EQ 'WD' OR TENTHGR.SEG01.APDC_CODE_1 EQ 'CN' OR TENTHGR.SEG01.APDC_CODE_1 EQ 'CT' OR TENTHGR.SEG01.APDC_CODE_1 EQ 'PN' ) AND IncompleteCount EQ 0 THEN 1 ELSE 0;
ReportTermLabel/A30=TENTHGR.SEG01.TERM_CODE_ENTRY_DESC;
END
TABLE FILE MONTHLYGR
SUM
     CNT.MONTHLYGR.SEG01.AD_PIDM AS 'COUNT,MONTHLYGR.SEG01.AD_PIDM'
BY  MONTHLYGR.SEG01.ReportingMajor AS 'Program'
ACROSS HIGHEST MONTHLYGR.SEG01.TERM_CODE_ENTRY NOPRINT
ACROSS LOWEST MONTHLYGR.SEG01.ReportTermLabel AS ''
     COMPUTE
          AVERAGE/D8 = ( C2 + C3 + C4 ) / 3; AS 'Avg Last 3 Yrs'
     COMPUTE
          CHANGE/D12 = C1 - AVERAGE; AS 'Change'
     COMPUTE
          CHANGEPERCENT/D5% = CHANGE/AVERAGE * 100; AS '% Change'
HEADING
"&PrimaryTerm_TEXT New Applications as of &AsOfDate"
ON TABLE SUBFOOT
"* All terms in this report are compared to the Month and Day of their respective year."
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SUMMARIZE MONTHLYGR.SEG01.AD_PIDM MONTHLYGR.SEG01.AD_PIDM AS 'TOTAL'
ON TABLE HOLD AS appsxlsx FORMAT XLSX TEMPLATE 'gradreporttemplate.xltm' SHEETNUMBER 2
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/WFC/Repository/GR/Reports/Admissions.sty,
$
TYPE=REPORT, TITLETEXT=New GR Apps, $
ENDSTYLE
END
TABLE FILE TENTHGR
SUM
     CNT.TENTHGR.SEG01.AD_PIDM AS 'COUNT,TENTHGR.SEG01.AD_PIDM'
BY  TENTHGR.SEG01.ReportingMajor AS 'Program'
ACROSS HIGHEST TENTHGR.SEG01.TERM_CODE_ENTRY NOPRINT
ACROSS LOWEST TENTHGR.SEG01.ReportTermLabel AS ''
     COMPUTE
          Average/D8 = ( C1 + C2 + C3 ) / 3; AS 'Avg Last 3 Yrs'
HEADING
"New Applications as of TENTH DAY Previous Terms"
ON TABLE SUBFOOT
"* If four terms are displayed, only most recent three terms counted in average"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SUMMARIZE TENTHGR.SEG01.AD_PIDM TENTHGR.SEG01.AD_PIDM AS 'TOTAL'
ON TABLE HOLD AS tenthxlsx FORMAT XLSX TEMPLATE 'appsxlsx.xlsm' SHEETNUMBER 3
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/WFC/Repository/GR/Reports/Admissions.sty,
$
TYPE=REPORT, TITLETEXT=10th Day Apps, $
ENDSTYLE
END
TABLE FILE MONTHLYGR
SUM
     CNT.MONTHLYGR.SEG01.AD_PIDM AS 'Total Applied'
     MONTHLYGR.SEG01.CurrentlyAdmittedCount AS 'Admitted'
     MONTHLYGR.SEG01.DeniedCount AS 'Denied'
     MONTHLYGR.SEG01.WaitlistedCount AS 'Waitlisted'
     MONTHLYGR.SEG01.WithdrawnCount AS 'WD/CN'
     MONTHLYGR.SEG01.IncompleteCount AS 'Inc/Need Dcsn'
BY  MONTHLYGR.SEG01.ReportingMajor AS 'Program'
HEADING
"Current Status of &PrimaryTerm_TEXT Applications as of &AsOfDate"
WHERE MONTHLYGR.SEG01.TERM_CODE_ENTRY EQ '&PrimaryTerm';
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE COLUMN-TOTAL AS 'TOTAL'
ON TABLE HOLD AS appstatusxlsx FORMAT XLSX TEMPLATE 'tenthxlsx.xlsm' SHEETNUMBER 4
ON TABLE SET STYLE *
     INCLUDE = IBFS:/WFC/Repository/GR/Reports/Admissions.sty,
$
TYPE=REPORT, TITLETEXT=Status &PrimaryTerm Apps, $
ENDSTYLE
END
TABLE FILE MONTHLYGR
SUM
     CNT.MONTHLYGR.SEG01.AD_PIDM AS 'Total Applied'
     MONTHLYGR.SEG01.CurrentlyAdmittedCount AS 'Admitted'
     MONTHLYGR.SEG01.DeniedCount AS 'Denied'
     MONTHLYGR.SEG01.WaitlistedCount AS 'Waitlisted'
     MONTHLYGR.SEG01.WithdrawnCount AS 'WD/CN'
     MONTHLYGR.SEG01.IncompleteCount AS 'Inc/Need Dcsn'
BY  LOWEST MONTHLYGR.SEG01.ReportCountry AS 'Country'
BY  MONTHLYGR.SEG01.ReportingMajor AS 'Program'
HEADING
"Current Status of &PrimaryTerm_TEXT International Applications as of &AsOfDate"
WHERE MONTHLYGR.SEG01.TERM_CODE_ENTRY EQ '&PrimaryTerm';
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE COLUMN-TOTAL AS 'TOTAL'
ON TABLE HOLD AS appstatuscountryxlsx FORMAT XLSX TEMPLATE 'appstatusxlsx.xlsm' SHEETNUMBER 5
ON TABLE SET STYLE *
     INCLUDE = IBFS:/WFC/Repository/GR/Reports/Admissions.sty,
$
TYPE=REPORT, TITLETEXT=Status &PrimaryTerm Apps, $
ENDSTYLE
END
TABLE FILE MONTHLYGR
SUM
     MONTHLYGR.SEG01.AdmittedCount AS 'Total Admitted'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.NotConfirmedEnrolledCount AS 'Not Confirmed'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.ConfirmedCount AS 'Confirmed'
     MONTHLYGR.SEG01.EnrolledCount AS 'Enrolled'
     MONTHLYGR.SEG01.WithdrawnCount AS 'Cancelled'
     MONTHLYGR.SEG01.HousingReceivedCount AS 'Housing App'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.FlightConfirmedCount AS 'Flight Confirmed'
     MONTHLYGR.SEG01.OrientationCount AS 'Orientation RSVP'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.SEVISFeePaidCount AS 'SEVIS Fee Paid'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.VisaReceivedCount AS 'Visa Received'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.I20CreateCount AS 'I-20 Created'
BY  MONTHLYGR.SEG01.ReportingMajor AS 'Program'
HEADING
"Current Status of &PrimaryTerm_TEXT Admitted Students as of &AsOfDate"
ON TABLE SUBFOOT
"Confirmed column comprised of admission confirmations, enrollment deposits paid, orientation RSVPs, housing applications, and international indicators (visa received, flight confirmed, SEVIS fee paid)."
"Indicators for Flight Confirmation, SEVIS Fee Paid, Visa Received, and Orientation counts are pulled from data that is not 1 to 1.  Students with multiple applications, or who have deferred, have the potential to skew these numbers."
WHERE ( MONTHLYGR.SEG01.TERM_CODE_ENTRY EQ '&PrimaryTerm' ) AND (( MONTHLYGR.SEG01.APDC_CODE_1 IN ('AD','A2') ) OR ( MONTHLYGR.SEG01.APDC_CODE_2 IN ('AD','A2') ) OR ( MONTHLYGR.SEG01.APDC_CODE_3 IN ('AD','A2') ));
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE COLUMN-TOTAL AS 'TOTAL'
ON TABLE HOLD AS admitstatusxlsx FORMAT XLSX TEMPLATE 'appstatuscountryxlsx.xlsm' SHEETNUMBER 6
ON TABLE SET STYLE *
     INCLUDE = IBFS:/WFC/Repository/GR/Reports/Admissions.sty,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=2,
     OBJECT=TEXT,
     ITEM=1,
     STYLE=ITALIC,
$
TYPE=REPORT, TITLETEXT=Status &PrimaryTerm Admits, $
ENDSTYLE
END
TABLE FILE MONTHLYGR
SUM
     MONTHLYGR.SEG01.AdmittedCount AS 'Total Admitted'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.NotConfirmedEnrolledCount AS 'Not Confirmed'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.ConfirmedCount AS 'Confirmed'
     MONTHLYGR.SEG01.EnrolledCount AS 'Enrolled'
     MONTHLYGR.SEG01.WithdrawnCount AS 'Cancelled'
     MONTHLYGR.SEG01.HousingReceivedCount AS 'Housing App'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.FlightConfirmedCount AS 'Flight Confirmed'
     MONTHLYGR.SEG01.OrientationCount AS 'Orientation RSVP'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.SEVISFeePaidCount AS 'SEVIS Fee Paid'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.VisaReceivedCount AS 'Visa Received'
     SUNADM.VIEWUMFSUNAPSISADMISSIONS_EXT.I20CreateCount AS 'I-20 Created'
BY  LOWEST MONTHLYGR.SEG01.ReportCountry AS 'Country'
HEADING
"Current Status of &PrimaryTerm_TEXT Admitted Students by Country as of &AsOfDate"
ON TABLE SUBFOOT
"Confirmed column comprised of admission confirmations, enrollment deposits paid, orientation RSVPs, housing applications, and international indicators (visa received, flight confirmed, SEVIS fee paid)."
"Indicators for Flight Confirmation, SEVIS Fee Paid, Visa Received, and Orientation counts are pulled from data that is not 1 to 1.  Students with multiple applications, or who have deferred, have the potential to skew these numbers."
WHERE ( MONTHLYGR.SEG01.TERM_CODE_ENTRY EQ '&PrimaryTerm' ) AND (( MONTHLYGR.SEG01.APDC_CODE_1 IN ('AD','A2') ) OR ( MONTHLYGR.SEG01.APDC_CODE_2 IN ('AD','A2') ) OR ( MONTHLYGR.SEG01.APDC_CODE_3 IN ('AD','A2') ));
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE COLUMN-TOTAL AS 'TOTAL'
ON TABLE PCHOLD FORMAT XLSX TEMPLATE 'admitstatusxlsx.xlsm' SHEETNUMBER 7
ON TABLE SET STYLE *
     INCLUDE = IBFS:/WFC/Repository/GR/Reports/Admissions.sty,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=2,
     OBJECT=TEXT,
     ITEM=1,
     STYLE=ITALIC,
$
TYPE=REPORT, TITLETEXT=Status &PrimaryTerm Admits - Country, $
ENDSTYLE
END
-RUN

 