/*

These are queries used to get a row count
by td term.  First I look to see my new term
is there, then compare the counts from earlier
td terms with the same suffix, ie 10, 20, ....

These are all the tables that should get new rows
when td is ran for reporting purposes.

*/

select 
'1' table_num,
'td_admissions_applicant' table_name,
term_code_entry term_code,
count(*) row_count
from td_admissions_applicant
where term_code_entry = (select current_term from um_current_term)
group by 
'1',
'td_admissions_applicant',
term_code_entry

union
select 
'2' table_num,
'um_admissions_applicant' table_name,
term_code_entry term_code,
count(*) row_count
from um_admissions_applicant
where term_code_entry = (select current_term from um_current_term)
group by 
'2',
'um_admissions_applicant',
term_code_entry
 union

select
'3' table_num,
'td_catalog_schedule' table_name,
term_code_key term_code,
count(*)row_count
from td_catalog_schedule
where term_code_key = (select current_term from um_current_term)
group by 
'3',
'td_catalog_schedule',
term_code_key
union

select
'4' table_num,
'um_catalog_schedule' table_name,
term_code_key term_code,
count(*)row_count
from um_catalog_schedule
where term_code_key = (select current_term from um_current_term)
group by 
'4',
'um_catalog_schedule',
term_code_key

union

select
'5' table_num,
'td_registration_detail' table_name,
td_term_code term_code,
count(*) row_count
from td_registration_detail
where td_term_code = (select current_term from um_current_term)
group by 
'5',
'td_registration_detail',
td_term_code

union

select
'6' table_num,
'um_registration_detail' table_name,
term_code term_code,
count(*) row_count
from um_registration_detail
where term_code = (select current_term from um_current_term)
group by 
'6',
'um_registration_detail',
term_code

union

select 
'7' table_num,
'td_student_data' table_name,
sd_term_code,
count(*)row_count
from td_student_data
where sd_term_code = (select current_term from um_current_term)
and registered_ind = 'Y'
group by 
'7',
'td_student_data',
sd_term_code

union

select 
'8' table_num,
'um_student_data' table_name,
sd_term_code,
count(*)row_count
from um_student_data
where sd_term_code = (select current_term from um_current_term)
and registered_ind = 'Y'
group by 
'8',
'um_student_data',
sd_term_code
;

