CREATE OR REPLACE VIEW IA_TERM_CONT_ROLLUP AS
SELECT
 *
FROM
 (
  WITH dp1 AS (
   SELECT
    *
   FROM
    ia_term_cont
  ), cur_term AS (
   SELECT
    cur_term_code,
    cur_term_desc,
    COUNT(*) cur_term_cont
   FROM
    dp1
   GROUP BY
    cur_term_code,
    cur_term_desc
  ), cur_reg_term AS (
   SELECT
    cur_term_code,
    cur_reg_term_desc,
    COUNT(*) cur_reg_term_cont
   FROM
    dp1
   WHERE
    ( cur_reg_term_enrolled_ind = 'Y'
      OR cur_reg_term_grad_ind = 'Y' )
   GROUP BY
    cur_term_code,
    cur_reg_term_desc
  ), next_reg_term AS (
   SELECT
    cur_term_code,
    next_reg_term_desc,
    COUNT(*) next_reg_term_cont
   FROM
    dp1
   WHERE
    ( next_reg_term_enrolled_ind = 'Y'
      OR next_reg_term_grad_ind = 'Y' )
   GROUP BY
    cur_term_code,
    next_reg_term_desc
  )
  SELECT
   'University Level'  continuation_type,
    cur_term.cur_term_desc,
   'ALL'               cur_col_code,
   cur_term.cur_term_cont,
   cur_reg_term.cur_reg_term_desc,
   cur_reg_term.cur_reg_term_cont,
   to_char((cur_reg_term.cur_reg_term_cont / cur_term.cur_term_cont) * 100, '900.00')
   || '%'               cur_reg_term_prcnt,
   next_reg_term.next_reg_term_desc,
   next_reg_term.next_reg_term_cont,
   to_char((next_reg_term.next_reg_term_cont / cur_term.cur_term_cont) * 100, '900.00')
   || '%'               next_reg_term_prcnt
  FROM
   cur_term
   LEFT JOIN cur_reg_term ON cur_term.cur_term_code = cur_reg_term.cur_term_code
   LEFT JOIN next_reg_term ON cur_term.cur_term_code = next_reg_term.cur_term_code
 ) unv_cont
UNION ALL
SELECT
 *
FROM
 (
  WITH dp1 AS (
   SELECT
    *
   FROM
    ia_term_cont
  ), cur_term AS (
   SELECT
    cur_term_code,
    cur_term_desc,
    cur_col_code,
    COUNT(*) cur_term_cont
   FROM
    dp1
   GROUP BY
    cur_term_code,
    cur_term_desc,
    cur_col_code
  ), cur_reg_term AS (
   SELECT
    cur_term_code,
    cur_col_code,
    cur_reg_term_desc,
    COUNT(*) cur_reg_term_cont
   FROM
    dp1
   WHERE
    ( cur_reg_term_enrolled_ind = 'Y'
      OR cur_reg_term_grad_ind = 'Y' )
   GROUP BY
    cur_term_code,
    cur_col_code,
    cur_reg_term_desc
  ), next_reg_term AS (
   SELECT
    cur_term_code,
    cur_col_code,
    next_reg_term_desc,
    COUNT(*) next_reg_term_cont
   FROM
    dp1
   WHERE
    ( next_reg_term_enrolled_ind = 'Y'
      OR next_reg_term_grad_ind = 'Y' )
   GROUP BY
    cur_term_code,
    cur_col_code,
    next_reg_term_desc
  )
  SELECT
   'College Level All'  continuation_type,
   cur_term.cur_term_desc,
   cur_term.cur_col_code,
   cur_term.cur_term_cont,
   cur_reg_term.cur_reg_term_desc,
   cur_reg_term.cur_reg_term_cont,
   to_char((cur_reg_term.cur_reg_term_cont / cur_term.cur_term_cont) * 100, '900.00')
   || '%'                cur_reg_term_prcnt,
   next_reg_term.next_reg_term_desc,
   next_reg_term.next_reg_term_cont,
   to_char((next_reg_term.next_reg_term_cont / cur_term.cur_term_cont) * 100, '900.00')
   || '%'                next_reg_term_prcnt
  FROM
   cur_term
   LEFT JOIN cur_reg_term ON cur_term.cur_term_code = cur_reg_term.cur_term_code
                             AND cur_term.cur_col_code = cur_reg_term.cur_col_code
   LEFT JOIN next_reg_term ON cur_term.cur_term_code = next_reg_term.cur_term_code
                              AND cur_term.cur_col_code = next_reg_term.cur_col_code
  ORDER BY
   1 DESC,
   3
 ) all_cont
UNION ALL
SELECT
 *
FROM
 (
  WITH dp1 AS (
   SELECT
    *
   FROM
    ia_term_cont
  ), cur_term AS (
   SELECT
    cur_term_code,
    cur_term_desc,
    cur_report_level,
    cur_col_code,
    COUNT(*) cur_term_cont
   FROM
    dp1
   GROUP BY
    cur_term_code,
    cur_term_desc,
    cur_report_level,
    cur_col_code
  ), cur_reg_term AS (
   SELECT
    cur_term_code,
    cur_report_level,
    cur_col_code,
    cur_reg_term_desc,
    COUNT(*) cur_reg_term_cont
   FROM
    dp1
   WHERE
    ( cur_reg_term_enrolled_ind = 'Y'
      OR cur_reg_term_grad_ind = 'Y' )
   GROUP BY
    cur_term_code,
    cur_report_level,
    cur_col_code,
    cur_reg_term_desc
  ), next_reg_term AS (
   SELECT
    cur_term_code,
    cur_report_level,
    cur_col_code,
    next_reg_term_desc,
    COUNT(*) next_reg_term_cont
   FROM
    dp1
   WHERE
    ( next_reg_term_enrolled_ind = 'Y'
      OR next_reg_term_grad_ind = 'Y' )
   GROUP BY
    cur_term_code,
    cur_report_level,
    cur_col_code,
    next_reg_term_desc
  )
  SELECT
   'College Level ' || cur_term.cur_report_level      continuation_type,
   cur_term.cur_term_desc,
   cur_term.cur_col_code,
   cur_term.cur_term_cont,
   cur_reg_term.cur_reg_term_desc,
   cur_reg_term.cur_reg_term_cont,
   to_char((cur_reg_term.cur_reg_term_cont / cur_term.cur_term_cont) * 100, '900.00')
   || '%'                                             cur_reg_term_prcnt,
   next_reg_term.next_reg_term_desc,
   next_reg_term.next_reg_term_cont,
   to_char((next_reg_term.next_reg_term_cont / cur_term.cur_term_cont) * 100, '900.00')
   || '%'                                             next_reg_term_prcnt
  FROM
   cur_term
   LEFT JOIN cur_reg_term ON cur_term.cur_term_code = cur_reg_term.cur_term_code
                             AND cur_term.cur_col_code = cur_reg_term.cur_col_code
                             AND cur_term.cur_report_level = cur_reg_term.cur_report_level
   LEFT JOIN next_reg_term ON cur_term.cur_term_code = next_reg_term.cur_term_code
                              AND cur_term.cur_col_code = next_reg_term.cur_col_code
                              AND cur_term.cur_report_level = next_reg_term.cur_report_level
  ORDER BY
   1 DESC,
   3
 ) level_cont;