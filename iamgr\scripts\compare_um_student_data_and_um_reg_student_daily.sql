
    select
    ct.day_of_term,
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.run_date = trunc(sysdate)
    and ct.term_code = (select um_current_term.current_term from um_current_term)
    group by
    ct.day_of_term,
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc
 ORDER BY 2,3

;
with dp1 as (
select
sd_pidm,
sd_term_code,
report_level_code
--student_type_code,
--count (*) Headcount
from td_student_data
where registered_ind = 'Y'
and sd_term_code = (select um_current_term.current_term from um_current_term)

minus

select 
sd_pidm,
sd_term_code,
report_level_code
--student_type_code,
--count (*) Headcount
from um_student_data
where registered_ind = 'Y'
and sd_term_code = (select um_current_term.current_term from um_current_term)
)

select
* 
from dp1

;

--gross loss since TD is 131 students as of 10-22-21 then we've gained 24 after TD that gives us net loss of 107 students.  the Daily tracker is giving us a net gain/loss overview.  That is where the disconect lies.
;
--group by
--sd_term_code
--,
--report_level_code,
--student_type_code
--order by 
--2,3
;

with td as (
select 
sd_pidm,
sd_term_code,
report_level_code,
student_type_code
from td_student_data
where registered_ind = 'Y'
and sd_term_code = '202210'
)
select 
td.report_level_code,
td.student_type_code,
count (*)
from td 
inner join um_student_data um on td.sd_pidm = um.sd_pidm
and td.sd_term_code = um.sd_term_code
where um.registered_ind = 'Y'
group by
td.report_level_code,
td.student_type_code
ORDER BY 1,2
;
