/*******************************************************************************
Materialized View (MV) DBA
by <PERSON> on 042922
*******************************************************************************/
--Drop a MV
DROP MATERIALIZED VIEW UMFLINT_PERSIST_EDU_PROG_MV;

--Create MV with refresh schedule
--CREATE MATERIALIZED VIEW UMFLINT_PERSIST_EDU_PROG_MV
-- REFRESH
--ON DEMAND
--  START WITH TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
--  NEXT TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM') + 1 WITH ROWID
--AS
-- SELECT
--  *
-- FROM
--  UMFLINT_PERSIST_EDU_PROG;
  
--Create MV with refresh schedule  
CREATE MATERIALIZED VIEW UMFLINT_PERSIST_EDU_PROG_MV
REFRESH
ON DEMAND
-- START WITH (SYSDATE) 
 START WITH TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
 NEXT (SYSDATE + 1) WITH ROWID
AS SELECT * FROM UMFLINT_PERSIST_EDU_PROG;

--Force a refresh of MV
EXEC DBMS_MVIEW.refresh('UMFLINT_PERSIST_EDU_PROG_MV');
--BEGIN EXEC dbms_mview.refresh_all_mviews;

--Identify last refresh of Materialized View
SELECT 
  OWNER, 
  MVIEW_NAME, 
  to_char(last_refresh_date, 'yyyymmddhh24miss') LAST_REFRESH_DATE,
  to_char(last_refresh_date, 'MM-DD-YYYY HH:MI AM') LAST_REFRESH_DAY
FROM all_mviews
WHERE owner = 'REGMGR'
AND mview_name = 'UMFLINT_PERSIST_EDU_PROG_MV';

--Alter refresh schedule MV
ALTER MATERIALIZED VIEW UMFLINT_PERSIST_EDU_PROG_MV 
REFRESH
ON COMMIT --DEMAND
  START WITH TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
  NEXT TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM') + 1
  ;

--refresh materialized view
EXEC DBMS_MVIEW.refresh('UMFLINT_PERSIST_EDU_PROG_MV');

create materialized view UMFLINT_PERSIST_EDU_PROG_MV
as
select * from UMFLINT_PERSIST_EDU_PROG;




