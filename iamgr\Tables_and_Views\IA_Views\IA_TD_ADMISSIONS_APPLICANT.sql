create or replace view IA_TD_ADMISSIONS_APPLICANT as (

select
AA.AD_PIDM,
DM.UMID,
FY.FY,
FY.FAFY,
AA.TERM_CODE_ENTRY,
AA.TERM_CODE_ENTRY_DESC,
AA.ENROLLED_IND,
AA.APPL_DATE,
case
when AA.APDC_CODE_1 in ('AD','A2') or
AA.APDC_CODE_2 in ('AD','A2') or
AA.APDC_CODE_3 in ('AD','A2') or
AA.APDC_CODE_4 in ('AD','A2') or
AA.APDC_CODE_5 in ('AD','A2') then 'Y'
else 'N'
end ia_apdc_code,
AA.APST_CODE,
AA.APST_DESC,
AA.APST_DATE,
AA.ADMT_CODE,
AA.ADMT_DESC,
AA.STYP_CODE,
AA.STYP_DESC,
AA.CAMP_CODE,
AA.RESD_CODE, 
AA.RESD_DESC,
AA.PRIMARY_COLLEGE_CODE,
AA.PRIMARY_COLLEGE_DESC,
AA.PRIMARY_LEVEL_CODE,
AA.PRIMARY_PROGRAM,
AA.PRIMARY_MAJOR_1,
AA.PRIMARY_MAJOR_1_DESC,
AA.PRIMARY_MAJOR_2,
AA.PRIMARY_MAJOR_2_DESC,
AA.PRIMARY_MINOR_1,
AA.PRIMARY_MINOR_1_DESC,
AA.PRIMARY_CONC_1,
AA.PRIMARY_CONC_1_DESC,
AA.PRIMARY_CONC_1_MAJOR_ATTACH,
AA.INST_ACCEPTED_APP_ANY_DATE_IND,
AA.COUNTY_CODE_ADMIT,
AA.COUNTY_DESC_ADMIT,
AA.STATE_CODE_ADMIT,
AA.STATE_DESC_ADMIT,
AA.NATION_CODE_ADMIT,
AA.NATION_DESC_ADMIT,
DM.FIRST_NAME,
DM.MIDDLE_INITIAL,
DM.LAST_NAME,
DM.NAME_SUFFIX,
DM.A1_STREET_LINE1,
DM.A1_STREET_LINE2,
DM.A1_STREET_LINE3,
DM.A1_CITY,
DM.A1_STATE_CODE,
DM.A1_STATE_DESC,
DM.A1_ZIP,
DM.A1_COUNTY_CODE,
DM.A1_COUNTY_DESC,
DM.A1_NATION_CODE,
DM.A1_NATION_DESC,
DM.A1_AREA_CODE,
DM.A1_PHONE_NUMBER,
case
when AA.COUNTY_CODE_ADMIT in 
('MI049','MI087','MI093','MI125','MI145','MI155','MI157')then 'Y'
else 'N'
end commutible_ind,
DM.CA_EMAIL,
DM.HM_EMAIL_1,
DM.BIRTHDATE,
DM.AGE,
DM.GENDER,
DM.GENDER_DESC,
DECODE(DM.gender,'F',2,1)IPEDS_GENDER_CODE,
DM.CITIZENSHIP_CODE,
DM.CITIZENSHIP_DESC,
DM.VETERAN_IND,
DM.INTL_IND,
DM.VETERAN_BENIFITS_ELIGIBILE,
DM.VISA_TYPE_CODE,
DM.VISA_TYPE_DESC,
DM.NATION_CITIZEN_CODE,
DM.NATION_CITIZEN_DESC,
DM.REPORT_ETHNICITY,
DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien',1,
      'Hispanic or Latino',2,
      'American Indian or Alaska Native',3,
      'Asian',4,
      'Black or African American',5,
      'Native Hawaiian and Other Pacific Islander',6,
      'White',7,
      'Two or more races',8,9)IPEDS_RACE_CODE,
DM.HSCH_CODE,
DM.HSCH_DESC,
DM.HSCH_GRAD_DATE,
DM.HSCH_RANK,
DM.HSCH_SIZE,
DM.HSCH_GPA,
DM.HSCH_STREET_LINE_1,
DM.HSCH_STREET_LINE_2,
DM.HSCH_CITY,
DM.HSCH_STATE,
DM.HSCH_ZIP,
DM.PCOL_CODE_1,
DM.PCOL_DESC_1,
DM.PCOL_TRANS_RECV_DATE_1,
DM.PCOL_DEGC_CODE_1,
DM.PCOL_DEGC_DATE_1,
DM.PCOL_HOURS_TRANSFERRED_1,
DM.PCOL_GPA_TRANSFERRED_1,
DM.VISA_NUMBER,
DM.NATION_BIRTH_CODE,
DM.NATION_BIRTH_DESC
from 
TD_ADMISSIONS_APPLICANT AA
inner join TD_DEMOGRAPHIC DM on DM.DM_PIDM = AA.AD_PIDM
and DM.TD_TERM_CODE = AA.TERM_CODE_ENTRY
LEFT JOIN IA_CW_FY_TERM FY ON FY.FY_TERM_CODE = AA.TERM_CODE_ENTRY
);