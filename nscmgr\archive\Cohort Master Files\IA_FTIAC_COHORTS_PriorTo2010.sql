/*
This query is designed to pull the FTIAC cohort and format it for the IA_CW_COHORT Table.

The rules to be in the cohort are:
  Student is a full time, registered, FTIAC, UG in the FAll term
   or the student is a full time or part time, registered, FTIAC, UG in the prior Summer term 
   and the student returned in the Fall term as a full time, registered, continuing, UG, in Fall term
  
This must be used between 2007 and 2009 becaue FTIAC students starting in summer during these
      3 years were not codded as FTIAC in the summer, whereas students starting in Fall 2010 were 
      rolled to the Fall as a student_type_code = 'F' and you will use the other script
*/

select
sd_pidm CO_PIDM,
sd_term_code CO_TERM_CODE_KEY, -- fall term code
sd_term_desc CO_TERM_DESC, -- fall term desc
student_type_code CO_STUDENT_TYPE_CODE,
student_type_desc CO_STUDNET_TYPE_DESC

from td_student_data td_started
where primary_level_code = 'UG'
and registered_ind = 'Y'
and student_type_code = 'F'
and (
  (
    sd_term_code = '200810'-- fall term code
    and full_part_time_ind_umf = 'F'
  ) or (
    sd_term_code = '200740'--summer term code
    and exists (
      select 'continued in fall'
      from td_student_data td_continued
      where td_started.sd_pidm = td_continued.sd_pidm
      and td_started.sd_term_code + 70 = td_continued.sd_term_code
      and td_continued.registered_ind = 'Y'
      and td_continued.full_part_time_ind_umf = 'F'
      and td_continued.primary_level_code = 'UG'
    )
  )  
)   
order by sd_pidm