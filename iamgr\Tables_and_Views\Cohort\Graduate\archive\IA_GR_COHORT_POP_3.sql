/*******************************************************************************
This script pulls the Graduate Student Cohorts going back to Fall 10
as of Fall 15 n=2641 with the 4 anamoulous student records removed

114623  201410
90306   201310
169980	201110
87706   201310
	
*******************************************************************************/
CREATE OR REPLACE VIEW IA_GR_COHORT_POP
AS
(
SELECT ia_td_student_data.sd_pidm AS CO_GR_PIDM,
  ia_td_student_data.UMID,
  ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
  to_date('01-SEP-'
  ||(to_number(SUBSTR(ia_td_student_data.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
  ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
  ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
  ia_td_student_data.report_ethnicity AS CO_GR_ETHNICITY,
  ia_td_student_data.gender           AS CO_GR_GENDER,
  ia_td_student_data.primary_level_code,
  ia_td_student_data.CITIZENSHIP_CODE,
  ia_td_student_data.CITIZENSHIP_DESC,
  ia_td_student_data.PCOL_GPA_TRANSFERRED_1,
  CASE
    WHEN PCOL_GPA_TRANSFERRED_1 > 5.000
    THEN PCOL_GPA_TRANSFERRED_1/2
    ELSE PCOL_GPA_TRANSFERRED_1
  END trans_gpa,-- This takes incoming grad students with 8. scale gpa and transforms it to a 4. scale
  ia_td_student_data.PCOL_DESC_1,
  ia_td_student_data.PRIMARY_MAJOR_1,
  ia_td_student_data.PRIMARY_PROGRAM,
  ia_td_student_data.PRIMARY_CONC_1
FROM ia_td_student_data
WHERE registered_ind      = 'Y'
AND ia_student_type_code IN ('N')
AND sd_term_code LIKE '%10'
AND sd_term_code >= '201110'
/* Students record anomalys removed*/
MINUS
SELECT ia_td_student_data.sd_pidm AS CO_GR_PIDM,
  ia_td_student_data.UMID,
  ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
  to_date('01-SEP-'
  ||(to_number(SUBSTR(ia_td_student_data.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
  ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
  ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
  ia_td_student_data.report_ethnicity AS CO_GR_ETHNICITY,
  ia_td_student_data.gender           AS CO_GR_GENDER,
  ia_td_student_data.primary_level_code,
  ia_td_student_data.CITIZENSHIP_CODE,
  ia_td_student_data.CITIZENSHIP_DESC,
  ia_td_student_data.PCOL_GPA_TRANSFERRED_1,
  CASE
    WHEN PCOL_GPA_TRANSFERRED_1 > 5.000
    THEN PCOL_GPA_TRANSFERRED_1/2
    ELSE PCOL_GPA_TRANSFERRED_1
  END trans_gpa, -- This takes incoming grad students with 8. scale gpa and transforms it to a 4. scale
  ia_td_student_data.PCOL_DESC_1,
  ia_td_student_data.PRIMARY_MAJOR_1,
  ia_td_student_data.PRIMARY_PROGRAM,
  ia_td_student_data.PRIMARY_CONC_1
FROM ia_td_student_data
--LEFT OUTER JOIN
--  (SELECT CO_GR_PIDM,CO_GR_TERM_CODE_KEY FROM IA_GR_COHORT_TRFMD
--  )cleaned
--ON cleaned.CO_GR_PIDM           = ia_td_student_data.sd_pidm
--AND cleaned.CO_GR_TERM_CODE_KEY = ia_td_student_data.SD_TERM_CODE
WHERE registered_ind            = 'Y'
AND ia_student_type_code       IN ('N')
AND sd_term_code LIKE '%10'
AND sd_term_code                   >= '201110'
/******************************************************************************
Anamalous student records removed Start
******************************************************************************/
AND (ia_td_student_data.sd_pidm     = 114623
AND ia_td_student_data.sd_term_code = '201410') 
OR (ia_td_student_data.sd_pidm      = 90306
AND ia_td_student_data.sd_term_code = '201310')
OR (ia_td_student_data.sd_pidm      = 169980
AND ia_td_student_data.sd_term_code = '201110')
OR (ia_td_student_data.sd_pidm      = 87706
AND ia_td_student_data.sd_term_code = '201310')

OR EXISTS --see explenation below*
  (SELECT CO_GR_PIDM,
    CO_GR_TERM_CODE_KEY
  FROM IA_GR_COHORT_TRFMD
  WHERE IA_GR_COHORT_TRFMD.CO_GR_PIDM  = ia_td_student_data.sd_pidm
  AND IA_GR_COHORT_TRFMD.CO_GR_TERM_CODE_KEY = ia_td_student_data.sd_term_code
  )
/******************************************************************************
Anamalous student records removed END
explenation*  This is a table created to clean up the records that had program
changes in the second fall of his or her cohort term.
******************************************************************************/  

UNION All
select *
from IA_GR_COHORT_TRFMD


  --Anamalous student records removed
);
/*filters the term code to pull only
fall terms greater than or equal to
fall 2011*/
--union all
--
--select
--ia_td_student_data.sd_pidm as CO_GR_PIDM,
--ia_td_student_data.sd_term_code,
--SUBSTR(ia_td_student_data.sd_term_code, 1, 4) || '10' CO_GR_TERM_CODE_KEY, -- Winter Term modified to Fall term
--ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
--ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
--ia_td_student_data.report_ethnicity as CO_GR_ETHNICITY,
--ia_td_student_data.gender as CO_GR_GENDER
--
--from ia_td_student_data
--
--where
--report_level_code in ('GR') and
--registered_ind = 'Y' and
--ia_student_type_code in ('N') and
--sd_term_code like '%20' and
--sd_term_code >= '201120' and  /*filters the term code to pull only fall terms greater than or equal to  fall 2011*/
--PRIMARY_MAJOR_1 = 'NUR' and
--PRIMARY_PROGRAM like 'MSN%'
--);
--
--select
--CO_GR_TERM_CODE_KEY,
--count (*)
--from IA_GR_COHORT_POP
--group by
--CO_GR_TERM_CODE_KEY
--order by
--CO_GR_TERM_CODE_KEY desc
--select distinct primary_level_code, PRIMARY_LEVEL_DESC
--from ia_td_student_data
--
--select distinct sd_term_code, sd_term_desc, count (sd_pidm)
--from ia_td_student_data
--where primary_level_code = 'UT' and
--registered_ind = 'Y'
--group by
--sd_term_code, sd_term_desc
--order by
--sd_term_code desc 