/*******************************************************************************
This query identifies and transforms the level code, major, and program in the
first fall term that do not match the third fall term.  Then replaces the first
fall term variables to the third fall term variables.
*******************************************************************************/
--n=140 of 2641
--create or replace view IA_GR_COHORT_cur3 as
--SELECT popsel3.*
--FROM
--  ( 
  WITH cur3 AS( 
--  SELECT IA_GR_COHORT_CUR1.* FROM IA_GR_COHORT_CUR1
--  MINUS
--  SELECT IA_GR_COHORT_CUR1.*
--  FROM IA_GR_COHORT_CUR1
--  INNER JOIN IA_GR_COHORT_CUR2
--  ON IA_GR_COHORT_CUR2.CO_GR_PIDM           = IA_GR_COHORT_CUR1.CO_GR_PIDM
--  AND IA_GR_COHORT_CUR2.FRST_FALL_TERM_CODE = IA_GR_COHORT_CUR1.FRST_FALL_TERM_CODE
--  UNION ALL
  SELECT IA_GR_COHORT_CUR2.* FROM IA_GR_COHORT_CUR2
  ),
  thrd_fall_to_frst_fall AS
  (SELECT CO_GR_PIDM,
    FRST_FALL_TERM_CODE,
    FRST_FALL_REG_IND,
    THRD_FALL_LVL_CODE FRST_FALL_LVL_CODE,
    THRD_FALL_MAJOR FRST_FALL_MAJOR,
    THRD_FALL_PROGRAM FRST_FALL_PROGRAM
  FROM cur3
  WHERE FRST_FALL_REG_IND    = 'Y'
  AND THRD_FALL_TERM_REG_IND = 'Y'
  AND (FRST_FALL_LVL_CODE   <> THRD_FALL_LVL_CODE
  OR FRST_FALL_MAJOR        <> THRD_FALL_MAJOR
  OR FRST_FALL_PROGRAM      <> THRD_FALL_PROGRAM )
  )
SELECT thrd_fall_to_frst_fall.CO_GR_PIDM,
  thrd_fall_to_frst_fall.FRST_FALL_TERM_CODE,
  thrd_fall_to_frst_fall.FRST_FALL_REG_IND,
  thrd_fall_to_frst_fall.FRST_FALL_LVL_CODE,
  thrd_fall_to_frst_fall.FRST_FALL_MAJOR,
  thrd_fall_to_frst_fall.FRST_FALL_PROGRAM,
  cur3.SCND_FALL_TERM_CODE,
  cur3.SCND_FALL_TERM_REG_IND,
  cur3.SCND_FALL_LVL_CODE,
  cur3.SCND_FALL_MAJOR,
  cur3.SCND_FALL_PROGRAM,
  cur3.THRD_FALL_CODE,
  cur3.THRD_FALL_TERM_REG_IND,
  cur3.THRD_FALL_LVL_CODE,
  cur3.THRD_FALL_MAJOR,
  cur3.THRD_FALL_PROGRAM,
  cur3.THRD_FALL_CODE,
  cur3.THRD_FALL_TERM_REG_IND,
  cur3.THRD_FALL_LVL_CODE,
  cur3.THRD_FALL_MAJOR,
  cur3.THRD_FALL_PROGRAM,
  cur3.FRTH_FALL_CODE,
  cur3.FRTH_FALL_TERM_REG_IND,
  cur3.FRTH_FALL_LVL_CODE,
  cur3.FRTH_FALL_MAJOR,
  cur3.FRTH_FALL_PROGRAM,
  cur3.FFTH_FALL_CODE,
  cur3.FFTH_FALL_TERM_REG_IND,
  cur3.FFTH_FALL_LVL_CODE,
  cur3.FFTH_FALL_MAJOR,
  cur3.FFTH_FALL_PROGRAM,
  cur3.SXTH_FALL_CODE,
  cur3.SXTH_FALL_TERM_REG_IND,
  cur3.SXTH_FALL_LVL_CODE,
  cur3.SXTH_FALL_MAJOR,
  cur3.SXTH_FALL_PROGRAM,
  cur3.SVNTH_FALL_CODE,
  cur3.SVNTH_FALL_TERM_REG_IND,
  cur3.SVNTH_FALL_LVL_CODE,
  cur3.SVNTH_FALL_MAJOR,
  cur3.SVNTH_FALL_PROGRAM,
  cur3.EIGTH_FALL_CODE,
  cur3.EIGTH_FALL_TERM_REG_IND,
  cur3.EIGTH_FALL_LVL_CODE,
  cur3.EIGTH_FALL_MAJOR,
  cur3.EIGTH_FALL_PROGRAM
FROM cur3
INNER JOIN thrd_fall_to_frst_fall
ON thrd_fall_to_frst_fall.CO_GR_PIDM           = cur3.CO_GR_PIDM
AND thrd_fall_to_frst_fall.FRST_FALL_TERM_CODE = cur3.FRST_FALL_TERM_CODE
--  )popsel3 
  
  ;