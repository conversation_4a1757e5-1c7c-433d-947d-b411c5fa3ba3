/*******************************************************************************
Author: <PERSON>, Jon <PERSON> of Michigan-Flint
Create date:  02-18-16
Description:	Queries to gather Banner data for upload to National 
              Student Clearinhouse Student Tracker for DE request
              used in competitor dashboard.
Notes:  EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
Known issue(s):	
*******************************************************************************/
--set basic selection criteria here
with base_sel as (
  select distinct 
  dm.first_name, 
  dm.middle_name, 
  dm.last_name, 
  dm.name_suffix, 
  dm.birthdate, 
  max(aa.term_code_entry) as term_code_entry, 
  dm.dm_pidm
  from td_admissions_applicant aa
  join td_demographic dm
    on aa.ad_pidm = dm_pidm  
    and aa.term_code_entry = dm.td_term_code
  inner join IA_CW_FY_TERM fy
    on aa.term_code_entry = fy.fy_term_code
  where 
  fy.fy in ('18-19','19-20','20-21','21-22','22-23')
  and aa.styp_code in ('F','T','R')						--F or T
  and aa.inst_accepted_app_any_date_ind = 'Y'		--Y or N
  and aa.registered_ind = 'N'						--Y or N
  group by dm.first_name, dm.middle_name, dm.last_name, dm.name_suffix, dm.birthdate, dm.dm_pidm
  ),

--select * from base_sel;

---------------------

pop_sel as (
  --This section creates a header file.
  select
  1 order_num,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  to_char(sysdate, 'YYYYMMDD') "E",
  'DA' "F",
  'I' "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from dual
  
  union
  --This section pulls the student records for the payload.
  select
  2 order_num,
  'D1' "A",
  null "B",
  case
  when (first_name is NULL and last_name is not NULL) then last_name
  when first_name = '.' then last_name
  when (first_name is NULL and last_name is NULL)then 'Jon'
  else  substr(first_name,1,20) 
  end "C",
  substr(regexp_replace(trim(middle_name), '[[:punct:] ]',null), 1 ,1) "D",
  case 
  when last_name is NULL then 'Doe'
  when last_name = '.' then 'Doe'
  else  substr(last_name,1,20) 
  end "E",
  substr(regexp_replace(trim(name_suffix), '[[:punct:] ]',null),1,5) "F",
  to_char(birthdate, 'YYYYMMDD') "G",
  to_char(to_number(SUBSTR(term_code_entry,1,4))-1 ||'0801') "H", 
  null "I",
  '002327' "J",
  '00' "K",
  to_char(dm_pidm) "L"
  from base_sel
  
    
  union
    --This is to count the number of records and append a trailer record
  select
  3 order_num,
  'T1' "A",
  to_char(count(base_sel.dm_pidm)+2) "B",
  null "C",
  null "D",
  null "E",
  null "F",
  null "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from base_sel
  
  order by order_num
)

select 
pop_sel.A,
pop_sel.B,
pop_sel.C,
pop_sel.D,
pop_sel.E,
pop_sel.F,
pop_sel.G,
pop_sel.H,
pop_sel.I,
pop_sel.J,
pop_sel.K,
pop_sel.L
from pop_sel
;
