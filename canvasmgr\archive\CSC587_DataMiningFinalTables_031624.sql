select
distinct
person_id,
term_code,
term_name

from temp;

--Time-Varying Semester Activity 
create table Time_Varying_Semester_Activity as
select
distinct
person_id,
term_code,
term_name,
AGE, 
CLASS_CODE, 
CLASS_DESC, 
ONLINE_COURSES_ONLY_IND, 
course_title,
status,
type,
max_enrollment,
CH, 
GRADE_QUALITY_POINTS, 
GRADE, 
SEC_GMOD_DESC, 
PDFW, 
GRADE_DIST, 
SATISFACTORY_UNSATISFACTORY, 
COMPLETED_IND, 
INSTRUCTOR_ID,
FINAL_GRADE,
UMF_TERM_GPA, 
UMF_OVERALL_GPA, 
HOLD_CODE_1, 
HOLD_DESC_1, 
HOLD_CODE_2, 
HOLD_DESC_2, 
HOLD_CODE_3, 
HOLD_DESC_3, 
HOLD_CODE_4, 
HOLD_DESC_4, 
HOLD_CODE_5, 
HOLD_DESC_5, 
MORE_HOLDS_IND

from temp;

--Time-Varying Course Assignement Activity 
create table Time_Varying_Course_Assign_Activity as
select
distinct
person_id,
term_code,
term_name,
course_title,
assign_title,
ASSIGN_SCORE, 
AVG_ASSIGN_SCORE, 
MAX_ASSIGN_SCORE, 
current_grade,
ASSIGN_COMMENT
from temp;

--
--Past Time-Invariant Student Data 
create table Past_Time_Invariant_Stud as 
select
distinct
person_id,
PRIMARY_COLLEGE_CODE, 
PRIMARY_COLLEGE_DESC, 
PRIMARY_DEGREE_CODE, 
PRIMARY_DEGREE_DESC, 
MAJOR, 
MAJOR_DESC, 
CONC, 
CONC_DESC,
PRIMARY_LEVEL_CODE, 
REPORT_LEVEL_CODE, 
GENDER, 
SEX, 
PPRN_CODE, 
PPRN_DESC, 
REPORT_ETHNICITY, 
REPORT_ETHNICITY_CODE, 
FLINT_PROMISE, 
PARENT_EUD, 
PARENT_EDU_DESC, 
FIRST_GEN, 
HSCH_CODE, 
HSCH_DESC, 
HSCH_GPA, 
PCOL_CODE, 
PCOL_DESC, 
PCOL_GPA, 
ACT_COMPOSITE, 
ACT_ENGLISH, 
ACT_MATH, 
ACT_READING, 
ACT_SCIENCE_REASONING, 
ACT_SUM_OF_STANDARD, 
SAT_TOTAL_COMBINED, 
SAT_READ_WRI, 
SAT_MATH


from temp;

