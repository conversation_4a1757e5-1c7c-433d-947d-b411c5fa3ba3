with 
-- all the ones that only have primary_major_1
pop_sel as( 
  select 
  case
    when ia_cw_hp.degree_code is not null then 1
    when ia_cw_stem.majr_code is not null then 2
    else 3
  end hp_stem_order_num,
  row_number() over(partition by ia_um_degree.pidm, ia_um_degree.college_code order by ia_um_degree.degree_seqno) college_order_num,
  ia_um_degree.college_code degree_college_code,
  ia_um_degree.primary_major_1 degree_major_code,  
  'primary degree' degree_source,
  ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
  where grad_term_code in ('201440', '201510', '201520', '201530')
  and ia_um_degree.degree_status = 'AW'
  and ia_um_degree.primary_major_2 is null
--  and pidm = 187157

  union all
  
  -- get the primary degree from primary_major_1 where primary_major_2 is not null
  select 
  case
    when ia_cw_hp.degree_code is not null then 1
    when ia_cw_stem.majr_code is not null then 2
    else 3
  end hp_stem_order_num,
  row_number() over(partition by ia_um_degree.pidm, ia_um_degree.college_code order by ia_um_degree.degree_seqno) college_order_num,
  ia_um_degree.college_code degree_college_code,
  ia_um_degree.primary_major_1 degree_major_code,  
  'primary degree first major' degree_source,
  ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
  where grad_term_code in ('201440', '201510', '201520', '201530')
  and ia_um_degree.degree_status = 'AW'
  and ia_um_degree.primary_major_2 is not null
  
    union all
 -- get the second degree from primary_major_2  
  select 
  case
    when ia_cw_hp.degree_code is not null then 1
    when ia_cw_stem.majr_code is not null then 2
    else 3
  end hp_stem_order_num,
  row_number() over(partition by ia_um_degree.pidm, ia_um_degree.college_code order by ia_um_degree.degree_seqno) college_order_num,
  ia_um_degree.college_code degree_college_code,
  ia_um_degree.primary_major_2 degree_major_code,
  'primary degree second major' degree_source,
  ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
  where grad_term_code in ('201440', '201510', '201520', '201530')
  and ia_um_degree.degree_status = 'AW'
  and ia_um_degree.primary_major_2 is not null

)
select 
pop_sel.pidm,
pop_sel.grad_term_code,
pop_sel.degree_college_code,
pop_sel.degree_major_code,
pop_sel.degree_source,
pop_sel.college_order_num,
pop_sel.hp_stem_order_num,
pop_sel.degree_seqno

from pop_sel

order by pop_sel.hp_stem_order_num, pop_sel.degree_seqno



--select
--spriden.spriden_pidm pidm,
--spriden.spriden_id umid,
--shrdgmr.shrdgmr_seq_no degree_seqno,
--shrdgmr.shrdgmr_degs_code degree_status,
--shrdgmr.shrdgmr_levl_code level_code,
--shrdgmr.shrdgmr_coll_code_1 college_code,
--shrdgmr.shrdgmr_degc_code degree_code,
--shrdgmr.shrdgmr_program primary_program,
--shrdgmr.shrdgmr_majr_code_1 primary_major_1,
--shrdgmr.shrdgmr_majr_code_1_2 primary_major_2,
--shrdgmr.shrdgmr_majr_code_minr_1 primary_minor_1,
--shrdgmr.shrdgmr_majr_code_minr_1_2 primary_minor_2,
--shrdgmr.shrdgmr_majr_code_conc_1 primary_conc_1,
--case
--  when length(shrdgmr.shrdgmr_majr_code_conc_1) > 0 then shrdgmr.shrdgmr_majr_code_1
--  else null
--end primary_conc_1_major_attach,
--shrdgmr.shrdgmr_majr_code_conc_1_2 primary_conc_2,
--case
--  when length(shrdgmr.shrdgmr_majr_code_conc_1_2) > 0 then shrdgmr.shrdgmr_majr_code_1
--  else null
--end primary_conc_2_major_attach,
--shrdgmr.shrdgmr_appl_date degree_appl_date,
--shrdgmr.shrdgmr_grad_date grad_date,
--shrdgmr.shrdgmr_term_code_grad grad_term_code,
--shrdgmr.shrdgmr_grst_code grad_status,
--shrdgmr.shrdgmr_term_code_ctlg_1 catalog_term_code,
--shrdgmr.shrdgmr_activity_date,
--(select
-- --Maps stvdegc_acat_code to ia_nces_code
-- decode(stvdegc_acat_code, '21', '1', '22', '2', '23', '3', '25', '4',
--                           '24', '5', '41', '6', '42', '7', '43', '8',
--                           '44', '17', '31', '18', '32','8','45','18',
--                          ' 46','19','0')
-- from stvdegc_ext stvdegc where stvdegc.stvdegc_code = shrdgmr.shrdgmr_degc_code) ia_nces_code,
-- 
--shrdgmr.shrdgmr_acyr_code academic_year,
--shrdgmr.shrdgmr_dept_code department_code,
--(select stvdept.stvdept_desc from stvdept_ext stvdept where stvdept.stvdept_code = shrdgmr.shrdgmr_dept_code) department_desc
--from shrdgmr_ext shrdgmr
--inner join spriden_ext spriden on spriden.spriden_pidm = shrdgmr.shrdgmr_pidm
--                               and spriden.spriden_change_ind is null
--where shrdgmr.shrdgmr_degs_code in ('AW', 'AD', 'PN', 'SO');
