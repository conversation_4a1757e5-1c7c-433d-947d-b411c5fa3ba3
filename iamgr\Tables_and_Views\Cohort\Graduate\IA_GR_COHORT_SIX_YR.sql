--CREATE OR REPLACE VIEW IA_GR_COHORT_SIX_YR
--AS
  /*******************************************************************************
  Table grain: 1 row / student
  Purpose: pull student data projected out 6 years on a student and create view
  Primary keys: IA_GR_COHORT_POP.CO_GR_PIDM
  Foreign keys: TD_STUDENT_DATA.SD_PIDM
  using report_level_code because the primary_level_code was changed in the
  system for many of the programs.  Tried to link on Primary_Major_1 and
  primary_program but many of these had been altered in the system as well.
  *******************************************************************************/
  SELECT DISTINCT CO_GR_PIDM, CO_GR_TERM_CODE_KEY,START_DATE,
    --First Fall********************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    ) frst_fall_term_code,
--    (SELECT td1.registered_ind
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    ) frst_fall_reg_ind,
    --------------------------Second----------------------------------------------
    --Second Fall Set *************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) Scnd_fall_term_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
    AND td1.student_type_code   = 'C'
    ) Scnd_fall_term_reg_ind,
    case
    when 
    (select count(*) 
    FROM ia_um_degree 
    WHERE ia_um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW' 
    AND ia_um_degree.DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.GRAD_DATE > IA_GR_COHORT_POP.START_DATE
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100)))>0
    then 'Y'
    else null
    end scnd_fall_grad_ind,
    ----------------------------Third-----------------------------------------------
    -- Third Fall Set **************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) thrd_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
    AND td1.student_type_code   = 'C'
    ) thrd_fall_term_reg_ind,
    case
    when 
    (select count(*) 
    FROM ia_um_degree 
    WHERE ia_um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW' 
    AND ia_um_degree.DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.GRAD_DATE > IA_GR_COHORT_POP.START_DATE
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200)))>0
    then 'Y'
    else null
    end thrd_fall_grad_ind,
    ----------------------------fourth---------------------------------------------------
    -- Fourth Fall Set ******************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )frth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
    AND td1.student_type_code   = 'C'
    )frth_fall_term_reg_ind,
    case
    when 
    (select count(*) 
    FROM ia_um_degree 
    WHERE ia_um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW' 
    AND ia_um_degree.DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.GRAD_DATE > IA_GR_COHORT_POP.START_DATE
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300)))>0
    then 'Y'
    else null
    end frth_fall_grad_ind,
    -----------------------------Fifth--------------------------------------------------
    -- Fifth Fall Set ******************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) ffth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
    AND td1.student_type_code   = 'C'
    ) ffth_fall_term_reg_ind,
    case
    when 
    (select count(*) 
    FROM ia_um_degree 
    WHERE ia_um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW' 
    AND ia_um_degree.DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.GRAD_DATE > IA_GR_COHORT_POP.START_DATE
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400)))>0
    then 'Y'
    else null
    end ffth_fall_grad_ind,
    -----------------------------Sixth--------------------------------------------
    -- Sixth Fall Set **********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) sxth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
    AND td1.student_type_code   = 'C'
    ) sxth_fall_term_reg_ind,
    case
    when 
    (select count(*) 
    FROM ia_um_degree 
    WHERE ia_um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW' 
    AND ia_um_degree.DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.GRAD_DATE > IA_GR_COHORT_POP.START_DATE
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500)))>0
    then 'Y'
    else null
    end sxth_fall_grad_ind,
    ---------------------------Seventh-------------------------------------------
    --Seventh Fall Set ***********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
--    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )svnth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code      = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
    AND td1.report_level_code   = IA_GR_COHORT_POP.report_level_code
    AND td1.PRIMARY_DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND td1.sd_pidm             = IA_GR_COHORT_POP.CO_GR_PIDM
    AND td1.student_type_code   = 'C'
    )svnth_fall_term_reg_ind,
    case
    when 
    (select count(*) 
    FROM ia_um_degree 
    WHERE ia_um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW' 
    AND ia_um_degree.DEGREE_CODE = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.GRAD_DATE > IA_GR_COHORT_POP.START_DATE
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600)))>0
    then 'Y'
    else null
    end SVNTH_fall_grad_ind,-- sixth Year Graduation Indicator
   
    (SELECT max(GRAD_DATE)
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND ia_um_degree.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND ia_um_degree.DEGREE_CODE    = IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE
    AND ia_um_degree.GRAD_DATE      > IA_GR_COHORT_POP.START_DATE
   
    )grad_date 
    -- Cohort Demographic Table joins and Where Clause ************************
    FROM TD_student_data
    INNER JOIN IA_GR_COHORT_POP
    ON IA_GR_COHORT_POP.CO_GR_PIDM           = TD_student_data.sd_pidm
    AND iA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY = TD_STUDENT_DATA.sd_term_code
    AND IA_GR_COHORT_POP.PRIMARY_DEGREE_CODE = TD_student_data.PRIMARY_DEGREE_CODE;
--graduated multiple degrees with the same degree code but different grad dates
--    where IA_GR_COHORT_POP.CO_GR_PIDM not in (187157,
--74927,
--97866,
--74840,
--89021,
--105140,
--197713);
