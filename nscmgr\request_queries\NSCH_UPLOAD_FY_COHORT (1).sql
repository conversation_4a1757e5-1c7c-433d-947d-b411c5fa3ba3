--SELECT COUNT (*)
--FROM IA_FY_COHORT_POP_UNDUP_TBL CO
--INNER JOIN IA_FY_COHORT_FA FA
--ON CO.CO_PIDM       = FA.CO_PIDM
--AND CO.FY_TERM_CODE = FA.FY_TERM_CODE
--INNER JOIN IA_FY_HLC_STUDY_TBL HLC
--ON CO.CO_PIDM                  = HLC.CO_PIDM
--AND CO.FY_TERM_CODE            = HLC.FY_TERM_CODE
--WHERE CO.FY                    = '09-10'
--AND CO.CO_IA_STUDENT_TYPE_CODE = 'T'  -- F or T
--AND CO.CO_FULL_PART_IND_UMF    = 'P'  -- F or P
--AND FA.PELL_GRANT_IND          = 'N'  -- Y or N
--AND HLC.DECEASED_IND           = 'NO' -- YES or NO
--;
/*******************************************************************************
AUTHOR: DAN GETTY UNIVERSITY OF MICHIGAN-FLINT
CREATE DATE:  02-18-16
DESCRIPTION:	QUERIES TO GATHER BANNER DATA FOR UPLOAD TO NATIONAL 
              STUDENT CLEARINHOUSE STUDENT TRACKER FOR IPEDS OUTCOME MEASURES
NOTES:  EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
KNOWN ISSUE(S):	
*******************************************************************************/
WITH BASE_SEL AS ( 
  SELECT DISTINCT 
  SD.FIRST_NAME,
  SD.MIDDLE_INITIAL,
  SD.LAST_NAME,
  SD.NAME_SUFFIX, 
  SD.BIRTHDATE,
  CO.FY_CO_TERM_CODE,
  SD.SD_PIDM
  FROM IA_TD_STUDENT_DATA SD
  INNER JOIN IA_FY_COHORT_POP_UNDUP_TBL CO
  ON CO.CO_PIDM = SD.SD_PIDM
  AND CO.FY_TERM_CODE = SD.SD_TERM_CODE
  INNER JOIN IA_FY_COHORT_FA FA
  ON FA.CO_PIDM = SD.SD_PIDM
  AND FA.FY_TERM_CODE = SD.SD_TERM_CODE
  INNER JOIN IA_FY_HLC_STUDY_TBL HLC
  ON HLC.CO_PIDM = SD.SD_PIDM
  AND HLC.FY_TERM_CODE = SD.SD_TERM_CODE
  WHERE CO.FY = '12-13'
  AND CO.CO_IA_STUDENT_TYPE_CODE = 'T'  -- F or T
  AND CO.CO_FULL_PART_IND_UMF = 'P'     -- F or P
  AND FA.PELL_GRANT_IND = 'N'           -- Y or N
  AND HLC.DECEASED_IND = 'NO'           -- YES or NO
),

POP_SEL AS (
  --THIS SECTION CREATES A HEADER FILE.
  SELECT
  1 ORDER_NUM,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  TO_CHAR(SYSDATE, 'YYYYMMDD') "E",
  'CO' "F",
  'I' "G",
  NULL "H",
  NULL "I",
  NULL "J",
  NULL "K",
  NULL "L"
  FROM DUAL
  
  UNION ALL
  --THIS SECTION PULLS THE STUDENT RECORDS FOR THE PAYLOAD.
  SELECT
  2 ORDER_NUM,
  'D1' "A",
  NULL "B",
  CASE
  WHEN (FIRST_NAME IS NULL or FIRST_NAME = '.') THEN 'Jon'
  ELSE  SUBSTR(FIRST_NAME,1,20) 
  END "C",
  SUBSTR(REGEXP_REPLACE(TRIM(MIDDLE_INITIAL), '[[:punct:] ]',NULL), 1 ,1) "D",
   CASE 
  WHEN (LAST_NAME IS NULL OR LAST_NAME = '.') THEN 'Doe'
  ELSE  SUBSTR(LAST_NAME,1,20) 
  END "E",
  SUBSTR(REGEXP_REPLACE(TRIM(NAME_SUFFIX), '[[:punct:] ]',NULL),1,5) "F",
  TO_CHAR(BIRTHDATE, 'YYYYMMDD') "G",
  TO_CHAR(TO_NUMBER(SUBSTR(FY_CO_TERM_CODE,1,4))-1 ||'0915') "H", 
  NULL "I",
  '002327' "J",
  '00' "K",
  TO_CHAR(SD_PIDM) "L"
  FROM BASE_SEL

  UNION ALL
    --THIS IS TO COUNT THE NUMBER OF RECORDS AND APPEND A TRAILER RECORD
  SELECT
  3 ORDER_NUM,
  'T1' "A",
  TO_CHAR(COUNT(SD_PIDM)+2) "B",
  NULL "C",
  NULL "D",
  NULL "E",
  NULL "F",
  NULL "G",
  NULL "H",
  NULL "I",
  NULL "J",
  NULL "K",
  NULL "L"
  FROM BASE_SEL
)
SELECT 
POP_SEL.A,
POP_SEL.B,
POP_SEL.C,
POP_SEL.D,
POP_SEL.E,
POP_SEL.F,
POP_SEL.G,
POP_SEL.H,
POP_SEL.I,
POP_SEL.J,
POP_SEL.K,
POP_SEL.L
FROM POP_SEL
ORDER BY ORDER_NUM ASC
;
