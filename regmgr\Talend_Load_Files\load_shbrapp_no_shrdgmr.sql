--------------------------------------------------------
--  File created - Friday-April-02-2021   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View LOAD_SHBGAPP_NO_SHRDGMR
--------------------------------------------------------

CREATE OR REPLACE VIEW load_shbgapp_no_shrdgmr AS
SELECT
 shbgapp.shbgapp_grad_term_code    grad_term,
 shbgapp.shbgapp_last_name
 || ', '
 || shbgapp.shbgapp_first_name
 || ' '
 || shbgapp.shbgapp_mi              last_first_mi,
 um.umid,
 shbgapp.shbgapp_seqno
FROM
      aimsmgr.shbgapp shbgapp
 INNER JOIN um_demographic um ON um.dm_pidm = shbgapp.shbgapp_pidm
WHERE
  shbgapp_grad_term_code >= '201910'
 AND NOT EXISTS (
  SELECT
   'x'
  FROM
   aimsmgr.shrdgmr shrdgmr
  WHERE
    shbgapp_pidm = shrdgmr_pidm
   AND shbgapp_grad_term_code = shrdgmr_term_code_grad
 )
ORDER BY
 shbgapp.shbgapp_grad_term_code DESC,
 um.umid;