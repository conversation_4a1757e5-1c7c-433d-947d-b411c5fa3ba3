TRUNCATE TABLE IA_NSC_DEGREES_BAC;

INSERT INTO IA_NSC_DEGREES_BAC 
SELECT * FROM IA_NSC_DEGREES;

COMMIT;

SELECT COUNT (*) from IA_NSC_DEGREES;

SELECT COUNT (*) from IA_NSC_DEGREES_BAC;

TRUNCATE TABLE IA_NSC_DEGREES;

insert into IA_NSC_DEGREES 
(
select * from
(
WITH data_pull AS
  (SELECT 
    REPLACE (raw_pidm,'_','') co_pidm,
    TO_CHAR(to_number(SUBSTR(SEARCH_DATE,1,4))+1)
    || '10' co_term_code_key,
    --GRADUATION_DATE,
    SUBSTR(GRADUATION_DATE,1,4) GRADUATION_DATE_year,
    SUBSTR(GRADUATION_DATE,5,2) GRADUATION_DATE_Month,
    to_date(GRADUATION_DATE, 'YYYYMMDD') graduation_date,
    TWOYEAR_FOURYEAR,
    PU<PERSON><PERSON>_PRIVATE,
    COLLEGE_CODE,
    COLLEGE_NAME,
    GRADUATED_IND,
    DEGREE_TITLE,
    DEGREE_MAJOR_1,
DEGREE_CIP_1
  FROM IA_COHORT_NSC_RAW
  WHERE (class_level IS NULL
  OR class_level     IN ('F','S','J','R','C','N','B'))
    --and
    --raw_pidm = '99839_'
  ),
  term_code_step1 AS
  (SELECT data_pull.*,
/*
Including graduation dates that do not match umflint graduation cycles because 
it is important to identify if the student recieved a degree at another institution.
This is inconsistant with the enrollment records. Enrollment records can be removed 
to to make a better fit with our enrollment schedule and not have a large impact 
on external retention rates whereas a degree records that is removed will have a
large impact on graduation rates at another institution.
*/
    CASE
      WHEN GRADUATION_DATE_Month in ('06','07','08')
      THEN '40'
      WHEN GRADUATION_DATE_Month in ('09','10','11')
      THEN '10'
      WHEN GRADUATION_DATE_Month in ('12','01','02')
      THEN '20'
      WHEN GRADUATION_DATE_Month in ('03','04','05')
      THEN '30'
    END grad_date_month_day_sem_code
   FROM data_pull
  ),
  term_code AS
  (SELECT term_code_step1.*,
    CASE
      WHEN grad_date_month_day_sem_code = '10'
      THEN TO_CHAR(to_number (GRADUATION_DATE_year)+1)
        || grad_date_month_day_sem_code
      WHEN grad_date_month_day_sem_code = '20'
      THEN GRADUATION_DATE_year
        || grad_date_month_day_sem_code
      WHEN grad_date_month_day_sem_code = '30'
      THEN GRADUATION_DATE_year
        || grad_date_month_day_sem_code
      WHEN grad_date_month_day_sem_code = '40'
      THEN GRADUATION_DATE_year
        || grad_date_month_day_sem_code
    END term_code_graduated
  FROM term_code_step1
  ), DEGREE_COUNT AS(
SELECT 
  row_number() over(partition BY CO_pidm order by CO_PIDM,term_code_graduated) DEGREE_COUNT,
  TERM_CODE.*
FROM term_code
WHERE GRADUATED_IND = 'Y'
AND DEGREE_TITLE IS NOT NULL
AND DEGREE_TITLE NOT LIKE 'MASTER%'
AND DEGREE_TITLE NOT LIKE 'CONCENTRATION%'
AND COLLEGE_CODE NOT IN ('002327-00')
)
SELECT 
  DEGREE_COUNT,
  to_number(co_pidm) co_pidm,
  co_term_code_key,
  term_code_graduated,
  GRADUATED_IND,
  TWOYEAR_FOURYEAR,
  PUBLIC_PRIVATE,
  COLLEGE_CODE,
  COLLEGE_NAME,
  DEGREE_TITLE,
  DEGREE_MAJOR_1,
  DEGREE_CIP_1

FROM DEGREE_COUNT
WHERE DEGREE_COUNT = '1'--keep only the first degree obtained by the student in a term
)popsel
)
;
commit;