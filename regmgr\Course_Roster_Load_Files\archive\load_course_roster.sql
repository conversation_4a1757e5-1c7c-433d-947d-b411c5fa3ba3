CREATE OR REPLACE VIEW load_course_roster AS 
 SELECT
 --student info
  rd.pidm,
  dm.umid,
  CASE
   WHEN pprn_desc IS NULL THEN
    dm.last_name
    || ','
    || dm.first_name
   ELSE
    dm.last_name
    || ','
    || dm.first_name
    || ','
    || dm.middle_initial
    || ','
    || dm.pprn_desc
  END                                                       stud_name,
  dm.ca_email,
  dm.a1_area_code || dm.a1_phone_number                     AS phone_number,
  rd.levl_code,
  rd.levl_desc,
  rd.term_code,
  rd.term_desc,
  rd.rsts_date,
  sd.full_part_time_ind_umf,
  sd.residency_code,
  sd.residency_desc,
  sd.primary_degree_code,
  sd.primary_program,
  sd.primary_major_1,
  sd.primary_conc_1,
  sd.class_code,
  sd.class_desc,
  sd.va_cert_date,
  sd.va_cert_hours,
  sd.va_cert_term,
  sd.va_cert_term_ind,
  sd.va_cert_type,
  sd.va_cert_type_desc,
  sd.primary_college_code,
  sd.primary_college_desc,
  sd.overall_hours_earned,

--instructor info
  replace(cs.primary_instructor_email, '@umich.edu')        primary_instructor_uniqname,
  cs.primary_instructor_id,
  cs.primary_instructor_last_name
  || ', '
  || cs.primary_instructor_first_name                       AS ins_name,
  cs.primary_instructor_email,
  cs.instructor_id2,
  cs.instructor_last_name2
  || ', '
  || cs.instructor_first_name2                              AS second_ins_name,
  cs.instructor_email2,
  cs.instructor_id3,
  cs.instructor_last_name3
  || ', '
  || cs.instructor_first_name3                              AS third_ins_name,
  cs.instructor_email3,

--course info
  rd.subj_code,
  rd.crse_number,
  rd.subj_code
  || ' '
  || rd.crse_number                                         AS course,
  rd.subj_code
  || ' '
  || rd.crse_number
  || ' '
  || rd.section_number                                      AS selected_course,
  rd.section_number,
  cs.cat_title,
  cs.course_text,
  rd.rsts_code,
  rd.rsts_desc,
  cs.meeting_start_date_1,
  cs.meeting_end_date_1,
  cs.meeting_days_1,
  cs.meeting_times_1,
  cs.meeting_schd_code_1,
  CASE
   WHEN cs.xlst_group IS NOT NULL THEN
    cs.xlst_group || 'XXX'
   ELSE
    cs.crn_key
  END                                                       um_crn_group_key,
  (
   SELECT
    LISTAGG(c1.subj_code
            || c1.crse_number
            || '-'
            || c1.seq_number_key, '_') WITHIN GROUP(
     ORDER BY
      c1.crse_number
      || c1.subj_code
      || c1.seq_number_key
    )
   FROM
    um_catalog_schedule c1
   WHERE
     CASE
      WHEN c1.xlst_group IS NOT NULL THEN
       c1.xlst_group || 'XXX'
      ELSE
       c1.crn_key
     END = CASE
            WHEN cs.xlst_group IS NOT NULL THEN
             cs.xlst_group || 'XXX'
            ELSE
             cs.crn_key
           END
    AND c1.term_code_key = cs.term_code_key
  )
  || '_'
  || replace(cs.term_desc, ' ', '-')                        um_crn_group_key_desc,
  rd.crn_key,
  cs.xlst_group,
  cs.xlst_courses,
  cs.xlst_actual_enrollment,
  cs.cat_civic_engage_ind,
  cs.sec_civic_engage_ind,
  rd.billing_hours,
  rd.camp_code,
  rd.camp_desc,
  rd.coll_code,
  rd.coll_desc,
  rd.credit_hours
 FROM
       um_registration_detail rd
  INNER JOIN um_catalog_schedule  cs ON cs.term_code_key = rd.term_code
                                       AND cs.crn_key = rd.crn_key
  INNER JOIN um_demographic       dm ON dm.dm_pidm = rd.pidm
  INNER JOIN um_student_data      sd ON sd.sd_pidm = rd.pidm
                                   AND sd.sd_term_code = rd.term_code
 WHERE
  rd.rsts_code IN (
   SELECT
    stvrsts.stvrsts_code
   FROM
    aimsmgr.stvrsts_ext stvrsts
   WHERE
    stvrsts.stvrsts_incl_sect_enrl = 'Y'
  )
  AND cs.csta_code IN ( 'A', 'X' )
  AND cs.ssts_code = 'A'
  AND cs.crse_number != 'XXX'
  AND rd.term_code >= (
   SELECT
    current_term
   FROM
    um_current_term
  )
  AND rd.term_code <= (
   SELECT
    MAX(sfrstcr_term_code)
   FROM
    aimsmgr.sfrstcr_ext
  );

--describe aimsmgr.sfrstcr_ext;