/*IA DataWarehouse
The purpose of the Data warehouse is to archive enrolment and retention 
data on a day by day basis.
*/


select 
/*Headcount ,Day, College/Student Type, Level,FT PT,
Promise Scholar Vet Ind, Int Ind*/
trunc(sysdate) as run_date,
primary_college_desc,
primary_college_code,
ia_projected_student_type_desc,
ia_projected_student_type,
report_level_code,
FULL_PART_TIME_IND_UMF,

(case PRIMARY_ADMIT_CODE
  when 'CH' Then 'Y'
  else 'N'
  End) Promise_Ind,

(case VETERAN_IND
  when 'Y' Then 'Y'
  else 'N'
  End) veteran_ind,
  
(case INTL_IND
  when 'Y' Then 'Y'
  else 'N'
  End) intl_ind,

count (sd_pidm) as Headcount,

/*Total Credit Hours/Day/Level (UG/GR)*/
count (total_credit_hours_umf) as Credit_Hours 





/*Retention /Day/ FT FTIAC, Domestic, International, Veteran, Promise Scholor
                  FT Transfer, Domestic, International, Veteran, */
                  
from
IA_UM_STUDENT_DATA

where
registered_ind = 'Y' and
--FULL_PART_TIME_IND_UMF = 'F' and

sd_term_code = '201610'
--sd_term_code like '%10' and sd_term_code > '201510'

group by
trunc(sysdate),
primary_college_desc,
primary_college_code,
ia_projected_student_type_desc,
ia_projected_student_type,
report_level_code,
FULL_PART_TIME_IND_UMF,
(case PRIMARY_ADMIT_CODE
  when 'CH' Then 'Y'
  else 'N'
  End),
(case VETERAN_IND
  when 'Y' Then 'Y'
  else 'N'
  End),
(case INTL_IND
  when 'Y' Then 'Y'
  else 'N'
  End)
;

--select
--trunc(sysdate) as run_date
--max(sd_term_code)
--from
--IA_UM_STUDENT_DATA
--where
--sd_term_code like '%10'

--select
--distinct PRIMARY_ADMIT_DESC,
--PRIMARY_ADMIT_CODE
--from IA_UM_STUDENT_DATA
----
--select
--(case PRIMARY_ADMIT_CODE
--  when 'CH' Then 'Y'
--  else 'N'
--  End) Promise_Ind
--from IA_UM_STUDENT_DATA

