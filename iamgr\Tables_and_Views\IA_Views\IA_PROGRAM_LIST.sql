--This view builds the program list
--identify onine courses
create or replace view ia_program_list as
with cmjr_sel as(
    select
    sorcmjr.*,
    row_number() over(partition by sorcmjr_curr_rule, sorcmjr_majr_code order by sorcmjr_term_code_eff desc) order_num
    from sorcmjr
    where sorcmjr_stu_ind = 'Y'
),
ccon_sel as(
    select
    sorccon.*,
    row_number() over(partition by sorccon_curr_rule, sorccon_majr_code_conc order by sorccon_term_code_eff desc) order_num
    from sorccon
    where sorccon_stu_ind = 'Y'
)

select 
sobcurr_levl_code,
(select stvlevl.stvlevl_desc from stvlevl stvlevl where stvlevl_code = sobcurr_levl_code) levl_desc,
sobcurr_coll_code,
(select stvcoll.stvcoll_desc from stvcoll stvcoll where stvcoll_code = sobcurr_coll_code) coll_desc,
sobcurr_program,
(select smrprle_program_desc from smrprle smrprle where smrprle_program = sobcurr_program) program_desc,
sorcmjr_majr_code,
(select stvmajr.stvmajr_desc from stvmajr stvmajr where stvmajr_code = sorcmjr_majr_code) majr_desc,
sorccon_majr_code_conc,
(select stvmajr.stvmajr_desc from stvmajr stvmajr where stvmajr_code = sorccon_majr_code_conc) conc_desc,
sobcurr_term_code_init

from sobcurr
inner join cmjr_sel on cmjr_sel.sorcmjr_curr_rule = sobcurr_curr_rule
                         and cmjr_sel.order_num = 1
--left outer join cmnr_sel on cmnr_sel.sorcmnr_curr_rule = sobcurr_curr_rule
--                         and cmnr_sel.order_num = 1
inner join ccon_sel on ccon_sel.sorccon_curr_rule = sobcurr_curr_rule
                         and ccon_sel.sorccon_cmjr_rule = cmjr_sel.sorcmjr_cmjr_rule
                         and ccon_sel.order_num = 1
where sobcurr_lock_ind = 'Y'
and sobcurr_term_code_init = (select max(s1.sobcurr_term_code_init)
                              from sobcurr s1
                              where s1.sobcurr_curr_rule = sobcurr.sobcurr_curr_rule
                              and s1.sobcurr_curr_rule <= '202010')
--and sobcurr_levl_code = 'UG'
--and sobcurr_program = 'B-EH'
and sobcurr_program not like '%3'
and sobcurr_program not like '%2'
--and sobcurr_levl_code  'UG'
order by
sobcurr_levl_code desc,
--sobcurr_coll_code,
--sobcurr_program,
sorcmjr_majr_code 
--,
--sorcmnr_majr_code_minr,
--sorccon_majr_code
;