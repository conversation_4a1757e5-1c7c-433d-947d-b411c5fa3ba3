desc ia_degree_completions;

------------------------------------Bachelor------------------------------------
--Completors and Completions of <PERSON>
select 
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '5'
and COMPLETION_TYPE_CODE = 1
;

--Completors and Completions of <PERSON> by age
select 
case 
when age >= 18 and age <= 24 then '18-24' 
when age >= 25 and age <= 39 then '25-39' 
when age >= 40 then '40+'
end age_group,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '5'
and COMPLETION_TYPE_CODE = 1
group by 
case 
when age >=18 and age <= 24 then '18-24' 
when age >=25 and age <= 39 then '25-39' 
when age >= 40 then '40+'
end
;

--Completors and Completions of <PERSON> by gender
select 
gender,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '5'
and COMPLETION_TYPE_CODE = 1
group by 
gender
;

--Completors and Completions of Bach by ethnicity
select 
REPORT_ETHNICITY,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '5'
and COMPLETION_TYPE_CODE = 1
group by 
REPORT_ETHNICITY
;select 
REPORT_ETHNICITY,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '5'
and COMPLETION_TYPE_CODE = 1
group by 
REPORT_ETHNICITY;

-------------------------------------Masters------------------------------------
--Completors and Completions of Bach
select 
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '7'
and COMPLETION_TYPE_CODE = 1
;

--Completors and Completions of Bach by age
select 
case 
when age <= 24 then '24-' 
when age >=25 and age <= 39 then '25-39' 
when age >= 40 then '40+'
end age_group,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '7'
and COMPLETION_TYPE_CODE = 1
group by 
case 
when age <= 24 then '24-' 
when age >=25 and age <= 39 then '25-39' 
when age >= 40 then '40+'
end
;

--Completors and Completions of Bach by gender
select 
gender,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '7'
and COMPLETION_TYPE_CODE = 1
group by 
gender
;

--Completors and Completions of Bach by ethnicity
select 
REPORT_ETHNICITY,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code = '7'
and COMPLETION_TYPE_CODE = 1
group by 
REPORT_ETHNICITY
;
----------------------------------Post Bach Cert--------------------------------
--Completors and Completions of Bach
select 
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code in ('6','8')
and COMPLETION_TYPE_CODE = 1
;

-------------------------------------Doctoral-----------------------------------
--Completors and Completions of Bach
select 
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code in ('17','18')
and COMPLETION_TYPE_CODE = 1
;

--Completors and Completions of Bach by age
select 
case 
when age <= 24 then '24-' 
when age >=25 and age <= 39 then '25-39' 
when age >= 40 then '40+'
end age_group,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code in ('17','18')
and COMPLETION_TYPE_CODE = 1
group by 
case 
when age <= 24 then '24-' 
when age >=25 and age <= 39 then '25-39' 
when age >= 40 then '40+'
end
;

--Completors and Completions of Bach by ethnicity
select 
REPORT_ETHNICITY,
count (distinct DC.PIDM) completors,
count (DC.PIDM) completions
from ia_degree_completions DC
where
fiscal_year = '22-23'
and nces_code in ('17','18')
and COMPLETION_TYPE_CODE = 1
group by 
REPORT_ETHNICITY
;
