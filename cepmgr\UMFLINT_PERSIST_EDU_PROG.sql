/******************************************************************************* 
This Query is designed to build a view to support a persistence dashboard 
it will run on the current term and pull in continuation varaiables for the 
following three semesters with open enrollment.  See query below for availible 
terms
Written by: Dan Getty Institutional Analisys
Date: 040521
*******************************************************************************/  
--DROP MATERIALIZED VIEW UMFLINT_PERSIST_EDU_PROG_MV;

CREATE MATERIALIZED VIEW UMFLINT_PERSIST_EDU_PROG_MV
BUILD IMMEDIATE 
REFRESH FORCE
ON DEMAND
as
select * from umflint_persist_edu_prog;

--CREATE OR REPLACE VIEW umflint_persist_edu_prog AS

/******************************************************************************* 
Cohort set Term Set 3312
*******************************************************************************/
 WITH co AS (
  SELECT
   ROW_NUMBER()
   OVER(PARTITION BY td.sd_pidm, td.primary_major_1
        ORDER BY td.sd_term_code ASC
   )                             row_id,
   td.sd_pidm                    co_pidm,
   td.umid,
   dm.first_name,
   dm.last_name,
   dm.age,
   dm.gender,
   dm.report_ethnicity,
   dm.hsch_gpa,
   dm.pcol_gpa_transferred_1     pcol_gpa,
   dm.pcol_degc_code_1           pcol_degree_code,
   td.residency_desc,
   stvterm.stvterm_start_date    start_date,
   fy.fy                         co_fy,
   td.sd_term_code               co_term_code,
   td.sd_term_desc               co_term_desc,
   td.primary_level_code         co_primary_level_code,
   td.primary_degree_code        co_degree_code,
   td.primary_major_1            co_major,
   CASE
    WHEN td.primary_conc_1 = 'ECHD'
         OR td.primary_conc_2 = 'ECHD'
         OR td.primary_conc_3 = 'ECHD'
         OR td.primary_conc_4 = 'ECHD'
         OR td.primary_conc_5 = 'ECHD'
         OR td.primary_conc_6 = 'ECHD' THEN
     'Yes'
    ELSE
     'No'
   END                           early_childhood_conc,
   CASE
    WHEN td.primary_conc_1 = 'ELED'
         OR td.primary_conc_2 = 'ELED'
         OR td.primary_conc_3 = 'ELED'
         OR td.primary_conc_4 = 'ELED'
         OR td.primary_conc_5 = 'ELED'
         OR td.primary_conc_6 = 'ELED' THEN
     'Yes'
    ELSE
     'No'
   END                           elem_ed_conc,
   CASE
    WHEN td.primary_conc_1 = 'EDL'
         OR td.primary_conc_2 = 'EDL'
         OR td.primary_conc_3 = 'EDL'
         OR td.primary_conc_4 = 'EDL'
         OR td.primary_conc_5 = 'EDL'
         OR td.primary_conc_6 = 'EDL' THEN
     'Yes'
    ELSE
     'No'
   END                           edu_leader_conc,
   CASE
    WHEN td.primary_conc_1 = 'ISMJ'
         OR td.primary_conc_2 = 'ISMJ'
         OR td.primary_conc_3 = 'ISMJ'
         OR td.primary_conc_4 = 'ISMJ'
         OR td.primary_conc_5 = 'ISMJ'
         OR td.primary_conc_6 = 'ISMJ' THEN
     'Yes'
    ELSE
     'No'
   END                           int_science_conc,
   CASE
    WHEN td.primary_conc_1 = 'LAMJ'
         OR td.primary_conc_2 = 'LAMJ'
         OR td.primary_conc_3 = 'LAMJ'
         OR td.primary_conc_4 = 'LAMJ'
         OR td.primary_conc_5 = 'LAMJ'
         OR td.primary_conc_6 = 'LAMJ'
         OR td.primary_conc_1 = 'LAMN'
         OR td.primary_conc_2 = 'LAMN'
         OR td.primary_conc_3 = 'LAMN'
         OR td.primary_conc_4 = 'LAMN'
         OR td.primary_conc_5 = 'LAMN'
         OR td.primary_conc_6 = 'LAMN' THEN
     'Yes'
    ELSE
     'No'
   END                           lang_arts_conc,
   CASE
    WHEN td.primary_conc_1 = 'LD'
         OR td.primary_conc_2 = 'LD'
         OR td.primary_conc_3 = 'LD'
         OR td.primary_conc_4 = 'LD'
         OR td.primary_conc_5 = 'LD'
         OR td.primary_conc_6 = 'LD' THEN
     'Yes'
    ELSE
     'No'
   END                           learn_disable_conc,
   CASE
    WHEN td.primary_conc_1 = 'LIT'
         OR td.primary_conc_2 = 'LIT'
         OR td.primary_conc_3 = 'LIT'
         OR td.primary_conc_4 = 'LIT'
         OR td.primary_conc_5 = 'LIT'
         OR td.primary_conc_6 = 'LIT' THEN
     'Yes'
    ELSE
     'No'
   END                           lit_k12_conc,
   CASE
    WHEN td.primary_conc_1 = 'MTMJ'
         OR td.primary_conc_2 = 'MTMJ'
         OR td.primary_conc_3 = 'MTMJ'
         OR td.primary_conc_4 = 'MTMJ'
         OR td.primary_conc_5 = 'MTMJ'
         OR td.primary_conc_6 = 'MTMJ'
         OR td.primary_conc_1 = 'MTMN'
         OR td.primary_conc_2 = 'MTMN'
         OR td.primary_conc_3 = 'MTMN'
         OR td.primary_conc_4 = 'MTMN'
         OR td.primary_conc_5 = 'MTMN'
         OR td.primary_conc_6 = 'MTMN' THEN
     'Yes'
    ELSE
     'No'
   END                           mth_conc,
   CASE
    WHEN td.primary_conc_1 = 'RK12'
         OR td.primary_conc_2 = 'RK12'
         OR td.primary_conc_3 = 'RK12'
         OR td.primary_conc_4 = 'RK12'
         OR td.primary_conc_5 = 'RK12'
         OR td.primary_conc_6 = 'RK12' THEN
     'Yes'
    ELSE
     'No'
   END                           read_k12_conc,
   CASE
    WHEN td.primary_conc_1 = 'SUHS'
         OR td.primary_conc_2 = 'SUHS'
         OR td.primary_conc_3 = 'SUHS'
         OR td.primary_conc_4 = 'SUHS'
         OR td.primary_conc_5 = 'SUHS'
         OR td.primary_conc_6 = 'SUHS' THEN
     'Yes'
    ELSE
     'No'
   END                           small_urban_conc,
   CASE
    WHEN td.primary_conc_1 = 'SSMJ'
         OR td.primary_conc_2 = 'SSMJ'
         OR td.primary_conc_3 = 'SSMJ'
         OR td.primary_conc_4 = 'SSMJ'
         OR td.primary_conc_5 = 'SSMJ'
         OR td.primary_conc_6 = 'SSMJ'
         OR td.primary_conc_1 = 'SSMN'
         OR td.primary_conc_2 = 'SSMN'
         OR td.primary_conc_3 = 'SSMN'
         OR td.primary_conc_4 = 'SSMN'
         OR td.primary_conc_5 = 'SSMN'
         OR td.primary_conc_6 = 'SSMN' THEN
     'Yes'
    ELSE
     'No'
   END                           social_studies_conc,
   CASE
    WHEN td.primary_conc_1 = 'SPMN'
         OR td.primary_conc_2 = 'SPMN'
         OR td.primary_conc_3 = 'SPMN'
         OR td.primary_conc_4 = 'SPMN'
         OR td.primary_conc_5 = 'SPMN'
         OR td.primary_conc_6 = 'SPMN' THEN
     'Yes'
    ELSE
     'No'
   END                           spanish_conc,
   CASE
    WHEN td.primary_conc_1 = 'SPED'
         OR td.primary_conc_2 = 'SPED'
         OR td.primary_conc_3 = 'SPED'
         OR td.primary_conc_4 = 'SPED'
         OR td.primary_conc_5 = 'SPED'
         OR td.primary_conc_6 = 'SPED' THEN
     'Yes'
    ELSE
     'No'
   END                           special_edu_conc,
   CASE
    WHEN td.primary_conc_1 = 'TECE'
         OR td.primary_conc_2 = 'TECE'
         OR td.primary_conc_3 = 'TECE'
         OR td.primary_conc_4 = 'TECE'
         OR td.primary_conc_5 = 'TECE'
         OR td.primary_conc_6 = 'TECE' THEN
     'Yes'
    ELSE
     'No'
   END                           tech_edu_conc,  
  
--    td.PRIMARY_CONC_1            co_conc_1,
--  td.PRIMARY_CONC_1_DESC, 
      ts.edu_tca_desc,
   ts.edu_ttc_desc,
   ts.edu_endo_desc,
   ts.edu_blda_desc,
   ts.edu_coa_desc,
   ts.edu_opi_desc,
   CASE
    WHEN td.primary_degree_code IN ( 'MA' )
         AND td.primary_major_1_desc LIKE '%TCP' THEN
     'Master of Arts with Certification (MAC)'
    WHEN td.primary_major_1_desc LIKE '%TCP' THEN
     td.primary_major_1_desc
    WHEN td.primary_minor_1_desc LIKE '%TCP'
         AND substr(td.primary_major_1_cipc_code, 1, 2) != '13' THEN
     td.primary_minor_1_desc
    ELSE
     primary_major_1_desc
   END                           caep_program,
   CASE
    WHEN td.primary_degree_code IN ( 'MA' )
         AND td.primary_major_1_desc LIKE '%TCP' THEN
     'Master of Arts with Certification (MAC)'
    WHEN td.primary_major_1_desc LIKE '%TCP' THEN
     'Secondary Education TCP'  --want all TCPs bucketed or not? td.primary_minor_1_desc
                        WHEN td.primary_minor_1_desc LIKE '%TCP'
         AND substr(td.primary_major_1_cipc_code, 1, 2) != '13' THEN
     'Secondary Education TCP'  --want all TCPs bucketed or not? td.primary_minor_1_desc
                        ELSE
     primary_major_1_desc
   END                           caep_program_group,
   CASE
    WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
     ( substr(td.sd_term_code, 1, 4) + 2
       || '10' )
    ELSE
     ( substr(td.sd_term_code, 1, 4) + 1
       || '10' )
   END                           sf_term_code,
   CASE
    WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
     ( substr(td.sd_term_code, 1, 4) + 3
       || '10' )
    ELSE
     ( substr(td.sd_term_code, 1, 4) + 2
       || '10' )
   END                           tf_term_code,
   CASE
    WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
     ( substr(td.sd_term_code, 1, 4) + 4
       || '10' )
    ELSE
     ( substr(td.sd_term_code, 1, 4) + 3
       || '10' )
   END                           fof_term_code,
   CASE
    WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
     ( substr(td.sd_term_code, 1, 4) + 5
       || '10' )
    ELSE
     ( substr(td.sd_term_code, 1, 4) + 4
       || '10' )
   END                           fif_term_code,
   CASE
    WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
     ( substr(td.sd_term_code, 1, 4) + 6
       || '10' )
    ELSE
     ( substr(td.sd_term_code, 1, 4) + 5
       || '10' )
   END                           sif_term_code,
   CASE
    WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
     ( substr(td.sd_term_code, 1, 4) + 7
       || '10' )
    ELSE
     ( substr(td.sd_term_code, 1, 4) + 6
       || '10' )
   END                           sef_term_code
  FROM
   td_student_data        td
   LEFT OUTER JOIN td_demographic         dm ON td.sd_term_code = dm.td_term_code
                                        AND td.sd_pidm = dm.dm_pidm
   LEFT OUTER JOIN ia_cw_fy_term          fy ON td.sd_term_code = fy.fy_term_code
   LEFT OUTER JOIN um_test_score          ts ON td.sd_pidm = ts.pidm
   LEFT JOIN aimsmgr.stvterm_ext    stvterm ON td.sd_term_code = stvterm.stvterm_code
  WHERE
    td.registered_ind = 'Y'
   AND substr(td.primary_major_1_cipc_code, 1, 2) = '13'
   AND td.primary_major_1 != 'INED'
   AND td.primary_major_1 != 'TESL'
   AND td.primary_major_1 != 'PEDU'
   AND td.primary_major_1 != 'PHST'
   OR ( td.registered_ind = 'Y'
        AND substr(td.primary_major_1_cipc_code, 1, 2) != '13'
        AND td.primary_minor_1_desc LIKE '%TCP' )
 ), co_reg AS (
  SELECT
   co.*,
--  sf_fy.fy second_fy,
--  tf.fy third_fy,
--  fof.fy forth_fy,
--  fif.fy fifth_fy,
--  sif.fy sixth_fy,
--  sef.fy seventh_fy,
       nvl(sf.registered_ind, 'N')         sf_reg_ind,
   nvl(tf.registered_ind, 'N')         tf_reg_ind,
   nvl(fof.registered_ind, 'N')        fof_reg_ind,
   nvl(fif.registered_ind, 'N')        fif_reg_ind,
   nvl(sif.registered_ind, 'N')        sif_reg_ind,
   nvl(sef.registered_ind, 'N')        sef_reg_ind,
   um_ff.overall_gpa                   ff_gpa,
   um_sf.overall_gpa                   sf_gpa,
   um_tf.overall_gpa                   tf_gpa,
   um_fof.overall_gpa                  fof_gpa,
   um_fif.overall_gpa                  fif_gpa,
   um_sif.overall_gpa                  sif_gpa,
   um_sef.overall_gpa                  sef_gpa,
   um_ff.overall_hours_earned          ff_ch,
   um_sf.overall_hours_earned          sf_ch,
   um_tf.overall_hours_earned          tf_ch,
   um_fof.overall_hours_earned         fof_ch,
   um_fif.overall_hours_earned         fif_ch,
   um_sif.overall_hours_earned         sif_ch,
   um_sef.overall_hours_earned         sef_ch,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = co.co_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = co_degree_code
      AND umd.level_code = co.co_primary_level_code
      AND grad_term_code >= co.co_term_code
      AND umd.grad_term_code <= co.sf_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                                 sf_grad_ind,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = co.co_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = co_degree_code
      AND umd.level_code = co.co_primary_level_code
      AND grad_term_code >= co.co_term_code
      AND umd.grad_term_code <= co.tf_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                                 tf_grad_ind,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = co.co_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = co_degree_code
      AND umd.level_code = co.co_primary_level_code
      AND grad_term_code >= co.co_term_code
      AND umd.grad_term_code <= co.fof_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                                 fof_grad_ind,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = co.co_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = co_degree_code
      AND umd.level_code = co.co_primary_level_code
      AND grad_term_code >= co.co_term_code
      AND umd.grad_term_code <= co.fif_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                                 fif_grad_ind,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = co.co_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = co_degree_code
      AND umd.level_code = co.co_primary_level_code
      AND grad_term_code >= co.co_term_code
      AND umd.grad_term_code <= co.sif_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                                 sif_grad_ind,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = co.co_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = co_degree_code
      AND umd.level_code = co.co_primary_level_code
      AND grad_term_code >= co.co_term_code
      AND umd.grad_term_code <= co.sef_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                                 sef_grad_ind
   ,
   (
    SELECT 
     DISTINCT
     umd.grad_date
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
    AND umd.primary_major_1 = co.co_major
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.sef_term_code
   )                                   grad_date
  FROM
   co
   LEFT OUTER JOIN td_student_data  sf ON co.co_pidm = sf.sd_pidm
                                         AND co.sf_term_code = sf.sd_term_code
                                         AND co.co_primary_level_code = sf.primary_level_code
                                         AND co.co_degree_code = sf.primary_degree_code
                                         AND co.co_major = sf.primary_major_1
   LEFT OUTER JOIN td_student_data  tf ON co.co_pidm = tf.sd_pidm
                                         AND co.tf_term_code = tf.sd_term_code
                                         AND co.co_primary_level_code = tf.primary_level_code
                                         AND co.co_degree_code = tf.primary_degree_code
                                         AND co.co_major = tf.primary_major_1
   LEFT OUTER JOIN td_student_data  fof ON co.co_pidm = fof.sd_pidm
                                          AND co.fof_term_code = fof.sd_term_code
                                          AND co.co_primary_level_code = fof.primary_level_code
                                          AND co.co_degree_code = fof.primary_degree_code
                                          AND co.co_major = fof.primary_major_1
   LEFT OUTER JOIN td_student_data  fif ON co.co_pidm = fif.sd_pidm
                                          AND co.fif_term_code = fif.sd_term_code
                                          AND co.co_primary_level_code = fif.primary_level_code
                                          AND co.co_degree_code = fif.primary_degree_code
                                          AND co.co_major = fif.primary_major_1
   LEFT OUTER JOIN td_student_data  sif ON co.co_pidm = sif.sd_pidm
                                          AND co.sif_term_code = sif.sd_term_code
                                          AND co.co_primary_level_code = sif.primary_level_code
                                          AND co.co_degree_code = sif.primary_degree_code
                                          AND co.co_major = sif.primary_major_1
   LEFT OUTER JOIN td_student_data  sef ON co.co_pidm = sef.sd_pidm
                                          AND co.sef_term_code = sef.sd_term_code
                                          AND co.co_primary_level_code = sef.primary_level_code
                                          AND co.co_degree_code = sef.primary_degree_code
                                          AND co.co_major = sef.primary_major_1
   LEFT OUTER JOIN um_student_data  um_ff ON co.co_pidm = um_ff.sd_pidm
                                            AND co.co_term_code = um_ff.sd_term_code
                                            AND co.co_primary_level_code = um_ff.primary_level_code
                                            AND co.co_degree_code = um_ff.primary_degree_code
                                            AND co.co_major = um_ff.primary_major_1
   LEFT OUTER JOIN um_student_data  um_sf ON co.co_pidm = um_sf.sd_pidm
                                            AND co.sf_term_code = um_sf.sd_term_code
                                            AND co.co_primary_level_code = um_sf.primary_level_code
                                            AND co.co_degree_code = um_sf.primary_degree_code
                                            AND co.co_major = um_sf.primary_major_1
   LEFT OUTER JOIN um_student_data  um_tf ON co.co_pidm = um_tf.sd_pidm
                                            AND co.tf_term_code = um_tf.sd_term_code
                                            AND co.co_primary_level_code = um_tf.primary_level_code
                                            AND co.co_degree_code = um_tf.primary_degree_code
                                            AND co.co_major = um_tf.primary_major_1
   LEFT OUTER JOIN um_student_data  um_fof ON co.co_pidm = um_fof.sd_pidm
                                             AND co.fof_term_code = um_fof.sd_term_code
                                             AND co.co_primary_level_code = um_fof.primary_level_code
                                             AND co.co_degree_code = um_fof.primary_degree_code
                                             AND co.co_major = um_fof.primary_major_1
   LEFT OUTER JOIN um_student_data  um_fif ON co.co_pidm = um_fif.sd_pidm
                                             AND co.fif_term_code = um_fif.sd_term_code
                                             AND co.co_primary_level_code = um_fif.primary_level_code
                                             AND co.co_degree_code = um_fif.primary_degree_code
                                             AND co.co_major = um_fif.primary_major_1
   LEFT OUTER JOIN um_student_data  um_sif ON co.co_pidm = um_sif.sd_pidm
                                             AND co.sif_term_code = um_sif.sd_term_code
                                             AND co.co_primary_level_code = um_sif.primary_level_code
                                             AND co.co_degree_code = um_sif.primary_degree_code
                                             AND co.co_major = um_sif.primary_major_1
   LEFT OUTER JOIN um_student_data  um_sef ON co.co_pidm = um_sef.sd_pidm
                                             AND co.sef_term_code = um_sef.sd_term_code
                                             AND co.co_primary_level_code = um_sef.primary_level_code
                                             AND co.co_degree_code = um_sef.primary_degree_code
                                             AND co.co_major = um_sef.primary_major_1
   
  WHERE
   row_id = 1
 )
 SELECT
  ROW_NUMBER()
  OVER(PARTITION BY co_pidm
       ORDER BY co_term_code ASC
  )                                                                     co_num,
  co_reg.co_pidm,
  co_reg.co_fy,
  co_reg.start_date,
  co_reg.grad_date,
  round((co_reg.grad_date - co_reg.start_date) / 365, 2)                AS ttd_years,
  round((co_reg.grad_date - co_reg.start_date) / 30.41, 2)              AS ttd_months,
  co_reg.umid,
  co_reg.first_name,
  co_reg.last_name,
  co_reg.age,
  co_reg.gender,
  co_reg.report_ethnicity,
  co_reg.hsch_gpa,
  co_reg.pcol_gpa,
  co_reg.pcol_degree_code,
  co_reg.residency_desc,
-- co_reg.co_gpa,
    co_reg.co_term_code,
  co_reg.co_term_desc,
  co_reg.co_primary_level_code,
  co_reg.co_degree_code,
  co_reg.co_major,
  co_reg.caep_program,
  co_reg.caep_program_group,
  co_reg.edu_tca_desc,
  co_reg.edu_ttc_desc,
  co_reg.edu_endo_desc,
  co_reg.edu_blda_desc,
  co_reg.edu_coa_desc,
  co_reg.edu_opi_desc,
  co_reg.early_childhood_conc,
  co_reg.elem_ed_conc,
  co_reg.edu_leader_conc,
  co_reg.int_science_conc,
  co_reg.lang_arts_conc,
  co_reg.learn_disable_conc,
  co_reg.lit_k12_conc,
  co_reg.mth_conc,
  co_reg.read_k12_conc,
  co_reg.small_urban_conc,
  co_reg.social_studies_conc,
  co_reg.spanish_conc,
  co_reg.special_edu_conc,
  co_reg.tech_edu_conc,
 
-- co_reg.sf_term_code,
-- co_reg.tf_term_code,
-- co_reg.fof_term_code,
-- co_reg.fif_term_code,
-- co_reg.sif_term_code,
-- co_reg.sef_term_code,
-- co_reg.sf_reg_ind,
-- co_reg.sf_grad_ind,
--         sf_fy.fy                                                              second_fy,
--  tf_fy.fy                                                              third_fy,
--  fof_fy.fy                                                             forth_fy,
--  fif_fy.fy                                                             fifth_fy,
--  sif_fy.fy                                                             sixth_fy,
--  sef_fy.fy                                                             seventh_fy,
  co_reg.ff_gpa                                                         first_fall_overall_gpa,
  co_reg.sf_gpa                                                         second_fall_overall_gpa,
  co_reg.tf_gpa                                                         third_fall_overall_gpa,
  co_reg.fof_gpa                                                        forth_fall_overall_gpa,
  co_reg.fif_gpa                                                        fifth_fall_overall_gpa,
  co_reg.sif_gpa                                                        sixth_fall_overall_gpa,
  co_reg.sef_gpa                                                        seventh_fall_overall_gpa,
  co_reg.ff_ch                                                          first_fall_credit_hours,
  co_reg.sf_ch                                                          second_fall_credit_hours,
  co_reg.tf_ch                                                          third_fall_credit_hours,
  co_reg.fof_ch                                                         fourth_fall_credit_hours,
  co_reg.fif_ch                                                         fifth_fall_credit_hours,
  co_reg.sif_ch                                                         sixth_fall_credit_hours,
  co_reg.sef_ch                                                         seventh_fall_credit_hours,
  CASE
   WHEN co_reg.sf_grad_ind = 'Y'    THEN
    'COMPLETERS'
   WHEN co_reg.sf_reg_ind = 'Y'     THEN
    'ENROLLED'
   WHEN co_reg.sf_reg_ind = 'N'
        AND co_reg.sf_grad_ind = 'N' THEN
    'LOST'
  END                                                                   second_fall_persist,
-- co_reg.tf_reg_ind,
-- co_reg.tf_grad_ind,
         CASE
   WHEN co_reg.tf_grad_ind = 'Y'    THEN
    'COMPLETERS'
   WHEN co_reg.tf_reg_ind = 'Y'     THEN
    'ENROLLED'
   WHEN co_reg.tf_reg_ind = 'N'
        AND co_reg.tf_grad_ind = 'N' THEN
    'LOST'
  END                                                                   third_fall_persist,
-- co_reg.fof_reg_ind,
-- co_reg.fof_grad_ind,
         CASE
   WHEN co_reg.fof_grad_ind = 'Y'    THEN
    'COMPLETERS'
   WHEN co_reg.fof_reg_ind = 'Y'     THEN
    'ENROLLED'
   WHEN co_reg.fof_reg_ind = 'N'
        AND co_reg.fof_grad_ind = 'N' THEN
    'LOST'
  END                                                                   forth_fall_persist,
-- co_reg.fif_reg_ind,
-- co_reg.fif_grad_ind,
         CASE
   WHEN co_reg.fif_grad_ind = 'Y'    THEN
    'COMPLETERS'
   WHEN co_reg.fif_reg_ind = 'Y'     THEN
    'ENROLLED'
   WHEN co_reg.fif_reg_ind = 'N'
        AND co_reg.fif_grad_ind = 'N' THEN
    'LOST'
  END                                                                   fifth_fall_persist,
-- co_reg.sif_reg_ind,
-- co_reg.sif_grad_ind,
         CASE
   WHEN co_reg.sif_grad_ind = 'Y'    THEN
    'COMPLETERS'
   WHEN co_reg.sif_reg_ind = 'Y'     THEN
    'ENROLLED'
   WHEN co_reg.sif_reg_ind = 'N'
        AND co_reg.sif_grad_ind = 'N' THEN
    'LOST'
  END                                                                   sixth_fall_persist,
-- co_reg.sef_reg_ind,
-- co_reg.sef_grad_ind,
         CASE
   WHEN co_reg.sef_grad_ind = 'Y'    THEN
    'COMPLETERS'
   WHEN co_reg.sef_reg_ind = 'Y'     THEN
    'ENROLLED'
   WHEN co_reg.sef_reg_ind = 'N'
        AND co_reg.sef_grad_ind = 'N' THEN
    'LOST'
  END                                                                   seventh_fall_persist
 FROM
  co_reg
--  LEFT OUTER JOIN ia_cw_fy_term  sf_fy ON co_reg.sf_term_code = sf_fy.fy_term_code
--  LEFT OUTER JOIN ia_cw_fy_term  tf_fy ON co_reg.tf_term_code = tf_fy.fy_term_code
--  LEFT OUTER JOIN ia_cw_fy_term  fof_fy ON co_reg.fof_term_code = fof_fy.fy_term_code
--  LEFT OUTER JOIN ia_cw_fy_term  fif_fy ON co_reg.fif_term_code = fif_fy.fy_term_code
--  LEFT OUTER JOIN ia_cw_fy_term  sif_fy ON co_reg.sif_term_code = sif_fy.fy_term_code
--  LEFT OUTER JOIN ia_cw_fy_term  sef_fy ON co_reg.sef_term_code = sef_fy.fy_term_code
--WHERE
-- co_term_code >= 201640;