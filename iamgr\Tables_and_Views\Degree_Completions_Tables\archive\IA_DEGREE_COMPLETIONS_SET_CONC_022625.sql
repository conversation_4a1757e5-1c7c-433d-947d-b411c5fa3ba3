create table temp_popsel2 as
WITH popsel AS (
    SELECT 
        d.pidm,
        d.UMID,
        s.age,
        d.GRAD_TERM_CODE,
        d.GRAD_DATE,
        d.REPORT_ETHNICITY,
        d.gender,
        d.<PERSON>EVEL_CODE AS PRIMARY_LEVEL_CODE,
        CASE 
            WHEN hp.degree_code IS NOT NULL THEN 'Health'
            WHEN stem.majr_code IS NOT NULL THEN 'STEM'
            ELSE 'Standard'
        END AS hp_stem_order_desc,
        CASE 
            WHEN hp.degree_code IS NOT NULL THEN 1
            WHEN stem.majr_code IS NOT NULL THEN 2
            ELSE 3
        END AS hp_stem_order_num,
        d.primary_major_1 AS degree_major_code,
        d.PRIMARY_MAJOR_1_DESC AS degree_major_desc,
        d.primary_major_1_cipc_code AS cip_code,
        d.PRIMARY_PROGRAM,
        d.PRIMARY_PROGRAM_DESC,
        d.PRIMARY_CONC_1 AS degree_conc_code,
        d.PRIMARY_CONC_1_DESC AS degree_conc_desc,
        s.ONLINE_COURSES_ONLY_IND AS online_only,
        nces.IA_NCES_CODE AS nces_code,
        nces.IA_NCES_DESC AS nces_desc,
        'primary degree' AS degree_source_desc,
        1 AS degree_source,
        d.degree_code,
        d.DEGREE_DESC
    FROM ia_um_degree d
    LEFT JOIN ia_um_student_data s
        ON s.sd_pidm = d.pidm
        AND s.sd_term_code = d.grad_term_code
    LEFT JOIN ia_cw_hp hp
        ON hp.degree_code = d.degree_code
        AND hp.primary_program = d.primary_program
    LEFT JOIN ia_cw_stem stem
        ON stem.majr_code = d.primary_major_1
    LEFT JOIN ia_cw_nces_code nces
        ON nces.degree_code = d.degree_code
        AND nces.MAJR_CODE = d.primary_major_1
    WHERE d.DEGREE_STATUS = 'AW'
    
    UNION ALL
    
    SELECT 
        d.pidm,
        d.UMID,
        s.age,
        d.GRAD_TERM_CODE,
        d.GRAD_DATE,
        d.REPORT_ETHNICITY,
        d.gender,
        d.LEVEL_CODE AS PRIMARY_LEVEL_CODE,
        'Standard' AS hp_stem_order_desc,
        3 AS hp_stem_order_num,
        d.primary_major_2 AS degree_major_code,
        d.PRIMARY_MAJOR_2_DESC AS degree_major_desc,
        d.primary_major_2_cipc_code AS cip_code,
        d.PRIMARY_PROGRAM,
        d.PRIMARY_PROGRAM_DESC,
        d.PRIMARY_CONC_2 AS degree_conc_code,
        d.PRIMARY_CONC_2_DESC AS degree_conc_desc,
        s.ONLINE_COURSES_ONLY_IND AS online_only,
        nces.IA_NCES_CODE AS nces_code,
        nces.IA_NCES_DESC AS nces_desc,
        'second major' AS degree_source_desc,
        2 AS degree_source,
        d.degree_code,
        d.DEGREE_DESC
    FROM ia_um_degree d
    LEFT JOIN ia_um_student_data s
        ON s.sd_pidm = d.pidm
        AND s.sd_term_code = d.grad_term_code
    LEFT JOIN ia_cw_hp hp
        ON hp.degree_code = d.degree_code
        AND hp.primary_program = d.primary_program
    LEFT JOIN ia_cw_stem stem
        ON stem.majr_code = d.primary_major_1
    LEFT JOIN ia_cw_nces_code nces
        ON nces.degree_code = d.degree_code
        AND nces.MAJR_CODE = d.primary_major_2
    WHERE d.primary_major_2 IS NOT NULL
        AND d.DEGREE_STATUS = 'AW'
),

popsel2 AS (
    SELECT 
        '24-25' AS Fiscal_Year, -- UPDATE THIS FIELD
        p.PIDM,
        p.UMID,
        p.age,
        p.GRAD_TERM_CODE,
        p.GRAD_DATE,
        DECODE(p.REPORT_ETHNICITY,
            'Nonresident Alien',1,
            'Hispanic or Latino',2,
            'American Indian or Alaska Native',3,
            'Asian',4,
            'Black or African American',5,
            'Native Hawaiian and Other Pacific Islander',6,
            'White',7,
            'Two or more races',8,9) AS IPEDS_RACE_CODE,
        p.REPORT_ETHNICITY,
        DECODE(p.gender,'F',2,1) AS IPEDS_GENDER_CODE,
        p.gender,
        p.PRIMARY_LEVEL_CODE,
        p.DEGREE_CODE,
        p.DEGREE_DESC,
        p.DEGREE_MAJOR_CODE,
        p.degree_major_desc,
        p.PRIMARY_PROGRAM,
        p.PRIMARY_PROGRAM_DESC,
        p.online_only,
        p.HP_STEM_ORDER_DESC,
        p.cip_code,
        p.NCES_CODE,
        p.nces_desc,
        p.DEGREE_SOURCE_DESC,
        DECODE(ROW_NUMBER() OVER(PARTITION BY p.PIDM, p.DEGREE_CODE ORDER BY p.DEGREE_SOURCE, p.HP_STEM_ORDER_NUM), 1, 1, 2) AS COMPLETION_TYPE_CODE,
        DECODE(ROW_NUMBER() OVER(PARTITION BY p.PIDM, p.DEGREE_CODE ORDER BY p.DEGREE_SOURCE, p.HP_STEM_ORDER_NUM), 1, 'first major', 'second major') AS COMPLETION_TYPE,
        p.degree_conc_code,
        p.degree_conc_desc
    FROM popsel p
    INNER JOIN IA_DEGREE_COMPLETIONS dc
        ON dc.PIDM = p.PIDM
        AND dc.GRAD_TERM_CODE = p.GRAD_TERM_CODE
        AND dc.DEGREE_MAJOR_CODE = p.DEGREE_MAJOR_CODE
        AND dc.DEGREE_CODE = p.DEGREE_CODE
)
select * from popsel2
;


UPDATE IA_DEGREE_COMPLETIONS dc
SET (dc.degree_conc_code, dc.degree_conc_desc) = 
    (SELECT degree_conc_code, degree_conc_desc
     FROM (
         SELECT 
             p2.PIDM,
             p2.GRAD_TERM_CODE,
             p2.DEGREE_CODE,
             p2.degree_major_code,
             p2.degree_conc_code,
             p2.degree_conc_desc,
             ROW_NUMBER() OVER (
                 PARTITION BY p2.PIDM, p2.GRAD_TERM_CODE, p2.DEGREE_CODE, p2.degree_major_code
                 ORDER BY p2.COMPLETION_TYPE_CODE  -- Prioritize first majors
             ) as rn
         FROM temp_popsel2 p2
     ) ranked
     WHERE ranked.PIDM = dc.PIDM
       AND ranked.GRAD_TERM_CODE = dc.GRAD_TERM_CODE
       AND ranked.DEGREE_CODE = dc.DEGREE_CODE
       AND ranked.degree_major_code = dc.degree_major_code
       AND ranked.rn = 1);  -- Only use the first row for each set of criteria






