WITH dp1 AS (
 SELECT
  *
 FROM
  ia_term_cont
), cur_term AS (
 SELECT
  cur_term_code,
  cur_term_desc,
  cur_col_code,
  COUNT(*) cur_term_cont
 FROM
  dp1
 GROUP BY
  cur_term_code,
  cur_term_desc,
  cur_col_code
), cur_reg_term AS (
 SELECT
  cur_term_code,
  cur_col_code,
  cur_reg_term_desc,
  COUNT(*) cur_reg_term_cont
 FROM
  dp1
 WHERE
  ( cur_reg_term_enrolled_ind = 'Y'
    OR cur_reg_term_grad_ind = 'Y' )
 GROUP BY
  cur_term_code,
  cur_col_code,
  cur_reg_term_desc
), next_reg_term AS (
 SELECT
  cur_term_code,
  cur_col_code,
  next_reg_term_desc,
  COUNT(*) next_reg_term_cont
 FROM
  dp1
 WHERE
  ( next_reg_term_enrolled_ind = 'Y'
    OR next_reg_term_grad_ind = 'Y' )
 GROUP BY
  cur_term_code,
  cur_col_code,
  next_reg_term_desc
), next_2_reg_term AS (
 SELECT
  cur_term_code,
  cur_col_code,
  next_2_reg_term_desc,
  COUNT(*) next_2_reg_term_cont
 FROM
  dp1
 WHERE
  ( next_2_reg_term_enrolled_ind = 'Y'
    OR next_2_reg_term_grad_ind = 'Y' )
 GROUP BY
 cur_term_code,
  cur_col_code,
  next_2_reg_term_desc
)
SELECT
 cur_term.*,
 cur_reg_term.cur_reg_term_desc,
 cur_reg_term.cur_reg_term_cont,
 next_reg_term.next_reg_term_desc,
 next_reg_term.next_reg_term_cont,
 next_2_reg_term.next_2_reg_term_desc,
 next_2_reg_term.next_2_reg_term_cont
FROM
 cur_term
 left join cur_reg_term on cur_term.cur_term_code = cur_reg_term.cur_term_code
 and cur_term.cur_col_code = cur_reg_term.cur_col_code
 left join next_reg_term on cur_term.cur_term_code = next_reg_term.cur_term_code
 and cur_term.cur_col_code = next_reg_term.cur_col_code
  left join next_2_reg_term on cur_term.cur_term_code = next_2_reg_term.cur_term_code
 and cur_term.cur_col_code = next_2_reg_term.cur_col_code
 ;