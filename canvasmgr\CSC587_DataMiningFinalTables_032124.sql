SELECT 
    person_id,
    term_name,
    term_code,
    dept_desc,
    course_title,
    status,
    type,
    decode(type, 'Laboratory', 1, 'Mixed Mode', 2,
          'Flex Mode', 3, 'Online/Web Based', 4, 'Lecture/Discussion',
          5, 'Lecture/Lab', 6, 'Study', 7,
          'On Campus or Online', 8, 'Performance', 9, 'Field Work',
          10, 11) type_cat,
    max_enrollment,
    assign_title,
    assign_score,
    avg_assign_score,
    max_assign_score,
    assign_comment,
    current_grade,
    final_grade,
    avg_course_grade,
    ch,
    grade_quality_points,
    grade,
    sec_gmod_desc,
    satisfactory_unsatisfactory,
    decode(satisfactory_unsatisfactory, 'Satisfactory', 1, 0) satisfactory_unsatisfactory_bin,
    instructor_id,
    student_type_desc,
    decode(student_type_code, 'C', 1, 'F', 2,
          'R', 3, 'N', 4, 'T',
          5, 6) student_type_cat,
    class_desc,
    decode(class_code, 'FR', 1, 'SO', 2,
          'JR', 3, 'SR', 4, 'GR',
          5, 6) class_ord,
    online_courses_only_ind online_only,
    decode(online_courses_only_ind, 'Y', 1, 0) online_only_bin,
    primary_college_code,
    primary_college_desc,
    primary_degree_code,
    primary_degree_desc,
    major,
    major_desc,
    conc,
    conc_desc,
    primary_level_code,
    decode(primary_level_code, 'UG', '1', 'U2', '2',
          'U3', '3', 'GR', '4', 'G2',
          '5', 'G3', '6', 'DR', '7',
          '8') primary_level_cat,
    gender,
    decode(gender, 'M', '1', 'F', '2',
          '3') gender_cat,
    age,
    report_ethnicity,
    report_ethnicity_code,
    flint_promise,
    decode(flint_promise, 'Y', '1', '0') flint_promise_bin,
    parent_edu_desc,
    decode(parent_edu_desc, 'Junior High/Middle School', '1', 'High School', '2',
          'College ', '3', '4') parent_edu_cat,
    first_gen,
    decode(first_gen, 'Y', 1, 0) first_gen_bin,
    hsch_desc,
    hsch_code hsch_cat,
    hsch_gpa,
    pcol_desc,
    CASE
       WHEN REGEXP_LIKE ( pcol_code,
                          '[^0-9]+' ) THEN
           '000000'
       ELSE
           pcol_code
    END AS pcol_cat,
    pcol_gpa,
    CASE
       WHEN hsch_gpa IS NULL
            AND pcol_gpa IS NOT NULL THEN
           pcol_gpa
       WHEN hsch_gpa IS NOT NULL
            AND pcol_gpa IS NULL THEN
           hsch_gpa
       ELSE
           hsch_gpa
    END gpa_mod,
    act_composite,
    act_english,
    act_math,
    act_reading,
    act_science_reasoning,
    act_sum_of_standard,
    sat_total_combined,
    sat_read_wri,
    sat_math,
    umf_term_gpa,
    umf_overall_gpa,
    hold_desc_1,
    hold_code_1,
    decode(hold_code_1, 'AS', 1, 'AR', 2,
          'GR', 3, 'SA', 4, 'TR',
          5, 'IC', 6, 'CH', 7,
          'AA', 8, 'CO', 9, 'FA',
          10, 'AD', 11, 'AC', 12,
          'PE', 13, 14) hold_1_cat,
    hold_desc_2,
    decode(hold_code_2, 'AS', 1, 'AR', 2,
          'GR', 3, 'SA', 4, 'TR',
          5, 'IC', 6, 'CH', 7,
          'AA', 8, 'CO', 9, 'FA',
          10, 'AD', 11, 'AC', 12,
          'PE', 13, 14) hold_2_cat,
    hold_desc_3,
    decode(hold_code_3, 'AS', 1, 'AR', 2,
          'GR', 3, 'SA', 4, 'TR',
          5, 'IC', 6, 'CH', 7,
          'AA', 8, 'CO', 9, 'FA',
          10, 'AD', 11, 'AC', 12,
          'PE', 13, 14) hold_3_cat,
    hold_desc_4,
    decode(hold_code_4, 'AS', 1, 'AR', 2,
          'GR', 3, 'SA', 4, 'TR',
          5, 'IC', 6, 'CH', 7,
          'AA', 8, 'CO', 9, 'FA',
          10, 'AD', 11, 'AC', 12,
          'PE', 13, 14) hold_4_cat,
    hold_desc_5,
    decode(hold_code_5, 'AS', 1, 'AR', 2,
          'GR', 3, 'SA', 4, 'TR',
          5, 'IC', 6, 'CH', 7,
          'AA', 8, 'CO', 9, 'FA',
          10, 'AD', 11, 'AC', 12,
          'PE', 13, 14) hold_5_cat,
        adv_visit_count
FROM
    temp
    ;