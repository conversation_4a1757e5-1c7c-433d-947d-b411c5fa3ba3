CREATE TABLE ia_cohort_majr_ret_tbl AS
  (SELECT popcel.co_pidm,
      popcel.CO_TERM_CODE_KEY,
      popcel.START_MAJOR,
      popcel.SCND_FALL_MAJOR,
      popcel.SCND_FALL_CO_majr_ret,
      popcel.SCND_FALL_majr_ret,
      popcel.THRD_FALL_MAJOR,
      popcel.THRD_FALL_CO_majr_ret,
      popcel.THRD_FALL_majr_ret,
      popcel.FRTH_FALL_MAJOR,
      popcel.FRTH_FALL_CO_majr_ret,
      popcel.FRTH_FALL_majr_ret,
      popcel.FFTH_FALL_MAJOR,
      popcel.FFTH_FALL_CO_majr_ret,
      popcel.FFTH_FALL_majr_ret,
      popcel.SXTH_FALL_MAJOR,
      popcel.SXTH_FALL_CO_majr_ret,
      popcel.SXTH_FALL_majr_ret,
      popcel.SVNTH_FALL_MAJOR,
      popcel.SVNTH_FALL_CO_majr_ret,
      popcel.SVNTH_FALL_majr_ret,
      popcel.EIGTH_FALL_MAJOR,
      popcel.EIGTH_FALL_CO_majr_ret,
      popcel.EIGTH_FALL_majr_ret,
      popcel.NINTH_FALL_MAJOR,
      popcel.NINTH_FALL_CO_majr_ret,
      popcel.NINTH_FALL_majr_ret,
      popcel.TENTH_FALL_MAJOR,
      popcel.TENTH_FALL_CO_majr_ret,
      popcel.TENTH_FALL_majr_ret
    FROM
      ( WITH data_pull AS
      (SELECT IA_COHORT_PERSIST_TBL_NSC.*,
        IA_HLC_STUDY_TBL.PRIMARY_MAJOR_1 START_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 100))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) Scnd_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 200))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) THRD_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 300))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) FRTH_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 400))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) ffth_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 500))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) SXTH_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 600))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) SVNTH_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 700))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) EIGTH_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 800))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) NINTH_fall_MAJOR,
        (SELECT td1.PRIMARY_MAJOR_1
        FROM td_student_data td1
        WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY) + 900))
        AND td1.sd_pidm            = IA_COHORT_PERSIST_TBL_NSC.co_pidm
        AND td1.primary_level_code = 'UG'
        AND td1.student_type_code IN ('C','T','R')
        AND td1.registered_ind     = 'Y'
        ) TENTH_fall_MAJOR
      FROM IA_COHORT_PERSIST_TBL_NSC
      INNER JOIN IA_HLC_STUDY_TBL
      ON IA_COHORT_PERSIST_TBL_NSC.co_pidm = IA_HLC_STUDY_TBL.co_pidm
--        where
--        IA_COHORT_PERSIST_TBL_NSC.co_pidm= 74937
        --IA_COHORT_PERSIST_TBL_NSC.CO_TERM_CODE_KEY not in ('201610')
      )
    SELECT data_pull.co_pidm,
      data_pull.CO_TERM_CODE_KEY,
      --data_pull.CO_IA_STUDENT_TYPE_CODE,
      --data_pull.CO_FULL_PART_IND_UMF,
      data_pull.START_MAJOR,
      data_pull.Scnd_fall_MAJOR,
      --data_pull.SCND_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR      = data_pull.Scnd_fall_MAJOR
        AND data_pull.SCND_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END SCND_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.START_MAJOR      = data_pull.Scnd_fall_MAJOR
        AND data_pull.SCND_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END SCND_FALL_majr_ret,
      data_pull.THRD_fall_MAJOR,
      --data_pull.THRD_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR      = data_pull.THRD_fall_MAJOR
        AND data_pull.THRD_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END THRD_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.Scnd_fall_MAJOR  = data_pull.THRD_fall_MAJOR
        AND data_pull.THRD_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END THRD_FALL_majr_ret,
      data_pull.FRTH_fall_MAJOR,
      --data_pull.FRTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR      = data_pull.FRTH_fall_MAJOR
        AND data_pull.FRTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END FRTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.THRD_fall_MAJOR  = data_pull.FRTH_fall_MAJOR
        AND data_pull.FRTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END FRTH_FALL_majr_ret,
      data_pull.FFTH_fall_MAJOR,
      --data_pull.FFTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR      = data_pull.FFTH_fall_MAJOR
        AND data_pull.FFTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END FFTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.FRTH_fall_MAJOR  = data_pull.FFTH_fall_MAJOR
        AND data_pull.FFTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END FFTH_FALL_majr_ret,
      data_pull.SXTH_fall_MAJOR,
      --data_pull.SXTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR      = data_pull.SXTH_fall_MAJOR
        AND data_pull.SXTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END SXTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.FFTH_fall_MAJOR  = data_pull.SXTH_fall_MAJOR
        AND data_pull.SXTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END SXTH_FALL_majr_ret,
      data_pull.SVNTH_fall_MAJOR,
      --data_pull.SVNTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR       = data_pull.SVNTH_fall_MAJOR
        AND data_pull.SVNTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END SVNTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.SXTH_fall_MAJOR   = data_pull.SVNTH_fall_MAJOR
        AND data_pull.SVNTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END SVNTH_FALL_majr_ret,
      data_pull.EIGTH_fall_MAJOR,
      --data_pull.EIGTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR       = data_pull.EIGTH_fall_MAJOR
        AND data_pull.EIGTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END EIGTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.SVNTH_fall_MAJOR  = data_pull.EIGTH_fall_MAJOR
        AND data_pull.EIGTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END EIGTH_FALL_majr_ret,
      data_pull.NINTH_fall_MAJOR,
      --data_pull.NINTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR       = data_pull.NINTH_fall_MAJOR
        AND data_pull.NINTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END NINTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.EIGTH_fall_MAJOR  = data_pull.NINTH_fall_MAJOR
        AND data_pull.NINTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END NINTH_FALL_majr_ret,
      data_pull.TENTH_fall_MAJOR,
      --data_pull.TENTH_FALL_PERSIST,
      CASE
        WHEN data_pull.START_MAJOR       = data_pull.TENTH_fall_MAJOR
        AND data_pull.TENTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END TENTH_FALL_CO_majr_ret,
      CASE
        WHEN data_pull.NINTH_fall_MAJOR  = data_pull.TENTH_fall_MAJOR
        AND data_pull.TENTH_FALL_PERSIST = 'UMF RETAINED'
        THEN 'RETAINED'
        ELSE 'LOST'
      END TENTH_FALL_majr_ret
    FROM data_pull
      )popcel
  ) ;