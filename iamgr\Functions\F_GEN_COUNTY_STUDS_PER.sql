-- percent of all students live in Genesee County
create or replace FUNCTION "F_GENE_COUNTY_STUDS_PER" 
   (
--   HEADCOUNT number,
--   GENESEE_COUNTY varchar2,
   TERM_CODE_IN varchar2)
   return VARCHAR2
as

REC_VAL VARCHAR2(10);

BEGIN

SELECT
--N.HEADCOUNT INTO 
TO_CHAR(
ROUND(N.HEADCOUNT/D.HEADCOUNT 
* 100,1), '900.0') 
||'%' INTO REC_VAL
FROM
(SELECT -- GENESEE RESIDENTS 3307
COUNT (*) HEADCOUNT
FROM IA_TD_STUDENT_DATA
WHERE SD_TERM_CODE = TERM_CODE_IN
AND A1_COUNTY_CODE = 'MI049'  --GENESEE COUNTY
AND REGISTERED_IND = 'Y'
)N CROSS JOIN
(SELECT --TOTAL STUDENTS--6829
COUNT (*) HEADCOUNT
FROM IA_TD_STUDENT_DATA
WHERE SD_TERM_CODE = TERM_CODE_IN
AND REGISTERED_IND = 'Y') D
;

RETURN REC_VAL;
END;
