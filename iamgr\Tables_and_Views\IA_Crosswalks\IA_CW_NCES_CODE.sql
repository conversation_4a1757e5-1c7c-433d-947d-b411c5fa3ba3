TRUNCATE table IA_CW_NCES_CODE_BAC;
INSERT INTO IA_CW_NCES_CODE_BAC 
SELECT * FROM IA_CW_NCES_CODE;
commit;
--CREATE OR REPLACE VIEW IA_CW_NCES_CODE_LOAD AS
--insert into IA_CW_NCES_CODE
WITH DP1 AS(--129
select 
distinct 
DEGREE_CODE, 
PRIMARY_MAJOR_1 MAJR_CODE
FROM UM_DEGREE
WHERE 
DEGREE_CODE NOT LIKE '%0%'
AND DEGREE_CODE NOT LIKE '%9%'
AND PRIMARY_MAJOR_1 NOT LIKE '%0%'
AND PRIMARY_MAJOR_1 NOT LIKE '%9%'
--AND DEGREE_STATUS = 'AW'
AND GRAD_TERM_CODE >= 202110
AND DEGREE_CODE != 'ENDR'

MINUS

select 
DEGREE_CODE, 
MAJR_CODE
from IA_CW_NCES_CODE_BAC nces
)
SELECT 
DISTINCT
dp1.degree_code,
dp1.majr_code,
um.PRIMARY_MAJOR_1_DESC MAJR_DESC, 
IA_NCES_CODE, 
'' IA_NCES_DESC, 
stvdegc.STVDEGC_ACAT_CODE IA_ACAT_CODE, 
'' IA_ACAT_DESC, 
substr(um.PRIMARY_MAJOR_1_CIPC_CODE,1,2) CIPC_FAM, 
um.PRIMARY_MAJOR_1_CIPC_CODE CIPC_CODE, 
substr(um.PRIMARY_MAJOR_1_CIPC_CODE,1,2)||'.'||substr(um.PRIMARY_MAJOR_1_CIPC_CODE,3,6)CIPC_DOT_CODE
FROM DP1 
INNER JOIN UM_DEGREE UM ON dp1.degree_code = UM.DEGREE_CODE
                        AND dp1.majr_code = UM.PRIMARY_MAJOR_1
LEFT JOIN aimsmgr.stvdegc_ext stvdegc on dp1.degree_code = stvdegc.stvdegc_code
;
COMMIT;

select count (*) from ia_cw_nces_code;
select count (*) from ia_cw_nces_code_bac;

select * from ia_cw_nces_code
minus
select * from ia_cw_nces_code_bac;


desc um_degree;



DESC STVDEGC_EXT;

SELECT * FROM STVDEGC_EXT;