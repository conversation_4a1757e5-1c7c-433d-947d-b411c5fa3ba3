/*******************************************************************************
This script pulls the Graduate Student Cohorts going back to Fall 10
with anamoulous student records removed at the bottom
*******************************************************************************/
CREATE OR REPLACE VIEW IA_GR_COHORT_POP_2 AS
(
SELECT ia_td_student_data.sd_pidm AS CO_GR_PIDM,
  ia_td_student_data.UMID,
  ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
  to_date('01-SEP-'||(to_number(SUBSTR(ia_td_student_data.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
  ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
  ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
  ia_td_student_data.report_ethnicity AS CO_GR_ETHNICITY,
  ia_td_student_data.gender           AS CO_GR_GENDER,
  --ia_td_student_data.report_level_code,
  ia_td_student_data.primary_level_code,
  case
  when (ia_td_student_data.PRIMARY_DEGREE_CODE  = 'MLS' 
  and ia_td_student_data.sd_term_code in ('201310')) Then 'MA'
  Else ia_td_student_data.PRIMARY_DEGREE_CODE
  END PRIMARY_DEGREE_CODE,
  ia_td_student_data.primary_major_1_CIPC_CODE,
  ia_td_student_data.CITIZENSHIP_CODE,
  ia_td_student_data.CITIZENSHIP_DESC,
  ia_td_student_data.PCOL_GPA_TRANSFERRED_1 trans_gpa,
  ia_td_student_data.PCOL_DESC_1,
  ia_td_student_data.PRIMARY_MAJOR_1,
  ia_td_student_data.PRIMARY_PROGRAM,
  ia_td_student_data.PRIMARY_CONC_1
FROM ia_td_student_data
WHERE registered_ind       = 'Y'
AND ia_student_type_code IN ('N')
AND sd_term_code LIKE '%10'
AND sd_term_code > = '201110'

/* Students record anomalys removed*/
MINUS
SELECT ia_td_student_data.sd_pidm AS CO_GR_PIDM,
  ia_td_student_data.UMID,
  ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
  to_date('01-SEP-'||(to_number(SUBSTR(ia_td_student_data.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
  ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
  ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
  ia_td_student_data.report_ethnicity AS CO_GR_ETHNICITY,
  ia_td_student_data.gender           AS CO_GR_GENDER,
  --ia_td_student_data.report_level_code,
  ia_td_student_data.primary_level_code,
  case
  when (ia_td_student_data.PRIMARY_DEGREE_CODE  = 'MLS' 
  and ia_td_student_data.sd_term_code in ('201310')) Then 'MA'
  Else ia_td_student_data.PRIMARY_DEGREE_CODE
  END PRIMARY_DEGREE_CODE,
  ia_td_student_data.primary_major_1_CIPC_CODE,
  ia_td_student_data.CITIZENSHIP_CODE,
  ia_td_student_data.CITIZENSHIP_DESC,
  ia_td_student_data.PCOL_GPA_TRANSFERRED_1 trans_gpa,
  ia_td_student_data.PCOL_DESC_1,
  ia_td_student_data.PRIMARY_MAJOR_1,
  ia_td_student_data.PRIMARY_PROGRAM,
  ia_td_student_data.PRIMARY_CONC_1
FROM ia_td_student_data
WHERE registered_ind       = 'Y'
AND ia_student_type_code IN ('N')
AND sd_term_code LIKE '%10'
AND sd_term_code > = '201110'
/******************************************************************************
Anamalous student records removed --re-enrolled and was a N twice kept second
******************************************************************************/
AND (ia_td_student_data.sd_pidm = '90306' AND ia_td_student_data.sd_term_code = '201110')
/******************************************************************************
Anamalous student records removed --Following students were in programs that had 
a curriculum change and fall out of the data-model.
******************************************************************************/
or (ia_td_student_data.sd_pidm = '103198' AND ia_td_student_data.sd_term_code  = '201210')
or (ia_td_student_data.sd_pidm = '102692' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '101493' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '93223' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '96266' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '49619' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '96739' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '113439' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '135697' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '86286' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '87037' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '112118' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '76543' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '75968' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '76127' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109274' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '111491' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '179255' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '111229' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '103201' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '103200' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102862' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102843' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102839' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102700' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '111260' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102770' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '112817' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '101278' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '101498' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '101488' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '100793' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '101100' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95581' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '84930' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '72645' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '114841' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '114590' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '95842' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '95837' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '104486' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '104506' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '17443' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '53837' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '115539' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '64559' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '110667' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '110774' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109271' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '111013' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102914' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '95702' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102656' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '113073' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '103659' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '109981' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '99802' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '110212' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '95700' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '102353' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '84870' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '96254' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '96086' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '72407' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '40024' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '73693' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '84572' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '114530' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '140984' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '93505' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '198857' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '104354' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '86192' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '94738' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '173680' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '106681' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '112140' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '112160' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109513' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '91099' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109265' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '111484' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '111012' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102358' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109266' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102840' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102685' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '108397' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '103654' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '100428' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '101150' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95769' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '190060' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '194223' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102351' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '93074' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '85110' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '96298' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '96277' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '174635' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '98016' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95887' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '93503' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '104266' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '97356' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '87302' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '117130' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '106218' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '106775' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '50790' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '11642' and ia_td_student_data.sd_term_code = '201510')
or (ia_td_student_data.sd_pidm = '112847' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102703' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '112115' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '112107' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109272' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109260' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '111493' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '103195' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '103358' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '112146' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102655' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102690' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '88694' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '101703' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '100427' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '100272' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '100071' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '99873' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95373' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '77733' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '113681' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '114000' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '114697' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '120250' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '175069' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '90307' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '106150' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '106720' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '106376' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '110761' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '110772' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109261' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109711' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109728' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102672' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102689' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102701' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '100269' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '103996' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95798' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '105140' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '189169' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '89021' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '91503' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '41529' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '113983' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '114079' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '105441' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '104188' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '74408' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '112144' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '91809' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '108748' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102695' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '103036' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '103288' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '108468' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '103655' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '108894' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '101495' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '195578' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '93927' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '88382' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '91714' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '175827' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '113380' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '90552' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '94568' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '95305' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '141563' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '116685' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '115455' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '107557' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '112845' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '112105' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '108749' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '108601' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '109715' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102661' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '103366' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '112142' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '102654' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '136798' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '101277' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '100422' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102366' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '102559' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95605' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '105869' and ia_td_student_data.sd_term_code = '201310')
or (ia_td_student_data.sd_pidm = '100792' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '101101' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '85846' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '91676' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '87680' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '81257' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '74927' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '98144' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '98224' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '95949' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '104150' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '104264' and ia_td_student_data.sd_term_code = '201210')
or (ia_td_student_data.sd_pidm = '92879' and ia_td_student_data.sd_term_code = '201110')
or (ia_td_student_data.sd_pidm = '55268' and ia_td_student_data.sd_term_code = '201210')
);
