create table IA_COHORT_SIX_YR_TBL as(
select DISTINCT
/*******************************************************************************
Table grain: 1 row / student
Purpose: pull student data projected out 6 years on a student and create view
Primary keys: IA_COHORT_POP_UNDUP_TBL.CO_PIDM
Foreign keys: TD_STUDENT_DATA.SD_PIDM
*******************************************************************************/
CO_PIDM,
 
--First Fall********************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm  ) frst_fall_term_code,

( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm  ) frst_fall_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm ) frst_fall_Std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm ) frst_fall_term_gpa,
 
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0 ) frst_fall_CH_atmpt,--Credit Hour Attempted
 
( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0 ) frst_fall_CH_cmpltn,--Credit Hour Completion 
  
-- First Winter Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm  ) frst_win_term_code,

( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm  ) frst_win_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm ) frst_win_Std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm ) frst_win_term_gpa,

( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0 ) frst_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0 ) frst_win_CH_cmpltn,--Credit Hour Completion 
  
----------------------------Second----------------------------------------------
-- Second Fall Set *************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) Scnd_fall_term_code,
  
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) Scnd_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) Scnd_fall_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) scnd_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')) scnd_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) scnd_fall_CH_cmpltn,--Credit Hour Completion 

( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
                                            )scnd_fall_grad_ind,-- Second Year Graduation Indicator   

-- Second Winter Set ***********************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) Scnd_win_term_code,

( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110)) 
  and td1.sd_pidm = td_student_data.sd_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) Scnd_win_term_reg_ind,
  
( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) Scnd_win_std_type, 
    
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) Scnd_win_term_gpa,

( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) scnd_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) scnd_win_CH_cmpltn,--Credit Hour Completion 
  
 
----------------------------Third-----------------------------------------------
-- Third Fall Set **************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) thrd_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) thrd_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) thrd_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) thrd_fall_term_gpa,
    
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')) thrd_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) thrd_fall_CH_cmpltn,--Credit Hour Completion 
  
( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
                                            )thrd_fall_grad_ind,-- Third Year Graduation Indicator  

-- Third Winter Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) thrd_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) thrd_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) thrd_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) thrd_win_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) thrd_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) thrd_win_CH_cmpltn,--Credit Hour Completion 

----------------------------fourth---------------------------------------------------
-- Fourth Fall Set ******************************************************************

( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) frth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) frth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) frth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) frth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) frth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) frth_fall_CH_cmpltn,--Credit Hour Completion 
  
( select distinct 'Y'    
  from um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
                                            )frth_fall_grad_ind,-- Fourth Year Graduation Indicator
 
-- Fourth Winter Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) frth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) frth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) frth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) frth_win_term_gpa,

( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) frth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) frth_win_CH_cmpltn,--Credit Hour Completion 

-----------------------------Fifth--------------------------------------------------
-- Fifth Fall Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ffth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ffth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')) ffth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ffth_fall_term_gpa,

( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ffth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0 
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ffth_fall_CH_cmpltn,--Credit Hour Completion 

( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
                                              )ffth_fall_grad_ind,-- Five Year Graduation Indicator

-- Fifth Winter Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ffth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ffth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ffth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ffth_win_term_gpa,

( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ffth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) ffth_win_CH_cmpltn,--Credit Hour Completion 
 
-----------------------------Sixth--------------------------------------------
-- Sixth Fall Set **************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) sxth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) sxth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) sxth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) sxth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) sxth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')) sxth_fall_CH_cmpltn,--Credit Hour Completion 

(select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
                                            )sxth_fall_grad_ind,-- sixth Year Graduation Indicator   

-- Sixth Winter Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) sxth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) sxth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) sxth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) sxth_win_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) sxth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) sxth_win_CH_cmpltn,--Credit Hour Completion 
  
-----------------------------Seventh-------------------------------------------
-- Seventh Fall Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) svnth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) svnth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) svnth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) svnth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) svnth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) svnth_fall_CH_cmpltn,--Credit Hour Completion

( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
                                            )svnth_fall_grad_ind,-- Seventh Year Graduation Indicator
-- Seventh Winter Set **********************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) svnth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) svnth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) svnth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) svnth_win_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) svnth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) svnth_win_CH_cmpltn,--Credit Hour Completion 

-----------------------------Eighth----------------------------------------------
-- Eighth Fall Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) eigth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) eigth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) eigth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) eigth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) eigth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) eigth_fall_CH_cmpltn,--Credit Hour Completion
  
( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
                                            )eigth_fall_grad_ind,-- eighth Year Graduation Indicator  

-- Eighth Winter Set **********************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')  ) eigth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')  ) eigth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')  ) eigth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) eigth_win_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) eigth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) eigth_win_CH_cmpltn,--Credit Hour Completion 
  
-----------------------------Ninth----------------------------------------------
-- Ninth Fall Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ninth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ninth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) ninth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ninth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ninth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) ninth_fall_CH_cmpltn,--Credit Hour Completion 
  
( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
                                              )ninth_fall_grad_ind,-- Ninth Year Graduation Indicator 

-- Ninth Winter Set **********************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')  ) ninth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')  ) ninth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R')  ) ninth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) ninth_win_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) ninth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')  ) ninth_win_CH_cmpltn,--Credit Hour Completion
  
-----------------------------Tenth----------------------------------------------
-- Tenth Fall Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) tenth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) tenth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) tenth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) tenth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')) tenth_fall_CH_atmpt,--Credit Hour Attempted,

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R')) tenth_fall_CH_cmpltn, --Credit Hour Completion 

( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = 'UG' 
  and um_degree.grad_term_code < to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
                                            )tenth_fall_grad_ind, -- Tenth Year Graduation Indicator  

-- Tenth Winter Set **********************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) tenth_win_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) tenth_win_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910)) 
  and td1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td1.primary_level_code = 'UG'
  and td1.student_type_code in ('C','T','R') ) tenth_win_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910)) 
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) tenth_win_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) tenth_win_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  and td2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  and td2.term_hours_attempted >0
  and td2.primary_level_code = 'UG'
  and td2.student_type_code in ('C','T','R') ) tenth_win_CH_cmpltn--Credit Hour Completion 

-- Cohort Demographic Table joins and Where Clause ********************************
  from TD_student_data
  inner join IA_COHORT_POP_UNDUP_TBL on IA_COHORT_POP_UNDUP_TBL.co_pidm = TD_student_data.sd_pidm
  )
