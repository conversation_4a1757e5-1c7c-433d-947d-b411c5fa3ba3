create or replace view AA_STUDENT_LEGAL_DEPT AS
with pop_sel as(
    select 
    sd.umid,
    dm.first_name,
    dm.last_name,
    dm.ca_email,
    sd.sd_term_desc term,
    sd.registered_ind,
    row_number() over(partition by sd_pidm order by sd_term_code ASC) order_num
    from um_student_data sd
    inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
    where sd.registered_ind = 'Y'
    and sd.sd_term_code >= (select current_term from um_current_term)
)
select 
    umid,
    first_name,
    last_name,
    ca_email,
    term,
    registered_ind
from pop_sel
where pop_sel.order_num = 1
;