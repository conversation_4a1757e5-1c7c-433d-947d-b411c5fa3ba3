select 
--sem_aims_hold_plus.academic_year,
sem_aims_hold_plus.loan_max_ind,
count(*)
from sem_aims_hold_plus
group by 
--sem_aims_hold_plus.academic_year, 
sem_aims_hold_plus.loan_max_ind

order by 
--sem_aims_hold_plus.academic_year, 
sem_aims_hold_plus.loan_max_ind



select 
sem_aims_hold_plus.academic_year,
sem_aims_hold_plus.fasfa_ind,
count(*)
from sem_aims_hold_plus
group by sem_aims_hold_plus.academic_year, sem_aims_hold_plus.fasfa_ind
order by sem_aims_hold_plus.academic_year, sem_aims_hold_plus.fasfa_ind


select distinct sapr_code
from sem_aims_hold_plus
order by sapr_code;

select distinct sapr_status
from sem_aims_hold_plus
order by sapr_status;

select distinct total_loan_debt
from sem_aims_hold_plus
order by total_loan_debt;

select distinct family_income
from sem_aims_hold_plus
order by family_income;

select distinct total_pell_usage
from sem_aims_hold_plus
order by total_pell_usage;

select distinct efc
from sem_aims_hold_plus
order by efc;



with pop_sel as(
  select count(*) total_count
  from sem_aims_hold_plus
)
, efc_count as(
  select count(*) total_count
  from sem_aims_hold_plus
  where efc is not null
)
, fasfa_ind_count as(
  select count(*) total_count
  from sem_aims_hold_plus
  where fasfa_ind is not null
)
, family_income_count as(
  select count(*) total_count
  from sem_aims_hold_plus
  where family_income is not null
)
, total_pell_count as(
  select count(*) total_count
  from sem_aims_hold_plus
  where total_pell_usage_loan_debt is not null
)
select 
(select efc_count.total_count from efc_count) w_efc_count,
(select fasfa_ind_count.total_count from fasfa_ind_count) w_fasfa_ind_count,
(select family_income_count.total_count from family_income_count) w_family_income_count,
(select total_pell_count.total_count from total_pell_count) w_pell__count,
pop_sel.total_count
from pop_sel



