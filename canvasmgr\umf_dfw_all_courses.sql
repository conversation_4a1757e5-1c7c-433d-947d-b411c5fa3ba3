--------------------------------------------------------
--  File created - Wednesday-July-03-2024   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View UMF_DFW_ALL_COURSES
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CANVASMGR"."UMF_DFW_ALL_COURSES" ("PIDM", "UMID", "TERM", "COURSE_TITLE", "DEPT_DESC", "GRADE_QUALITY_POINTS", "TERM_DESC", "CLASS", "SUBJECT", "COURSE", "COURSE_LEVEL", "SECTION", "COLL_CODE", "COLL_DESC", "CH", "SEC_ATTRIBUTE", "CRN", "TITLE", "GRADE", "SEC_GMOD_DESC", "PDFW", "GRADE_DIST", "SATISFACTORY_UNSATISFACTORY", "COMPLETED_IND", "INSTRUCTOR_ID", "INSTRUCTOR_NAME") AS 
  select 
st.pidm,
st.umid,
nvl(st.term, '200510') term,
st.subject || '-' ||st.course|| '-' || st.section || ' (' || nvl(st.term, '200510') || ')' as course_title,                        --SWR-441-W1 (202420)
cs.DEPT_DESC,
st.GRADE_QUALITY_POINTS,
ST.TERM_DESC,
st.subject || ' ' ||st.course as class,
st.subject, 
st.course , 
case
when st.course < '100' then 'Remedial Level'
when st.course >= '100' and st.course < '200' then '100 Level'
when st.course >= '200' and st.course < '300' then '200 Level'
when st.course >= '300' and st.course < '400' then '300 Level'
when st.course >= '400' and st.course < '500' then '400 Level'
when st.course >= '500' and st.course < '600' then '500 Level'
when st.course >= '600' and st.course < '700' then '600 Level'
when st.course >= '700' and st.course < '800' then '700 Level'
when st.course >= '800' and st.course < '900' then '800 Level'
when st.course >= '900'  then '900 Level'
else course
end course_level,

st.section,
CS.COLL_CODE,
CS.COLL_DESC,

hours CH,
nvl(cs.SEC_ATTRIBUTE_1,'None') sec_attribute,
st.CRN, 
st.Title, 
ST.Grade,
CS.SEC_GMOD_DESC,


CASE
WHEN ST.Grade IN ('A-','.A-','IA-','.IA-','A','.A',
                  'IA','.IA','A+','.A+','IA+','.IA+',
                  'P','IP','S','IS','.IS','.S',
                  'B-','.B-','IB-','.IB-','B','.B',
                  'IB','.IB','B+','.B+','IB+','.IB+',
                  '.-C-','-C-','C-','.C-','IC-','.IC-',
                  'C','.C','IC','.IC','C+','.C+','IC+',
                  'D','.D','ID','.ID','D+','.D+','ID+',
                  'ED','.ED','.Y','Y'
                  )
THEN 'Pass'
WHEN ST.Grade IN ('.W','W','YW'
                  )
THEN 'Withdrawal'
WHEN ST.Grade IN ('.I','I','*'
                  )
THEN 'Incomplete'
WHEN ST.Grade IN ('D-','.D-','ID-',
                  'E-','.E-','IE-','E','.E',
                  'IE','.IE','E+','.E+',
                  'IE+','IF','NE','IU','U','F',
                  'N','IN'
                  )
THEN 'Fail'
ELSE ST.GRADE
END PDFW,

CASE
WHEN ST.Grade IN ('A-','.A-','IA-','.IA-','A','.A',
                  'IA','.IA','A+','.A+','IA+','.IA+',
                  'P','IP','S','IS','.IS','.S','.Y','Y'
                  )
THEN 'A'
WHEN ST.Grade IN ('B-','.B-','IB-','.IB-','B','.B',
                  'IB','.IB','B+','.B+','IB+','.IB+'
                  )
THEN 'B'
WHEN ST.Grade IN ('.-C-','-C-','C-','.C-',
                  'IC-','.IC-','C','.C',
                  'IC','.IC','C+','.C+','IC+'
                  )
THEN 'C'
WHEN ST.Grade IN ('D','.D','ID','.ID','D+','.D+','ID+',
                  'ED','.ED',
                  'D-','.D-','ID-'
                  )
THEN 'D'
WHEN ST.Grade IN ('E-','.E-','IE-','E','.E',
                  'IE','.IE','E+','.E+','IE+',
                  'IF','NE','IU'
                  )
THEN 'E'
WHEN ST.Grade IN ('.W','W','YW'
                  )
THEN 'W'
WHEN ST.Grade IN ('.I','I','*'
                  )
THEN 'I'
WHEN st.grade in ('N','IN'
                  )
THEN 'N'

ELSE ST.Grade
END Grade_Dist,

CASE
WHEN ST.Grade IN ('A-','.A-','IA-','.IA-','A','.A',
                  'IA','.IA','A+','.A+','IA+','.IA+',
                  'P','IP','S','IS','.IS','.S','.Y','Y',
                  'B-','.B-','IB-','.IB-','B','.B',
                  'IB','.IB','B+','.B+','IB+','.IB+',
                  '.-C-','-C-','C-','.C-',
                  'IC-','.IC-','C','.C',
                  'IC','.IC','C+','.C+','IC+',
                  'D','.D','ID','.ID','D+','.D+','ID+',
                  'ED','.ED','.Y','Y'
                  )
THEN 'Satisfactory'
WHEN ST.Grade IN ('.W','W','YW',
                  '.I','I','IU','*','U',
                  'D-','.D-','ID-',
                  'E-','.E-','IE-','E','.E','IE','.IE','E+','.E+','IE+','NE',
                  'IF','F','N','IN'
                  )
THEN 'Unsatisfactory'
ELSE ST.Grade
END Satisfactory_Unsatisfactory,

COMPLETED_IND,
cs.PRIMARY_INSTRUCTOR_ID INSTRUCTOR_ID,
cs.PRIMARY_INSTRUCTOR_LAST_NAME || ', ' || cs.PRIMARY_INSTRUCTOR_FIRST_NAME as INSTRUCTOR_NAME 


from UM_STUDENT_TRANSCRIPT ST
inner join UM_CATALOG_SCHEDULE CS on nvl(ST.term, '200510') = CS.term_code_key AND 
                                  ST.CRN = CS.CRN_KEY
where 
st.type = 'I'  
--and st.course < '500'
and nvl(st.term,'200510') >= 201110 
and st.term < (select current_term from um_current_term)
--(select last_4_term from um_current_term)
--and nvl(st.term,'200510') = '201110'
--and (nvl(st.term,'200510') like '%10' or nvl(st.term,'200510') like '%20')
--and st.grade not in ('I','N','Y','V','*')
and st.grade not in ('Y','V','.V','*','.*','**')
--Y In Progress, V is under audit and * is missing
--and st.pidm = 126975
;
