    --create table ia_cohort_retention as (
    select
    td_student_data.sd_pidm,
    td_student_data.sd_term_code,
    td_demo_join.age,
    td_demo_join.gender,
    td_student_data.full_part_time_ind,
    td_student_data.class_code,
    td_student_data.orig_student_type_code,
    td_student_data.primary_admit_code,
    td_student_data.residency_code,
    td_demo_join.a1_city,
    td_demo_join.a1_state_code,
    td_demo_join.a1_zip5,
    td_demo_join.a1_county_code,
    td_student_data.primary_major_1,
    td_student_data.primary_program,
    nvl(td_demo_join.intl_ind, 'N') intl_ind ,
    td_demo_join.hsch_gpa,
    td_test_join.act_composite,
    td_test_join.act_math,
    td_test_join.act_english,
    td_demo_join.report_ethnicity,
    nvl(td_student_data.housing_ind, 'N') housing_ind,
    
    case
      when current_join.sd_pidm > 0 then 'Y'
      else 'N'
    end current_enrolled_ind
    
    from td_student_data
    
    inner join(
      select *
      from td_demographic
    )td_demo_join on td_demo_join.dm_pidm = td_student_data.sd_pidm
                  and td_demo_join.td_term_code = td_student_data.sd_term_code
    
    inner join(
      select *
      from td_test_score
    )td_test_join on td_test_join.pidm = td_student_data.sd_pidm
                  and td_test_join.td_term_code = td_student_data.sd_term_code
    
    left outer join(
      select *
      from um_student_data
    )current_join on current_join.sd_pidm = td_student_data.sd_pidm
                  and current_join.sd_term_code = '201510'
                  and current_join.registered_ind = 'Y'
    where td_student_data.sd_term_code in (select distinct td_2.sd_term_code
                                           from td_student_data td_2
                                           where substr(td_2.sd_term_code, 5, 2) = '10')
    and td_student_data.registered_ind = 'Y'
    and (td_student_data.student_type_code = 'F'
    or td_student_data.student_type_code = 'T')
