--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View AFFILIATE_2_UM
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "HEDAMGR"."AFFILIATE_2_UM" ("ID", "HED__ACCOUNT__C", "HED__CONTACT__C", "HED__PRIMARY__C", "HED__ROLE__C", "AFFILIATION_EXTERNAL_KEY__C") AS 
  with fac_staff_all_sel as(
  select 
  sibinst.sibinst_pidm sibinst_pidm,
  sibinst.sibinst_fcst_code,
  sirdpcl.sirdpcl_term_code_eff
  from aimsmgr.sibinst
  inner join aimsmgr.sirdpcl on sirdpcl.sirdpcl_pidm = sibinst.sibinst_pidm
                             and sirdpcl.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
                                                                  from aimsmgr.sirdpcl s1
                                                                  where s1.sirdpcl_pidm = sirdpcl.sirdpcl_pidm)
  where sibinst.sibinst_term_code_eff = (select max(s1.sibinst_term_code_eff)
                                         from aimsmgr.sibinst s1
                                         where s1.sibinst_pidm = sibinst.sibinst_pidm)
  and not exists (select 'is a dup'
                  from aimsmgr.sprhold sprhold
                  where sprhold.sprhold_pidm = sibinst.sibinst_pidm
                  and sprhold.sprhold_hldd_code = 'DP')
  order by 1
),
fac_staff_sel as(
  select
  distinct fac_staff_all_sel.sibinst_pidm,
  'Faculty/Staff' banner_rec_type
  from fac_staff_all_sel
  where fac_staff_all_sel.sibinst_fcst_code = 'AC'
--  and fac_staff_all_sel.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
--                                                 from fac_staff_all_sel s1
--                                                 where s1.sibinst_pidm = fac_staff_all_sel.sibinst_pidm)
),
student_sel as(
  select 
  distinct ps.sgbstdn_pidm
  from(
    select 
    sgbstdn.sgbstdn_pidm,
    sgbstdn.sgbstdn_stst_code,
    row_number() over(partition by sgbstdn.sgbstdn_pidm order by sgbstdn.sgbstdn_term_code_eff desc) order_num
    from aimsmgr.sgbstdn
    where not exists (select 'is a dup'
                      from aimsmgr.sprhold sprhold
                      where sprhold.sprhold_pidm = sgbstdn.sgbstdn_pidm
                      and sprhold.sprhold_hldd_code = 'DP')
  )ps
  where ps.sgbstdn_stst_code = 'AS'
  and ps.order_num = 1
),
--applicant_sel as(
--  select
--  distinct saradap.saradap_pidm
--  from aimsmgr.saradap
--  where saradap.saradap_term_code_entry >= (select min(stvterm.stvterm_code)
--                                            from aimsmgr.stvterm
--                                            where stvterm.stvterm_end_date >= trunc(sysdate))
--  and not exists (select 'is a dup'
--                  from aimsmgr.sprhold sprhold
--                  where sprhold.sprhold_pidm = saradap.saradap_pidm
--                  and sprhold.sprhold_hldd_code = 'DP')
--),
pop_sel as(
  select
  fac_staff_sel.sibinst_pidm pop_sel_pidm,
  'Faculty/Staff' banner_rec_type
  from fac_staff_sel
  union all
  select
  student_sel.sgbstdn_pidm,
  'Student' banner_rec_type
  from student_sel
--  union all
--  select 
--  applicant_sel.saradap_pidm,
--  'Applicant'
--  from applicant_sel
)

select 
aff.id,
--account.id hed__account__c,
(select id from account where account.name = 'University of Michigan-Flint') hed__account__c,
contact.id hed__contact__c,
'true' hed__primary__c,
pop_sel.banner_rec_type hed__role__c,
pop_sel.pop_sel_pidm || ' - 001853 - ' || pop_sel.banner_rec_type affiliation_external_key__c

from pop_sel
inner join contact on contact.pidm__c = pop_sel.pop_sel_pidm
inner join (select id from account where account.name = 'University of Michigan-Flint') um_account on um_account.id != 'XX'
left outer join hed__affiliation__c aff on aff.hed__account__c = um_account.id
                                        and aff.hed__contact__c = contact.id
                                        and aff.hed__role__c = pop_sel.banner_rec_type



--where pop_sel.pop_sel_pidm in (55342, 10475)

order by pop_sel.pop_sel_pidm
;
