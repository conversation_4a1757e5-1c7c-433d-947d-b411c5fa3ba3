/*The purpose of this query is to remove the duplicate values from the ia_cohort_pop table.
All of the duplicates that exist are because of miss coded transfer students.  Many of the the
Transfer students denoted as CO_IA_STUDENT_TYPE_CODE = 'T' are actually readmits and belong do a
previous year cohort and have been reported as a FTIAC or Transfer in an earlier year.
Miss Coded Students:
co_pidm = 76842, co_term_code_key = '200910' Student Coded as U2 and FTIAC, the student was placed back into the
cohort in the IA_COHORT_POP_VIEW script.
co_pidm = 65383, co_term_code_key = '200910' Student coded as Transfer in Banner and should be a Continuing
Student was removed from IA_CO_ALL_2008_2010 by <PERSON> on 6/1/15
Transfer Students with primary admit code = CH; this is only supposed to be applied to FTIAC's
108163, 104372, 116503
*/
CREATE or replace view ia_cohort_pop_undup AS
  (SELECT popcel2.co_pidm ,
      popcel2.co_term_code_key,
      popcel2.CO_IA_STUDENT_TYPE_CODE,
      popcel2.CO_FULL_PART_IND_UMF,
      popcel2.CO_ETHNICITY,
      popcel2.CO_GENDER,
      POPCEL2.PRIMARY_DEGREE_CODE,
      to_date('01-SEP-'||(to_number(SUBSTR(popcel2.co_term_code_key,3,2))-1),'DD-MON-YY') start_date,
      dup_ind
    FROM
      (SELECT co_pidm ,
        MIN(co_term_code_key) AS min_term_code,
        COUNT(*)              AS dup_ind
      FROM ia_cohort_pop
      GROUP BY co_pidm
      ) popcel1
    INNER JOIN
      (SELECT * FROM ia_cohort_pop
      )popcel2
    ON popcel1.co_pidm        = popcel2.co_pidm
    AND popcel1.min_term_code = popcel2.co_term_code_key
  );
