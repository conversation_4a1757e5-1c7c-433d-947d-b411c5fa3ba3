create table IA_COHORT_PERSIST_TBL AS (
SELECT 
IA_COHORT_POP_UNDUP_TBL.*,

----------------------------Second Fall Persistance-----------------------------
CASE 
   WHEN  SCND_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (SCND_FALL_TERM_REG_IND = 'Y' AND SCND_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END SCND_FALL_PERSIST,

----------------------------Third Fall Persistance------------------------------
CASE 
   WHEN  THRD_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (THRD_FALL_TERM_REG_IND = 'Y' AND THRD_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END THRD_FALL_PERSIST,
----------------------------Fourth Fall Persistance-----------------------------
CASE 
   WHEN  FRTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (FRTH_FALL_TERM_REG_IND = 'Y' AND FRTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END FRTH_FALL_PERSIST,
----------------------------Fifth Fall Persistance------------------------------
CASE 
   WHEN  FFTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (FFTH_FALL_TERM_REG_IND = 'Y' AND FFTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END FFTH_FALL_PERSIST,
   
----------------------------Sixth Fall Persistance------------------------------
CASE 
   WHEN  SXTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (SXTH_FALL_TERM_REG_IND = 'Y' AND SXTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END SXTH_FALL_PERSIST,

----------------------------Seventh Fall Persistance----------------------------
CASE 
   WHEN  SVNTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (SVNTH_FALL_TERM_REG_IND = 'Y' AND SVNTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END SVNTH_FALL_PERSIST,
   
----------------------------Eighth Fall Persistance----------------------------
CASE 
   WHEN  EIGTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (EIGTH_FALL_TERM_REG_IND = 'Y' AND EIGTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END EIGTH_FALL_PERSIST,

----------------------------Ninth Fall Persistance----------------------------
CASE 
   WHEN  NINTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (NINTH_FALL_TERM_REG_IND = 'Y' AND NINTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END NINTH_FALL_PERSIST,
   
----------------------------Tenth Fall Persistance----------------------------
CASE 
   WHEN  TENTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (TENTH_FALL_TERM_REG_IND = 'Y' AND TENTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END TENTH_FALL_PERSIST

FROM 
IA_COHORT_SIX_YR_TBL
inner join 
IA_COHORT_POP_UNDUP_TBL on IA_COHORT_POP_UNDUP_TBL.CO_PIDM = IA_COHORT_SIX_YR_TBL.CO_PIDM and
                           IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY = IA_COHORT_SIX_YR_TBL.FRST_FALL_TERM_CODE     
);
