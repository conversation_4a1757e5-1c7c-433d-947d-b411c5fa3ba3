/*******************************************************************************
Cohort Data Mart Update Process
--by <PERSON>
--10-08-20
*******************************************************************************/
/*******************************************************************************
Update Cohort Population Table
*******************************************************************************/

--Truncate backup file FALL ONLY
TRUNCATE TABLE IA_COHORT_POP_UNDUP_BAC;

--Create Backup FALL ONLY
INSERT INTO IA_COHORT_POP_UNDUP_BAC
SELECT * FROM IA_COHORT_POP_UNDUP_TBL;

--Count rows to varify backup FALL ONLY
SELECT COUNT (*) FROM IA_COHORT_POP_UNDUP_TBL;
SELECT COUNT (*) FROM IA_COHORT_POP_UNDUP_BAC;

--Insert rows for new Students in Cohort FALL ONLY
INSERT INTO IA_COHORT_POP_UNDUP_TBL 
SELECT * FROM IA_COHORT_POP_UNDUP
WHERE CO_TERM_CODE_KEY = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM); 

--Identify students removed from cohorts FALL ONLY
SELECT 
SD.SD_PIDM,
SD.SD_TERM_CODE,
SD.PRIMARY_LEVEL_CODE,
SD.IA_STUDENT_TYPE_CODE
FROM IA_TD_STUDENT_DATA SD
WHERE SD.REGISTERED_IND = 'Y'
AND SD.SD_TERM_CODE = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
AND NOT EXISTS (
    SELECT CO.CO_PIDM
    FROM IA_COHORT_POP_UNDUP CO
    WHERE SD.SD_PIDM = CO.CO_PIDM
    AND CO.CO_TERM_CODE_KEY = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
    )
;

--Identify students removed from cohorts FALL ONLY
SELECT 
SD.SD_TERM_CODE,
SD.IA_STUDENT_TYPE_CODE,
SD.FULL_PART_TIME_IND_UMF,
COUNT (*)
FROM IA_TD_STUDENT_DATA SD
WHERE SD.REGISTERED_IND = 'Y'
AND SD.SD_TERM_CODE = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
AND NOT EXISTS (
    SELECT CO.CO_PIDM
    FROM IA_COHORT_POP_UNDUP CO
    WHERE SD.SD_PIDM = CO.CO_PIDM
    AND CO.CO_TERM_CODE_KEY = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
    )
    
GROUP BY
SD.SD_TERM_CODE,
SD.IA_STUDENT_TYPE_CODE,
SD.FULL_PART_TIME_IND_UMF
;


--Count rows to varify backup FALL ONLY
with dp1 as (
SELECT * FROM IA_COHORT_POP_UNDUP_TBL
minus
SELECT * FROM IA_COHORT_POP_UNDUP_BAC
)
select count (*) incomming_added from dp1
;

--total FTIAC and Transfers FALL ONLY
with dp1 as (
SELECT 
SD.SD_PIDM,
SD.SD_TERM_CODE,
SD.PRIMARY_LEVEL_CODE,
SD.IA_STUDENT_TYPE_CODE
FROM IA_TD_STUDENT_DATA SD
WHERE SD.REGISTERED_IND = 'Y'
AND SD.SD_TERM_CODE = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
)
select count (*) total_incoming from dp1
;

--total FTIAC and Transfers by full/part time FALL ONLY
SELECT
CO_IA_STUDENT_TYPE_CODE,
CO_FULL_PART_IND_UMF,
COUNT(*)
FROM IA_COHORT_POP_UNDUP_TBL
WHERE co_term_code_key = 202310
GROUP BY
CO_IA_STUDENT_TYPE_CODE,
CO_FULL_PART_IND_UMF
;


/*******************************************************************************
Process for SE Datamart Clearinghouse Detail File integration
*******************************************************************************/
/*******************************************************************************
Update NSC Raw Data Table from detail return file to identify students who 
accepted but never registered.
*******************************************************************************/

TRUNCATE TABLE IA_COMPETITOR_NSC_RAW_BAC;
INSERT INTO IA_COMPETITOR_NSC_RAW_BAC
SELECT * FROM IA_COMPETITOR_NSC_RAW;
COMMIT;
SELECT COUNT (*) from IA_COMPETITOR_NSC_RAW;
SELECT COUNT (*) from IA_COMPETITOR_NSC_RAW_BAC;
--Only truncate if you are pulling the entire population of students who were
--admitted but never registered
--TRUNCATE TABLE IA_COMPETITOR_NSC_RAW;

--The uplad to NSC included only the new student applicants who did not register
--import file 002327st_xxxxxx_DETLRPT_SE_xxxxxxxxxxxxx_002327_unregistered_se_xxxxxx.csv

/*******************************************************************************
Update NSC Raw Data Table from detail return file to identify students who
registered and are in the Cohort Datamart but were lost in the future.
*******************************************************************************/

TRUNCATE TABLE IA_COHORT_NSC_RAW_BAC;
INSERT INTO IA_COHORT_NSC_RAW_BAC
SELECT * FROM IA_COHORT_NSC_RAW;
COMMIT;
SELECT COUNT (*) from IA_COHORT_NSC_RAW;
SELECT COUNT (*) from IA_COHORT_NSC_RAW_BAC;
TRUNCATE TABLE IA_COHORT_NSC_RAW;
--import file 002327st_xxxxxx_DETLRPT_SE_xxxxxxxxxxxxxx_002327_se_datamart_xxxxxx

/*******************************************************************************
Update NSC Student Data Table
*******************************************************************************/

TRUNCATE TABLE IA_NSC_STUDENT_DATA_BAC;
INSERT INTO IA_NSC_STUDENT_DATA_BAC
SELECT * FROM IA_NSC_STUDENT_DATA;
COMMIT;
SELECT COUNT (*) from IA_NSC_STUDENT_DATA;
SELECT COUNT (*) from IA_NSC_STUDENT_DATA_BAC;
TRUNCATE TABLE IA_NSC_STUDENT_DATA;
--run ia_nsc_student_data.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update NSC Degrees Table
*******************************************************************************/

TRUNCATE TABLE IA_NSC_DEGREES_BAC;
INSERT INTO IA_NSC_DEGREES_BAC 
SELECT * FROM IA_NSC_DEGREES;
COMMIT;
SELECT COUNT (*) from IA_NSC_DEGREES;
SELECT COUNT (*) from IA_NSC_DEGREES_BAC;
TRUNCATE TABLE IA_NSC_DEGREES;
--run IA_NSC_DEGREES.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update Ten year registered or graduated table
*******************************************************************************/

TRUNCATE TABLE IA_COHORT_TEN_YR_TBL_NSC_BAC;
INSERT INTO IA_COHORT_TEN_YR_TBL_NSC_BAC 
SELECT * FROM IA_COHORT_TEN_YR_TBL_NSC;
COMMIT;
SELECT COUNT (*) from IA_COHORT_TEN_YR_TBL_NSC;
SELECT COUNT (*) from IA_COHORT_TEN_YR_TBL_NSC_BAC;
TRUNCATE TABLE IA_COHORT_TEN_YR_TBL_NSC;
--run IA_COHORT_TEN_YR_TBL_NSC.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update cohort persistence table
*******************************************************************************/

TRUNCATE TABLE IA_COHORT_PERSIST_TBL_NSC_BAC;
INSERT INTO IA_COHORT_PERSIST_TBL_NSC_BAC 
SELECT * FROM IA_COHORT_PERSIST_TBL_NSC;
COMMIT;
SELECT COUNT (*) from IA_COHORT_PERSIST_TBL_NSC;
SELECT COUNT (*) from IA_COHORT_PERSIST_TBL_NSC_BAC;
TRUNCATE TABLE IA_COHORT_PERSIST_TBL_NSC;
--run IA_COHORT_PERSIST_TBL_NSC.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update HLC study table 
This provides filter data for analysis of persistence data.
*******************************************************************************/

TRUNCATE TABLE IA_HLC_STUDY_BAC;
INSERT INTO IA_HLC_STUDY_BAC 
SELECT * FROM IA_HLC_STUDY_TBL;
COMMIT;
SELECT COUNT (*) from IA_HLC_STUDY_TBL;
SELECT COUNT (*) from IA_HLC_STUDY_BAC;
TRUNCATE TABLE IA_HLC_STUDY_TBL;
--run IA_HLC_STUDY_TBL.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update Cohort Financial Aid Table
This provides filter data for analysis of persistence data.
*******************************************************************************/

TRUNCATE TABLE IA_COHORT_FA_BAC;
INSERT INTO IA_COHORT_FA_BAC 
SELECT * FROM IA_COHORT_FA;
COMMIT;
SELECT COUNT (*) from IA_COHORT_FA;
SELECT COUNT (*) from IA_COHORT_FA_BAC;
--run IA_COHORT_FA.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad
