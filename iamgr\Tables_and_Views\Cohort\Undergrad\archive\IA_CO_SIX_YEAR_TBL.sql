CREATE TABLE IA_COHORT_SIX_YR_TBL_2 AS
  ( SELECT DISTINCT
      /*******************************************************************************
      Table grain: 1 row / student
      Purpose: pull student data projected out 6 years on a student and create view
      Primary keys: IA_COHORT_POP_UNDUP_TBL.CO_PIDM
      Foreign keys: TD_STUDENT_DATA.SD_PIDM
      *******************************************************************************/
      CO_PIDM,
      --First Fall********************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      ) frst_fall_term_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      ) frst_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      ) frst_fall_Std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
      AND td2.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      ) frst_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      ) frst_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      ) frst_fall_CH_cmpltn,--Credit Hour Completion
     
      -- First Winter Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_win_term_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_win_Std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      AND td2.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) frst_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      AND td2.term_hours_attempted >0
      ) frst_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      AND td2.term_hours_attempted >0
      ) frst_win_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
      )frst_win_grad_ind,-- Second Year Graduation Indicator
      
      -- First Spring Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_spr_term_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_spr_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_spr_Std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      AND td2.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) frst_spr_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      AND td2.term_hours_attempted >0
      ) frst_spr_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      AND td2.term_hours_attempted >0
      ) frst_spr_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
      )frst_spr_grad_ind,-- Second Year Graduation Indicator
      
       -- First Summer Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_sum_term_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_sum_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      AND td1.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frst_sum_Std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      AND td2.sd_pidm        = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) frst_sum_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      AND td2.term_hours_attempted >0
      ) frst_sum_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      AND td2.term_hours_attempted >0
      ) frst_sum_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
      )frst_sum_grad_ind,-- Second Year Graduation Indicator
      
      ----------------------------Second----------------------------------------------
      -- Second Fall Set *************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) Scnd_fall_term_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) Scnd_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) Scnd_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) scnd_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) scnd_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) scnd_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
      )scnd_fall_grad_ind,-- Second Year Graduation Indicator
      -- Second Winter Set ***********************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) Scnd_win_term_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
      AND td1.sd_pidm            = td_student_data.sd_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) Scnd_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) Scnd_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) Scnd_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) scnd_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) scnd_win_CH_cmpltn,--Credit Hour Completion
      ----------------------------Third-----------------------------------------------
      -- Third Fall Set **************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) thrd_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) thrd_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) thrd_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) thrd_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) thrd_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) thrd_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
      )thrd_fall_grad_ind,-- Third Year Graduation Indicator
      -- Third Winter Set ******************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) thrd_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) thrd_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) thrd_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) thrd_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) thrd_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) thrd_win_CH_cmpltn,--Credit Hour Completion
      ----------------------------fourth---------------------------------------------------
      -- Fourth Fall Set ******************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) frth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) frth_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) frth_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
      )frth_fall_grad_ind,-- Fourth Year Graduation Indicator
      -- Fourth Winter Set ******************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) frth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) frth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) frth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) frth_win_CH_cmpltn,--Credit Hour Completion
      -----------------------------Fifth--------------------------------------------------
      -- Fifth Fall Set ******************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ffth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ffth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ffth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) ffth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ffth_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ffth_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
      )ffth_fall_grad_ind,-- Five Year Graduation Indicator
      -- Fifth Winter Set ******************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ffth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ffth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ffth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) ffth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ffth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ffth_win_CH_cmpltn,--Credit Hour Completion
      -----------------------------Sixth--------------------------------------------
      -- Sixth Fall Set **************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) sxth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) sxth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) sxth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) sxth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) sxth_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) sxth_fall_CH_cmpltn,--Credit Hour Completion
      (SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
      )sxth_fall_grad_ind,-- sixth Year Graduation Indicator
      -- Sixth Winter Set ******************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) sxth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) sxth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) sxth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) sxth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) sxth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) sxth_win_CH_cmpltn,--Credit Hour Completion
      -----------------------------Seventh-------------------------------------------
      -- Seventh Fall Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) svnth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) svnth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) svnth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) svnth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) svnth_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) svnth_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
      )svnth_fall_grad_ind,-- Seventh Year Graduation Indicator
      -- Seventh Winter Set **********************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) svnth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) svnth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) svnth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) svnth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) svnth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) svnth_win_CH_cmpltn,--Credit Hour Completion
      -----------------------------Eighth----------------------------------------------
      -- Eighth Fall Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) eigth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) eigth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) eigth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) eigth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) eigth_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) eigth_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
      )eigth_fall_grad_ind,-- eighth Year Graduation Indicator
      -- Eighth Winter Set **********************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) eigth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) eigth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) eigth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) eigth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) eigth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) eigth_win_CH_cmpltn,--Credit Hour Completion
      -----------------------------Ninth----------------------------------------------
      -- Ninth Fall Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ninth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ninth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ninth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) ninth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ninth_fall_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ninth_fall_CH_cmpltn,--Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
      )ninth_fall_grad_ind,-- Ninth Year Graduation Indicator
      -- Ninth Winter Set **********************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ninth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ninth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) ninth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) ninth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ninth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) ninth_win_CH_cmpltn,--Credit Hour Completion
      -----------------------------Tenth----------------------------------------------
      -- Tenth Fall Set ************************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) tenth_fall_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) tenth_fall_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) tenth_fall_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) tenth_fall_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) tenth_fall_CH_atmpt,--Credit Hour Attempted,
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) tenth_fall_CH_cmpltn, --Credit Hour Completion
      ( SELECT DISTINCT 'Y'
      FROM um_degree
      WHERE um_degree.pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND um_degree.degree_status  = 'AW'
      AND um_degree.level_code     = 'UG'
      AND um_degree.grad_term_code < TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
      )tenth_fall_grad_ind, -- Tenth Year Graduation Indicator
      -- Tenth Winter Set **********************************************************
      (
      SELECT td1.sd_term_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) tenth_win_code,
      (SELECT td1.registered_ind
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) tenth_win_term_reg_ind,
      (SELECT td1.student_type_code
      FROM td_student_data td1
      WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
      AND td1.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td1.primary_level_code = 'UG'
      AND td1.student_type_code IN ('C','T','R')
      ) tenth_win_std_type,
      (SELECT td2.term_gpa
      FROM um_student_data td2
      WHERE td2.sd_term_code     = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
      AND td2.sd_pidm            = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.primary_level_code = 'UG'
      AND td2.student_type_code IN ('C','T','R')
      ) tenth_win_term_gpa,
      (SELECT td2.term_hours_attempted
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) tenth_win_CH_atmpt,--Credit Hour Attempted
      (SELECT ROUND ((td2.term_hours_passed/td2.term_hours_attempted),2)
      FROM um_student_data td2
      WHERE td2.sd_term_code       = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
      AND td2.sd_pidm              = IA_COHORT_POP_UNDUP_TBL.co_pidm
      AND td2.term_hours_attempted >0
      AND td2.primary_level_code   = 'UG'
      AND td2.student_type_code   IN ('C','T','R')
      ) tenth_win_CH_cmpltn --Credit Hour Completion
     -- Cohort Demographic Table joins and Where Clause ********************************
    FROM TD_student_data
    INNER JOIN IA_COHORT_POP_UNDUP_TBL
    ON IA_COHORT_POP_UNDUP_TBL.co_pidm = TD_student_data.sd_pidm
  );
