--------------------------------------------------------
--  DDL for View IA_UM_DEGREE
--------------------------------------------------------

CREATE OR REPLACE VIEW IA_UM_DEGREE as (
Select
UM_DEGREE.ACADEMIC_YEAR,
UM_DEGREE.ACTIVITY_DATE,
UM_DEGREE.CATALOG_TERM_CODE,
UM_DEGREE.CATALOG_TERM_DESC,
UM_DEGREE.COLLEGE_CODE,
UM_DEGREE.COLLEGE_DESC,
UM_DEGREE.DEGREE_APPL_DATE,
UM_DEGREE.DEGREE_CODE,
UM_DEGREE.DEGREE_DESC,
nvl(IA_CW_HP.HP_IND, 'N') AS HP_DEGREE_IND,
IA_CW_NCES_CODE.IA_NCES_CODE,
IA_CW_NCES_CODE.IA_NCES_DESC,
UM_DEGREE.DEGREE_SEQNO,
UM_DEGREE.DEGREE_STATUS,
UM_DEGREE.DEGREE_STATUS_DESC,
UM_DEGREE.DEPARTMENT_CODE,
UM_DEGREE.DEPARTMENT_DESC,
UM_DEGREE.GRAD_DATE,
UM_DEGREE.GRAD_STATUS,
UM_DEGREE.GRAD_STATUS_DESC,
UM_DEGREE.GRAD_TERM_CODE,
UM_DEGREE.GRAD_TERM_DESC,
UM_DEGREE.LEVEL_CODE,
decode(UM_DEGREE.LEVEL_CODE,'UG','UG','U2','UG','U3','UG','GR')report_level_code,
UM_DEGREE.LEVEL_DESC,
UM_DEGREE.PIDM,
UM_DEGREE.PRIMARY_CONC_1,
UM_DEGREE.PRIMARY_CONC_1_DESC,
UM_DEGREE.PRIMARY_CONC_1_MAJOR_ATTACH,
UM_DEGREE.PRIMARY_CONC_2,
UM_DEGREE.PRIMARY_CONC_2_DESC,
UM_DEGREE.PRIMARY_CONC_2_MAJOR_ATTACH,
UM_DEGREE.PRIMARY_MAJOR_1,
UM_DEGREE.PRIMARY_MAJOR_1_CIPC_CODE,
UM_DEGREE.PRIMARY_MAJOR_1_DESC,
nvl(IA_CW_STEM.STEM_IND, 'N') AS STEM_MJR_IND_1,
UM_DEGREE.PRIMARY_MAJOR_2,
UM_DEGREE.PRIMARY_MAJOR_2_CIPC_CODE,
UM_DEGREE.PRIMARY_MAJOR_2_DESC,
case 
WHEN IA_CW_STEM.MAJR_CODE = UM_DEGREE.PRIMARY_MAJOR_2 THEN IA_CW_STEM.STEM_IND
ELSE 'N'
END STEM_MJR_IND_2,
UM_DEGREE.PRIMARY_MINOR_1,
UM_DEGREE.PRIMARY_MINOR_1_DESC,
UM_DEGREE.PRIMARY_MINOR_2,
UM_DEGREE.PRIMARY_MINOR_2_DESC,
UM_DEGREE.PRIMARY_PROGRAM,
UM_DEGREE.PRIMARY_PROGRAM_DESC,
UM_DEGREE.UMID,
UM_DEMOGRAPHIC.FIRST_NAME,
UM_DEMOGRAPHIC.MIDDLE_INITIAL,
UM_DEMOGRAPHIC.LAST_NAME,
UM_DEMOGRAPHIC.NAME_SUFFIX,
UM_DEMOGRAPHIC.A1_STREET_LINE1,
UM_DEMOGRAPHIC.A1_STREET_LINE2,
UM_DEMOGRAPHIC.A1_STREET_LINE3,
UM_DEMOGRAPHIC.A1_CITY,
UM_DEMOGRAPHIC.A1_STATE_CODE,
UM_DEMOGRAPHIC.A1_STATE_DESC,
UM_DEMOGRAPHIC.A1_ZIP,
UM_DEMOGRAPHIC.A1_COUNTY_CODE,
UM_DEMOGRAPHIC.A1_COUNTY_DESC,
UM_DEMOGRAPHIC.A1_NATION_CODE,
UM_DEMOGRAPHIC.A1_NATION_DESC,
UM_DEMOGRAPHIC.A1_AREA_CODE,
UM_DEMOGRAPHIC.A1_PHONE_NUMBER,
UM_DEMOGRAPHIC.NR_STREET_LINE1,
UM_DEMOGRAPHIC.NR_STREET_LINE2,
UM_DEMOGRAPHIC.NR_STREET_LINE3,
UM_DEMOGRAPHIC.NR_CITY,
UM_DEMOGRAPHIC.NR_STATE_CODE,
UM_DEMOGRAPHIC.NR_STATE_DESC,
UM_DEMOGRAPHIC.NR_ZIP,
UM_DEMOGRAPHIC.NR_COUNTY_CODE,
UM_DEMOGRAPHIC.NR_COUNTY_DESC,
UM_DEMOGRAPHIC.NR_NATION_CODE,
UM_DEMOGRAPHIC.NR_NATION_DESC,
UM_DEMOGRAPHIC.NR_AREA_CODE,
UM_DEMOGRAPHIC.NR_PHONE_NUMBER,
UM_DEMOGRAPHIC.CA_EMAIL,
UM_DEMOGRAPHIC.HM_EMAIL_1,
UM_DEMOGRAPHIC.BIRTHDATE,
UM_DEMOGRAPHIC.AGE,
UM_DEMOGRAPHIC.GENDER,
UM_DEMOGRAPHIC.NEW_ETHNICITY,
UM_DEMOGRAPHIC.NEW_ETHNICITY_DESC,
UM_DEMOGRAPHIC.RACE_CODE_1,
UM_DEMOGRAPHIC.RACE_DESC_1,
UM_DEMOGRAPHIC.RACE_CODE_2,
UM_DEMOGRAPHIC.RACE_DESC_2,
UM_DEMOGRAPHIC.RACE_CODE_3,
UM_DEMOGRAPHIC.RACE_DESC_3,
UM_DEMOGRAPHIC.RACE_CODE_4,
UM_DEMOGRAPHIC.RACE_DESC_4,
UM_DEMOGRAPHIC.RACE_CODE_5,
UM_DEMOGRAPHIC.RACE_DESC_5,
UM_DEMOGRAPHIC.MULTI_RACE_IND,
UM_DEMOGRAPHIC.CITIZENSHIP_CODE,
UM_DEMOGRAPHIC.CITIZENSHIP_DESC,
UM_DEMOGRAPHIC.VETERAN_IND,
UM_DEMOGRAPHIC.INTL_IND,
UM_DEMOGRAPHIC.VETERAN_BENIFITS_ELIGIBILE,
UM_DEMOGRAPHIC.VISA_TYPE_CODE,
UM_DEMOGRAPHIC.VISA_TYPE_DESC,
UM_DEMOGRAPHIC.NATION_CITIZEN_CODE,
UM_DEMOGRAPHIC.NATION_CITIZEN_DESC,
UM_DEMOGRAPHIC.REPORT_ETHNICITY,
UM_DEMOGRAPHIC.HSCH_CODE,
UM_DEMOGRAPHIC.HSCH_DESC,
UM_DEMOGRAPHIC.HSCH_GRAD_DATE,
UM_DEMOGRAPHIC.HSCH_RANK,
UM_DEMOGRAPHIC.HSCH_SIZE,
UM_DEMOGRAPHIC.HSCH_GPA,
UM_DEMOGRAPHIC.HSCH_STREET_LINE_1,
UM_DEMOGRAPHIC.HSCH_STREET_LINE_2,
UM_DEMOGRAPHIC.HSCH_CITY,
UM_DEMOGRAPHIC.HSCH_STATE,
UM_DEMOGRAPHIC.HSCH_ZIP,
UM_DEMOGRAPHIC.PCOL_CODE_1,
UM_DEMOGRAPHIC.PCOL_DESC_1,
UM_DEMOGRAPHIC.PCOL_TRANS_RECV_DATE_1,
UM_DEMOGRAPHIC.PCOL_CODE_2,
UM_DEMOGRAPHIC.PCOL_DESC_2,
UM_DEMOGRAPHIC.PCOL_DEGC_CODE_1,
UM_DEMOGRAPHIC.PCOL_DEGC_DATE_1,
UM_DEMOGRAPHIC.PCOL_HOURS_TRANSFERRED_1,
UM_DEMOGRAPHIC.PCOL_GPA_TRANSFERRED_1,
UM_DEMOGRAPHIC.GRADUATED_IND,
UM_DEMOGRAPHIC.VISA_NUMBER,
UM_DEMOGRAPHIC.NATION_BIRTH_CODE,
UM_DEMOGRAPHIC.NATION_BIRTH_DESC,
UM_DEMOGRAPHIC.GENDER_DESC,
UM_DEMOGRAPHIC.NATIVE_LANGUAGE_CODE,
UM_DEMOGRAPHIC.NATIVE_LANGUAGE_DESC

from um_degree
inner join UM_DEMOGRAPHIC on UM_DEMOGRAPHIC.DM_PIDM = um_degree.PIDM
left join IA_CW_HP on IA_CW_HP.DEGREE_CODE = UM_DEGREE.DEGREE_CODE AND
                      IA_CW_HP.PRIMARY_PROGRAM = UM_DEGREE.PRIMARY_PROGRAM
left join IA_CW_STEM on IA_CW_STEM.MAJR_CODE = UM_DEGREE.PRIMARY_MAJOR_1
LEFT JOIN ia_cw_nces_code
    ON IA_CW_NCES_CODE.degree_code      = um_degree.degree_code
    AND IA_CW_NCES_CODE.MAJR_CODE = um_degree.primary_major_1
);
