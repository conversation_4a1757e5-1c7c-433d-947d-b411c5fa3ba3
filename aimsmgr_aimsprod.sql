--select 
----pidm --,
--count(*)
--from um_degree
--where degree_status = 'AW'
--and grad_term_code = '201810'
--
----having count(*) > 1
----
----group by
----pidm
--
--
--and pidm not in (
--
--select 
--count(*)
----sd_pidm
--from um_student_data
--where sd_term_code = '201810'
--and student_status_code = 'GD'
--and exp_grad_term = sd_term_code
--)
--;




with all_pop_sel as(
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201830' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201830' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201820' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201820' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201810' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201810' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201740' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201740' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201730' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201730' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201720' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201720' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201710' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201710' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201640' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201640' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
    union all
    select
    um_student_data.sd_pidm,
    um_student_data.sd_term_code,
    um_student_data.primary_major_1,
    um_student_data.term_gpa,
    um_student_data.overall_gpa
    from um_student_data
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
                              and um_demographic.deceased_ind is null

    where sd_term_code = '201630' 
    and student_status_code = 'AS'
    and nvl(um_student_data.term_registered_hours, 0) > 0
    and not exists(select 'has GDd in the future' 
                   from um_student_data s1 
                   where s1.sd_term_code > '201630' 
                   and s1.student_status_code = 'GD'
                   and s1.exp_grad_term = s1.sd_term_code
                   and s1.sd_pidm = um_student_data.sd_pidm)
)
, order_pop_sel as(
    select 
    all_pop_sel.sd_pidm,
    all_pop_sel.sd_term_code,
    all_pop_sel.primary_major_1,
    all_pop_sel.term_gpa,
    all_pop_sel.overall_gpa,
    row_number() over(partition by all_pop_sel.sd_pidm order by all_pop_sel.sd_term_code desc) order_num
    from all_pop_sel
),
pop_sel as(
    select *
    from order_pop_sel
    where order_pop_sel.order_num = 1


--    where ((sd_term_code = '201630' and student_status_code = 'AS') or
--          (sd_term_code = '201640' and student_status_code = 'AS') or
--          (sd_term_code = '201710' and student_status_code = 'AS') or
--          (sd_term_code = '201720' and student_status_code = 'AS') or
--          (sd_term_code = '201730' and student_status_code = 'AS') or
--          (sd_term_code = '201740' and student_status_code = 'AS') or
--          (sd_term_code = '201810' and student_status_code = 'AS') or
--          (sd_term_code = '201820' and student_status_code = 'AS') or
--          (sd_term_code = '201830' and student_status_code = 'AS'))
--
--    and 
--
--
--
----    and um_student_data.report_level_code = 'UG'
----    and um_student_data.student_type_code in ('F', 'T', 'C')
--    and nvl(um_student_data.term_registered_hours, 0) > 0
--
--    order by
--    sd_pidm,
--    sd_term_code
----    and sd_pidm = 37935
),
holds_cur as(
    select 
    pidm,
    count(*) hold_ctr
    from um_holds
    where um_holds.hold_status_ind = 'CURRENT'
    and um_holds.reg_hold_ind = 'Y'
    group by
    pidm
)
, nsc_meta_cur as(
    select 
    nsc_meta.*,
    row_number() over(partition by nsc_meta.pidm order by nsc_meta.mapped_term_code desc) order_num
    from NSCMGR.nsc_meta
    where nsc_meta.mapped_term_code is not null
)
--, degree_cur as(
--    select 
--    um_degree.*,
--    row_number() over(partition by um_degree.pidm order by um_degree.grad_term_code desc) order_num
--    from um_degree
--    where um_degree.degree_status = 'AW'
--    and (um_degree.grad_term_code is not null and um_degree.grad_term_code >= '201630')
--)
, post_pop_sel as(
    select 
    um_student_data.sd_term_code,
    um_student_data.sd_pidm,
    um_demographic.umid,
    um_demographic.last_name,
    um_demographic.first_name,
    um_demographic.intl_ind,
    um_student_data.student_status_code,
    um_student_data.most_recent_astd_code,
    um_student_data.most_recent_astd_desc,
    pop_sel.primary_major_1,
    um_student_data.primary_major_1 current_primary_major_1,
    pop_sel.overall_gpa,
    pop_sel.term_gpa,
    nsc_meta.mapped_term_code,
    case
        when nsc_meta.college_name is null then 'NSC Rec Not Found' 
        else initcap(nsc_meta.college_name) 
    end college_name,
    nvl(nsc_meta.record_found, 'N') record_found,
    nvl(holds_cur.hold_ctr, 0) hold_ctr,
    --degree_cur.degree_status,
    um_demographic.deceased_ind
    from um_student_data
    inner join pop_sel on pop_sel.sd_pidm = um_student_data.sd_pidm
    --                   and pop_sel.order_num = 1
    inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
    left outer join nscmgr.nsc_meta_cur nsc_meta on nsc_meta.pidm = um_student_data.sd_pidm
                                                 --and nsc_meta.mapped_term_code >= '201820' -- pop_sel.sd_term_code
                                                 and nsc_meta.order_num = 1
    left outer join holds_cur on holds_cur.pidm = um_student_data.sd_pidm
    --left outer join degree_cur on degree_cur.pidm = um_student_data.sd_pidm
    --                           and degree_cur.grad_term_code >= pop_sel.sd_term_code
    --                           and degree_cur.degree_status = 'AW'
    --                           and degree_cur.order_num = 1
    where um_student_data.sd_term_code = '201830'
    --and um_student_data.report_level_code = 'UG'
    --and um_student_data.student_status_code = 'IS'
    and nvl(um_student_data.most_recent_astd_code, 'XX') = 'GD'
    and um_student_data.student_status_code != 'GD'
    and not exists(select ad_pidm
                   from um_admissions_applicant 
                   where um_admissions_applicant.ad_pidm = um_student_data.sd_pidm
                   and um_admissions_applicant.term_code_entry > '201830'
                   and um_admissions_applicant.apdc_code_1 in ('AD', 'A2'))
    order by 
    4
    --um_student_data.sd_pidm,
    --nsc_meta.mapped_term_code
)

select 
post_pop_sel.sd_term_code,
post_pop_sel.student_status_code,
--post_pop_sel.term_gpa,
--post_pop_sel.overall_gpa,
case
    when post_pop_sel.overall_gpa < 2 then '<-2'
    when post_pop_sel.overall_gpa >= 2 and post_pop_sel.overall_gpa < 3 then '2-3'
    when post_pop_sel.overall_gpa >= 3 then '3->'
    else '---'
end overall_gpa,
--post_pop_sel.mapped_term_code,
post_pop_sel.college_name,
count(*)
from post_pop_sel
where student_status_code = 'AS'
group by
post_pop_sel.sd_term_code,
post_pop_sel.student_status_code,
--post_pop_sel.term_gpa,
case
    when post_pop_sel.overall_gpa < 2 then '<-2'
    when post_pop_sel.overall_gpa >= 2 and post_pop_sel.overall_gpa < 3 then '2-3'
    when post_pop_sel.overall_gpa >= 3 then '3->'
    else '---'
end,
--post_pop_sel.mapped_term_code,
post_pop_sel.college_name

order by

 4, 3
--post_pop_sel.sd_term_code,
--post_pop_sel.student_status_code,
--post_pop_sel.mapped_term_code,
--post_pop_sel.college_name



;

select 
um_degree.*,
row_number() over(partition by um_degree.pidm order by um_degree.grad_term_code desc) order_num
from um_degree
where um_degree.degree_status = 'AW'
and (um_degree.grad_term_code is not null and um_degree.grad_term_code >= '201630')

;

