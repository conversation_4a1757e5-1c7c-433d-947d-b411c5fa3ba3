select 
sum_cur.grad_yr,
sum_cur.level_code,
sum_cur.pell_ind,
count(sum_cur.pidm) student_count
from (
  with grad_cur as(
    select
    distinct um_degree.pidm,
    um_degree.level_code,
    '11-12' grad_yr
    from um_degree
    where (um_degree.grad_term_code = '201130' or 
           um_degree.grad_term_code = '201140' or
           um_degree.grad_term_code = '201210' or
           um_degree.grad_term_code = '201220')
    and um_degree.degree_status = 'AW'
    union all
    select 
    distinct um_degree.pidm,
    um_degree.level_code,
    '12-13' grad_yr
    from um_degree
    where (um_degree.grad_term_code = '201230' or 
           um_degree.grad_term_code = '201240' or
           um_degree.grad_term_code = '201310' or
           um_degree.grad_term_code = '201320')
    and um_degree.degree_status = 'AW'
  )
  select
  grad_cur.pidm,
  grad_cur.grad_yr,
  case
    when grad_cur.level_code = 'UG' or
         grad_cur.level_code = 'U2' or
         grad_cur.level_code = 'U3' then 'UG'
    else 'GR'
  end level_code,
  case
    when pell_join.pell_efc > 0 then 'Y'
    else 'N'
  end pell_ind
  from grad_cur
  left outer join(
    select
    um_finaid_by_term.fa_pidm,
    sum(nvl(um_finaid_by_term.pell_efc_amount, 0)) pell_efc
    from um_finaid_by_term
    group by um_finaid_by_term.fa_pidm
  )pell_join on pell_join.fa_pidm = grad_cur.pidm
)sum_cur
group by sum_cur.grad_yr, sum_cur.level_code, sum_cur.pell_ind
order by sum_cur.grad_yr, sum_cur.level_code, sum_cur.pell_ind
