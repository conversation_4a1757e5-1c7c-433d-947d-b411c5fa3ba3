--------------------------------------------------------
--  DDL for View IA_FA_RECIP
--------------------------------------------------------

--CREATE OR REPLACE VIEW iamgr.ia_fa_recip AS
 SELECT DISTINCT 
--    FY,
  sd.sd_pidm,
  sd.sd_term_code,
--    fy.fy_term_code,
  fy.fy,
  fy.fafy,
  sd.registered_ind,
    
   
    
/*Loan Zone*/
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type = 'LOAN'
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') loan_recip_ind,
    
/*Grant Zone*/
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type IN('GRNT')
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') grant_recip_ind,
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_code = '1PELL'
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') pell_recip_ind,
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_code = '2FLTPR'
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') flint_promise_recip_ind, 
      
/*Scholorship Zone*/
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type IN('SCHL')
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') schl_recip_ind, 

/*Work Study*/
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type = 'WORK'
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') work_study_recip_ind, 

/*Any Aid*/
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') any_aid_recip_ind
 FROM
       um_student_data sd
  INNER JOIN um_demographic dm ON sd.sd_pidm = dm.pidm_key
  LEFT JOIN ia_cw_fy_term  fy ON sd_term_code = fy.fy_term_code
 WHERE
  sd.sd_term_code >= 201810

--  and sd.registered_ind = 'Y'
  ;

--desc aimsmgr.STVTERM_EXT;