--------------------------------------------------------
--  DDL for View IA_UM_STUDENT_DATA
--------------------------------------------------------

CREATE OR REPLACE VIEW IA_UM_STUDENT_DATA AS (

Select
SD.SD_PIDM,
SD.SD_TERM_CODE,
SD.MOST_RECENT_TERM_CODE,
SD.MOST_RECENT_TERM_DESC,
SD.MOST_RECENT_ASTD_CODE, 
SD.MOST_RECENT_ASTD_DESC,
fy.fy,
fy.fafy,
fy.cy,
SD.SD_TERM_DESC,
SD.UMID,
DM.FIRST_GENERATION_STUDENT_IND,
--DM.SSN,

case
when PRIMARY_PROGRAM like '%DAP%' OR PRIMARY_PROGRAM like '%DNP%' 
OR PRIMARY_PROGRAM like '%DPT%' OR PRIMARY_PROGRAM like '%PTP%' 
OR PRIMARY_PROGRAM like '%DNAP%' OR PRIMARY_PROGRAM like '%NUR%'
OR PRIMARY_PROGRAM like '%OTD%' OR PRIMARY_PROGRAM like '%DR'
then 'Drs-Prof'
when PRIMARY_PROGRAM like '%EDD%' or PRIMARY_PROGRAM like '%PHD%' 
then 'Drs-Acad' 
else class_code
end HEIDI_Class_Code,

case
when PRIMARY_DEGREE_CODE = 'CERG' then 'Post Masters Cert'
WHEN PRIMARY_PROGRAM like '%EDS%'Then 'Ed Specialist'
when PRIMARY_PROGRAM like '%DAP%' OR PRIMARY_PROGRAM like '%DNP%' 
OR PRIMARY_PROGRAM like '%DPT%' OR PRIMARY_PROGRAM like '%PTP%' 
OR PRIMARY_PROGRAM like '%DNAP%' OR PRIMARY_PROGRAM like '%NUR%'
OR PRIMARY_PROGRAM like '%OTD%' OR PRIMARY_PROGRAM like '%DR'
then 'Drs-Prof'
when PRIMARY_PROGRAM like '%EDD%' or PRIMARY_PROGRAM like '%PHD%' 
then 'Drs-Acad' 
else report_level_code
end Class_Standing,

--case
--when PRIMARY_PROGRAM like '%DAP%' OR PRIMARY_PROGRAM like '%DNP%' 
--OR PRIMARY_PROGRAM like '%DPT%' OR PRIMARY_PROGRAM like '%PTP%' 
--OR PRIMARY_PROGRAM like '%DNAP%' OR PRIMARY_PROGRAM like '%NUR%'
--OR PRIMARY_PROGRAM like '%OTD%'
--then 'Drs-Prof'
--when PRIMARY_PROGRAM like '%EDD%' or PRIMARY_PROGRAM like '%PHD%' 
--then 'Drs-Acad' 
--else REPORT_LEVEL_CODE
--end Dist_Ed_Class_Code,


case
when PRIMARY_PROGRAM like '%DAP%' OR PRIMARY_PROGRAM like '%DNP%' 
OR PRIMARY_PROGRAM like '%DPT%' OR PRIMARY_PROGRAM like '%PTP%' 
OR PRIMARY_PROGRAM like '%DNAP%' OR PRIMARY_PROGRAM like '%NUR%'
OR PRIMARY_PROGRAM like '%OTD%' OR PRIMARY_PROGRAM like '%EDD%' 
OR PRIMARY_PROGRAM like '%PHD%' OR PRIMARY_PROGRAM like '%DR'
then 'Drs' 
else REPORT_LEVEL_CODE
end FINANCE_Class_Code,

case
when PRIMARY_PROGRAM like '%DAP%' OR PRIMARY_PROGRAM like '%DNP%' 
OR PRIMARY_PROGRAM like '%DPT%' OR PRIMARY_PROGRAM like '%PTP%' 
OR PRIMARY_PROGRAM like '%DNAP%' OR PRIMARY_PROGRAM like '%NUR%'
OR PRIMARY_PROGRAM like '%OTD%' OR PRIMARY_PROGRAM like '%DR'
then 'Drs-Prof'
when PRIMARY_PROGRAM like '%EDD%' or PRIMARY_PROGRAM like '%PHD%' 
then 'Drs-Acad' 
else report_level_code
end IPEDS_Class_Code,

case
when PRIMARY_PROGRAM like '%DAP%' OR PRIMARY_PROGRAM like '%DNP%' 
OR PRIMARY_PROGRAM like '%DPT%' OR PRIMARY_PROGRAM like '%PTP%' 
OR PRIMARY_PROGRAM like '%DNAP%' OR PRIMARY_PROGRAM like '%NUR%'
OR PRIMARY_PROGRAM like '%OTD%' OR PRIMARY_PROGRAM like '%DR'
then 'Drs-Prof'
when PRIMARY_PROGRAM like '%EDD%' or PRIMARY_PROGRAM like '%PHD%' 
then 'Drs-Acad' 
else report_level_code
end Audits_Class_Code,

case
when INTL_IND is NULL and ia_student_type_code = 'F' then 'Dom_Freshman'
when INTL_IND is NULL and ia_student_type_code = 'T' then 'Dom_Transfer'
when INTL_IND is NULL and ia_student_type_code = 'N' then 'Dom_New_Grad'
when INTL_IND is NULL and ia_student_type_code in ('G','D','E','S') then 'Dom_Other'
when INTL_IND = 'Y' and ia_student_type_code = 'F' then 'Int_Freshman'
when INTL_IND = 'Y' and ia_student_type_code = 'T' then 'Int_Transfer'
when INTL_IND = 'Y' and ia_student_type_code = 'N' then 'Int_New_Grad'
when INTL_IND = 'Y' and ia_student_type_code in ('G','D','E','S') then 'Int_Other'
else 'Continuing and Other'
end sem_groups,
case
when (SD.IA_STUDENT_TYPE_CODE = 'T' 
and primary_level_code in ('U2','U3')) then 'C'
else SD.IA_STUDENT_TYPE_CODE
end IPEDS_STUDENT_TYPE_CODE,

CASE
WHEN DM.AGE <= 17 THEN 'Under 18'
WHEN DM.AGE >= 18 AND DM.AGE <= 19 THEN '18-19'
WHEN DM.AGE >= 20 AND DM.AGE <= 21 THEN '20-21'
WHEN DM.AGE >= 22 AND DM.AGE <= 24 THEN '22-24'
WHEN DM.AGE >= 25 AND DM.AGE <= 29 THEN '25-29'
WHEN DM.AGE >= 30 AND DM.AGE <= 34 THEN '30-34'
WHEN DM.AGE >= 35 AND DM.AGE <= 39 THEN '35-39'
WHEN DM.AGE >= 40 AND DM.AGE <= 49 THEN '40-49'
WHEN DM.AGE >= 50 AND DM.AGE <= 64 THEN '50-64'
WHEN DM.AGE >= 65 THEN 'Over 65'
ELSE 'Unknown'
END IPEDS_AGE_DIST,
DECODE(DM.gender,'F',2,1)IPEDS_GENDER_CODE,
DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien',1,
      'Hispanic or Latino',2,
      'American Indian or Alaska Native',3,
      'Asian',4,
      'Black or African American',5,
      'Native Hawaiian and Other Pacific Islander',6,
      'White',7,
      'Two or more races',8,9)IPEDS_RACE_CODE,
SD.IA_PROJECTED_STUDENT_TYPE,
SD.IA_PROJECTED_STUDENT_TYPE_DESC,
SD.IA_STUDENT_TYPE_CODE,
SD.IA_STUDENT_TYPE_DESC,
DECODE(DM.gender,'F','F','M')IA_GENDER,
substr(primary_major_1_cipc_code,1,2)IA_CIP_FAMILY,
SD.HONORS_PROGRAM,
SD.REGISTERED_IND,
SD.RATE_CODE,
SD.TERM_BILLING_HOURS, 
SD.TERM_REGISTERED_HOURS,
SD.FULL_PART_TIME_IND,
SD.STUDENT_STATUS_CODE,
SD.STUDENT_STATUS_DESC,
SD.STUDENT_TYPE_CODE,
SD.STUDENT_TYPE_DESC,

SD.STU_ATTR_1, 
SD.STU_ATTR_2, 
SD.STU_ATTR_3, 
SD.STU_ATTR_4, 
SD.STU_ATTR_5,

SD.DEEP_IND,
SD.CLASS_CODE,
SD.CLASS_DESC,
SD.HOUSING_IND,
SD.RESIDENCY_CODE,
SD.RESIDENCY_DESC,
SD.PRIMARY_PROGRAM,
SD.PRIMARY_PROGRAM_DESC,
SD.PRIMARY_LEVEL_CODE,
SD.PRIMARY_LEVEL_DESC,
SD.PRIMARY_COLLEGE_CODE,
SD.PRIMARY_COLLEGE_DESC,
SD.PRIMARY_DEGREE_CODE,
SD.PRIMARY_DEGREE_DESC,
SD.PRIMARY_ADMIT_CODE,
SD.PRIMARY_ADMIT_DESC,
SD.PRIMARY_ADMIT_TERM,
SD.PRIMARY_ADMIT_TERM_DESC,
SD.PRIMARY_MAJOR_1,
SD.PRIMARY_MAJOR_1_DESC,
SD.PRIMARY_MAJOR_2,
SD.PRIMARY_MAJOR_2_DESC,
SD.PRIMARY_MINOR_1,
SD.PRIMARY_MINOR_1_DESC,
SD.PRIMARY_CONC_1,
SD.PRIMARY_CONC_1_DESC,
SD.TERM_HOURS_ATTEMPTED,
SD.TERM_HOURS_EARNED,
SD.TERM_GPA_HOURS,
SD.TERM_GPA,
SD.TERM_HOURS_PASSED,
SD.TERM_ASTD_CODE,
SD.TERM_ASTD_DESC,
SD.OVERALL_HOURS_ATTEMPTED,
SD.OVERALL_HOURS_EARNED,
SD.OVERALL_GPA_HOURS,
SD.OVERALL_GPA,
SD.INST_GPA,
SD.inst_hours_earned,
SD.REPORT_LEVEL_CODE,
SD.PROJECTED_STUDENT_TYPE,
SD.ORIG_STUDENT_TYPE_CODE,
SD.ORIG_STUDENT_TYPE_DESC,
SD.ONLINE_COURSES_ONLY_IND,
SD.FULL_PART_TIME_IND_UMF,
SD.TOTAL_CREDIT_HOURS_UMF,
SD.PRIMARY_MAJOR_1_CIPC_CODE,
SD.PRIMARY_MAJOR_2_CIPC_CODE,
NVL(SD.EDUC_GOAL,'00')EDUC_GOAL,
NVL(SD.EDUC_GOAL_DESC,'Undeclared')EDUC_GOAL_DESC,
SD.PRIMARY_CONC_1_CIPC_CODE,
SD.PRIMARY_CONC_2_CIPC_CODE,
SD.MOST_RECENT_LEVL_CODE,
SD.ENR_STATUS_DATE,
DM.VETERAN_IND,
DM.VETERAN_BENIFITS_ELIGIBILE,
SD.VA_CERT_DATE VETERAN_CERT_DATE,
SD.VA_CERT_HOURS VETERAN_CERT_HOURS,
SD.VA_CERT_TERM VETERAN_CERT_TERM,
SD.VA_CERT_TERM_IND VETERAN_CERT_TERM_IND,
SD.VA_CERT_TYPE VETERAN_CERT_TYPE,
SD.VA_CERT_TYPE_DESC VETERAN_CERT_TYPE_DESC,
--SD.AR_ACCOUNT_BALANCE,
--SD.AR_AMOUNT_DUE,
DM.FIRST_NAME,
DM.MIDDLE_INITIAL,
DM.LAST_NAME,
DM.NAME_SUFFIX,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT DM_PIDM,
    TD_TERM_CODE
    FROM TD_DEMOGRAPHIC dm
    WHERE dm.a1_county_code IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    and dm.dm_pidm = sd.sd_pidm
    and dm.TD_TERM_CODE = sd.sd_term_code
    )
  ), 'No') Commutable_ind_A1,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT AD.AD_pidm,
      MAX(AD.APST_DATE)
    FROM TD_ADMISSIONS_APPLICANT AD
    WHERE INST_ACCEPTED_APP_ANY_DATE_IND        = 'Y' --was admitted
    AND registered_ind                          = 'Y' --enrolled
    AND AD.ad_pidm         = sd.sd_pidm
    AND AD.term_code_entry = sd.sd_term_code
    AND AD.COUNTY_CODE_ADMIT IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    GROUP BY AD_pidm
    )
  ), 'No') Commutable_ind_origin,
 CASE
 WHEN DM.A1_STATE_CODE IN ( 'AK','AL','AR','AZ','CA',
                         'CO','CT','DC','DE','FL',
                         'GA','HI','IA','ID','IL',
                         'IN','KS','KY','LA','MA',
                         'MD','ME','MI','MN','MO',
                         'MS','MT','NC','ND','NE',
                         'NH','NJ','NM','NV','NY',
                         'OH','OK','OR','PA','RI',
                         'SC','SD','TN','TX','UT',
                         'VA','VT','WA','WI','WV',
                         'WY' ) 
 THEN
  'Y'
 ELSE
  'N'
END living_in_US_ind,
DM.A1_STREET_LINE1,
DM.A1_STREET_LINE2,
DM.A1_STREET_LINE3,
DM.A1_CITY,
DM.A1_STATE_CODE,
DM.A1_STATE_DESC,
DM.A1_ZIP,
DM.A1_COUNTY_CODE,
DM.A1_COUNTY_DESC,
DM.A1_NATION_CODE,
DM.A1_NATION_DESC,
DM.A1_AREA_CODE,
DM.A1_PHONE_NUMBER,
DM.A1_DISTANCE_FROM_CAMPUS_BY_ZIP,
DM.MO_AREA_CODE,
DM.MO_PHONE_NUMBER,
DM.PR_CITY,
DM.PR_COUNTY_CODE,
DM.PR_COUNTY_DESC,
DM.PR_STATE_CODE,
DM.PR_STREET_LINE1,
DM.PR_STREET_LINE2,
DM.PR_ZIP,
DM.PR_ZIP5,
--DM.NR_STREET_LINE1,
--DM.NR_STREET_LINE2,
--DM.NR_STREET_LINE3,
--DM.NR_CITY,
--DM.NR_STATE_CODE,
--DM.NR_STATE_DESC,
--DM.NR_ZIP,
--DM.NR_COUNTY_CODE,
--DM.NR_COUNTY_DESC,
--DM.NR_NATION_CODE,
--DM.NR_NATION_DESC,
--DM.NR_AREA_CODE,
--DM.NR_PHONE_NUMBER,
DM.CA_EMAIL,
DM.HM_EMAIL_1,
DM.BIRTHDATE,
DM.AGE,
DM.GENDER,
DM.GENDER_DESC,
DM.CITIZENSHIP_CODE,
DM.CITIZENSHIP_DESC,
DM.INTL_IND,
DM.VISA_TYPE_CODE,
DM.VISA_TYPE_DESC,
DM.NATION_CITIZEN_CODE,
DM.NATION_CITIZEN_DESC,
DM.REPORT_ETHNICITY,
DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien','N',
      'Hispanic or Latino','Y',
      'American Indian or Alaska Native','Y',
      'Asian','N',
      'Black or African American','Y',
      'Native Hawaiian and Other Pacific Islander','Y',
      'White','N',
      'Two or more races','Y','UK')REPORT_URM,
DM.HSCH_CODE,
DM.HSCH_DESC,
DM.HSCH_GRAD_DATE,
DM.HSCH_RANK,
DM.HSCH_SIZE,
DM.HSCH_GPA,
DM.HSCH_STREET_LINE_1,
DM.HSCH_STREET_LINE_2,
DM.HSCH_CITY,
DM.HSCH_STATE,
DM.HSCH_ZIP,
DM.PCOL_CODE_1,
DM.PCOL_DESC_1,
DM.PCOL_TRANS_RECV_DATE_1,
DM.PCOL_CODE_2,
DM.PCOL_DESC_2,
DM.PCOL_DEGC_CODE_1,
DM.PCOL_DEGC_DATE_1,
DM.PCOL_HOURS_TRANSFERRED_1,
DM.PCOL_GPA_TRANSFERRED_1,
DM.GRADUATED_IND,
DM.VISA_NUMBER,
DM.DECEASED_IND,
DM.DECEASED_DATE

from um_student_data SD
left join um_DEMOGRAPHIC DM on DM.DM_PIDM = SD.SD_PIDM
left join  IA_CW_FY_TERM fy
on sd.sd_term_code = fy.fy_term_code
);
desc um_demographic;
