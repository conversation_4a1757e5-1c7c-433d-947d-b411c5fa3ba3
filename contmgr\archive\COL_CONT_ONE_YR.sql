/*
This query builds the table COL_CONT_ONE_YR (Obtion B).

The table is intended to identify students registered for the subsequent fall
by college.  
Including:
   * Both Undergraduate and Graduate students
   * Those seeking second or third degrees (U2, U3, GR2, GR3)
   * Student who have graduated
Excluding
   * Guests,
   * Non-candidate for Degree,
   * Dual Enrolled,
   * Early College, and
   * X student types.

Method:
Students who graduate are maintained in the model until the model base refreshes
Undeclared majors are separated from the unit calculations; however remain in 
the "all" calculations

The “All” calculations by unit hold units harmless for changing majors outside 
of the unit. Original unit is credited with the continuation if they are still 
registered at UM-Flint. Once base refreshes the unit data is realigned to start 
fresh with a new set of continuation calculations.
*/

CREATE OR REPLACE VIEW IA_TD_CONT_ONE_YR AS
  WITH cur_term AS (
  SELECT
   td.sd_pidm,
   td.sd_term_code          sd_term_code,
   td.sd_term_desc          sd_term_desc,
    CASE
    WHEN td.primary_college_code IN ( 'HP', 'HS' ) THEN
     'HS'
    WHEN td.primary_college_code IN ( 'RK', 'AS' ) THEN
     'AS'
    WHEN td.primary_major_1 = '0000' THEN
     '00'
    ELSE
     td.primary_college_code
   END                      cur_col_code,
   td.primary_major_1       cur_major_1,
   td.primary_level_code    cur_level_code,
   td.report_level_code     cur_report_level,
   td.primary_degree_code
  FROM
   td_student_data  td
   LEFT OUTER JOIN td_demographic      dm ON dm.td_term_code = td.sd_term_code
                                        AND dm.dm_pidm = td.sd_pidm
  WHERE
    sd_term_code  >= 201910
    AND sd_term_code like '%10'
    AND td.registered_ind = 'Y'
    and sd_term_code <= (select substr(current_term,1,4)-1 || '10' cur_fall 
                    from um_current_term)    --'202110'
 ),
/******************************************************************************* 
Current Reg Term Set
*******************************************************************************/ 
reg_term AS (
  SELECT
--  cur_term.*,
       sd.sd_pidm,
   sd.sd_term_desc          reg_term_desc,
   sd.registered_ind        reg_term_enr_ind,
   sd.primary_level_code    cur_reg_level_code,
--  sd.primary_college_code    reg_term_col_code,
    CASE
    WHEN sd.primary_college_code IN ( 'HP', 'HS' ) THEN
     'HS'
    ELSE
     sd.primary_college_code
   END                      reg_term_col_code,
   sd.primary_major_1       reg_term_major_1,
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = cur_term.sd_pidm
      AND umd.degree_status = 'AW'
     -- AND umd.degree_code = cur_term.primary_degree_code
           AND umd.level_code = cur_term.cur_level_code
      AND umd.grad_term_code <= sd.sd_term_code
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                      reg_term_grad_ind
   
  FROM
   cur_term
   LEFT JOIN um_student_data sd ON cur_term.sd_pidm = sd.sd_pidm
  WHERE
   sd.sd_term_code = ( cur_term.sd_term_code + 100 )
 )
 SELECT
  cur_term.SD_PIDM,
  cur_term.sd_term_code,
  reg_term_enr_ind,
  reg_term_grad_ind,
  case 
  when reg_term_enr_ind = 'Y' and reg_term_grad_ind = 'N' then 'Still Enrolled'
  when reg_term_enr_ind = 'N' and reg_term_grad_ind = 'Y' then 'Completed'
  when reg_term_enr_ind = 'Y' and reg_term_grad_ind = 'Y' then 'Completed'
  else 'Lost'
  end reg_term_cont

 FROM
  cur_term
  LEFT JOIN reg_term ON cur_term.sd_pidm = reg_term.sd_pidm

;


select * from IA_TD_CONT_ONE_YR;