/*******************************************************************************
revoke datamart views
*******************************************************************************/

create or replace procedure p_dm_view_revoke_select (id_in varchar2) as

 obj_name   VARCHAR2(4000);
 CURSOR table_cur IS
 SELECT
view_name
FROM
 all_views
WHERE
owner LIKE 'AIMSMGR'
and (view_name like 'GER%'
OR view_name like 'GJ%'
OR view_name like 'GL%'
OR view_name like 'GT%'
OR view_name like 'GU%'
OR view_name like 'RB%'
OR view_name like 'RC%'
OR view_name like 'RF%'
OR view_name like 'RN%'
OR view_name like 'RO%'
OR view_name like 'RP%'
OR view_name like 'RR%'
OR view_name like 'RT%'
OR view_name like 'SA%'
OR view_name like 'SB%'
OR view_name like 'SC%'
OR view_name like 'SF%'
OR view_name like 'SG%'
OR view_name like 'SH%'
OR view_name like 'SI%'
OR view_name like 'SL%'
OR view_name like 'SM%'
OR view_name like 'SO%'
OR view_name like 'SR%'
OR view_name like 'SS%'
OR view_name like 'ST%'
OR view_name like 'TB%'
OR view_name like 'TG%'
OR view_name like 'TT%'
OR view_name like 'SS%')
;
 table_rec  table_cur%rowtype;
BEGIN
 dbms_output.enable(NULL);
 OPEN table_cur;
 LOOP
  FETCH table_cur INTO table_rec;
  EXIT WHEN table_cur%notfound;
  obj_name := 'REVOKE SELECT ON aimsmgr.'
              || table_rec.view_name
              || ' FROM '|| id_in;
  dbms_output.put_line(obj_name);
  EXECUTE IMMEDIATE obj_name;
 END LOOP;

 CLOSE table_cur;
END p_dm_view_revoke_select;