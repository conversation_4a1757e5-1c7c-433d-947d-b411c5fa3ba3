TRUNCATE TABLE IA_FY_COHORT_PERSIST_TBL_BAC;

INSERT INTO IA_FY_COHORT_PERSIST_TBL_BAC
SELECT * FROM IA_FY_COHORT_PERSIST_TBL;
COMMIT;

SELECT COUNT (*) FROM IA_FY_COHORT_PERSIST_TBL_BAC;
SELECT COUNT (*) FROM IA_FY_COHORT_PERSIST_TBL;

TRUNCATE TABLE IA_FY_COHORT_PERSIST_TBL;
COMMIT;

INSERT INTO IA_FY_COHORT_PERSIST_TBL


--CREATE TABLE IA_FY_COHORT_PERSIST_TBL AS
  (SELECT CO.*,
      ----------------------------SECOND FALL PERSISTENCE-----------------------
      CASE
        WHEN SCND_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
            END SCND_FALL_PERSIST,
      ----------------------------THIRD FALL PERSISTENCE------------------------
      CASE
        WHEN THRD_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END THRD_FALL_PERSIST,
      ----------------------------FOURTH FALL PERSISTENCE-----------------------
      CASE
        WHEN FRTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END FRTH_FALL_PERSIST,
      ----------------------------FIFTH FALL PERSISTENCE------------------------
      CASE
        WHEN FFTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FFTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END FFTH_FALL_PERSIST,
      ----------------------------SIXTH FALL PERSISTENCE------------------------
      CASE
        WHEN SXTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SXTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END SXTH_FALL_PERSIST,
      ----------------------------SEVENTH FALL PERSISTENCE----------------------
      CASE
        WHEN SVNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SVNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END SVNTH_FALL_PERSIST,
      ----------------------------EIGTH FALL PERSISTENCE------------------------
      CASE
        WHEN EIGTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN EIGTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END EIGTH_FALL_PERSIST,
       ----------------------------NINTH FALL PERSISTENCE------------------------
      CASE
        WHEN NINTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN NINTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((NINTH_FALL_TERM_REG_IND IS NULL
        OR NINTH_FALL_TERM_REG_IND      = 'N')
        AND NINTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END NINTH_FALL_PERSIST,
      ----------------------------TENTH FALL PERSISTENCE------------------------
      CASE
        WHEN TENTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN TENTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((TENTH_FALL_TERM_REG_IND IS NULL
        OR TENTH_FALL_TERM_REG_IND      = 'N')
        AND TENTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END TENTH_FALL_PERSIST,
       ----------------------------ELEVENTH FALL PERSISTENCE---------------------
      CASE
        WHEN ELEVENTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN ELEVENTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((ELEVENTH_FALL_TERM_REG_IND IS NULL
        OR ELEVENTH_FALL_TERM_REG_IND      = 'N')
        AND ELEVENTH_FALL_GRAD_IND        IS NULL)
        THEN 'LOST'
      END ELEVENTH_FALL_PERSIST
      ---------------------------END SELECT---------------------------------------
    FROM IA_FY_COHORT_TEN_YR_TBL TY
    INNER JOIN IA_FY_COHORT_POP_UNDUP_TBL CO
    ON CO.CO_PIDM = TY.CO_PIDM
      --where CO.co_pidm = '72357'
  );
