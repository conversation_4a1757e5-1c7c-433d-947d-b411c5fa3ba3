CREATE OR REPLACE package zdlsales as 

  procedure p_trans_salesforce;












--  procedure p_trans_contacts;
--  procedure p_load_load_contacts;
--  procedure p_trans_sf_staff_history;
--  procedure p_trans_sf_demo;  
--

end zdlsales;
/


CREATE OR REPLACE package body zdlsales as

  procedure p_trans_salesforce as
  
  
  begin
    execute immediate('truncate table load_salesforce');
    
    insert into load_salesforce
    with fac_staff_all_sel as(
        select 
        sibinst.sibinst_pidm,
        sibinst.sibinst_fcst_code,
        sirdpcl.sirdpcl_term_code_eff,
        sirdpcl.sirdpcl_dept_code
        from aimsmgr.sibinst
        inner join aimsmgr.sirdpcl on sirdpcl.sirdpcl_pidm = sibinst.sibinst_pidm
                                   and sirdpcl.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
                                                                        from aimsmgr.sirdpcl s1
                                                                        where s1.sirdpcl_pidm = sirdpcl.sirdpcl_pidm)
        where sibinst.sibinst_term_code_eff = (select max(s1.sibinst_term_code_eff)
                                               from aimsmgr.sibinst s1
                                               where s1.sibinst_pidm = sibinst.sibinst_pidm)
        and not exists (select 'is a dup'
                        from aimsmgr.sprhold sprhold
                        where sprhold.sprhold_pidm = sibinst.sibinst_pidm
                        and sprhold.sprhold_hldd_code = 'DP')
      ),
      fac_staff_sel as(
        select
        fac_staff_all_sel.sibinst_pidm,
        fac_staff_all_sel.sirdpcl_dept_code,
        'Faculty/Staff' banner_rec_type,
        fac_staff_all_sel.sirdpcl_term_code_eff
        from fac_staff_all_sel
        where fac_staff_all_sel.sibinst_fcst_code = 'AC'
        and fac_staff_all_sel.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
                                                       from fac_staff_all_sel s1
                                                       where s1.sibinst_pidm = fac_staff_all_sel.sibinst_pidm)
      ),
      student_sel as(
        select 
        sgbstdn.sgbstdn_pidm,
        'Student' banner_rec_type,
        sgbstdn.sgbstdn_levl_code,
        sgbstdn.sgbstdn_styp_code,
        sgbstdn.sgbstdn_coll_code_1,
        sgbstdn.sgbstdn_program_1,
        sgbstdn.sgbstdn_majr_code_1,
        sgbstdn.sgbstdn_majr_code_conc_1,
        sgbstdn.sgbstdn_term_code_admit,
        sgbstdn.sgbstdn_stst_code
        from aimsmgr.sgbstdn
        where sgbstdn.sgbstdn_term_code_eff = (select max(s1.sgbstdn_term_code_eff)
                                               from aimsmgr.sgbstdn s1
                                               where s1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm)
        and sgbstdn.sgbstdn_stst_code = 'AS'
        and not exists (select 'is a dup'
                        from aimsmgr.sprhold sprhold
                        where sprhold.sprhold_pidm = sgbstdn.sgbstdn_pidm
                        and sprhold.sprhold_hldd_code = 'DP')
      ),
      app_desc_sel as(
        select
        sarappd.sarappd_pidm,
        sarappd.sarappd_appl_no,
        sarappd.sarappd_apdc_code,
        sarappd.sarappd_apdc_date,
        row_number() over(partition by sarappd.sarappd_pidm, sarappd.sarappd_appl_no order by sarappd.sarappd_seq_no desc) order_num
        from aimsmgr.sarappd
      ),
      applicant_sel as(
        select
        saradap.saradap_pidm,
        'Applicant' banner_rec_type,
        saradap.saradap_levl_code,
        saradap.saradap_styp_code,
        saradap.saradap_coll_code_1,
        saradap.saradap_program_1,
        saradap.saradap_majr_code_1,
        saradap.saradap_majr_code_conc_1,
        saradap.saradap_term_code_entry,
        saradap.saradap_appl_no,
        sarappd.sarappd_apdc_code,
        sarappd.sarappd_apdc_date
        from aimsmgr.saradap
        left outer join app_desc_sel sarappd on sarappd.sarappd_pidm = saradap.saradap_pidm
                                            and sarappd.sarappd_appl_no = saradap.saradap_appl_no
                                            and sarappd.order_num = 1 
        where saradap.saradap_term_code_entry >= (select min(stvterm.stvterm_code)
                                                  from aimsmgr.stvterm
                                                  where stvterm.stvterm_end_date >= trunc(sysdate))
        and not exists (select 'is a dup'
                        from aimsmgr.sprhold sprhold
                        where sprhold.sprhold_pidm = saradap.saradap_pidm
                        and sprhold.sprhold_hldd_code = 'DP')
      )
      ,
      pop_sel as(
        select
        fac_staff_sel.sibinst_pidm pop_sel_pidm,
        fac_staff_sel.banner_rec_type,
        fac_staff_sel.sirdpcl_dept_code dept_coll_code,
        cast(null as varchar2(6)) program_1,
        cast(null as varchar2(6)) majr_code_1,
        cast(null as varchar2(6)) majr_code_conc_1,
        cast(null as varchar2(2)) levl_code,
        cast(null as varchar2(1)) styp_code,
        cast(null as varchar2(2)) stst_code,
        fac_staff_sel.sirdpcl_term_code_eff term_code,
        cast(null as number) appl_no
        from fac_staff_sel
        union all
        select
        student_sel.sgbstdn_pidm,
        student_sel.banner_rec_type,
        student_sel.sgbstdn_coll_code_1,
        student_sel.sgbstdn_program_1,
        student_sel.sgbstdn_majr_code_1,
        student_sel.sgbstdn_majr_code_conc_1,
        student_sel.sgbstdn_levl_code,
        student_sel.sgbstdn_styp_code,
        student_sel.sgbstdn_stst_code,
        student_sel.sgbstdn_term_code_admit,
        cast(null as number) appl_no
        from student_sel
        union all
        select 
        applicant_sel.saradap_pidm,
        applicant_sel.banner_rec_type,
        applicant_sel.saradap_coll_code_1,
        applicant_sel.saradap_program_1,
        applicant_sel.saradap_majr_code_1,
        applicant_sel.saradap_majr_code_conc_1,
        applicant_sel.saradap_levl_code,
        applicant_sel.saradap_styp_code,
        cast(null as varchar2(3)) stst_code,
        applicant_sel.saradap_term_code_entry,
        applicant_sel.saradap_appl_no
        from applicant_sel
      )
      select
      pop_sel.banner_rec_type,
      pop_sel.dept_coll_code,
      case
        when pop_sel.banner_rec_type = 'Faculty/Staff' then (select 
                                                         stvdept.stvdept_desc || ' (Dept)'
                                                         from aimsmgr.stvdept where stvdept.stvdept_code = pop_sel.dept_coll_code)
        else (select
              stvcoll.stvcoll_desc || ' (College)' 
              from aimsmgr.stvcoll where stvcoll.stvcoll_code = pop_sel.dept_coll_code)
      end dept_coll_desc,
      pop_sel.program_1,
      (select smrprle.smrprle_program_desc from aimsmgr.smrprle where smrprle.smrprle_program = pop_sel.program_1) program_1_desc,
      pop_sel.majr_code_1,
      (select stvmajr.stvmajr_desc from aimsmgr.stvmajr where stvmajr.stvmajr_code = pop_sel.majr_code_1) majr_desc_1,
      pop_sel.majr_code_conc_1,
      (select stvmajr.stvmajr_desc from aimsmgr.stvmajr where stvmajr.stvmajr_code = pop_sel.majr_code_conc_1) majr_conc_desc_1,
      pop_sel.levl_code,
      (select stvlevl.stvlevl_desc from aimsmgr.stvlevl where stvlevl.stvlevl_code = pop_sel.levl_code) levl_desc,
      pop_sel.styp_code,
      (select stvstyp.stvstyp_desc from aimsmgr.stvstyp where stvstyp.stvstyp_code = pop_sel.styp_code) styp_desc,
      pop_sel.stst_code,
      (select stvstst.stvstst_desc from aimsmgr.stvstst where stvstst.stvstst_code = pop_sel.stst_code) stst_desc,
      pop_sel.term_code,
      (select stvterm.stvterm_desc from aimsmgr.stvterm where stvterm.stvterm_code = pop_sel.term_code) term_desc,
      pop_sel.pop_sel_pidm,
      um_demographic.umid,
      um_demographic.legal_first_name,
      um_demographic.pref_first_name,
      um_demographic.middle_initial,
      um_demographic.last_name,
      pop_sel.term_code academic_year,
      um_demographic.a1_street_line1,
      um_demographic.a1_street_line2,
      um_demographic.a1_city,
      um_demographic.a1_state_code,
      um_demographic.a1_zip,
      um_demographic.birthdate birth_date,
      um_demographic.ca_email email_camp,
      
      um_demographic.gender,
      um_demographic.gender_desc,
      
      um_demographic.ca_email um_uniqname,  
  
      um_demographic.a1_area_code,
      um_demographic.a1_phone_number,
      
      um_demographic.mo_area_code,
      um_demographic.mo_phone_number,
  
      um_demographic.nation_birth_code,
      um_demographic.nation_birth_desc,
  
      case
        when pop_sel.banner_rec_type = 'Student' then um_student_data.class_code
        else null
      end class_code,
          
      case
        when pop_sel.banner_rec_type = 'Student' then um_student_data.class_desc
        else null
      end class_desc,
      
      case
        when pop_sel.banner_rec_type = 'Student' then um_student_data.reg_ind_with_nonacademic
        else null
      end reg_ind,
  
      um_demographic.report_ethnicity,
      um_demographic.intl_ind intl_ind,
      um_demographic.veteran_ind,    
      um_demographic.uic_id,
      um_demographic.deceased_ind,
      um_student_data.housing_ind
  
      from pop_sel
      inner join um_demographic on um_demographic.dm_pidm = pop_sel.pop_sel_pidm
                                and um_demographic.first_name != 'Advisor'
                                and um_demographic.ca_email is not null
      left outer join um_student_data on um_student_data.sd_pidm = pop_sel.pop_sel_pidm
                                      and um_student_data.sd_term_code = pop_sel.term_code
                                      
--      where pop_sel.pop_sel_pidm = 135054 --10475 -- 66686 -- 132931 --55342
      where banner_rec_type != 'Applicant'      
      order by pop_sel.pop_sel_pidm;

      commit;
  
  end p_trans_salesforce;



end zdlsales;
/
