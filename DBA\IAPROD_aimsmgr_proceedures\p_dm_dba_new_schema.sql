CREATE OR <PERSON><PERSON>LACE PROCEDURE p_dm_dba_new_schema(
	pi_username IN NVARCHAR2,
	pi_password IN NVARCHAR2) IS
	
	user_name NVARCHAR2(20)  	:= pi_username;
	pwd NVARCHAR2(20) 		      := pi_password;

BEGIN

 dbms_output.enable(null);
         -- ****** Object: Create User Account ******
EXECUTE IMMEDIATE 'CREATE USER ' || user_name || ' IDENTIFIED BY ' || pwd ;

        -- ****** Object: Roles for user ******
EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT CREATE SYNONYM TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT CREATE JOB TO ' || user_name;
EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO ' || user_name;
EXECUTE IMMEDIATE 'ALTER USER ' || user_name || ' DEFAULT ROLE ALL';
EXECUTE IMMEDIATE 'ALTER USER ' || user_name || ' QUOTA 100M ON DEVELOPMENT';
EXECUTE IMMEDIATE 'ALTER USER ' || user_name || ' PROFILE TOPGUN';

COMMIT;
END p_dm_dba_new_schema;


--begin
--	p_dm_dba_new_schema ('AATEST','TESTPASS');
--end;
--
--CREATE USER foo IDENTIFIED BY bar DEFAULT TABLESPACE DEVELOPMENT;