CREATE OR REPLACE VIEW ia_um_test_score AS
  (SELECT UM_TEST_SCORE.*,
    CASE
      WHEN UM_TEST_SCORE.TOEFL_IBT_TOTAL IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL    IS NULL
      THEN NULL
      WHEN UM_TEST_SCORE.TOEFL_IBT_TOTAL <=31
      AND UM_TEST_SCORE.IELTS_OVERALL    IS NULL
      THEN '4.0'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 32
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 34
      THEN '4.5'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 35
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 45
      THEN '5.5'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 46
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 59
      THEN '5.5'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 60
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 78
      THEN '6.0'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 79
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 93
      THEN '6.5'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 94
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 101
      THEN '7.0'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 102
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 109
      THEN '7.5'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 110
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 114
      THEN '8.0'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 115
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 117
      THEN '8.5'
      WHEN UM_TEST_SCORE.IELTS_OVERALL  IS NULL
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL >= 118
      AND UM_TEST_SCORE.TOEFL_IBT_TOTAL <= 120
      THEN '9.0'
      ELSE UM_TEST_SCORE.IELTS_OVERALL 
    END IELETS_OVERALL_TOEFL_CONV,
    CASE
      WHEN UM_TEST_SCORE.TOEFL_IBT_TOTAL IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL    IS NULL
      THEN NULL
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 32
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=34
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '4.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 32
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=34
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '4.5')
      THEN 'I1'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 35
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=45
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 35
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 45
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      THEN 'I2'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 46
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=59
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 46
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=59
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      THEN 'I3'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 60
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=78
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 60
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=78
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      THEN 'I4'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 79
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=93
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 79
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <=93
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.5')
      THEN 'A1'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 94
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 101
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '7.0')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 94
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 101
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '7.0')
      THEN 'A2'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 102
      AND UM_TEST_SCORE.IELTS_OVERALL                 > '7.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 102
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '7.5')
      THEN 'A3'
      ELSE 'Outlier'
    END ILSC_LEVEL,
    CASE
      WHEN UM_TEST_SCORE.TOEFL_IBT_TOTAL IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL    IS NULL
      THEN NULL
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) < 60
      AND UM_TEST_SCORE.IELTS_OVERALL                = '4.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   < 60
      AND UM_TEST_SCORE.IELTS_OVERALL               IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)  IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                = '4.5')
      THEN 'Pre-Intermediate'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 60
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 69
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 60
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 69
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      THEN 'Intermediate'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 70
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 79
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 70
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 79
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      THEN 'Upper Intermediate'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 80
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 89
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 80
      AND to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 89
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      THEN 'Lower Advanced'
      WHEN (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL) >= 90
      AND UM_TEST_SCORE.IELTS_OVERALL                 = '6.5')
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 90
      AND UM_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(UM_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND UM_TEST_SCORE.IELTS_OVERALL                IN ('6.5','7.0','7.5'))
      THEN 'Upper Advanced'
      ELSE 'Outlier'
    END VGC_LEVEL
  FROM UM_TEST_SCORE
  );