--------------------------------------------------------
--  DDL for View LOAD_COMMENCEMENT
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "BO_FLINT_STUDENT"."LOAD_COMMENCEMENT" ("COM_SELECT", "DEG_COUNT", "SEASON_CODE", "PIDM", "UMID", "GRAD_TERM_CODE", "GRAD_TERM_DESC", "SUM_TERM_JOIN", "FIRST_NAME", "LAST_NAME", "FULL_NAME", "HONORS_PROG_IND", "GRAD_DATE", "PRIMARY_COLLEGE_CODE", "PRIMARY_COLLEGE_DESC", "PRIMARY_LEVEL_CODE", "PRIMARY_LEVEL_DESC", "REPORT_LEVEL_CODE", "REPORT_LEVEL_DESC", "DEGREE_CODE", "DEGREE_DESC", "PRIMARY_MAJOR_1", "PRIMARY_MAJOR_1_DESC", "HONOR_ROLE", "HONOR_ROLE_DESC", "GRAD_STATUS", "MULT_DEG_IND", "CLIST_NAME", "AA_CLIST_NAME") AS 
  WITH dp1 AS (
  SELECT
    ROW_NUMBER()
    OVER(PARTITION BY dg.pidm, dg.grad_term_code
         ORDER BY
           dg.pidm DESC
    )                               deg_count,
    substr(dg.grad_term_code, 5, 6) season_code,
    dg.pidm,
    dg.umid,
    dg.grad_term_code,
    dg.grad_term_desc,
    CASE
      WHEN substr(dg.grad_term_code, 5, 6) = 20 THEN
        to_number(dg.grad_term_code) + 20
      WHEN substr(dg.grad_term_code, 5, 6) = 40 THEN
        to_number(dg.grad_term_code)
      WHEN substr(dg.grad_term_code, 5, 6) = 10 THEN
        to_number(dg.grad_term_code) - 70
    END                             sum_term_join,
    dm.first_name,
    dm.last_name,
    CASE
      WHEN dm.middle_initial IS NULL
           AND dm.name_suffix IS NULL THEN
        dm.first_name
        || '  '
        || dm.last_name
      WHEN dm.middle_initial IS NULL
           AND dm.name_suffix IS NOT NULL THEN
        dm.first_name
        || '  '
        || dm.last_name
        || ' '
        || dm.name_suffix
      WHEN dm.middle_initial IS NOT NULL
           AND dm.name_suffix IS NULL THEN
        dm.first_name
        || ' '
        || dm.middle_initial
        || '. '
        || dm.last_name
      ELSE
        dm.first_name
        || ' '
        || dm.middle_initial
        || '. '
        || dm.last_name
        || ' '
        || dm.name_suffix
    END                             full_name,
    decode(sd.honors_program, 'HO', 'Y', 'JS', 'Y',
           'N')                     honors_prog_ind,
    dg.grad_date,
    dg.college_code                 primary_college_code,
    dg.college_desc                 primary_college_desc,
    dg.degree_code,
    dg.degree_desc,
    dg.primary_major_1,
    dg.primary_major_1_desc,
    dg.level_code primary_level_code,
    dg.LEVEL_DESC primary_level_desc,
    sd.report_level_code,
    SD.REPORT_LEVEL_DESC,

    CASE
      WHEN sd.inst_gpa >= 3.75 
        THEN '**'
      WHEN sd.inst_gpa >= 3.50
        THEN '*'
      ELSE ''
    END                             honor_role,

    CASE
      WHEN sd.inst_gpa >= 3.75 
        THEN 'High Honors'
      WHEN sd.inst_gpa >= 3.50
        THEN 'Honors'
      ELSE ''
    END                             honor_role_desc,

    dg.grad_status
  FROM
    um_degree       dg
    LEFT JOIN um_student_data sd ON dg.pidm = sd.sd_pidm
                                    AND dg.grad_term_code = sd.sd_term_code
    INNER JOIN um_demographic  dm ON dg.pidm = dm.dm_pidm
  WHERE 
--dg.grad_term_code = (select current_term from um_current_term)
      dg.grad_term_code >= (
        SELECT
          last_2_term
        FROM
          um_current_term
      )
    AND dg.grad_term_code <= (
      SELECT
        next_4_term
      FROM
        um_current_term
    )
    AND dg.grad_status IN ( 'GR', 'PG' )
    AND dg.level_code in ('UG','U2','U3')
    AND degree_code not like 'CER%'
), dp2 AS (
  SELECT
    '+' mult_deg_ind,
    dp1.pidm,
    dp1.grad_term_code
  FROM
    dp1
  WHERE
    deg_count > 1
), dp3 AS (
  SELECT
    dp1.*,
    dp2.mult_deg_ind
  FROM
    dp1
    LEFT JOIN dp2 ON dp1.pidm = dp2.pidm
                     AND dp1.grad_term_code = dp2.grad_term_code
), dp4 AS (
  SELECT
    dp3.*,
    dp3.full_name
    || dp3.honor_role
    || mult_deg_ind               clist_name,
    dp3.full_name || mult_deg_ind aa_clist_name
  FROM
    dp3
), w1 AS (
  SELECT DISTINCT
    dp4.grad_term_desc || ' Commencement' com_select,
    dp4.*
  FROM
    dp4
  WHERE
    season_code = '20'
), w2 AS (
  SELECT DISTINCT
    w1.grad_term_desc || ' Commencement' com_select,
    dp4.*
  FROM
         dp4
    INNER JOIN w1 ON dp4.grad_term_code = w1.sum_term_join
  WHERE
    dp4.grad_status = 'PG'
), s1 AS (
  SELECT DISTINCT
    dp4.grad_term_desc || ' Commencement' com_select,
    dp4.*
  FROM
    dp4
  WHERE
    season_code = '40'
), f1 AS (
  SELECT DISTINCT
    grad_term_desc || ' Commencement' com_select,
    dp4.*
  FROM
    dp4
  WHERE
    season_code = '10'
), f2 AS (
  SELECT DISTINCT
    f1.grad_term_desc || ' Commencement' com_select,
    dp4.*
  FROM
         dp4
    INNER JOIN f1 ON dp4.grad_term_code = f1.sum_term_join
  WHERE
    dp4.grad_status = 'GR'
), com1 AS (
  SELECT
    *
  FROM
    w1
  UNION ALL
  SELECT
    *
  FROM
    w2
  UNION ALL
  SELECT
    *
  FROM
    s1
  UNION ALL
  SELECT
    *
  FROM
    f1
  UNION ALL
  SELECT
    *
  FROM
    f2
)
SELECT DISTINCT
  com1."COM_SELECT",
  com1."DEG_COUNT",
  com1."SEASON_CODE",
  com1."PIDM",
  com1."UMID",
  com1."GRAD_TERM_CODE",
  com1."GRAD_TERM_DESC",
  com1."SUM_TERM_JOIN",
  com1."FIRST_NAME",
  com1."LAST_NAME",
  com1."FULL_NAME",
  com1."HONORS_PROG_IND",
  com1."GRAD_DATE",
  com1."PRIMARY_COLLEGE_CODE",
  com1."PRIMARY_COLLEGE_DESC",
  com1."PRIMARY_LEVEL_CODE",
  com1."PRIMARY_LEVEL_DESC",
  com1."REPORT_LEVEL_CODE",
  com1."REPORT_LEVEL_DESC",
  com1."DEGREE_CODE",
  com1."DEGREE_DESC",
  com1."PRIMARY_MAJOR_1",
  com1."PRIMARY_MAJOR_1_DESC",
  com1."HONOR_ROLE",
  com1."HONOR_ROLE_DESC",
  com1."GRAD_STATUS",
  com1."MULT_DEG_IND",
  com1."CLIST_NAME",
  com1."AA_CLIST_NAME"
FROM
  com1
WHERE honor_role is not null
;
