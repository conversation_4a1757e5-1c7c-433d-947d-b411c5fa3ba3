TRUNCATE TABLE IA_AR_AWARD_DET_TERM_EXT_BAC;

INSERT INTO IA_AR_AWARD_DET_TERM_EXT_BAC
SELECT * FROM IA_AR_AWARD_DETAIL_BY_TERM_EXT;
COMMIT;

SELECT COUNT (*) FROM IA_AR_AWARD_DET_TERM_EXT_BAC;
SELECT COUNT (*) FROM IA_AR_AWARD_DETAIL_BY_TERM_EXT;


INSERT INTO IA_AR_AWARD_DETAIL_BY_TERM_EXT
  (SELECT pidm_key,
    term_code_key,
    TERM_DESC,
    FUND_CODE_KEY,
    FB.FA_FUND_SOURCE,
    FB.FA_FUND_TYPE_CODE,
    AWARD_TERM_OFFER_AMOUNT,
    APPLICATION_DECISION_CODE1,
    STUDENT_TOTAL_CREDIT_HOURS,
    STUDENT_TYPE_CODE,
    REGISTERED_IND,
    FM_TFC,
    FM_GROSS_NEED
  FROM aimsmgr.AR_AWARD_DETAIL_BY_TERM DP1
    LEFT JOIN IA_FA_FUND_BASE FB
    ON FB.FA_PIDM       = DP1.PIDM_KEY
    AND FB.FA_TERM_CODE = DP1.TERM_CODE_KEY
    AND FB.FA_FUND_CODE = DP1.FUND_CODE_KEY
  WHERE TERM_CODE_KEY in (select current_term from um_current_term)
--  AND TERM_CODE_KEY LIKE '%10'
  AND APPLICATION_DECISION_CODE1 IN ('AD','A2')--Admitted, 2nd degree
  AND STUDENT_TYPE_CODE          IN ('F')-- FTIAC
  ) ;
commit;

--Varify records inserted
with dp1 as (
SELECT * FROM IA_AR_AWARD_DETAIL_BY_TERM_EXT
MINUS
SELECT * FROM IA_AR_AWARD_DET_TERM_EXT_BAC
)
select count (*) record_count from dp1
;
