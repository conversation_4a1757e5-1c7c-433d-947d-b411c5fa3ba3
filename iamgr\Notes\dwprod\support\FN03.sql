SELECT
  M_GLDW1.JRNL_LN.JOUR<PERSON>L_ID,
  M_GLDW1.JRNL_LN.JOURNAL_DATE,
  M_GLDW1.JRNL_LN.JOURNAL_LINE,
  M_GLDW1.JRNL_LN.ACCOUNT,
  M_GLDW1.JRNL_LN.FUND_CODE,
  M_GLDW1.JRNL_LN.DEPTID,
  M_GLDW1.JRNL_LN.PROGRAM_CODE,
  M_GLDW1.JRNL_LN.CLASS,
  M_GLDW1.JRNL_LN.PROJECT_GRANT,
  M_GLDW1.JRNL_LN.JRNL_LN_REF,
  M_GLDW1.JRNL_LN.DOLLAR_AMOUNT,
  M_GLDW1.JRNL_LN.JOURNAL_LINE_DESCR,
  M_GLDW1.JRNL_LN.STATISTIC_AMOUNT,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_FLG,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_BY,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_DT,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_NOTE,
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_DT,
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_BY,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_CD,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_DESCR,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_CD,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_DESCR,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_CD,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_DESCR,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_ASSGN_UNQNM,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_ID,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_DATE,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_LINE,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_CD,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_DES,
  JRNL_LN_CNVCHARTFD.SHORTCODE,
  JRNL_LN_CNVCHARTFD.SHORTCODE_DESCR50,
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS,
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS_DESCRSHORT,
  JRNL_LN_CNVCHARTFD.FUND_CODE,
  JRNL_LN_CNVCHARTFD.DEPTID,
  JRNL_LN_CNVCHARTFD.PROGRAM_CODE,
  JRNL_LN_CNVCHARTFD.CLASS,
  JRNL_LN_CNVCHARTFD.PROJECT_GRANT,
  JRNL_LN_CNVCHARTFD.CLASS2,
  JRNL_LN_CNVCHARTFD.ACCOUNT,
  M_GLDW1.JRNL_HEADER.JOURNAL_ID,
  M_GLDW1.JRNL_HEADER.JOURNAL_DATE,
  CONCAT(rpad(( M_GLDW1.JRNL_HEADER.JOURNAL_ID ),11,'-'),to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'mm/dd/yyyy')),
  to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'DAY'),
  M_GLDW1.JRNL_HEADER.FISCAL_YEAR,
  M_GLDW1.JRNL_HEADER.ACCOUNTING_PERIOD,
  M_GLDW1.JRNL_HEADER.REVERSAL_CD,
  M_GLDW1.JRNL_HEADER.REVERSAL_DATE,
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE,
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE_DESCR,
  M_GLDW1.JRNL_HEADER.TRANS_REF_NUM,
  M_GLDW1.JRNL_HEADER.JOURNAL_DESCR254,
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_EMPLID,
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_NAME,
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_PHONE,
  M_GLDW1.JRNL_HEADER.JOURNAL_ENTERED_OPRID,
  M_GLDW1.JRNL_HEADER.JOURNAL_SYSTEM_SOURCE,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_EMPLID,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_NAME,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PHONE,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PRMRY_APPT_DEPT_CD,
  M_GLDW1.CAL_DETP_TBL.FISCAL_YEAR,
  M_GLDW1.CAL_DETP_TBL.ACCOUNTING_PERIOD,
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_CODE,
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_DESCR,
  M_GLDW1.CAL_DETP_TBL.CAL_YEAR,
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_BEGIN_DT,
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_END_DT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFFDT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFF_STATUS,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_TYPE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCRLONG,
  JRNL_LN_CURR_FUND_VW.FUND_CODE,
  JRNL_LN_CURR_FUND_VW.FUND_EFFDT,
  JRNL_LN_CURR_FUND_VW.FUND_EFF_STATUS,
  JRNL_LN_CURR_FUND_VW.FUND_DESCR,
  JRNL_LN_CURR_FUND_VW.FUND_GRP,
  JRNL_LN_CURR_FUND_VW.FUND_GRP_DESCR,
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP,
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPTID,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFFDT,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFF_STATUS,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.EMPLID,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR,
  M_GLDW1.PERSONAL_DATA.EMPLID,
  M_GLDW1.PERSONAL_DATA.PERSON_NAME,
  M_GLDW1.PERSONAL_DATA.PERSON_UNIQNAME,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS1,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS2,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS3,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS4,
  M_GLDW1.PERSONAL_DATA.PERSON_ZIP,
  M_GLDW1.PERSONAL_DATA.PERSON_COUNTRY,
  M_GLDW1.PERSONAL_DATA.PERSON_PHONE,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFFDT,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFF_STATUS,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_DESCR,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFFDT,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFF_STATUS,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_DESCR,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFFDT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFF_STATUS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TITLE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TYPE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.MINORITY_SUB,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRANT_STATUS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_START_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_END_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_FUND_CODE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_DEPTID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_START_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_END_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_CD,
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DESCR100,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_AWARD,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DOC_NBR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PAYMENT_TYPE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.LOC_FUNDED,
  M_GLDW1.CURR_PROJECT_GRANT_VW.CFDA_NBR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_NBR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PARENT_PROJ_GRANT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_BASIS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_RATE_PCT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.CLASS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROGRAM_CODE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BILLED_BY,
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR_DESCR100,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE_DESCR100,
  M_GLDW1.CURR_PROJECT_GRANT_VW.BUDG_MUST_EQUAL_REV,
  M_GLDW1.CURR_PROJECT_GRANT_VW.REPORTING_BUDGET_AMT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.M_FED_AWARD_ID_NBR,
  PROJ_GRANT_CURR_FUND_VW.FUND_CODE,
  PROJ_GRANT_CURR_FUND_VW.FUND_EFFDT,
  PROJ_GRANT_CURR_FUND_VW.FUND_EFF_STATUS,
  PROJ_GRANT_CURR_FUND_VW.FUND_DESCR,
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP,
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP_DESCR,
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP,
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPTID,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFFDT,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFF_STATUS,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.EMPLID,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR,
  SUM ( M_GLDW1.JRNL_LN.DOLLAR_AMOUNT),
  SUM (M_GLDW1.JRNL_LN.STATISTIC_AMOUNT)
FROM
  M_GLDW1.JRNL_LN,
  M_GLDW1.JRNL_LN_RECON_EXCPTN,
  M_GLDW1.CNVCHARTFD  JRNL_LN_CNVCHARTFD,
  M_GLDW1.JRNL_HEADER,
  M_GLDW1.CAL_DETP_TBL,
  M_GLDW1.CURR_ACCOUNT_VW,
  M_GLDW1.CURR_FUND_VW  JRNL_LN_CURR_FUND_VW,
  M_GLDW1.CURR_FN_DEPT_VW  JRNL_LN_CURR_FN_DEPT_VW,
  M_GLDW1.PERSONAL_DATA,
  M_GLDW1.CURR_PROGRAM_VW,
  M_GLDW1.CURR_CLASS_CF_VW,
  M_GLDW1.CURR_PROJECT_GRANT_VW,
  M_GLDW1.CURR_FUND_VW  PROJ_GRANT_CURR_FUND_VW,
  M_GLDW1.CURR_FN_DEPT_VW  PROJ_GRANT_CURR_FN_DEPT_VW
WHERE
  ( M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_FUND_CODE=PROJ_GRANT_CURR_FUND_VW.FUND_CODE  )
  AND  ( M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_DEPTID=PROJ_GRANT_CURR_FN_DEPT_VW.DEPTID  )
  AND  ( M_GLDW1.CAL_DETP_TBL.FISCAL_YEAR=M_GLDW1.JRNL_HEADER.FISCAL_YEAR AND M_GLDW1.CAL_DETP_TBL.ACCOUNTING_PERIOD = M_GLDW1.JRNL_HEADER.ACCOUNTING_PERIOD  )
  AND  ( M_GLDW1.JRNL_HEADER.JOURNAL_ID=M_GLDW1.JRNL_LN.JOURNAL_ID AND M_GLDW1.JRNL_HEADER.JOURNAL_DATE = M_GLDW1.JRNL_LN.JOURNAL_DATE  )
  AND  ( M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT=M_GLDW1.JRNL_LN.ACCOUNT  )
  AND  ( M_GLDW1.JRNL_LN.PROGRAM_CODE=M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE  )
  AND  ( M_GLDW1.JRNL_LN.PROJECT_GRANT=M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT  )
  AND  ( JRNL_LN_CNVCHARTFD.PROJECT_GRANT(+)=M_GLDW1.JRNL_LN.PROJECT_GRANT  )
  AND  ( M_GLDW1.JRNL_LN.CLASS=M_GLDW1.CURR_CLASS_CF_VW.CLASS  )
  AND  ( JRNL_LN_CURR_FUND_VW.FUND_CODE=M_GLDW1.JRNL_LN.FUND_CODE  )
  AND  ( M_GLDW1.JRNL_LN.DEPTID=JRNL_LN_CURR_FN_DEPT_VW.DEPTID  )
  AND  ( JRNL_LN_CURR_FN_DEPT_VW.EMPLID=M_GLDW1.PERSONAL_DATA.EMPLID(+)  )
  AND  ( JRNL_LN_CNVCHARTFD.CLASS(+)=M_GLDW1.JRNL_LN.CLASS  )
  AND  ( JRNL_LN_CNVCHARTFD.DEPTID(+)=M_GLDW1.JRNL_LN.DEPTID  )
  AND  ( JRNL_LN_CNVCHARTFD.FUND_CODE(+)=M_GLDW1.JRNL_LN.FUND_CODE  )
  AND  ( JRNL_LN_CNVCHARTFD.PROGRAM_CODE(+)=M_GLDW1.JRNL_LN.PROGRAM_CODE  )
  AND  ( M_GLDW1.JRNL_LN.JOURNAL_ID=M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_ID(+)  )
  AND  ( M_GLDW1.JRNL_LN.JOURNAL_DATE=M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_DATE(+)  )
  AND  ( M_GLDW1.JRNL_LN.JOURNAL_LINE=M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_LINE(+)  )
GROUP BY
  M_GLDW1.JRNL_LN.JOURNAL_ID, 
  M_GLDW1.JRNL_LN.JOURNAL_DATE, 
  M_GLDW1.JRNL_LN.JOURNAL_LINE, 
  M_GLDW1.JRNL_LN.ACCOUNT, 
  M_GLDW1.JRNL_LN.FUND_CODE, 
  M_GLDW1.JRNL_LN.DEPTID, 
  M_GLDW1.JRNL_LN.PROGRAM_CODE, 
  M_GLDW1.JRNL_LN.CLASS, 
  M_GLDW1.JRNL_LN.PROJECT_GRANT, 
  M_GLDW1.JRNL_LN.JRNL_LN_REF, 
  M_GLDW1.JRNL_LN.DOLLAR_AMOUNT, 
  M_GLDW1.JRNL_LN.JOURNAL_LINE_DESCR, 
  M_GLDW1.JRNL_LN.STATISTIC_AMOUNT, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_FLG, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_BY, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_DT, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_NOTE, 
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_DT, 
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_BY, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_CD, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_DESCR, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_CD, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_DESCR, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_CD, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_DESCR, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_ASSGN_UNQNM, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_ID, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_DATE, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_LINE, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_CD, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_DES, 
  JRNL_LN_CNVCHARTFD.SHORTCODE, 
  JRNL_LN_CNVCHARTFD.SHORTCODE_DESCR50, 
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS, 
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS_DESCRSHORT, 
  JRNL_LN_CNVCHARTFD.FUND_CODE, 
  JRNL_LN_CNVCHARTFD.DEPTID, 
  JRNL_LN_CNVCHARTFD.PROGRAM_CODE, 
  JRNL_LN_CNVCHARTFD.CLASS, 
  JRNL_LN_CNVCHARTFD.PROJECT_GRANT, 
  JRNL_LN_CNVCHARTFD.CLASS2, 
  JRNL_LN_CNVCHARTFD.ACCOUNT, 
  M_GLDW1.JRNL_HEADER.JOURNAL_ID, 
  M_GLDW1.JRNL_HEADER.JOURNAL_DATE, 
  CONCAT(rpad(( M_GLDW1.JRNL_HEADER.JOURNAL_ID ),11,'-'),to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'mm/dd/yyyy')), 
  to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'DAY'), 
  M_GLDW1.JRNL_HEADER.FISCAL_YEAR, 
  M_GLDW1.JRNL_HEADER.ACCOUNTING_PERIOD, 
  M_GLDW1.JRNL_HEADER.REVERSAL_CD, 
  M_GLDW1.JRNL_HEADER.REVERSAL_DATE, 
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE, 
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE_DESCR, 
  M_GLDW1.JRNL_HEADER.TRANS_REF_NUM, 
  M_GLDW1.JRNL_HEADER.JOURNAL_DESCR254, 
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_EMPLID, 
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_NAME, 
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_PHONE, 
  M_GLDW1.JRNL_HEADER.JOURNAL_ENTERED_OPRID, 
  M_GLDW1.JRNL_HEADER.JOURNAL_SYSTEM_SOURCE, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_EMPLID, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_NAME, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PHONE, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PRMRY_APPT_DEPT_CD, 
  M_GLDW1.CAL_DETP_TBL.FISCAL_YEAR, 
  M_GLDW1.CAL_DETP_TBL.ACCOUNTING_PERIOD, 
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_CODE, 
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_DESCR, 
  M_GLDW1.CAL_DETP_TBL.CAL_YEAR, 
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_BEGIN_DT, 
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_END_DT, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFFDT, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFF_STATUS, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_TYPE, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCRLONG, 
  JRNL_LN_CURR_FUND_VW.FUND_CODE, 
  JRNL_LN_CURR_FUND_VW.FUND_EFFDT, 
  JRNL_LN_CURR_FUND_VW.FUND_EFF_STATUS, 
  JRNL_LN_CURR_FUND_VW.FUND_DESCR, 
  JRNL_LN_CURR_FUND_VW.FUND_GRP, 
  JRNL_LN_CURR_FUND_VW.FUND_GRP_DESCR, 
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP, 
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPTID, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFFDT, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFF_STATUS, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.EMPLID, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR, 
  M_GLDW1.PERSONAL_DATA.EMPLID, 
  M_GLDW1.PERSONAL_DATA.PERSON_NAME, 
  M_GLDW1.PERSONAL_DATA.PERSON_UNIQNAME, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS1, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS2, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS3, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS4, 
  M_GLDW1.PERSONAL_DATA.PERSON_ZIP, 
  M_GLDW1.PERSONAL_DATA.PERSON_COUNTRY, 
  M_GLDW1.PERSONAL_DATA.PERSON_PHONE, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFFDT, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFF_STATUS, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_DESCR, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFFDT, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFF_STATUS, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_DESCR, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFFDT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFF_STATUS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TITLE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TYPE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.MINORITY_SUB, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRANT_STATUS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_START_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_END_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_FUND_CODE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_DEPTID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_START_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_END_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_CD, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DESCR100, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_AWARD, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DOC_NBR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PAYMENT_TYPE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.LOC_FUNDED, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.CFDA_NBR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_NBR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PARENT_PROJ_GRANT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_BASIS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_RATE_PCT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.CLASS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROGRAM_CODE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BILLED_BY, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR_DESCR100, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE_DESCR100, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.BUDG_MUST_EQUAL_REV, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.REPORTING_BUDGET_AMT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.M_FED_AWARD_ID_NBR, 
  PROJ_GRANT_CURR_FUND_VW.FUND_CODE, 
  PROJ_GRANT_CURR_FUND_VW.FUND_EFFDT, 
  PROJ_GRANT_CURR_FUND_VW.FUND_EFF_STATUS, 
  PROJ_GRANT_CURR_FUND_VW.FUND_DESCR, 
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP, 
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP_DESCR, 
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP, 
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPTID, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFFDT, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFF_STATUS, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.EMPLID, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR


SELECT
  M_GLDW1.JRNL_LN.JOURNAL_ID,
  M_GLDW1.JRNL_LN.JOURNAL_DATE,
  M_GLDW1.JRNL_LN.JOURNAL_LINE,
  M_GLDW1.JRNL_LN.ACCOUNT,
  M_GLDW1.JRNL_LN.FUND_CODE,
  M_GLDW1.JRNL_LN.DEPTID,
  M_GLDW1.JRNL_LN.PROGRAM_CODE,
  M_GLDW1.JRNL_LN.CLASS,
  M_GLDW1.JRNL_LN.PROJECT_GRANT,
  M_GLDW1.JRNL_LN.JRNL_LN_REF,
  M_GLDW1.JRNL_LN.DOLLAR_AMOUNT,
  M_GLDW1.JRNL_LN.JOURNAL_LINE_DESCR,
  M_GLDW1.JRNL_LN.STATISTIC_AMOUNT,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_FLG,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_BY,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_DT,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_NOTE,
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_DT,
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_BY,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_CD,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_DESCR,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_CD,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_DESCR,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_CD,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_DESCR,
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_ASSGN_UNQNM,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_ID,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_DATE,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_LINE,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_CD,
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_DES,
  JRNL_LN_CNVCHARTFD.SHORTCODE,
  JRNL_LN_CNVCHARTFD.SHORTCODE_DESCR50,
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS,
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS_DESCRSHORT,
  JRNL_LN_CNVCHARTFD.FUND_CODE,
  JRNL_LN_CNVCHARTFD.DEPTID,
  JRNL_LN_CNVCHARTFD.PROGRAM_CODE,
  JRNL_LN_CNVCHARTFD.CLASS,
  JRNL_LN_CNVCHARTFD.PROJECT_GRANT,
  JRNL_LN_CNVCHARTFD.CLASS2,
  JRNL_LN_CNVCHARTFD.ACCOUNT,
  M_GLDW1.JRNL_HEADER.JOURNAL_ID,
  M_GLDW1.JRNL_HEADER.JOURNAL_DATE,
  CONCAT(rpad(( M_GLDW1.JRNL_HEADER.JOURNAL_ID ),11,'-'),to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'mm/dd/yyyy')),
  to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'DAY'),
  M_GLDW1.JRNL_HEADER.FISCAL_YEAR,
  M_GLDW1.JRNL_HEADER.ACCOUNTING_PERIOD,
  M_GLDW1.JRNL_HEADER.REVERSAL_CD,
  M_GLDW1.JRNL_HEADER.REVERSAL_DATE,
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE,
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE_DESCR,
  M_GLDW1.JRNL_HEADER.TRANS_REF_NUM,
  M_GLDW1.JRNL_HEADER.JOURNAL_DESCR254,
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_EMPLID,
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_NAME,
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_PHONE,
  M_GLDW1.JRNL_HEADER.JOURNAL_ENTERED_OPRID,
  M_GLDW1.JRNL_HEADER.JOURNAL_SYSTEM_SOURCE,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_EMPLID,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_NAME,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PHONE,
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PRMRY_APPT_DEPT_CD,
  M_GLDW1.CAL_DETP_TBL.FISCAL_YEAR,
  M_GLDW1.CAL_DETP_TBL.ACCOUNTING_PERIOD,
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_CODE,
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_DESCR,
  M_GLDW1.CAL_DETP_TBL.CAL_YEAR,
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_BEGIN_DT,
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_END_DT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFFDT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFF_STATUS,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_TYPE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCRLONG,
  JRNL_LN_CURR_FUND_VW.FUND_CODE,
  JRNL_LN_CURR_FUND_VW.FUND_EFFDT,
  JRNL_LN_CURR_FUND_VW.FUND_EFF_STATUS,
  JRNL_LN_CURR_FUND_VW.FUND_DESCR,
  JRNL_LN_CURR_FUND_VW.FUND_GRP,
  JRNL_LN_CURR_FUND_VW.FUND_GRP_DESCR,
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP,
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPTID,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFFDT,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFF_STATUS,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.EMPLID,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ,
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR,
  M_GLDW1.PERSONAL_DATA.EMPLID,
  M_GLDW1.PERSONAL_DATA.PERSON_NAME,
  M_GLDW1.PERSONAL_DATA.PERSON_UNIQNAME,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS1,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS2,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS3,
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS4,
  M_GLDW1.PERSONAL_DATA.PERSON_ZIP,
  M_GLDW1.PERSONAL_DATA.PERSON_COUNTRY,
  M_GLDW1.PERSONAL_DATA.PERSON_PHONE,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFFDT,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFF_STATUS,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_DESCR,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFFDT,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFF_STATUS,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_DESCR,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP,
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFFDT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFF_STATUS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TITLE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TYPE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.MINORITY_SUB,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRANT_STATUS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_START_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_END_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_FUND_CODE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_DEPTID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_START_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_END_DT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_CD,
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_EMPLID,
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_NAME,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DESCR100,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_AWARD,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DOC_NBR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PAYMENT_TYPE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.LOC_FUNDED,
  M_GLDW1.CURR_PROJECT_GRANT_VW.CFDA_NBR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_NBR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PARENT_PROJ_GRANT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_BASIS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_RATE_PCT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.CLASS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROGRAM_CODE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BILLED_BY,
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR_DESCR100,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE,
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE_DESCR100,
  M_GLDW1.CURR_PROJECT_GRANT_VW.BUDG_MUST_EQUAL_REV,
  M_GLDW1.CURR_PROJECT_GRANT_VW.REPORTING_BUDGET_AMT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP,
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP_DESCR,
  M_GLDW1.CURR_PROJECT_GRANT_VW.M_FED_AWARD_ID_NBR,
  PROJ_GRANT_CURR_FUND_VW.FUND_CODE,
  PROJ_GRANT_CURR_FUND_VW.FUND_EFFDT,
  PROJ_GRANT_CURR_FUND_VW.FUND_EFF_STATUS,
  PROJ_GRANT_CURR_FUND_VW.FUND_DESCR,
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP,
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP_DESCR,
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP,
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPTID,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFFDT,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFF_STATUS,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.EMPLID,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ,
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR,
  sum(M_GLDW1.CURR_PROJECT_GRANT_VW.REPORTING_BUDGET_AMT)
FROM
  M_GLDW1.JRNL_LN,
  M_GLDW1.JRNL_LN_RECON_EXCPTN,
  M_GLDW1.CNVCHARTFD  JRNL_LN_CNVCHARTFD,
  M_GLDW1.JRNL_HEADER,
  M_GLDW1.CAL_DETP_TBL,
  M_GLDW1.CURR_ACCOUNT_VW,
  M_GLDW1.CURR_FUND_VW  JRNL_LN_CURR_FUND_VW,
  M_GLDW1.CURR_FN_DEPT_VW  JRNL_LN_CURR_FN_DEPT_VW,
  M_GLDW1.PERSONAL_DATA,
  M_GLDW1.CURR_PROGRAM_VW,
  M_GLDW1.CURR_CLASS_CF_VW,
  M_GLDW1.CURR_PROJECT_GRANT_VW,
  M_GLDW1.CURR_FUND_VW  PROJ_GRANT_CURR_FUND_VW,
  M_GLDW1.CURR_FN_DEPT_VW  PROJ_GRANT_CURR_FN_DEPT_VW
WHERE
  ( M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_FUND_CODE=PROJ_GRANT_CURR_FUND_VW.FUND_CODE  )
  AND  ( M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_DEPTID=PROJ_GRANT_CURR_FN_DEPT_VW.DEPTID  )
  AND  ( M_GLDW1.CAL_DETP_TBL.FISCAL_YEAR=M_GLDW1.JRNL_HEADER.FISCAL_YEAR AND M_GLDW1.CAL_DETP_TBL.ACCOUNTING_PERIOD = M_GLDW1.JRNL_HEADER.ACCOUNTING_PERIOD  )
  AND  ( M_GLDW1.JRNL_HEADER.JOURNAL_ID=M_GLDW1.JRNL_LN.JOURNAL_ID AND M_GLDW1.JRNL_HEADER.JOURNAL_DATE = M_GLDW1.JRNL_LN.JOURNAL_DATE  )
  AND  ( M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT=M_GLDW1.JRNL_LN.ACCOUNT  )
  AND  ( M_GLDW1.JRNL_LN.PROGRAM_CODE=M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE  )
  AND  ( M_GLDW1.JRNL_LN.PROJECT_GRANT=M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT  )
  AND  ( JRNL_LN_CNVCHARTFD.PROJECT_GRANT(+)=M_GLDW1.JRNL_LN.PROJECT_GRANT  )
  AND  ( M_GLDW1.JRNL_LN.CLASS=M_GLDW1.CURR_CLASS_CF_VW.CLASS  )
  AND  ( JRNL_LN_CURR_FUND_VW.FUND_CODE=M_GLDW1.JRNL_LN.FUND_CODE  )
  AND  ( M_GLDW1.JRNL_LN.DEPTID=JRNL_LN_CURR_FN_DEPT_VW.DEPTID  )
  AND  ( JRNL_LN_CURR_FN_DEPT_VW.EMPLID=M_GLDW1.PERSONAL_DATA.EMPLID(+)  )
  AND  ( JRNL_LN_CNVCHARTFD.CLASS(+)=M_GLDW1.JRNL_LN.CLASS  )
  AND  ( JRNL_LN_CNVCHARTFD.DEPTID(+)=M_GLDW1.JRNL_LN.DEPTID  )
  AND  ( JRNL_LN_CNVCHARTFD.FUND_CODE(+)=M_GLDW1.JRNL_LN.FUND_CODE  )
  AND  ( JRNL_LN_CNVCHARTFD.PROGRAM_CODE(+)=M_GLDW1.JRNL_LN.PROGRAM_CODE  )
  AND  ( M_GLDW1.JRNL_LN.JOURNAL_ID=M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_ID(+)  )
  AND  ( M_GLDW1.JRNL_LN.JOURNAL_DATE=M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_DATE(+)  )
  AND  ( M_GLDW1.JRNL_LN.JOURNAL_LINE=M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_LINE(+)  )
GROUP BY
  M_GLDW1.JRNL_LN.JOURNAL_ID, 
  M_GLDW1.JRNL_LN.JOURNAL_DATE, 
  M_GLDW1.JRNL_LN.JOURNAL_LINE, 
  M_GLDW1.JRNL_LN.ACCOUNT, 
  M_GLDW1.JRNL_LN.FUND_CODE, 
  M_GLDW1.JRNL_LN.DEPTID, 
  M_GLDW1.JRNL_LN.PROGRAM_CODE, 
  M_GLDW1.JRNL_LN.CLASS, 
  M_GLDW1.JRNL_LN.PROJECT_GRANT, 
  M_GLDW1.JRNL_LN.JRNL_LN_REF, 
  M_GLDW1.JRNL_LN.DOLLAR_AMOUNT, 
  M_GLDW1.JRNL_LN.JOURNAL_LINE_DESCR, 
  M_GLDW1.JRNL_LN.STATISTIC_AMOUNT, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_FLG, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_BY, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_LAST_UPDATE_DT, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_NOTE, 
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_DT, 
  M_GLDW1.JRNL_LN.RECON_NOTE_LAST_UPDT_BY, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_CD, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_STATUS_DESCR, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_CD, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_TYPE_DESCR, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_CD, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_RSLTN_RSN_DESCR, 
  M_GLDW1.JRNL_LN.JRNL_LN_RECON_ASSGN_UNQNM, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_ID, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_DATE, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JOURNAL_LINE, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_CD, 
  M_GLDW1.JRNL_LN_RECON_EXCPTN.JRNL_LN_RECON_EXCPTN_TYPE_DES, 
  JRNL_LN_CNVCHARTFD.SHORTCODE, 
  JRNL_LN_CNVCHARTFD.SHORTCODE_DESCR50, 
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS, 
  JRNL_LN_CNVCHARTFD.SHORTCODE_STATUS_DESCRSHORT, 
  JRNL_LN_CNVCHARTFD.FUND_CODE, 
  JRNL_LN_CNVCHARTFD.DEPTID, 
  JRNL_LN_CNVCHARTFD.PROGRAM_CODE, 
  JRNL_LN_CNVCHARTFD.CLASS, 
  JRNL_LN_CNVCHARTFD.PROJECT_GRANT, 
  JRNL_LN_CNVCHARTFD.CLASS2, 
  JRNL_LN_CNVCHARTFD.ACCOUNT, 
  M_GLDW1.JRNL_HEADER.JOURNAL_ID, 
  M_GLDW1.JRNL_HEADER.JOURNAL_DATE, 
  CONCAT(rpad(( M_GLDW1.JRNL_HEADER.JOURNAL_ID ),11,'-'),to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'mm/dd/yyyy')), 
  to_char(( M_GLDW1.JRNL_HEADER.JOURNAL_DATE ),'DAY'), 
  M_GLDW1.JRNL_HEADER.FISCAL_YEAR, 
  M_GLDW1.JRNL_HEADER.ACCOUNTING_PERIOD, 
  M_GLDW1.JRNL_HEADER.REVERSAL_CD, 
  M_GLDW1.JRNL_HEADER.REVERSAL_DATE, 
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE, 
  M_GLDW1.JRNL_HEADER.JOURNAL_SOURCE_DESCR, 
  M_GLDW1.JRNL_HEADER.TRANS_REF_NUM, 
  M_GLDW1.JRNL_HEADER.JOURNAL_DESCR254, 
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_EMPLID, 
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_NAME, 
  M_GLDW1.JRNL_HEADER.JOURNAL_AUTH_PHONE, 
  M_GLDW1.JRNL_HEADER.JOURNAL_ENTERED_OPRID, 
  M_GLDW1.JRNL_HEADER.JOURNAL_SYSTEM_SOURCE, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_EMPLID, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_NAME, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PHONE, 
  M_GLDW1.JRNL_HEADER.JRNL_RQSTR_PRMRY_APPT_DEPT_CD, 
  M_GLDW1.CAL_DETP_TBL.FISCAL_YEAR, 
  M_GLDW1.CAL_DETP_TBL.ACCOUNTING_PERIOD, 
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_CODE, 
  M_GLDW1.CAL_DETP_TBL.CAL_MONTH_DESCR, 
  M_GLDW1.CAL_DETP_TBL.CAL_YEAR, 
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_BEGIN_DT, 
  M_GLDW1.CAL_DETP_TBL.ACCTG_PERIOD_END_DT, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFFDT, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFF_STATUS, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_TYPE, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_GASB_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SPON_PROJ_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SP_VOL_RES_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_ISR_BUDGET_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_MED_SCH_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_UMHS_DESCR, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCRLONG, 
  JRNL_LN_CURR_FUND_VW.FUND_CODE, 
  JRNL_LN_CURR_FUND_VW.FUND_EFFDT, 
  JRNL_LN_CURR_FUND_VW.FUND_EFF_STATUS, 
  JRNL_LN_CURR_FUND_VW.FUND_DESCR, 
  JRNL_LN_CURR_FUND_VW.FUND_GRP, 
  JRNL_LN_CURR_FUND_VW.FUND_GRP_DESCR, 
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP, 
  JRNL_LN_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPTID, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFFDT, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_EFF_STATUS, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.EMPLID, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ, 
  JRNL_LN_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR, 
  M_GLDW1.PERSONAL_DATA.EMPLID, 
  M_GLDW1.PERSONAL_DATA.PERSON_NAME, 
  M_GLDW1.PERSONAL_DATA.PERSON_UNIQNAME, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS1, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS2, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS3, 
  M_GLDW1.PERSONAL_DATA.PERSON_ADDRESS4, 
  M_GLDW1.PERSONAL_DATA.PERSON_ZIP, 
  M_GLDW1.PERSONAL_DATA.PERSON_COUNTRY, 
  M_GLDW1.PERSONAL_DATA.PERSON_PHONE, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFFDT, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFF_STATUS, 
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_DESCR, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFFDT, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_EFF_STATUS, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_DESCR, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP, 
  M_GLDW1.CURR_CLASS_CF_VW.CLASS_GRP_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFFDT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFF_STATUS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TITLE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TYPE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.MINORITY_SUB, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRANT_STATUS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_START_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_END_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_FUND_CODE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.INITIAL_DEPTID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_START_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BUD_PERIOD_END_DT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_DIR_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SINGLE_ADMIN_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_CD, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.FIN_OPS_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_EMPLID, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_CNTCT_NAME, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DESCR100, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_TYPE_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_CLASS_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_AWARD, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SPONSOR_DOC_NBR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PAYMENT_TYPE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.LOC_FUNDED, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.CFDA_NBR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.DRDA_NBR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PARENT_PROJ_GRANT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_BASIS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.IC_RATE_PCT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.CLASS, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROGRAM_CODE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJ_GRT_BILLED_BY, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.BILL_SPONSOR_DESCR100, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.SUB_CONTRACT_AWARDEE_DESCR100, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.BUDG_MUST_EQUAL_REV, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.REPORTING_BUDGET_AMT, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_GRP_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.ISR_REPORT_SUBGRP_DESCR, 
  M_GLDW1.CURR_PROJECT_GRANT_VW.M_FED_AWARD_ID_NBR, 
  PROJ_GRANT_CURR_FUND_VW.FUND_CODE, 
  PROJ_GRANT_CURR_FUND_VW.FUND_EFFDT, 
  PROJ_GRANT_CURR_FUND_VW.FUND_EFF_STATUS, 
  PROJ_GRANT_CURR_FUND_VW.FUND_DESCR, 
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP, 
  PROJ_GRANT_CURR_FUND_VW.FUND_GRP_DESCR, 
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP, 
  PROJ_GRANT_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPTID, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFFDT, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_EFF_STATUS, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.EMPLID, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ, 
  PROJ_GRANT_CURR_FN_DEPT_VW.DEPT_BUD_SEQ_DESCR


