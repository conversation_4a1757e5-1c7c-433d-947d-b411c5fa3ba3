CREATE OR <PERSON><PERSON><PERSON>CE VIEW UM_CASHIER_TRANSACTION AS
SELECT 
TBRACCD.TBRACCD_PIDM PIDM,
TBRACCD.TBRACCD_TERM_CODE TERM_CODE,
TBRACCD.TBRACCD_DETAIL_CODE DETAIL_CODE,
TBBDETC.TBBDETC_DESC,
TBBDETC.TBBDETC_TYPE_IND TYPE_IND,
TBRACCD.TBRACCD_USER,
TBRACCD.TBRACCD_ENTRY_DATE ENTRY_DATE,
TBRACCD.TBRACCD_AMOUNT AMOUNT,
TBRACCD.TBRACCD_BALANCE BALANCE,
TBRACCD.TBRACCD_EFFECTIVE_DATE EFFECTIVE_DATA,
TBRACCD.TBRACCD_DUE_DATE DUE_DATE,
TBRACCD.TBRACCD_TRANS_DATE TRANS_DATE,
TBRACCD.TBRACCD_FEED_DATE FEED_DATA,
TBRACCD.TBRACCD_FEED_DOC_CODE,
TBRACCD.TBRACCD_INVOICE_NUMBER INVOICE_NUMBER,
TBRACCD.TBRACCD_DESC TBRACCD_DESC
FROM AIMSMGR.TBRACCD TBRACCD  
INNER JOIN AIMSMGR.TBBDETC TBBDETC ON TBRACCD.TBRACCD_DETAIL_CODE = TBBDETC.TBBDETC_DETAIL_CODE;

CREATE OR REPLACE VIEW UM_CASHIER_BALANCE AS
SELECT 
TBRACCD.TBRACCD_PIDM PIDM,
TBRACCD.TBRACCD_TERM_CODE TERM_CODE,
SUM(TBRACCD.TBRACCD_BALANCE) BALANCE
FROM AIMSMGR.TBRACCD TBRACCD  
GROUP BY
TBRACCD.TBRACCD_PIDM,
TBRACCD.TBRACCD_TERM_CODE
ORDER BY 3 DESC
;


SELECT 
DM_PIDM
FROM um_demographic
WHERE UMID = '12805441';

BEGIN
gar_gar_b_gone;
END;