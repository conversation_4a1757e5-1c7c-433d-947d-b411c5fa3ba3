/*******************************************************************************
The Purpose of this Query is to build a degree completions table for reporting
that will move duplicate degrees (ie 1 of 2BA's or 2BS's) from 1st degree to 2nd
degree.  The query will also need to identify if a degree is a STEM or Health 
field degree and give these degrees precedence when deciding which degree to 
keep as the first major completion and which to push to the second degree 
following State reporting methodologies.
Table Name: IA_DEGREE_COMP_TBL
Grain: 1 row/FY/Student
Variables:
FY,pidm,report_ethnicity,gender,degree_count,distinct_degree_count,
degree_1_code,degree_1_program,degree_1_mjr,degree_1_code_cip,degree_1_NCES,
degree_2_code,degree_2_program,degree_2_mjr,degree_2_code_cip,degree_2_NCES,
degree_3_code,degree_3_program,degree_3_mjr,degree_3_code_cip,degree_3_NCES,
degree_4_code,degree_4_program,degree_4_mjr,degree_4_code_cip,degree_4_NCES
*******************************************************************************/
create or replace view IA_DEGREE_COMPLETIONS_SUMMARY as (

select 
pidm,
GRAD_TERM_CODE,
count (pidm) as student_record_count,
count (PRIMARY_MAJOR_1) + count (PRIMARY_MAJOR_2) as completions_count,
count (distinct degree_code) as first_major_completions,
count (PRIMARY_MAJOR_2) as second_majors,
count (PRIMARY_MAJOR_1) - count (distinct degree_code) as primary_to_secondary_major,
count (PRIMARY_MAJOR_1) - count (distinct degree_code) + count (PRIMARY_MAJOR_2) as total_second_majors
from ia_um_degree

group by pidm, GRAD_TERM_CODE

--order by 
--student_record_count desc, 
--completions_count desc, 
--first_major_completions desc,
--primary_to_secondary_major desc,
--second_majors desc,
--total_second_majors desc
);
