/******************************************************************************
SELECT * FROM
(
  SELECT column1, column2
  FROM tables
  WHERE conditions
)
PIVOT 
(
  aggregate_function(column2)
  FOR column2
  IN ( expr1, expr2, ... expr_n) | subquery
)
ORDER BY expression [ ASC | DESC ];
*******************************************************************************/

select *  from
(
select *
from IA_DEGREE_COMPLETIONS
where Fiscal_year = 'FY 14-15'
)
PIVOT
(
count (*)
for COMPLETION_TYPE
in ('first major','second major')
)
order by NCES_DESC
