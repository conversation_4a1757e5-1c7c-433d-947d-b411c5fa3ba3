select count(*) from learner_activity_result; --4065927

select 
learner_activity_result_id,
grader_id,
learner_activity_id,
learner_group_id,
person_id,
quiz_id,
quiz_result_id,
attempt,
body,
created_date,
grade,
grade_state,
gradebook_status,
graded_date,
grading_status,
posted_at,
process_attempts,
score,
submission_comments_count,
type
from
entity.learner_activity_result
where learner_activity_result_id > 100000
and learner_activity_result_id <=1000000
; 

select 
learner_activity_result_id,
grader_id,
learner_activity_id,
learner_group_id,
person_id,
quiz_id,
quiz_result_id,
attempt,
body,
created_date,
grade,
grade_state,
gradebook_status,
graded_date,
grading_status,
posted_at,
process_attempts,
score,
submission_comments_count,
type,
updated_date
from
entity.learner_activity_result
;

select 
annotation_id,
author_id,
learner_activity_result_id,
author_name,
substring (body_value from 1 for 4000) body_value,
to_char(created_date,'dd-MON-yyyy') created_date,
message_character_count,
message_line_count,
message_size_bytes,
message_word_count,
to_char(updated_date,'dd-MON-yyyy') updated_date
from entity.annotation; --517771