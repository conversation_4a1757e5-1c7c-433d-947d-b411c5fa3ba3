/*******************************************************************************
Update Cohort Population Table
*******************************************************************************/

--Truncate backup file
TRUNCATE TABLE IA_COHORT_POP_UNDUP_BAC;

--Create Backup
INSERT INTO IA_COHORT_POP_UNDUP_BAC
SELECT * FROM IA_COHORT_POP_UNDUP_TBL;

--Count rows to varify backup
SELECT COUNT (*) FROM IA_COHORT_POP_UNDUP_TBL;
SELECT COUNT (*) FROM IA_COHORT_POP_UNDUP_BAC;

--Truncate Table
TRUNCATE TABLE IA_COHORT_POP_UNDUP_TBL;

--Insert rows for new Students in Cohort
INSERT INTO IA_COHORT_POP_UNDUP_TBL 
 SELECT * FROM IA_COHORT_POP_UNDUP
 WHERE CO_TERM_CODE_KEY = 202110;      /*<--UPDATE TO CURRENT FALL*/

--Identify students removed from cohorts
SELECT 
SD.SD_PIDM,
SD.SD_TERM_CODE,
SD.PRIMARY_LEVEL_CODE,
SD.IA_STUDENT_TYPE_CODE
FROM IA_TD_STUDENT_DATA SD
WHERE SD.REGISTERED_IND = 'Y'
AND SD.SD_TERM_CODE = 202110
AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
AND NOT EXISTS (
    SELECT CO.CO_PIDM
    FROM IA_COHORT_POP_UNDUP CO
    WHERE SD.SD_PIDM = CO.CO_PIDM
    AND CO.CO_TERM_CODE_KEY = 202110
    )
;

