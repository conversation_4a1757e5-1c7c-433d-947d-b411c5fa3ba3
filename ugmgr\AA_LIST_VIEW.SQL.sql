--------------------------------------------------------
--  File created - Saturday-December-09-2023   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View AA_LIST_VIEW
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "UGMGR"."AA_LIST_VIEW" ("FIRST_NAME", "MIDDLE_NAME", "LAST_NAME", "UMF_TARGETED_COMMUNICATIONS_ATTRIBUTES", "UMF_TEXT_OPT_IN__C", "UMF_INTERNATIONAL_INDICATOR_C", "BIRTHDATE", "SEX", "EMAIL_ADDRESS", "SCHOOL_LOOKUP_CEEBCODE", "SCHOOL_LOOKUP_CEEBNAME", "RESIDENCY", "PERMANENT_ADDRESS_ADDRESS1", "PERMANENT_ADDRESS_ADDRESS2", "PERMANENT_ADDRESS_ADDRESS3", "PERMANENT_ADDRESS_CITY", "PERMANENT_ADDRESS_STATE", "PERMANENT_ADDRESS_ZIP", "PERMANENT_ADDRESS_COUNTRY", "PERMANENT_ADDRESS_COUNTY") AS 
  select FIRST_NAME, MIDDLE_NAME, LAST_NAME,
case when HAIL = 'Y' then 'Eligible for Hail' else '' end umf_Targeted_Communications_Attributes   ,
case when opt_in = 'Y' then 1 else 0 end UMF_Text_Opt_in__c,
case when Intl_ind = 'Y' then 1 else 0 end UMF_International_Indicator_c,
to_char(DATE_OF_BIRTH, 'YYYYMMDD') BIRTHDATE,--	IIF(NOT ISNULL(BIRTHDATE), TO_DATE(BIRTHDATE,'YYYYMMDD'), null)
SEX, EMAIL_ADDRESS, PREFERRED_PHONE_NUMBER
SCHOOL_LOOKUP_CEEBCODE, SCHOOL_LOOKUP_CEEBNAME,RESIDENCY,
PERMANENT_ADDRESS_ADDRESS1, PERMANENT_ADDRESS_ADDRESS2, PERMANENT_ADDRESS_ADDRESS3, 
PERMANENT_ADDRESS_CITY, PERMANENT_ADDRESS_STATE, 
PERMANENT_ADDRESS_ZIP, PERMANENT_ADDRESS_COUNTRY, PERMANENT_ADDRESS_COUNTY
from AA_List_batch_2 where IMPORT_IND is null
;
