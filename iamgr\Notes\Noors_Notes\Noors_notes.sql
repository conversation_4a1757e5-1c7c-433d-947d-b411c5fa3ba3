select *  from 
IA_COHORT_TRANS_POP inner join IA_COHORT_POP_UNDUP on IA_COHORT_TRANS_POP.CO_PIDM = IA_COHORT_POP_UNDUP.CO_PIDM
WHERE CO_IA_STUDENT_TYPE_CODE = 'F' AND IA_COHORT_POP_UNDUP.CO_TERM_CODE_KEY = '201010' AND SUBJECT = 'BIO' AND COURSE = '111' AND COMPLETED_IND ='Y' AND (repeat_ind = 'I' OR repeat_ind IS NULL)


select *  from UM_STUDENT_TRANSCRIPT INNER JOIN IA_COHORT_POP_UNDUP ON UM_STUDENT_TRANSCRIPT.PIDM = IA_COHORT_POP_UNDUP.CO_PIDM
where
 subject = 'BIO' AND COURSE = '111' AND IA_COHORT_POP_UNDUP.CO_TERM_CODE_KEY = '201010' AND TYPE ='I' AND IA_COHORT_POP_UNDUP.CO_IA_STUDENT_TYPE_CODE ='F'  AND 
COMPLETED_IND ='Y'
GROUP BY PIDM;

SELECT CO_PIDM, SUBJECT,COURSE , COUNT(*) FROM IA_COHORT_TRANS_POP GROUP BY CO_PIDM, SUBJECT,COURSE

SELECT * FROM  IA_COHORT_TRANS_POP WHERE PIDM = 76650 AND SUBJECT = 'MTH' AND COURSE = '118'

select * from IA_COHORT_TRANS_TBL WHERE co_PIDM = 76650 --AND SUBJECT = 'MTH' AND COURSE = '118'

CREATE TABLE IA_COHORT_TRANS_TBL AS SELECT * FROM IA_COHORT_TRANS

******************************************************************************************************************

select * from
IA_COHORT_SIX_YR inner join IA_COHORT_POP_UNDUP
on IA_COHORT_SIX_YR.co_pidm = IA_COHORT_POP_UNDUP.co_pidm
where CO_TERM_CODE_KEY = '200910' and IA_COHORT_SIX_YR.SIXTH_FALL_GRAD_IND = 'Y';


select * from IA_COHORT_TRANS_TBL inner join IA_COHORT_POP_UNDUP
on IA_COHORT_TRANS_TBL.co_pidm = IA_COHORT_POP_UNDUP.co_pidm
where CO_TERM_CODE_KEY = 201410 and MTH_090_IND = 'I'  and CO_IA_STUDENT_TYPE_CODE = 'F' and CO_FULL_PART_IND_UMF = 'F'

                               
                        