
/


CREATE OR REPLACE package body gclsclss as

  procedure p_load_room_size_count as
  begin
    execute immediate('truncate table gcls_room_size_summary');
    
    insert into gcls_room_size_summary(
    gcls_room_size_summary.um_room_size,
    gcls_room_size_summary.room_count,
    gcls_room_size_summary.term_code)

        select
        distinct gcls_course_time_summary.um_room_size_act um_room_size_act,
        sum(gcls_course_time_summary.section_count) room_count,
        gcls_course_time_summary.term_code term_code
        from gcls_course_time_summary
        group by gcls_course_time_summary.um_room_size_act, gcls_course_time_summary.term_code;
    
    commit;
  end p_load_room_size_count;

end gclsclss;
/
