with dp1 as (
select
distinct 
COURSE,
SECTION_NUMBER,
MEETING_SCHD_CODE_1,
CAT_TITLE,
case
when RD.CRSE_NUMBER >= 500 then 'GR'
when RD.CRSE_NUMBER < 500 then 'UG'
else RD.CRSE_NUMBER
end level_code,
count (RD.pidm) course_enrollment
from
IA_TD_REGISTRATION_DETAIL RD
WHERE
RD.TD_TERM_CODE in (select current_term from um_current_term)         
and RD.MEETING_SCHD_CODE_1 in ('L/L','L/D','MM','LAB')
and RD.section_number not like ('W%')
and RD.section_number not like ('9%')
and RD.section_number != 'IS'
group by 
COURSE,
SECTION_NUMBER,
MEETING_SCHD_CODE_1,
CAT_TITLE,
case
when RD.CRSE_NUMBER >= 500 then 'GR'
when RD.CRSE_NUMBER < 500 then 'UG'
else RD.CRSE_NUMBER
end
)
select 
--level_code,
round ((sum (course_enrollment) / count (course)),2) avg_count
from dp1
--group by
--level_code
;
