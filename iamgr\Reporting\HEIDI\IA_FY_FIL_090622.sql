--------------------------------------------------------
--  DDL for View IA_FACULTY_INSTRUCTIONAL_LOAD
--------------------------------------------------------

  SELECT 
    FY,
    TD_TERM_CODE,
    COLL_CODE,
    CRN_KEY,
    SUBJ_CODE,
    CRSE_NUMBER,
    SECTION_NUMBER,
    MEETING_SCHD_CODE_1,
    XLST_GROUP,
    instructor_name,
    instructor_number,
    crse_level,
    TD_SECTION_ENROLLMENT,
    TD_CREDIT_HOURS
  FROM
    (SELECT popsel.TD_TERM_CODE,
      popsel.FY,
      CASE
        WHEN popsel.instructor_name  = ', '
        AND popsel.instructor_number = 1
        THEN 'keep'
        WHEN popsel.instructor_name   = ', '
        AND popsel.instructor_number <> 1
        THEN 'remove'
        ELSE 'keep'
      END ins_clean,
      CASE
        WHEN popsel.COLL_CODE = '00'
        AND (popsel.DEPT_CODE = 'HON'
        OR popsel.DEPT_CODE   = 'UNV')
        THEN 'AS'
        WHEN popsel.COLL_CODE = '00'
        AND popsel.DEPT_CODE  = 'SEC'
        THEN 'EH'
        ELSE popsel.COLL_CODE
      END coll_code,
      popsel.CRN_KEY,
      popsel.SUBJ_CODE,
      popsel.CRSE_NUMBER,
      popsel.SECTION_NUMBER,
      popsel.MEETING_SCHD_CODE_1,
      popsel.XLST_GROUP,
      popsel.instructor_name,
      popsel.instructor_number,
      CASE
        WHEN popsel.CRSE_NUMBER <300
        THEN 'LL-UG'
        WHEN popsel.CRSE_NUMBER <500
        AND popsel.CRSE_NUMBER >=300
        THEN 'UL-UG'
        ELSE 'GR'
      END crse_level,
      COUNT (popsel.td_pidm)       AS TD_SECTION_ENROLLMENT,
      SUM (popsel.TD_credit_hours) AS TD_CREDIT_HOURS
    FROM
      (
      --primary instructor
      SELECT IA_TD_REGISTRATION_DETAIL.pidm td_pidm,
        IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
        IA_TD_REGISTRATION_DETAIL.FY,
        um_catalog_schedule.COLL_CODE,
        um_catalog_schedule.COLL_DESC,
        um_catalog_schedule.DEPT_CODE,
        um_catalog_schedule.DEPT_DESC,
        IA_TD_REGISTRATION_DETAIL.CRN_KEY,
        IA_TD_REGISTRATION_DETAIL.SUBJ_CODE,
        IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
        IA_TD_REGISTRATION_DETAIL.SECTION_NUMBER,
        IA_TD_REGISTRATION_DETAIL.MEETING_SCHD_CODE_1,
        IA_TD_REGISTRATION_DETAIL.XLST_GROUP,
        IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours,
        um_catalog_schedule.PRIMARY_INSTRUCTOR_LAST_NAME
        ||', '
        || um_catalog_schedule.PRIMARY_INSTRUCTOR_FIRST_NAME instructor_name,
        '1' AS instructor_number
      FROM IA_TD_REGISTRATION_DETAIL
      FULL OUTER JOIN um_catalog_schedule
      ON um_catalog_schedule.TERM_CODE_KEY = IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE
      AND um_catalog_schedule.CRN_KEY      = IA_TD_REGISTRATION_DETAIL.CRN_KEY
      UNION ALL
      --secondary instructor
      SELECT IA_TD_REGISTRATION_DETAIL.pidm td_pidm,
        IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
        IA_TD_REGISTRATION_DETAIL.FY,
        um_catalog_schedule.COLL_CODE,
        um_catalog_schedule.COLL_DESC,
        um_catalog_schedule.DEPT_CODE,
        um_catalog_schedule.DEPT_DESC,
        IA_TD_REGISTRATION_DETAIL.CRN_KEY,
        IA_TD_REGISTRATION_DETAIL.SUBJ_CODE,
        IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
        IA_TD_REGISTRATION_DETAIL.SECTION_NUMBER,
        IA_TD_REGISTRATION_DETAIL.MEETING_SCHD_CODE_1,
        IA_TD_REGISTRATION_DETAIL.XLST_GROUP,
        IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours,
        um_catalog_schedule.INSTRUCTOR_LAST_NAME2
        ||', '
        || um_catalog_schedule.INSTRUCTOR_FIRST_NAME2 instructor_name,
        '2' AS instructor_number
      FROM IA_TD_REGISTRATION_DETAIL
      FULL OUTER JOIN um_catalog_schedule
      ON um_catalog_schedule.TERM_CODE_KEY = IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE
      AND um_catalog_schedule.CRN_KEY      = IA_TD_REGISTRATION_DETAIL.CRN_KEY
      UNION ALL
      --Third instructor
      SELECT IA_TD_REGISTRATION_DETAIL.pidm td_pidm,
        IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
        IA_TD_REGISTRATION_DETAIL.FY,
        um_catalog_schedule.COLL_CODE,
        um_catalog_schedule.COLL_DESC,
        um_catalog_schedule.DEPT_CODE,
        um_catalog_schedule.DEPT_DESC,
        IA_TD_REGISTRATION_DETAIL.CRN_KEY,
        IA_TD_REGISTRATION_DETAIL.SUBJ_CODE,
        IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
        IA_TD_REGISTRATION_DETAIL.SECTION_NUMBER,
        IA_TD_REGISTRATION_DETAIL.MEETING_SCHD_CODE_1,
        IA_TD_REGISTRATION_DETAIL.XLST_GROUP,
        IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours,
        um_catalog_schedule.INSTRUCTOR_LAST_NAME3
        ||', '
        || um_catalog_schedule.INSTRUCTOR_FIRST_NAME3 instructor_name,
        '3' AS instructor_number
      FROM IA_TD_REGISTRATION_DETAIL
      FULL OUTER JOIN um_catalog_schedule
      ON um_catalog_schedule.TERM_CODE_KEY = IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE
      AND um_catalog_schedule.CRN_KEY      = IA_TD_REGISTRATION_DETAIL.CRN_KEY
      )popsel
    WHERE
--      popsel.FY = (select substr(current_aidy_code,1,2)||'-'||substr(current_aidy_code,3,4) FY from um_current_term)             --update for FY
      popsel.FY = '21-22'                                         --UPDATE THIS
      --and
--      popsel.TD_TERM_CODE >= 201240
    GROUP BY FY,
      popsel.TD_TERM_CODE,
      popsel.FY,
      CASE
        WHEN popsel.COLL_CODE = '00'
        AND (popsel.DEPT_CODE = 'HON'
        OR popsel.DEPT_CODE   = 'UNV')
        THEN 'AS'
        WHEN popsel.COLL_CODE = '00'
        AND popsel.DEPT_CODE  = 'SEC'
        THEN 'EH'
        ELSE popsel.COLL_CODE
      END,
      CASE
        WHEN popsel.instructor_name  = ', '
        AND popsel.instructor_number = 1
        THEN 'keep'
        WHEN popsel.instructor_name   = ', '
        AND popsel.instructor_number <> 1
        THEN 'remove'
        ELSE 'keep'
      END,
      popsel.CRN_KEY,
      popsel.SUBJ_CODE,
      popsel.CRSE_NUMBER,
      popsel.SECTION_NUMBER,
      popsel.MEETING_SCHD_CODE_1,
      popsel.XLST_GROUP,
      popsel.instructor_name,
      popsel.instructor_number,
      CASE
        WHEN popsel.CRSE_NUMBER <300
        THEN 'LL-UG'
        WHEN popsel.CRSE_NUMBER <500
        AND popsel.CRSE_NUMBER >=300
        THEN 'UL-UG'
        ELSE 'GR'
      END
    )popsel2
  WHERE ins_clean = 'keep'
  ORDER BY td_term_code,
    subj_code,
    crse_number,
    section_number,
    instructor_number
;
