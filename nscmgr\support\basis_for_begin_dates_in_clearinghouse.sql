select 
TD_TERM_CODE,
     CASE
      WHEN TD_TERM_CODE LIKE ('%40') THEN 'Su'
      WHEN TD_TERM_CODE LIKE ('%10') THEN 'Fa'
      WHEN TD_TERM_CODE LIKE ('%20') THEN 'Wi'
      WHEN TD_TERM_CODE LIKE ('%30') THEN 'Sp'
      END SEASON,
COURSE,
substr(MEETING_START_DATE_1,1,2) begin_month,
MEETING_START_DATE_1,
substr(MEETING_END_DATE_1,1,2) end_month,
MEETING_END_DATE_1,
count (pidm)
from ia_td_registration_detail
where td_term_code >= 201110
and LEVL_CODE = 'UG'
and MEETING_START_DATE_1 is not NULL
group by
TD_TERM_CODE,
     CASE
      WHEN TD_TERM_CODE LIKE ('%40') THEN 'Su'
      WHEN TD_TERM_CODE LIKE ('%10') THEN 'Fa'
      WHEN TD_TERM_CODE LIKE ('%20') THEN 'Wi'
      WHEN TD_TERM_CODE LIKE ('%30') THEN 'Sp'
      END ,
COURSE,
substr(MEETING_START_DATE_1,1,2),
MEETING_START_DATE_1,
substr(MEETING_END_DATE_1,1,2) ,
MEETING_END_DATE_1
--;