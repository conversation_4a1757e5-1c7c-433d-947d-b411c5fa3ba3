--New FTIAC/Transfers with prior student records
with dp1 as (
select 
sd_pidm pidm, 
sds.SD_TERM_code start_term,
ia_student_type_code stype,
primary_level_code
from ia_um_student_data sds
where sds.registered_ind = 'Y'
and sds.sd_term_code <= ('202510') 
and sds.sd_term_code like '%10'
--and sds.sd_term_code in ('202010','202110','202210') 
AND sds.STUDENT_TYPE_CODE in ('T','F')
and sds.primary_level_code = 'UG'
),dp2 as (
select 
dp1.pidm,
sd.UMID,
start_term,
student_type_code,
dp1.primary_level_code,
min(sd.sd_term_code) prior_term
from dp1
inner join td_student_data sd on dp1.pidm = sd.sd_pidm
where sd.student_type_code in ('T','F')
and sd.registered_ind = 'Y'
and sd.sd_term_code < 202510
and sd.primary_level_code = 'UG'
group by 
dp1.pidm,
sd.umid,
start_term,
student_type_code,
dp1.primary_level_code
)
select
start_term,
student_type_code,
count(*) headcount
from dp2
where 
prior_term like '%40'
or (prior_term like '%30' and prior_term >= 202430)
group by
start_term,
student_type_code
order by
1 desc,
2
;