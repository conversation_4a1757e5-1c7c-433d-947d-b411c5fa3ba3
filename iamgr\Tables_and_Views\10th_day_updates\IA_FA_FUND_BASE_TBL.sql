TRUNCATE TABLE ia_fa_fund_base_tbl_bac;

--drop table ia_fa_fund_base_tbl_bac;
--
--create table ia_fa_fund_base_tbl_bac as
-- SELECT
--  *
-- FROM
--  ia_fa_fund_base_tbl;

INSERT INTO ia_fa_fund_base_tbl_bac
 SELECT
  *
 FROM
  ia_fa_fund_base_tbl;

COMMIT;

SELECT COUNT(*) FROM IA_FA_FUND_BASE_TBL_BAC;
SELECT COUNT (*) FROM IA_FA_FUND_BASE_TBL;


INSERT INTO ia_fa_fund_base_tbl
 SELECT
 fb.*
--  fa_pidm,
--  umid,
--  fa_term_code,
--  fa_fy,
--  fa_aid_year_code,
--  fa_fund_code,
--  fa_fund_source,
--  fa_fund_type_code,
--  fa_fund_detail_code,
--  fa_fund_title,
--  fa_offer_amt,
--  fa_paid_amt,
--  fa_accept_amt,
--  fa_orig_offer_amt,
--  fa_orig_offer_date,
--  efc,
--  fisap_inc
 FROM
  ia_fa_fund_base fb
 WHERE
  fb.fa_term_code = (
   SELECT
    current_term
   FROM
    um_current_term
  )
  ;

COMMIT;
--Varify records inserted
WITH dp1 AS (
 SELECT
  *
 FROM
  ia_fa_fund_base_tbl
 MINUS
 SELECT
  *
 FROM
  ia_fa_fund_base_tbl_bac
)
SELECT
 COUNT(*) rows_inserted
FROM
 dp1;

DESCRIBE ia_fa_fund_base_tbl;
DESCRIBE ia_fa_fund_base_tbl_bac;


flashback table ia_fa_fund_base_tbl_bac to BEFORE DROP;
