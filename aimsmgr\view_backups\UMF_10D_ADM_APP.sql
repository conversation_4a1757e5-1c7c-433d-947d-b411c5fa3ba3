--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View UMF_10D_ADM_APP
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "AIMSMGR"."UMF_10D_ADM_APP" ("PIDM_KEY", "TERM_CODE_KEY", "APPL_NO_KEY", "COMPLETE_IND", "ENROLLED_IND", "FINAID_APPLICANT_IND", "LEVL_CODE", "APPLICATION_DATE", "APST_CODE", "APPLICATION_STATUS_DATE", "ADMT_CODE", "STYP_CODE", "RESD_CODE", "RATE_CODE", "COLL_CODE1", "DEGC_CODE1", "MAJR_CODE1", "COLL_CODE2", "DEGC_CODE2", "MAJR_CODE2", "MAJR_CODE1_CONC1", "MAJR_CODE1_CONC2", "APDC_CODE1", "APDC_DECISION_DATE1", "APDC_CODE2", "APDC_DECISION_DATE2", "APDC_CODE3", "APDC_DECISION_DATE3", "SBGI_CODE_HIGH_SCHOOL", "SBGI_DESC_HIGH_SCHOOL", "HIGH_SCHOOL_GRAD_DATE", "HIGH_SCHOOL_RANK", "HIGH_SCHOOL_SIZE", "HIGH_SCHOOL_REPORTED_GPA", "SBGI_CODE_PRIOR_COLLEGE1", "SBGI_CODE_PRIOR_COLLEGE2", "SBGI_CODE_PRIOR_COLLEGE3", "TEST_CODE1", "TEST_SCORE1", "TEST_DATE1", "TEST_CODE2", "TEST_SCORE2", "TEST_DATE2", "TEST_CODE3", "TEST_SCORE3", "TEST_DATE3", "TEST_CODE4", "TEST_SCORE4", "TEST_DATE4", "TEST_CODE5", "TEST_SCORE5", "TEST_DATE5", "TEST_CODE6", "TEST_SCORE6", "TEST_DATE6", "TEST_CODE7", "TEST_SCORE7", "TEST_DATE7", "ADDITIONAL_TESTS_IND", "INTERNATIONAL_BIRTH_NATION", "INTERNATIONAL_LEGAL_NATION", "INTERNATIONAL_VISA_NUMBER", "INTERNATIONAL_VISA_TYPE", "CAMP_CODE", "PROGRAM_1", "EGOL_CODE") AS 
  select /*+DRIVING_SITE(umf_10d_adm_app)*/ umf_10d_adm_app."PIDM_KEY",umf_10d_adm_app."TERM_CODE_KEY",umf_10d_adm_app."APPL_NO_KEY",umf_10d_adm_app."COMPLETE_IND",umf_10d_adm_app."ENROLLED_IND",umf_10d_adm_app."FINAID_APPLICANT_IND",umf_10d_adm_app."LEVL_CODE",umf_10d_adm_app."APPLICATION_DATE",umf_10d_adm_app."APST_CODE",umf_10d_adm_app."APPLICATION_STATUS_DATE",umf_10d_adm_app."ADMT_CODE",umf_10d_adm_app."STYP_CODE",umf_10d_adm_app."RESD_CODE",umf_10d_adm_app."RATE_CODE",umf_10d_adm_app."COLL_CODE1",umf_10d_adm_app."DEGC_CODE1",umf_10d_adm_app."MAJR_CODE1",umf_10d_adm_app."COLL_CODE2",umf_10d_adm_app."DEGC_CODE2",umf_10d_adm_app."MAJR_CODE2",umf_10d_adm_app."MAJR_CODE1_CONC1",umf_10d_adm_app."MAJR_CODE1_CONC2",umf_10d_adm_app."APDC_CODE1",umf_10d_adm_app."APDC_DECISION_DATE1",umf_10d_adm_app."APDC_CODE2",umf_10d_adm_app."APDC_DECISION_DATE2",umf_10d_adm_app."APDC_CODE3",umf_10d_adm_app."APDC_DECISION_DATE3",umf_10d_adm_app."SBGI_CODE_HIGH_SCHOOL",umf_10d_adm_app."SBGI_DESC_HIGH_SCHOOL",umf_10d_adm_app."HIGH_SCHOOL_GRAD_DATE",umf_10d_adm_app."HIGH_SCHOOL_RANK",umf_10d_adm_app."HIGH_SCHOOL_SIZE",umf_10d_adm_app."HIGH_SCHOOL_REPORTED_GPA",umf_10d_adm_app."SBGI_CODE_PRIOR_COLLEGE1",umf_10d_adm_app."SBGI_CODE_PRIOR_COLLEGE2",umf_10d_adm_app."SBGI_CODE_PRIOR_COLLEGE3",umf_10d_adm_app."TEST_CODE1",umf_10d_adm_app."TEST_SCORE1",umf_10d_adm_app."TEST_DATE1",umf_10d_adm_app."TEST_CODE2",umf_10d_adm_app."TEST_SCORE2",umf_10d_adm_app."TEST_DATE2",umf_10d_adm_app."TEST_CODE3",umf_10d_adm_app."TEST_SCORE3",umf_10d_adm_app."TEST_DATE3",umf_10d_adm_app."TEST_CODE4",umf_10d_adm_app."TEST_SCORE4",umf_10d_adm_app."TEST_DATE4",umf_10d_adm_app."TEST_CODE5",umf_10d_adm_app."TEST_SCORE5",umf_10d_adm_app."TEST_DATE5",umf_10d_adm_app."TEST_CODE6",umf_10d_adm_app."TEST_SCORE6",umf_10d_adm_app."TEST_DATE6",umf_10d_adm_app."TEST_CODE7",umf_10d_adm_app."TEST_SCORE7",umf_10d_adm_app."TEST_DATE7",umf_10d_adm_app."ADDITIONAL_TESTS_IND",umf_10d_adm_app."INTERNATIONAL_BIRTH_NATION",umf_10d_adm_app."INTERNATIONAL_LEGAL_NATION",umf_10d_adm_app."INTERNATIONAL_VISA_NUMBER",umf_10d_adm_app."INTERNATIONAL_VISA_TYPE",umf_10d_adm_app."CAMP_CODE",umf_10d_adm_app."PROGRAM_1",umf_10d_adm_app."EGOL_CODE" from <EMAIL> umf_10d_adm_app
;
