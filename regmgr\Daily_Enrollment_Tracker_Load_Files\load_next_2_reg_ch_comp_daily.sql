--------------------------------------------------------
--  DDL for View LOAD_NEXT_2_REG_CH_COMP_DAILY
--------------------------------------------------------

  CREATE OR REPLACE LOAD_NEXT_2_REG_CH_COMP_DAILY as
  with t1 as(
    select
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code,
    sum(um_reg_course_section_daily.total_credit_hours) total_credit_hours
    from um_reg_course_section_daily
    where um_reg_course_section_daily.run_date = trunc(sysdate)
    and um_reg_course_section_daily.term_code = (select um_current_term.next_2_bridge_term from um_current_term)
    
    group by
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code
)
, t2 as(
    select
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code,
    sum(um_reg_course_section_daily.total_credit_hours) total_credit_hours
    from um_reg_course_section_daily
    where um_reg_course_section_daily.day_of_term = (select distinct t1.day_of_term from t1)
    and um_reg_course_section_daily.term_code = (select distinct t1.term_code - 100 from t1)
    
    group by
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code
)
, t3 as(
    select
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code,
    sum(um_reg_course_section_daily.total_credit_hours) total_credit_hours
    from um_reg_course_section_daily
    where um_reg_course_section_daily.day_of_term = (select distinct t1.day_of_term from t1)
    and um_reg_course_section_daily.term_code = (select distinct t1.term_code - 200 from t1)
    
    group by
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code
)
, t4 as(
    select
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code,
    sum(um_reg_course_section_daily.total_credit_hours) total_credit_hours
    from um_reg_course_section_daily
    where um_reg_course_section_daily.day_of_term = (select distinct t1.day_of_term from t1)
    and um_reg_course_section_daily.term_code = (select distinct t1.term_code - 300 from t1)
    
    group by
    um_reg_course_section_daily.day_of_term,
    um_reg_course_section_daily.term_code,
    um_reg_course_section_daily.term_desc,
    um_reg_course_section_daily.college_code,
    um_reg_course_section_daily.college_desc,
    um_reg_course_section_daily.subject_code
)
, td as(
    select 
    td_course_credits.term_code_key,
    td_course_credits.coll_code,
    td_course_credits.coll_desc,
    td_course_credits.subj_code,
    td_course_credits.credit_hours
    
    from td_course_credits
    
    where td_course_credits.term_code_key = (select distinct t1.term_code - 100 from t1)
)
, pop_sel as(
    select
    tr.day_of_term,
    tr.term_code,
    tr.term_desc,
    a1.college_code,
    a1.college_desc,
    a1.subject_code
    from (
        select
        t1.college_code,
        t1.college_desc,
        t1.subject_code
        from t1
        union
        select
        t2.college_code,
        t2.college_desc,
        t2.subject_code
        from t2
        union
        select
        t3.college_code,
        t3.college_desc,
        t3.subject_code
        from t3
        union
        select
        t4.college_code,
        t4.college_desc,
        t4.subject_code
        from t4
        union
        select
        td.coll_code,
        td.coll_desc,
        td.subj_code
        from td
    )a1
    inner join (
        select
        distinct t1.day_of_term,
        t1.term_code,
        t1.term_desc
        from t1    
    )tr on tr.day_of_term is not null
)


select
pop_sel.day_of_term,
pop_sel.term_code,
pop_sel.term_desc,
pop_sel.college_code,
pop_sel.college_desc,
pop_sel.subject_code,
nvl(t1.total_credit_hours, 0) current_total_credit_hours,
nvl(t2.total_credit_hours, 0) prev_total_credit_hours,
nvl(t3.total_credit_hours, 0) prev_2_total_credit_hours,
nvl(t4.total_credit_hours, 0) prev_3_total_credit_hours,
nvl(td.credit_hours, 0) td_total_credit_hours

from pop_sel
left outer join t1 on t1.college_code = pop_sel.college_code
                   and t1.subject_code = pop_sel.subject_code
left outer join t2 on t2.college_code = pop_sel.college_code
                   and t2.subject_code = pop_sel.subject_code
left outer join t3 on t3.college_code = pop_sel.college_code
                   and t3.subject_code = pop_sel.subject_code
left outer join t4 on t4.college_code = pop_sel.college_code
                   and t4.subject_code = pop_sel.subject_code
left outer join td on td.coll_code = pop_sel.college_code
                   and td.subj_code = pop_sel.subject_code

--where t1.college_code = 'AS'
order by
pop_sel.day_of_term,
pop_sel.term_code,
pop_sel.college_code,
pop_sel.college_desc,
pop_sel.subject_code
;
