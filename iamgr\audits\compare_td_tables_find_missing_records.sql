select 
--class_code, 
sum(TOTAL_CREDIT_HOURS_UMF)
from td_student_data
where sd_term_code = '201420'
and REGISTERED_IND = 'Y'
;


select td_term_code,
count (*)
from td_demographic 
where td_term_code >= '201110'
--and registered_ind = 'Y'
group by td_term_code
order by td_term_code desc;

select sd_term_code,
count (*)
from td_student_data
where sd_term_code >= '201110'
and registered_ind = 'Y'
group by sd_term_code
order by sd_term_code desc;

select 
td_demographic.td_term_code,
td_student_data.sd_term_code,
count (td_student_data.sd_pidm) Student_data_count,
count (td_demographic.dm_pidm)demographic_count
from td_student_data 
full outer join td_demographic 
on td_demographic.dm_pidm = td_student_data.sd_pidm
and td_demographic.td_term_code = td_student_data.sd_term_code
where sd_term_code >= '201110'
and td_student_data.registered_ind = 'Y'
group by td_demographic.td_term_code, td_student_data.sd_term_code
order by td_demographic.td_term_code desc


select *
from td_student_data
where not exists (select * from td_demographic where dm_pidm = sd_pidm and td_term_code = sd_term_code)
