/*
This query is designed to pull the FTIAC  and Transfer cohorts and format it 
for the IA_CW_COHORT_ Table.

The rules to be in the cohort are:
  Student is a full time, registered, FTIAC or Transfer, UG in the FAll term
   or the student is a full time or part time, registered, FTIAC, UG in the prior Summer term 
   and the student returned in the Fall term as a full time, registered, continuing, UG, in Fall term
  
This must be used between 2007 and 2009 becaue FTIAC students starting in summer during these
      3 years were not codded as FTIAC in the summer, whereas students starting in Fall 2010 were 
      rolled to the Fall as a student_type_code = 'F' and you will use the other script
*/
--create table IA_CO_ALL_2006 as (
select 
ia_td_student_data.sd_pidm co_pidm,
ia_td_student_data.sd_term_code co_term_code_key,
case                   --SUMMER TERMS 
when popsel.sd_term_code = '200640' and  popsel.student_type_code = 'T' Then 'T'
when popsel.sd_term_code = '200640' and  popsel.student_type_code = 'F' Then 'F'
else ia_td_student_data.ia_student_type_code
end
CO_IA_STUDENT_TYPE_CODE,
ia_td_student_data.FULL_PART_TIME_IND CO_FULL_PART_IND_UMF,
popsel.CO_ETHNICITY,
popsel.co_gender
from
ia_td_student_data
  inner join  
  (
    select
    td_started.sd_pidm,
    td_started.sd_term_code,
    td_started.student_type_code,
    aimsmgr.spbpers.SPBPERS_CITZ_CODE,
    aimsmgr.spbpers.SPBPERS_ETHN_CODE,
    case                   --TRANSFORMS ETHNICITY
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE in ('NC','NO') then 'Nonresident Alien'
   
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    and aimsmgr.spbpers.SPBPERS_ETHN_CODE = 1 then 'Black or African American'
   
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    and aimsmgr.spbpers.SPBPERS_ETHN_CODE = 2 then 'Asian'
   
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    and aimsmgr.spbpers.SPBPERS_ETHN_CODE = 3 then 'American Indian or Alaska Native'
   
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    and aimsmgr.spbpers.SPBPERS_ETHN_CODE = 4 then 'Hispanic or Latino'
   
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    and aimsmgr.spbpers.SPBPERS_ETHN_CODE = 5 then 'White'
   
    when aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    and aimsmgr.spbpers.SPBPERS_ETHN_CODE = 7 then 'Two or more races'
   
    when (aimsmgr.spbpers.SPBPERS_CITZ_CODE not in ('NC','NO') 
    or aimsmgr.spbpers.SPBPERS_CITZ_CODE is NULL) 
    and (aimsmgr.spbpers.SPBPERS_ETHN_CODE = 9 
    or aimsmgr.spbpers.SPBPERS_ETHN_CODE is NULL) then 'Unknown'
    end CO_ETHNICITY,
    
    decode (aimsmgr.spbpers.SPBPERS_SEX, 'F','F','M') CO_GENDER
    
    from td_student_data td_started
    left outer join aimsmgr.spbpers on aimsmgr.spbpers.SPBPERS_PIDM = td_started.sd_pidm
    where primary_level_code = 'UG'
    and registered_ind = 'Y'
    and student_type_code in ('F','T')
    and (
      (
        sd_term_code = '200710'-- fall term code
        --and full_part_time_ind_umf = 'F'
      ) or (
        sd_term_code = '200640'--summer term code
        and exists (
          select 'continued in fall'
          from td_student_data td_continued
          where td_started.sd_pidm = td_continued.sd_pidm
          and td_started.sd_term_code + 70 = td_continued.sd_term_code
          and td_continued.registered_ind = 'Y'
          --and td_continued.full_part_time_ind_umf = 'F'
          and td_continued.primary_level_code = 'UG'
      )
    )  
  )   
  --order by sd_pidm;
  )popsel on popsel.sd_pidm = ia_td_student_data.sd_pidm

where ia_td_student_data.sd_term_code = '200710' 
--)
      
      ;
  
