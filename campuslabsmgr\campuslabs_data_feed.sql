--------------------------------------------------------
--  DDL for View CAMPUSLABS_DATA_FEED
--------------------------------------------------------

  CREATE OR REPLACE FORCE VIEW "CAMPUSLABSMGR"."CAMPUSLABS_DATA_FEED" ("ACTION", "USERNAME", "INSTITUTIONPROVIDEDFIRSTNAME", "INSTITUTIONPROVIDEDLASTNAME", "INSTITUTIONPROVIDEDMIDDLENAME", "L<PERSON><PERSON>FIRS<PERSON>NAME", "L<PERSON>ALLASTNA<PERSON>", "SUFFIX", "CAMPUSEMA<PERSON>", "PREFERREDEMAIL", "CARDID", "SISID", "HOMETOWN", "AFFILIATION", "MO<PERSON><PERSON><PERSON>HON<PERSON>", "DATEOFBIRTH", "SEX", "RACE", "ETHNICITY", "E<PERSON><PERSON><PERSON>ENTSTATUS", "CURRENTTE<PERSON><PERSON><PERSON><PERSON>LED", "CURRENTTERMGPA", "PREVIOUSTERMENROLLED", "PREVIOUSTERMGPA", "CREDITHOURSEARNED", "ANTICIPATEDDATEOFGRADUATION", "CAREERLEVEL", "CLASSSTANDING", "PRIMARYSCHOOLOFENROLLMENT", "DEGREESOUGHT", "MAJOR", "MINOR", "MAJORADVISOR", "OTHERADVISOR", "LOCALRESIDENCYSTATUS", "HOUSINGFACILITY", "INTERNATIONAL", "TRANSFER", "ATHLETE", "ATHLETICPARTICIPATION", "LOCALPHONECOUNTRYCODE", "LOCALPHONE", "LOCALPHONEEXTENSION", "LOCALSTREET1", "LOCALSTREET2", "LOCALSTREET3", "LOCALCITY", "LOCALSTATEPROVINCE", "LOCALPOSTALCODE", "LOCALCOUNTRY", "HOMEPHONECOUNTRYCODE", "HOMEPHONE", "HOMEPHONEEXTENSION", "HOMESTREET1", "HOMESTREET2", "HOMESTREET3", "HOMECITY", "HOMESTATEPROVINCE", "HOMEPOSTALCODE", "HOMECOUNTRY", "ABROADPHONECOUNTRYCODE", "ABROADPHONE", "ABROADPHONEEXTENSION", "ABROADSTREET1", "ABROADSTREET2", "ABROADSTREET3", "ABROADCITY", "ABROADSTATEPROVINCE", "ABROADPOSTALCODE", "ABROADCOUNTRY") AS 
 
  with um_student_data_sel as(
    select
    um_student_data.*,
    row_number() over(partition by um_student_data.sd_pidm order by um_student_data.sd_term_code) order_num
    from um_student_data um_student_data
    where um_student_data.sd_term_code >= (select um_current_term.current_term from um_current_term um_current_term)
    and um_student_data.student_status_code = 'AS'
)
select
'Overwrite' action,
um_demographic.uniqname username,
replace(
    replace(um_demographic.first_name, ',', '') 
, '  ', ' ') institutionprovidedfirstname,
replace(
    replace(um_demographic.last_name, ',', '') 
, '  ', ' ') institutionprovidedlastname,
replace(
    replace(um_demographic.middle_name, ',', '')
, '  ', ' ') institutionprovidedmiddlename,

cast(null as varchar2(255)) legalfirstname,
cast(null as varchar2(64)) legallastname,
um_demographic.name_suffix suffix,
um_demographic.ca_email campusemail,
cast(null as varchar2(64)) preferredemail,
um_demographic.umid cardid,
um_demographic.umid sisid,
cast(null as varchar2(64)) hometown,
cast(null as varchar2(64)) affiliation,
cast(null as varchar2(64)) mobilephone,
cast(null as varchar2(64)) dateofbirth,
case 
    when um_demographic.gender = 'F' then 'Female'
    when um_demographic.gender = 'M' then 'Male'
    else 'Other'
end sex,
cast(null as varchar2(64)) race,
um_demographic.report_ethnicity ethnicity,
case
    when (select count(*) 
          from um_student_data r1
          where r1.sd_pidm = um_student_data.sd_pidm
          and r1.sd_term_code >= (select um_current_term.current_term from um_current_term)
          and um_student_data.registered_ind = 'Y') > 0 then 'Registered'
    else 'Active'
end enrollmentstatus,
um_student_data.sd_term_desc currenttermenrolled,
cast(null as varchar2(64)) currenttermgpa,
cast(null as varchar2(64)) previoustermenrolled,
cast(null as varchar2(64)) previoustermgpa,
cast(null as varchar2(64)) credithoursearned,
to_char(um_student_data.exp_grad_date, 'MM/DD/YYYY') anticipateddateofgraduation,
um_student_data.report_level_desc careerlevel,
um_student_data.class_desc classstanding,
um_student_data.primary_college_desc primaryschoolofenrollment,
cast(null as varchar2(64)) degreesought,
replace(
    replace(um_student_data.primary_major_1_desc, ',', '') 
, '  ', ' ') major,
cast(null as varchar2(64)) minor,
cast(null as varchar2(64)) majoradvisor,
cast(null as varchar2(64)) otheradvisor,
cast(null as varchar2(64)) localresidencystatus,
cast(null as varchar2(64)) housingfacility,
case
    when nvl(um_demographic.intl_ind, 'N') = 'Y' then 'TRUE'
    else 'FALSE'
end international,
cast(null as varchar2(64)) transfer,
cast(null as varchar2(64)) athlete,
cast(null as varchar2(64)) athleticparticipation,
cast(null as varchar2(64)) localphonecountrycode,
case
    when um_demographic.mo_phone_number is not null then um_demographic.mo_area_code || 
 um_demographic.mo_phone_number
    else um_demographic.a1_area_code || um_demographic.a1_phone_number
end localphone,
cast(null as varchar2(64)) localphoneextension,
replace(
    replace(um_demographic.a1_street_line1, ',', '') 
, '  ', ' ') localstreet1,
replace(
    replace(um_demographic.a1_street_line2, ',', '')
, '  ', ' ') localstreet2,

cast(null as varchar2(64)) localstreet3,
um_demographic.a1_city localcity,
um_demographic.a1_state_desc localstateprovince,
um_demographic.a1_zip localpostalcode,
cast(null as varchar2(64)) localcountry,
cast(null as varchar2(64)) homephonecountrycode,
cast(null as varchar2(64)) homephone,
cast(null as varchar2(64)) homephoneextension,
cast(null as varchar2(64)) homestreet1,
cast(null as varchar2(64)) homestreet2,
cast(null as varchar2(64)) homestreet3,
cast(null as varchar2(64)) homecity,
cast(null as varchar2(64)) homestateprovince,
cast(null as varchar2(64)) homepostalcode,
cast(null as varchar2(64)) homecountry,
cast(null as varchar2(64)) abroadphonecountrycode,
cast(null as varchar2(64)) abroadphone,
cast(null as varchar2(64)) abroadphoneextension,
cast(null as varchar2(64)) abroadstreet1,
cast(null as varchar2(64)) abroadstreet2,
cast(null as varchar2(64)) abroadstreet3,
cast(null as varchar2(64)) abroadcity,
cast(null as varchar2(64)) abroadstateprovince,
cast(null as varchar2(64)) abroadpostalcode,
cast(null as varchar2(64)) abroadcountry

from um_demographic um_demographic
inner join um_student_data_sel um_student_data on um_student_data.sd_pidm = um_demographic.dm_pidm
                                               and um_student_data.order_num = 1
-- where um_demographic.dm_pidm in (132931, 8987, 14790, 210985, 210961, 210949, 132147, 125056, 108672)
and um_demographic.ca_email is not null
and um_demographic.deceased_ind is null

order by
um_demographic.last_name,
um_demographic.first_name
;
