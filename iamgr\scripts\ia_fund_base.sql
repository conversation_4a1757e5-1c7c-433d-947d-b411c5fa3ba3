      with pop_sel as(
        select
        /*+ MATERIALIZE */
        spriden.spriden_pidm,
        term_join.stvterm_code,
        term_join.stvterm_fa_proc_yr,
        (select max(rorsapr.rorsapr_term_code) 
         from rorsapr_ext rorsapr 
         where rorsapr.rorsapr_pidm = spriden.spriden_pidm
         and rorsapr.rorsapr_term_code <= term_join.stvterm_code
        ) max_rorsapr_term_code
        from spriden_ext spriden
        inner join(
          select
          stvterm.stvterm_code,
          stvterm.stvterm_fa_proc_yr
          from stvterm_ext stvterm
        )term_join on term_join.stvterm_code >= (select max(stvterm.stvterm_code) past_term_codes
                                          from stvterm_ext stvterm 
                                          where stvterm.stvterm_end_date <= add_months(sysdate, -120))
                   and term_join.stvterm_code <= (select min(stvterm.stvterm_code) future_term_codes
                                          from stvterm_ext stvterm 
                                          where stvterm.stvterm_end_date >= add_months(sysdate, 12))
        where spriden.spriden_change_ind is null
        and not exists(select 'has a dup flag'
                       from sprhold_ext sprhold
                       where sprhold.sprhold_hldd_code = 'DP'
                       and sprhold.sprhold_pidm = spriden.spriden_pidm)
        and (exists(select 1
                    from spbpers_ext spbpers
                    where spbpers.spbpers_pidm = spriden.spriden_pidm)
             or exists(select 1
                       from spraddr_ext spraddr
                       where spraddr.spraddr_pidm = spriden.spriden_pidm)
             or exists(select 1
                       from goremal_ext goremal
                       where goremal.goremal_pidm = spriden.spriden_pidm)
             or exists(select 1
                       from gorvisa_ext gorvisa
                       where gorvisa.gorvisa_pidm = spriden.spriden_pidm)
             or exists(select 1
                       from gobintl_ext gobintl
                       where gobintl.gobintl_pidm = spriden.spriden_pidm)
             or exists(select 1
                       from sibinst_ext sibinst
                       where sibinst_pidm = spriden.spriden_pidm))
        --and spriden.spriden_pidm = 91183
        order by spriden.spriden_pidm, term_join.stvterm_code
      )
      select
      pop_sel.spriden_pidm,
      pop_sel.stvterm_code,
      (select /*+ FIRST_ROWS */ stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = pop_sel.stvterm_code) stvterm_desc,
      pop_sel.stvterm_fa_proc_yr,
      case when rpratrm_join.rpratrm_fund_code is null then 'NONE' else rpratrm_join.rpratrm_fund_code end,
      case when rpratrm_join.rfrbase_fund_title is null then 'NONE' else rpratrm_join.rfrbase_fund_title end,
      nvl(rpratrm_join.rpratrm_paid_amt, 0),
      nvl(rpratrm_join.rpratrm_offer_amt, 0),
      nvl(rpratrm_join.rpratrm_accept_amt, 0),
      case when rpratrm_join.rfrbase_fsrc_code is null then 'NONE' else rpratrm_join.rfrbase_fsrc_code end,
      case when rpratrm_join.rfrbase_ftyp_code is null then 'NONE' else rpratrm_join.rfrbase_ftyp_code end,
      rpratrm_join.rfrbase_detail_code
      from pop_sel
      left outer join(
        select
        rpratrm.rpratrm_pidm,
        rpratrm.rpratrm_aidy_code,
        rpratrm.rpratrm_fund_code,
        rpratrm.rpratrm_period,
        rpratrm.rpratrm_paid_amt,
        rpratrm.rpratrm_offer_amt,
        rpratrm.rpratrm_accept_amt,
        rfrbase_join.rfrbase_fund_title,
        rfrbase_join.rfrbase_fsrc_code,
        rfrbase_join.rfrbase_ftyp_code,
        rfrbase_join.rfrbase_detail_code
        from aimsmgr.rpratrm_ext rpratrm
        inner join(
          select
          rfrbase.rfrbase_fund_code,
          rfrbase.rfrbase_fund_title,
          rfrbase.rfrbase_fsrc_code,
          rfrbase.rfrbase_ftyp_code,
          rfrbase.rfrbase_detail_code
          from aimsmgr.rfrbase_ext rfrbase
        )rfrbase_join on rfrbase_join.rfrbase_fund_code = rpratrm.rpratrm_fund_code
      )rpratrm_join on rpratrm_join.rpratrm_pidm = pop_sel.spriden_pidm
                 and rpratrm_join.rpratrm_period = pop_sel.stvterm_code  
      order by pop_sel.spriden_pidm, pop_sel.stvterm_code;


grant select on rpratrm_ext to iamgr;

