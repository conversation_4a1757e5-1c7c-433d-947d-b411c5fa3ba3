CREATE OR <PERSON><PERSON>LACE VIEW IA_CO_COMPETITOR_STUDENT_DATA
AS
  (SELECT POPSEL.CO_PIDM,
    POPSEL.CO_TERM_CODE_KEY,
    POPSEL.SCND_FALL_PERSIST,
    POPSEL.COLLEGE_ATTEND_ORDER,
    POPSEL.COLLEGE_NAME,
    POPSEL.ENROLLMENT_MAJOR,
    COUNT (POPSEL.CO_PIDM)PIDM_COUNT
  FROM
    ( WITH DP1 AS
    (SELECT CO.CO_PIDM,
      CO.CO_TERM_CODE_KEY,
      CO.SCND_FALL_PERSIST,
--      NSC.COLLEGE_ATTEND_ORDER,
      NSC.COLLEGE_NAME,
      NSC.ENROLLMENT_MAJOR,
      ROW_NUMBER() OVER(PARTITION BY NSC.CO_PIDM, NSC.COLLEGE_NAME order by NSC.TERM_CODE_ENROLLMENT_BEGIN) college_attend_order
    FROM CONTMGR.CO_PERSIST_NSC_TBL CO
    LEFT JOIN CONTMGR.CO_NSC_STUDENT_DATA_TBL NSC
    ON CO.CO_PIDM                      = NSC.CO_PIDM
    AND CO.CO_TERM_CODE_KEY            = NSC.CO_TERM_CODE_KEY
    WHERE CO.CO_TERM_CODE_KEY         >= 201710
    AND NSC.TERM_CODE_ENROLLMENT_BEGIN = CO.CO_TERM_CODE_KEY + 100
    AND TERM_CODE_ENROLLMENT_BEGIN    IS NOT NULL
    AND SCND_FALL_PERSIST             IN ('OTHER 4 YEAR RETAINED',
    'OTHER 4 YEAR GRADUATED','OTHER 2 YEAR RETAINED','OTHER 2 YEAR GRADUATED')
    )
  SELECT DP1.CO_PIDM,
    DP1.CO_TERM_CODE_KEY,
    DP1.SCND_FALL_PERSIST,
    DP1.COLLEGE_NAME,
    DP1.ENROLLMENT_MAJOR,
    MIN (DP1.COLLEGE_ATTEND_ORDER) COLLEGE_ATTEND_ORDER
  FROM DP1
  GROUP BY DP1.CO_PIDM,
    DP1.CO_TERM_CODE_KEY,
    DP1.SCND_FALL_PERSIST,
    DP1.COLLEGE_NAME,
    DP1.ENROLLMENT_MAJOR
    ) POPSEL
  GROUP BY POPSEL.CO_PIDM,
    POPSEL.CO_TERM_CODE_KEY,
    POPSEL.SCND_FALL_PERSIST,
    POPSEL.COLLEGE_ATTEND_ORDER,
    POPSEL.COLLEGE_NAME,
    POPSEL.ENROLLMENT_MAJOR
  ) ;