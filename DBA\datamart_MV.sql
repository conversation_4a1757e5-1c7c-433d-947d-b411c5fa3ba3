/*******************************************************************************
Materialized View (MV) DBA
by <PERSON> on 042922
*******************************************************************************/
--Drop a MV
DROP MATERIALIZED VIEW reg_stud_trans_mv;

--Create MV with refresh schedule
CREATE MATERIALIZED VIEW reg_stud_trans_mv
 REFRESH
ON DEMAND
  START WITH TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
  NEXT TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM') + 1 WITH ROWID
AS
 SELECT
  *
 FROM
  reg_stud_trans;
  
--<PERSON><PERSON> MV with refresh schedule  
CREATE MATERIALIZED VIEW reg_stud_trans_mv
REFRESH
ON DEMAND
 START WITH (SYSDATE) 
-- START WITH TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
 NEXT (SYSDATE + 1) WITH ROWID
AS SELECT * FROM reg_stud_trans;

--Force a refresh of MV

EXEC DBMS_MVIEW.refresh('reg_stud_trans_MV');
EXEC DBMS_MVIEW.refresh('UMFLINT_PERSIST_EDU_PROG_MV');
EXEC dbms_mview.refresh_all_mviews;


--Identify last refresh of Materialized View
SELECT 
  OWNER, 
  MVIEW_NAME, 
  to_char(last_refresh_date, 'yyyymmddhh24miss') LAST_REFRESH_DATE,
  to_char(last_refresh_date, 'MM-DD-YYYY HH:MI AM') LAST_REFRESH_DAY
FROM all_mviews
WHERE owner = 'REGMGR'
AND mview_name = 'REG_STUD_TRANS_MV';

--Alter refresh schedule MV
ALTER MATERIALIZED VIEW reg_stud_trans_mv 
REFRESH
ON COMMIT --DEMAND
  START WITH TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
  NEXT TO_DATE('05-20-2022 06:00 AM','MM-DD-YYYY HH:MI AM') + 1
  ;
  

SELECT
 owner,
 object_name,
 object_type,
 status
FROM
 dba_objects
WHERE
 status = 'INVALID';

SELECT
 owner,
 object_name,
 object_type,
 status
FROM
 dba_objects
WHERE
  status = 'INVALID'
 AND object_type = 'SYNONYM';
 
alter public synonym reg_stud_trans_mv compile;



  
--Create MV with refresh schedule
CREATE MATERIALIZED VIEW <NAME_MV>
 REFRESH
ON DEMAND
  START WITH TO_DATE('01-01-2023 06:00 AM','MM-DD-YYYY HH:MI AM')
  NEXT TO_DATE('01-02-2023 06:00 AM','MM-DD-YYYY HH:MI AM') + 1 WITH ROWID
AS
 SELECT...;
  

  