
TRUNCATE TABLE IA_FY_HLC_STUDY_TBL_BAC;


INSERT INTO IA_FY_HLC_STUDY_TBL_BAC
SELECT * FROM IA_FY_HLC_STUDY_TBL;
COMMIT;

select count (*) from IA_FY_COHORT_POP_UNDUP_TBL;
SELECT COUNT (*) FROM IA_FY_HLC_STUDY_TBL;
SELECT COUNT (*) FROM IA_FY_HLC_STUDY_TBL_BAC;

TRUNCATE TABLE IA_FY_HLC_STUDY_TBL;

INSERT INTO IA_FY_HLC_STUDY_TBL

--CREATE TABLE IA_FY_HLC_STUDY_TBL AS
(
SELECT
  /***************************************
  COHORT UN-DUPED
  ***************************************/
  FY.CO_PIDM,
  SD.UMID,
  FY.FY_TERM_CODE,
  FY.CO_IA_STUDENT_TYPE_CODE,
  FY.CO_FULL_PART_IND_UMF,
  FY.CO_ETHNICITY,
  FY.CO_GENDER,
  /***************************************
  COHORT DEMOGRAPHIC
  ***************************************/
  SD.AGE,
  SD.HSCH_RANK,
  SD.HSCH_SIZE,
  SD.HSCH_GPA,
  SD.PRIMARY_COLLEGE_CODE,
  SD.PRIMARY_DEGREE_CODE,
  SD.HSCH_DESC,
  SD.HSCH_CITY,
  SD.HSCH_STATE,
  SD.HSCH_ZIP,
  SD.PCOL_DESC_1              AS PCOL_DESC,
  SD.PCOL_GPA_TRANSFERRED_1   AS PCOL_GPA,
  SD.PCOL_DEGC_CODE_1         AS PCOL_DEGC_CODE,
  SD.PCOL_HOURS_TRANSFERRED_1 AS PCOL_HOURS,
  NVL( SD.VETERAN_IND,'N') AS VETERAN_IND,
  SD.CLASS_CODE,
  SD.PRIMARY_ADMIT_CODE,
  SD.RESIDENCY_CODE,
  SD.PRIMARY_MAJOR_1,
  NVL(SD.HOUSING_IND,'N') HOUSING_IND,
  SD.TOTAL_CREDIT_HOURS_UMF,
    CASE
    WHEN  MOST_RECENT.DECEASED_DATE IS NOT NULL THEN 'YES'
    ELSE 'NO'
  END DECEASED_IND,
  MOST_RECENT.OVERALL_GPA MOST_RECENT_GPA,
  MOST_RECENT.MOST_RECENT_ASTD_CODE,
  MOST_RECENT.STUDENT_STATUS_CODE
FROM IA_FY_COHORT_POP_UNDUP_TBL FY
LEFT JOIN IA_TD_STUDENT_DATA SD
ON SD.SD_PIDM       = FY.CO_PIDM
AND SD.SD_TERM_CODE = FY.FY_TERM_CODE
LEFT JOIN
  (SELECT SD_PIDM,
    TERM_GPA,
    SD_TERM_CODE,
    OVERALL_GPA,
    MOST_RECENT_ASTD_CODE,
    STUDENT_STATUS_CODE,
    OVERALL_HOURS_EARNED,
    UMID,
    DECEASED_DATE
  FROM UM_STUDENT_DATA T
  WHERE SD_TERM_CODE =
    (SELECT MAX(SD_TERM_CODE)
    FROM UM_STUDENT_DATA
    WHERE SD_PIDM          = T.SD_PIDM
    AND PRIMARY_LEVEL_CODE = 'UG'
    )
  ) MOST_RECENT
ON MOST_RECENT.SD_PIDM = FY.CO_PIDM




--WHERE FY.CO_PIDM = '152270'
) 
;
