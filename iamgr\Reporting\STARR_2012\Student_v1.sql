select
spriden.spriden_id as "SchoolAssignedPersonID",

goradid_join.goradid_additional_id as "AgencyAssignedID",

to_char(spbpers_join.spbpers_birth_date, 'yyyy-mm-dd') as "BirthDate",

substr(spriden.spriden_first_name, 1, 35) as "FirstName",

substr(spriden.spriden_mi, 1, 35) as "MiddleName",

substr(spriden.spriden_last_name, 1, 35) as "LastName",

spbpers_join.spbpers_name_suffix as "NameSuffix",

substr(previous_last_name_join.spriden_last_name, 1, 35) as "PreviousLastName",

primary_hsch_join.stvsbgi_desc as "OrganizationName",

substr(null, 1, 1) as "MutuallyDefined", 

to_char(primary_hsch_join.sorhsch_graduation_date, 'yyyy') As "GraduationYear",

substr(null, 1, 1) as "DistrictEntityCode",

case 
when a1_join.spraddr_stat_code = '00' then null
when a1_join.spraddr_stat_code = 'FR' then null
else a1_join.spraddr_stat_code
end as "StateProvinceCode",

case 
  when in_rh_join.slrrasg_pidm <> null then 'US'
  else case 
         when a1_join.STVNATN_EDI_EQUIV is not null then a1_join.STVNATN_EDI_EQUIV
         else 'US'
       end
end as "CountryCode", -- , CEPI_GetCountry(spriden.spriden_pidm) AS 'CountryCode'

case spbpers_join.spbpers_sex 
  when 'F' then 'Female'
  when 'M' then 'Male' 
  else 'Unreported'
end as "GenderCode",

case sgbstdn_join.sgbstdn_resd_code
  when 'N' then 'NonResident'
  when 'R' then 'InState'
  else 'NotReported'
end as "ResidencyStatusCode",

case spbpers_join.spbpers_citz_code
  when 'NC' then 'International'
  when 'NO' then 'International'
  else 'U.S. resident'
end as "CountryIndicator",

case spbpers_join.spbpers_ethn_cde
  when '2' then 'Hispanic/Latino'
  when '1' then 'Non-Hispanic/Non-Latino'
  else null
end as "EthnicityCode",

case
  when (select count(*) from gorprac g1 where g1.gorprac_pidm = spriden.spriden_pidm) > 1 then 'Two or more races'
  when gorprac_join.gorprac_race_cde = 1 then 'American Indian or Alaska Native'
  when gorprac_join.gorprac_race_cde = 2 then 'Asian'
  when gorprac_join.gorprac_race_cde = 3 then 'Black or African American'
  when gorprac_join.gorprac_race_cde = 4 then 'Native Hawaiian or Other Pacific Islander'
  when gorprac_join.gorprac_race_cde = 5 then 'White/Caucasian'
  else 'Unknown'
end as "RaceCode",

case
when (select count(*)
      from rpratrm
      where rpratrm_fund_code = '1PELL'
      and rpratrm_paid_amt > 0
      and rpratrm.rpratrm_term_code >= start_term.start_term_code
      and rpratrm.rpratrm_term_code <= end_term.end_term_code
      and rpratrm.rpratrm_aidy_code = (select stvterm.stvterm_fa_proc_yr
                                       from stvterm
                                       where stvterm.stvterm_code = rpratrm.rpratrm_term_code)
      and rpratrm_pidm = spriden.spriden_pidm) > 0 then 'PellEligible'
else 'NotPellEligible' 
end as "EconomicallyDisadvantaged"

from spriden
-- start term
inner join(
  select 
  max(stvterm.STVTERM_CODE) as start_term_code
  from stvterm
  where stvterm.stvterm_code = '201130'
) start_term on start_term.start_term_code <> '999999'
-- end term
inner join(
  select max(stvterm.STVTERM_CODE) as end_term_code
  from stvterm
  where stvterm.STVTERM_CODE = '201220'
) end_term on end_term.end_term_code <> '999999'
-- has to have a UID
inner join(
  select 
  goradid.GORADID_PIDM,
  goradid.GORADID_ADDITIONAL_ID
  from goradid
  where goradid.GORADID_ADID_CODE = 'UIC'
  and goradid.GORADID_ADDITIONAL_ID is not null
) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
-- sgbstdn from the end term
inner join(
  select
  sgbstdn.sgbstdn_pidm,
  sgbstdn.sgbstdn_term_code_eff,
  sgbstdn.sgbstdn_resd_code
  from sgbstdn
)sgbstdn_join on sgbstdn_join.SGBSTDN_PIDM = spriden.SPRIDEN_PIDM
              and sgbstdn_join.SGBSTDN_TERM_CODE_EFF = (select max(s2.sgbstdn_term_code_eff)
                                                        from sgbstdn s2
                                                        where s2.sgbstdn_pidm = sgbstdn_join.sgbstdn_pidm
                                                        and s2.sgbstdn_term_code_eff <= end_term.end_term_code)
-- spbpers on not dead
inner join(
  select
  spbpers.spbpers_pidm,
  spbpers.spbpers_ethn_cde,
  spbpers.spbpers_citz_code,
  spbpers.spbpers_sex,
  spbpers.spbpers_name_suffix,
  spbpers.spbpers_birth_date
  from spbpers
  where spbpers.SPBPERS_DEAD_IND is null
)spbpers_join on spbpers_join.SPBPERS_PIDM = spriden.SPRIDEN_PIDM
-- a1 address
left outer join(
  select 
  spraddr.spraddr_pidm,
  spraddr.spraddr_stat_code,
  stvnatn.STVNATN_EDI_EQUIV
  from spraddr
  left outer join stvnatn on stvnatn.STVNATN_CODE = spraddr.spraddr_natn_code
  where spraddr.spraddr_atyp_code = 'A1'
  and ((trunc(spraddr.spraddr_from_date) <= trunc(sysdate) and spraddr.spraddr_to_date is null) 
        or (trunc(spraddr.spraddr_from_date) < trunc(sysdate) and trunc(spraddr.spraddr_to_date) >= trunc(sysdate)))
  and spraddr.spraddr_status_ind is null
) a1_join on a1_join.spraddr_pidm = spriden.spriden_pidm
-- if they are in the residence hall
left outer join(
  select
  slrrasg.SLRRASG_PIDM,
  slrrasg.SLRRASG_TERM_CODE
  from slrrasg
  where slrrasg.SLRRASG_ASCD_CODE = 'AC'
  and slrrasg.SLRRASG_ROOM_NUMBER != '0000'
) in_rh_join on in_rh_join.slrrasg_pidm = spriden.spriden_pidm
             and in_rh_join.slrrasg_term_code = end_term.end_term_code
-- previous last name
left outer join(
  select
  s3.spriden_pidm,
  s3.spriden_last_name,
  row_number() over (partition by s3.spriden_pidm order by s3.spriden_activity_date desc) as order_num
  from spriden s3, spriden s2
  where s3.SPRIDEN_CHANGE_IND = 'N'
  and s3.spriden_pidm = s2.spriden_pidm
  and s2.SPRIDEN_CHANGE_IND is null
  and s3.spriden_last_name <> s2.spriden_last_name
) previous_last_name_join on previous_last_name_join.spriden_pidm = spriden.spriden_pidm 
                          and upper(previous_last_name_join.spriden_last_name) <> upper(spriden.spriden_last_name) 
                          and previous_last_name_join.order_num = 1
-- primary high school
left outer join(
  select
  sorhsch.SORHSCH_PIDM,
  stvsbgi.STVSBGI_DESC,
  sorhsch.sorhsch_sbgi_code,
  sorhsch.sorhsch_graduation_date,
  row_number() over(partition by sorhsch.SORHSCH_PIDM order by sorhsch.SORHSCH_GRADUATION_DATE, sorhsch.SORHSCH_SBGI_CODE) as order_num
  from sorhsch, stvsbgi
  where sorhsch.sorhsch_sbgi_code = stvsbgi.stvsbgi_code
) primary_hsch_join on primary_hsch_join.sorhsch_pidm = spriden.spriden_pidm
                    and primary_hsch_join.order_num = 1
-- gorprac race
left outer join(
  select
  gorprac.GORPRAC_PIDM,
  gorprac.GORPRAC_RACE_CDE,
  row_number() over(partition by gorprac.GORPRAC_PIDM order by gorprac.GORPRAC_RACE_CDE) as order_num
  from gorprac
) gorprac_join on gorprac_join.GORPRAC_PIDM = spriden.SPRIDEN_PIDM
               and gorprac_join.order_num = 1

where spriden.spriden_change_ind is null
and exists (select
            'has course work in their primary level in these terms'
            from shrtgpa
            inner join(
              select
              sgbstdn.sgbstdn_pidm,
              sgbstdn.sgbstdn_term_code_eff,
              sgbstdn.sgbstdn_levl_code
              from sgbstdn
            ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                           and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                     from sgbstdn sgbstdn_1
                                                                     where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                     and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                           and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
            where shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
            and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
            and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
--and spriden.SPRIDEN_PIDM in (65753)
