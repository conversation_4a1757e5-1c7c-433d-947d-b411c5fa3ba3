/*
USE CLEANING METHODOLOGY LAID OUT IN CHORT CLEANING SCRIPT THEN UNION DATA IN
TEMP WITH IA_FY_COHORT_POP_UNDUP_TBL
*/

/*Insert New FY Cohort Set*/
select count(*) from IA_CW_FY_TERM_BAC;
select count(*) from IA_CW_FY_TERM;

truncate table IA_CW_FY_TERM_BAC;

insert into IA_CW_FY_TERM_BAC
select * from IA_CW_FY_TERM;

select distinct fy_co_term_code from IA_FY_COHORT_POP_UNDUP_TBL;

INSERT INTO IA_FY_COHORT_POP_UNDUP_TBL
WITH DP1 AS ( 
  SELECT  
  row_number() over (partition by sd_pidm order by fy_term_code desc) dup_ind,
   FY.FY,
    to_date('01-SEP-'
  ||(to_number(SUBSTR(FY_TERM_CODE,3,2))-1),'DD-MON-YY') start_date,
    SD.SD_PIDM CO_PIDM,
    FY.FY_CO_TERM_CODE,
     FY.FY_TERM_CODE,
    SD.IA_STUDENT_TYPE_CODE CO_IA_STUDENT_TYPE_CODE,
    SD.FULL_PART_TIME_IND_UMF CO_FULL_PART_IND_UMF,
    DM.REPORT_ETHNICITY CO_ETHNICITY,
    DM.GENDER CO_GENDER,
    SD.PRIMARY_DEGREE_CODE

  FROM TD_STUDENT_DATA SD
  INNER JOIN TD_DEMOGRAPHIC DM
  ON DM.DM_PIDM       = SD.SD_PIDM
  AND DM.TD_TERM_CODE = SD.SD_TERM_CODE
  LEFT JOIN IA_CW_FY_TERM FY
  ON SD.SD_TERM_CODE           = FY.FY_TERM_CODE
  WHERE SD.PRIMARY_LEVEL_CODE  = 'UG'
  AND SD.REGISTERED_IND        = 'Y'
  AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
  AND FY.FY   = '23-24'
     )
  SELECT 
--  dup_ind,
  FY,
  CO_PIDM,
  FY_CO_TERM_CODE,
  FY_TERM_CODE,
  CO_IA_STUDENT_TYPE_CODE,
  CO_FULL_PART_IND_UMF,
  CO_GENDER,
  PRIMARY_DEGREE_CODE,
  START_DATE,
  CO_ETHNICITY
  from dp1
  where dup_ind = 1
  order by 3
  ;
  
  commit;

  select  --1601
  fy, 
  count (*) headcount
  from IA_FY_COHORT_POP_UNDUP_TBL
  group by fy
  order by 1
  
  ;