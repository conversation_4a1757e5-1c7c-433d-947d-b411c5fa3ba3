/**********************************************************************************
This script builds the cohort FA data based on a snapshot of the finaid status
of the studnent at tenth day.  

INSTERT <PERSON>ATA IN FALL FOR NEW COHORT
**********************************************************************************/

TRUNCATE TABLE IA_COHORT_FA_BAC;

INSERT INTO IA_COHORT_FA_BAC
SELECT * FROM IA_COHORT_FA;

SELECT COUNT (*) FROM IA_COHORT_FA_BAC;
SELECT COUNT (*) FROM IA_COHORT_FA;
commit;

INSERT INTO IA_COHORT_FA

SELECT CO.co_pidm,
  CO.co_term_code_key,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM     = CO.co_pidm
    AND FB.FA_TERM_CODE  = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE in ('SCHL','GRNT')
    AND FB.FA_FUND_SOURCE in ('INST','OTHR')
    AND FB.FA_FUND_CODE NOT LIKE ('PRIV%')
    AND FB.FA_PAID_AMT   >0
    AND FA_PAID_AMT                  IS NOT NULL
    )
  ), 'N') ins_grant_ind,--includes grants and scholorships
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM     = CO.co_pidm
  AND FB.FA_TERM_CODE  = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE in ('SCHL','GRNT')
    AND FB.FA_FUND_SOURCE in ('INST','OTHR')
    AND FB.FA_FUND_CODE NOT LIKE ('PRIV%')
  ),0) ins_grant_amt,--includes grants and scholorships
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM     = CO.co_pidm
    AND FB.FA_TERM_CODE  = CO.CO_TERM_CODE_KEY
    AND (FB.FA_FUND_CODE IN ('UMFSAW','2TRUBL')
    OR FB.FA_FUND_CODE LIKE ('2FMS%') 
    OR FB.FA_FUND_CODE LIKE ('2FT%'))
--    AND FB.FA_FUND_CODE IN ('2FMS','2FMS3','2FMS5','2FMS7','UMFSAW')--check for changes in the future
    AND FB.FA_PAID_AMT   >0
    AND FA_PAID_AMT                  IS NOT NULL
    )
  ), 'N') freshman_merit_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM     = CO.co_pidm
  AND FB.FA_TERM_CODE  = CO.CO_TERM_CODE_KEY
  AND (FB.FA_FUND_CODE IN ('UMFSAW','2TRUBL')
  OR FB.FA_FUND_CODE LIKE ('2FMS%') 
  OR FB.FA_FUND_CODE LIKE ('2FT%'))
--  AND FB.FA_FUND_CODE IN ('2FMS','2FMS3','2FMS5','2FMS7','UMFSAW')--check for changes in the future
  ),0) freshman_merit_amt,  
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM     = CO.co_pidm
    AND FB.FA_TERM_CODE  = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE in ('SCHL','GRNT')
    AND FB.FA_FUND_SOURCE IN ('FDRL','STAT')
    OR (FB.FA_FUND_TYPE_CODE = 'SCHL' 
    AND FB.FA_FUND_SOURCE = 'OTHR' 
    AND FB.FA_FUND_CODE LIKE ('PRIV%'))
    AND FB.FA_PAID_AMT   >0
    AND FA_PAID_AMT                  IS NOT NULL
    )
  ), 'N') outside_grant_ind,--includes grants and scholorships
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM     = CO.co_pidm
    AND FB.FA_TERM_CODE  = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE in ('SCHL','GRNT')
    AND FB.FA_FUND_SOURCE IN ('FDRL','STAT')
    OR (FB.FA_FUND_TYPE_CODE = 'SCHL' 
    AND FB.FA_FUND_SOURCE = 'OTHR' 
    AND FB.FA_FUND_CODE LIKE ('PRIV%'))
  ),0) outside_grant_amt,--includes grants and scholorships
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM    = CO.co_pidm
    AND FB.FA_TERM_CODE = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_CODE = '1PELL'
    AND FB.FA_PAID_AMT  >0
    AND FA_PAID_AMT                 IS NOT NULL
    )
  ), 'N') pell_grant_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM    = CO.co_pidm
  AND FB.FA_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND FB.FA_FUND_CODE = '1PELL'
  ),0) pell_grant_amt,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM    = CO.co_pidm
    AND FB.FA_TERM_CODE = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_CODE = '1SUBLN'
    AND FB.FA_PAID_AMT  >0
    AND FA_PAID_AMT                 IS NOT NULL
    )
  ), 'N') fed_subsidized_loan_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM    = CO.co_pidm
  AND FB.FA_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND FB.FA_FUND_CODE = '1SUBLN'
  ),0) fed_subsidized_loan_amt,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM         = CO.co_pidm
    AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE = 'LOAN'
    AND FB.FA_PAID_AMT       >0
    AND FA_PAID_AMT                      IS NOT NULL
    )
  ), 'N') loan_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM         = CO.co_pidm
  AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
  AND FB.FA_FUND_TYPE_CODE = 'LOAN'
  ),0) loan_amt,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM         = CO.co_pidm
    AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE = 'WORK'
    AND FB.FA_PAID_AMT       >0
    AND FA_PAID_AMT                      IS NOT NULL
    )
  ), 'N') work_study_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM         = CO.co_pidm
  AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
  AND FB.FA_FUND_TYPE_CODE = 'WORK'
  ),0) work_study_amt,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM         = CO.co_pidm
    AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE = 'GRNT'
    AND FB.FA_PAID_AMT       >0
    AND FA_PAID_AMT                      IS NOT NULL
    )
  ), 'N') grant_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM         = CO.co_pidm
  AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
  AND FB.FA_FUND_TYPE_CODE = 'GRNT'
  ),0) grant_amt,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE_TBL FB
    WHERE FB.FA_PIDM         = CO.co_pidm
    AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_TYPE_CODE = 'SCHL'
    AND FB.FA_PAID_AMT       >0
    AND FA_PAID_AMT                      IS NOT NULL
    )
  ), 'N') scholorship_ind,
  NVL(
  (SELECT SUM(FA_OFFER_AMT)
  FROM IA_FA_FUND_BASE_TBL FB
  WHERE FB.FA_PIDM         = CO.co_pidm
  AND FB.FA_TERM_CODE      = CO.CO_TERM_CODE_KEY
  AND FB.FA_FUND_TYPE_CODE = 'SCHL'
  ),0) scholorship_amt
FROM IA_COHORT_POP_UNDUP_TBL CO
WHERE CO.CO_TERM_CODE_KEY = 
              (select current_term from um_current_term) --UPDTE THIS IN FALL
ORDER BY 1,2

;