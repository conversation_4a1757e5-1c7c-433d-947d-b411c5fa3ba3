JOIN
 UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM IN UM_DEMOGRAPHIC TO MULTIPLE
UM_ADMISSIONS_APPLICANT.UM_ADMISSIONS_APPLICANT.AD_PIDM
IN UM_ADMISSIONS_APPLICANT TAG J2 AS J2
END
JOIN
J2.UM_ADMISSIONS_APPLICANT.AD_PIDM IN UM_DEMOGRAPHIC TO MULTIPLE
UM_EMAS.UM_EMAS.STUDENTNO_PIDM IN UM_EMAS TAG J3 AS J3
END
DEFINE FILE UM_DEMOGRAPHIC ADD
REPORTYEAR/ A4=SUBSTR(6, J2.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY , 1, 4, 4, 'A4');
END
TABLE FILE UM_DEMOGRAPHIC
SUM 
     MAX.J2.UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY AS 'MaxTerm'
     MAX.J2.UM_ADMISSIONS_APPLICANT.APPL_NO AS 'MaxApplNo'
BY  LOWEST UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.REPORTYEAR
BY  LOWEST J3.UM_EMAS.ISOURCE_DESC
BY  LOWEST J3.UM_EMAS.ST_INT_LVL_DESC
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.PRIMARY_PROGRAM_DESC
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.PRIMARY_MAJOR_1_DESC
BY  J3.UM_EMAS.FA_APP
BY  J3.UM_EMAS.STAGE_200_DATE
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.ENROLLED_IND
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.ADMT_DESC
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_DESC_1
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_DESC_2
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.REG_IND_WITH_NONACADEMIC
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_CODE_1
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_CODE_3
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_CODE_4
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_CODE_2
BY  LOWEST J2.UM_ADMISSIONS_APPLICANT.APDC_CODE_5
WHERE ( J2.UM_ADMISSIONS_APPLICANT.REPORTYEAR EQ '&YearToAnalyze' ) AND ( J3.UM_EMAS.EMAS_INSTANCE EQ 'GR' ) AND ( J2.UM_ADMISSIONS_APPLICANT.STYP_CODE LIKE 'N' );
ON TABLE SET PAGE-NUM NOLEAD 
ON TABLE SET BYDISPLAY ON 
ON TABLE NOTOTAL
ON TABLE HOLD AS ANALYSISDATA FORMAT FOCUS
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
TYPE=DATA,
     COLUMN=N17,
     BACKCOLOR='WHITE',
$
TYPE=REPORT,
     COLUMN=N4,
     WRAP=6.000000,
$
TYPE=REPORT,
     COLUMN=N3,
     WRAP=6.000000,
$
ENDSTYLE
END
DEFINE FILE ANALYSISDATA ADD
EnrolledCount/I5=IF ANALYSISDATA.SEG01.ENROLLED_IND EQ 'Y' THEN 1 ELSE 0;
FallAppCount/I5=IF EDIT( ANALYSISDATA.SEG01.MAXTERM,'$$$$99') EQ '10' THEN 1 ELSE 0;
SpringAppCount/I5=IF EDIT( ANALYSISDATA.SEG01.MAXTERM,'$$$$99') EQ '30' THEN 1 ELSE 0;
SummerAppCount/I5=IF EDIT( ANALYSISDATA.SEG01.MAXTERM,'$$$$99') EQ '40' THEN 1 ELSE 0;
WinterAppCount/I5=IF EDIT( ANALYSISDATA.SEG01.MAXTERM,'$$$$99') EQ '20' THEN 1 ELSE 0;
EverAdmitted/I5=IF ( ANALYSISDATA.SEG01.APDC_CODE_1 LIKE 'A%' OR ANALYSISDATA.SEG01.APDC_CODE_2 LIKE 'A%' OR ANALYSISDATA.SEG01.APDC_CODE_3 LIKE 'A%' OR ANALYSISDATA.SEG01.APDC_CODE_4 LIKE 'A%' OR ANALYSISDATA.SEG01.APDC_CODE_5 LIKE 'A%' ) THEN 1 ELSE 0;
FallEverAdmittedCount/I5=IF EverAdmitted EQ 1 AND FallAppCount EQ 1 THEN 1 ELSE 0;
SpringEverAdmittedCount/I5=IF EverAdmitted EQ 1 AND SpringAppCount EQ 1 THEN 1 ELSE 0;
SummerEverAdmittedCount/I5=IF EverAdmitted EQ 1 AND SummerAppCount EQ 1 THEN 1 ELSE 0;
WinterEverAdmittedCount/I5=IF EverAdmitted EQ 1 AND WinterAppCount EQ 1 THEN 1 ELSE 0;
FallEnrolledCount/I5=IF EnrolledCount EQ 1 AND FallAppCount EQ 1 THEN 1 ELSE 0;
SpringEnrolledCount/I5=IF EnrolledCount EQ 1 AND SpringAppCount EQ 1 THEN 1 ELSE 0;
SummerEnrolledCount/I5=IF EnrolledCount EQ 1 AND SummerAppCount EQ 1 THEN 1 ELSE 0;
WinterEnrolledCount/I5=IF EnrolledCount EQ 1 AND WinterAppCount EQ 1 THEN 1 ELSE 0;
END
TABLE FILE ANALYSISDATA
SUM 
     ANALYSISDATA.SEG01.WinterAppCount AS 'Winter App Count'
     ANALYSISDATA.SEG01.WinterEverAdmittedCount AS 'Winter Ever Admitted'
     ANALYSISDATA.SEG01.WinterEnrolledCount AS 'Winter Enrolled'
     ANALYSISDATA.SEG01.SpringAppCount AS 'Spring App Count'
     ANALYSISDATA.SEG01.SpringEverAdmittedCount AS 'Spring Ever Admitted'
     ANALYSISDATA.SEG01.SpringEnrolledCount AS 'Spring Enrolled'
     ANALYSISDATA.SEG01.SummerAppCount AS 'Summer App Count'
     ANALYSISDATA.SEG01.SummerEverAdmittedCount AS 'Summer Ever Admitted'
     ANALYSISDATA.SEG01.SummerEnrolledCount AS 'Summer Enrolled'
     ANALYSISDATA.SEG01.FallAppCount AS 'Fall App Count'
     ANALYSISDATA.SEG01.FallEverAdmittedCount AS 'Fall Ever Admitted'
     ANALYSISDATA.SEG01.FallEnrolledCount AS 'Fall Enrolled'
     CNT.ANALYSISDATA.SEG01.DM_PIDM AS 'Total App Count'
     ANALYSISDATA.SEG01.EverAdmitted AS 'Total EverAdmitted Count'
     ANALYSISDATA.SEG01.EnrolledCount AS 'Total Enrolled Count'
BY  LOWEST ANALYSISDATA.SEG01.ISOURCE_DESC AS 'Source Code'
BY  LOWEST ANALYSISDATA.SEG01.ST_INT_LVL_DESC AS 'Referred By'
ON TABLE SET PAGE-NUM NOLEAD 
ON TABLE SET BYDISPLAY ON 
ON TABLE COLUMN-TOTAL AS 'TOTAL'
ON TABLE PCHOLD FORMAT HTML
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
TYPE=REPORT,
     COLUMN=N2,
     WRAP=6.000000,
$
TYPE=REPORT,
     COLUMN=N1,
     WRAP=6.000000,
$
ENDSTYLE
END
 