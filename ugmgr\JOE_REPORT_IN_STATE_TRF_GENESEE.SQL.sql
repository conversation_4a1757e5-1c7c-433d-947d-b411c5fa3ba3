--------------------------------------------------------
--  File created - Saturday-December-09-2023   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View TRANSFER_SCHOLARSHIP
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "UGMGR"."TRANSFER_SCHOLARSHIP" ("UMID", "FIRST_NAME", "LAST_NAME", "UNIQNAME", "CA_EMAIL", "HM_EMAIL_1", "TERM_CODE_ENTRY_DESC", "SORPCOL_SBGI_CODE", "SORGPAT_GPA", "SORDEGR_HOURS_TRANSFERRED", "RECEIVED") AS 
  select  distinct 
UMID, FIRST_NAME, LAST_NAME,UNIQNAME, CA_EMAIL, HM_EMAIL_1,TERM_CODE_ENTRY_DESC,
SORPCOL_SBGI_CODE,
SORGPAT_GPA,SORDEGR_HOURS_TRANSFERRED,
case when  SARCHKL_RECEIVE_DATE is null then 'N'
else 'Y' end as RECEIVED
from aimsmgr.um_demographic d
join aimsmgr.um_admissions_applicant a on a.ad_pidm=d.dm_pidm
join aimsmgr.sorpcol_ext c on c.SORPCOL_PIDM=d.dm_pidm
join aimsmgr.SORDEGR_ext cg on cg.SORDEGR_PIDM=c.SORPCOL_PIDM
join <EMAIL> g on g.SORGPAT_PIDM=d.dm_pidm
join aimsmgr.sarchkl_ext k on k.SARCHKL_PIDM=d.dm_pidm
where 
TERM_CODE_ENTRY='202310'
and STYP_CODE='T'
and APST_CODE='D'
and APDC_CODE_1 in ('AD','A2')
and APST_DATE < to_date('01 MAY 2022', 'DD MON YYYY',
                           'NLS_DATE_LANGUAGE = American')
--and  APST_DATE >= to_date('01 MAY 2021', 'DD MON YYYY',
--                           'NLS_DATE_LANGUAGE = American') 
and SORDEGR_HOURS_TRANSFERRED>50
and SORDEGR_PRIMARY_IND='Y'
and SORGPAT_GPA>=3.7
and SARCHKL_ADMR_CODE in ('0051','0052','0061')
order by umid
;
