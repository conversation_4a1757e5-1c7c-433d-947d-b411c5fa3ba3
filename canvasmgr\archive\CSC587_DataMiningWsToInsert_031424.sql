--drop table temp;
--create table temp as
insert into temp
--with dp1 as (--63679  Rows
select 
--keys
USK.PERSON_ID,
--sd.sd_pidm,
dfw.TERM_DESC TERM_NAME,
DFW.TERM TERM_CODE, 

--Course Data
DFW.DEPT_DESC, 
trim(dfw.subject ||'-'||dfw.course||'-'|| dfw.section||' ('||dfw.term||')') COURSE_TITLE,
--DFW.COURSE_TITLE_KEY,
V1.STATUS, 
V1.TYPE, 
V1.MAX_ENROLLMENT,
nvl(v3.assignmet_title,'None') assign_title,
v3.assignment_score assign_score,
v3.avg_assignment_score avg_assign_score,
v3.max_assignment_score max_assign_score,
nvl(v3.assignment_comment,'None') assign_comment,  --Sentinment Analysis
v1.CURRENT_GRADE, 
v1.FINAL_GRADE, 
v1.AVG_COURSE_GRADE, 
DFW.CH,                             --DV?
DFW.GRADE_QUALITY_POINTS,           --DV?
DFW.GRADE,                          --DV?
DFW.SEC_GMOD_DESC,                  --DV?
DFW.PDFW,                           --DV?
DFW.GRADE_DIST,                     --DV?
DFW.SATISFACTORY_UNSATISFACTORY,    --DV?
DFW.COMPLETED_IND,                  --DV?
DFW.INSTRUCTOR_ID,

--Student Data
sd.STUDENT_TYPE_CODE, 
sd.STUDENT_TYPE_DESC, 
sd.CLASS_CODE, 
sd.CLASS_DESC, 
SD.ONLINE_COURSES_ONLY_IND,
sd.PRIMARY_COLLEGE_CODE, 
sd.PRIMARY_COLLEGE_DESC, 
sd.PRIMARY_DEGREE_CODE, 
sd.PRIMARY_DEGREE_DESC, 
sd.PRIMARY_MAJOR_1 MAJOR, 
sd.PRIMARY_MAJOR_1_DESC MAJOR_DESC, 
sd.PRIMARY_CONC_1 CONC, 
nvl(sd.PRIMARY_CONC_1_DESC, 'None') CONC_DESC, 
sd.PRIMARY_LEVEL_CODE, 
sd.REPORT_LEVEL_CODE, 

--Demographic Data
DM.GENDER, 
DM.SEX,
nvl(DM.PPRN_CODE,'NA') pprn_code,
nvl (DM.PPRN_DESC,'None') pprn_desc,
DM.AGE, 
DM.REPORT_ETHNICITY, 
DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien',1,
      'Hispanic or Latino',2,
      'American Indian or Alaska Native',3,
      'Asian',4,
      'Black or African American',5,
      'Native Hawaiian and Other Pacific Islander',6,
      'White',7,
      'Two or more races',8,9)REPORT_ETHNICITY_CODE,
nvl(DM.FLINT_PROMISE_ELIGIBLE_IND,'NA') FLINT_PROMISE,
nvl(DM.FAFSA_PARENT_EDUCATION,'NA') PARENT_EUD,
nvl(DM.FAFSA_PARENT_EDUCATION_DESC,'NA') PARENT_EDU_DESC,
nvl(DM.FIRST_GENERATION_STUDENT_IND, 'NA') FIRST_GEN,
--Incomming Score Data
nvl(DM.HSCH_CODE,'NA') hsch_code, 
nvl(DM.HSCH_DESC,'None') hsch_desc,
cast(DM.HSCH_GPA as DECIMAL(3,2)) HSCH_GPA, 
nvl(DM.PCOL_CODE_1,'NA') PCOL_CODE,
nvl(DM.PCOL_DESC_1,'None') PCOL_DESC,
CAST(DM.PCOL_GPA_TRANSFERRED_1 AS DECIMAL(3,2)) PCOL_GPA, 
CAST(ts.act_composite AS INT) act_composite,
CAST(TS.ACT_ENGLISH AS INT) ACT_ENGLISH,
CAST(TS.ACT_MATH AS INT) ACT_MATH,
CAST(TS.ACT_READING AS INT) ACT_READING,
CAST(TS.ACT_SCIENCE_REASONING AS INT) ACT_SCIENCE_REASONING,
CAST(TS.ACT_SUM_OF_STANDARD AS INT) ACT_SUM_OF_STANDARD,
CAST(ts.SAT_TOTAL_COMBINED_S10 AS INT) SAT_TOTAL_COMBINED,
CAST(ts.SAT_READ_WRI_S11 AS INT) SAT_READ_WRI,
CAST(TS.SAT_MATH_S12 AS INT) SAT_MATH,

--Current GPA
CAST(sd.TERM_GPA AS DECIMAL(3,2)) UMF_TERM_GPA, 
CAST(sd.OVERALL_GPA AS DECIMAL(3,2)) UMF_OVERALL_GPA,

--Holds
nvl(sd.HOLD_CODE_1, 'NA') HOLD_CODE_1,                            
nvl(sd.HOLD_DESC_1, 'None')  HOLD_DESC_1,     
nvl(sd.HOLD_CODE_2, 'NA') HOLD_CODE_2,                            
nvl(sd.HOLD_DESC_2, 'None')  HOLD_DESC_2, 
nvl(sd.HOLD_CODE_3, 'NA') HOLD_CODE_3,                            
nvl(sd.HOLD_DESC_3, 'None')  HOLD_DESC_3, 
nvl(sd.HOLD_CODE_4, 'NA') HOLD_CODE_4,                            
nvl(sd.HOLD_DESC_4, 'None')  HOLD_DESC_4, 
nvl(sd.HOLD_CODE_5, 'NA') HOLD_CODE_5,                            
nvl(sd.HOLD_DESC_5, 'None')  HOLD_DESC_5,                                          
MORE_HOLDS_IND,

--adv visits
nvl(ssc.visit_count,0) adv_visit_count

from  UMF_DFW_ALL_COURSES_MV DFW

LEFT JOIN UDP_UMF_STUD_KEY USK
    ON DFW.PIDM = USK.PIDM

left join V1_STUDENT_COURSE_MV v1
    ON V1.PIDM = DFW.PIDM
    AND trim(V1.COURSE_TITLE) = trim(dfw.subject ||'-'||dfw.course||'-'|| dfw.section||' ('||dfw.term||')')
    
    left join v3_term_course_activities_mv v3 on v1.person_id = v3.person_id
    and v1.course_offering_id = v3.course_offering_id

LEFT JOIN um_student_data sd
   on dfw.pidm = sd.sd_pidm
   and dfw.term = sd.sd_term_code
LEFT JOIN um_demographic dm
   on dfw.pidm = dm.dm_pidm
   
LEFT JOIN um_test_score ts
  on dfw.pidm = ts.pidm
  
LEFT JOIN (select 
advswipe_pidm pidm,
term_code,
count(*) visit_count
from UDP_UMF_SSC_VISITS_EXT
group by
advswipe_pidm,
term_code) ssc
  on dfw.pidm = ssc.pidm
  and dfw.term = ssc.term_code

where dfw.pdfw = 'Withdrawal'
--and dfw.term = v1.TERM_CODE
and sd.PRIMARY_COLLEGE_CODE = 'IT'
and sd.PRIMARY_MAJOR_1 in ('CSC','EGR','MEGN')

--from V1_STUDENT_COURSE_MV v1
--left join v3_term_course_activities_mv v3 on v1.person_id = v3.person_id
--and v1.course_offering_id = v3.course_offering_id
--
--INNER JOIN (select DFW.*, 
--subject ||'-'||course||'-'|| section||' ('||term||')' course_title_key
--from UMF_DFW_ALL_COURSES_MV DFW)DFW 
--    ON V1.PIDM = DFW.PIDM
--    AND trim(V1.COURSE_TITLE) = trim(DFW.COURSE_TITLE_KEY)
--
--LEFT JOIN um_student_data sd
--   on v1.pidm = sd.sd_pidm
--   and v1.term_code = sd.sd_term_code
--LEFT JOIN um_demographic dm
--   on v1.pidm = dm.dm_pidm
--LEFT JOIN um_test_score ts
--  on v1.pidm = ts.pidm
--LEFT JOIN (select 
--advswipe_pidm pidm,
--term_code,
--count(*) visit_count
--from UDP_UMF_SSC_VISITS_EXT
--group by
--advswipe_pidm,
--term_code) ssc
--  on v1.pidm = ssc.pidm
--  and v1.term_code = ssc.term_code


--)
--select
--*
--from dp1
--where TERM_CODE < (select current_term from um_current_term)--202420

