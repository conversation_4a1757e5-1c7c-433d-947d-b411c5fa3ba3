with pop_sel as(
  select
  dm_pidm,
  td_term_code,
  hsch_gpa,
  case
    when hsch_gpa >= 3.5 then '4.00-3.50'
    when hsch_gpa < 3.5 and hsch_gpa >= 3.0 then '3.49-3.00'
    when hsch_gpa < 3.0 and hsch_gpa >= 2.7 then '2.99-2.70'
    when hsch_gpa < 2.7 and hsch_gpa >= 2.5 then '2.69-2.50'
    when hsch_gpa < 2.5 and hsch_gpa >= 2.0 then '2.49-2.00'
    else '0.00-1.99'
  end cut_score
  from td_demographic
  inner join( 
    select *
    from td_student_data
  )sd_join on sd_join.sd_pidm = td_demographic.dm_pidm
           and sd_join.sd_term_code = td_demographic.td_term_code
           and sd_join.student_type_code = 'F'
  where (td_demographic.hsch_gpa is not null or
         td_demographic.hsch_gpa > 0)
)
select
all_terms_join.td_term_code,
gpa_dist_cur.cut_score_sel,
nvl(pop_sel_join.number_per_cut_score, 0) number_per_cut_score
from (
  select '4.00-3.50'  cut_score_sel  from dual
  union
  select '3.49-3.00' from dual
  union
  select '2.99-2.70' from dual
  union
  select '2.69-2.50' from dual
  union 
  select '2.49-2.00' from dual
  union
  select '0.00-1.99' from dual
  order by 1 desc
)gpa_dist_cur
inner join(
  select
  distinct td_demographic.td_term_code td_term_code
  from td_demographic
  where td_demographic.td_term_code >= '201110'
)all_terms_join on all_terms_join.td_term_code <> '99999'
left outer join(
  select
  pop_sel.td_term_code,
  pop_sel.cut_score,
  count(pop_sel.hsch_gpa) number_per_cut_score
  from pop_sel
--  where pop_sel.td_term_code = '201420'
  group by pop_sel.td_term_code, pop_sel.cut_score
)pop_sel_join on pop_sel_join.cut_score = gpa_dist_cur.cut_score_sel
              and pop_sel_join.td_term_code = all_terms_join.td_term_code
order by all_terms_join.td_term_code desc, gpa_dist_cur.cut_score_sel desc

--select
--pop_sel.td_term_code,
--pop_sel.primary_degree_desc,
--sum("total head count")
--from (
--  select
--  td_term_code,
--  primary_degree_desc,
--  sum(head_count) "total head count"
--  from td_term_summary
--  group by td_term_code, primary_degree_desc
--)pop_sel
--group by pop_sel.td_term_code, pop_sel.primary_degree_desc
--
--select *
--from (
--  select
--  primary_degree_desc,
--  sum(head_count) "total head count"
--  from td_term_summary
--  where td_term_code = '201430'
--  group by primary_degree_desc
--)pop_sel
--where "total head count" > 600
--order by "total head count" desc
--
--
--select
--td_term_code,
--primary_college_code,
--report_level_code,
--sum(td_term_summary.head_count) total_head_count,
--sum(td_term_summary.total_credit_hours) total_credit_hours
--from td_term_summary
--group by td_term_summary.td_term_code, report_level_code, primary_college_code
--order by td_term_summary.td_term_code, report_level_code, primary_college_code
--
--
-- 