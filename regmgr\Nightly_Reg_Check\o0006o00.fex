-* Created: 02/01/2013
-* The SQL below creates 3 tables, CHECK_DEMOGRAPHIC, CHECK_STUDENT and CHECK_ADMISSIONS_APPLICANT.
-* The data is from production tables.  DEMOGRAPHIC and STUDENT data is created for anyone registered (including <PERSON><PERSON>) for
-* the current term or any future terms. STUDENT data is no longer checked after 10th day.  ADMISSIONS data is created for anyone admitted (AD or A2)
-* Once the tables are created, several queries are run to check for errors in the data.  All the
-* hold files are pulled together at the end in order to display the error messages in one report.
-*

ENGINE SQLORA SET DEFAULT_CONNECTION AIMSPROD_aimsmgr
SQL SQLORA PREPARE SQLOUT FOR
select zdmclen.f_fill_check_tables as R<PERSON><PERSON><PERSON>_MESSAGE from dual;
END
TABLE FILE SQLOUT
PRINT
	RETURN_MESSAGE
ON TABLE HOLD AS HOLD_YYY FORMAT ALPHA
-RUN

SET ASNAMES = ON
SET EMPTYREPORT = ON

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'Missing Gender';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.GENDER EQ MISSING;
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_GEN FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'Missing Birthdate';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.BIRTHDATE EQ MISSING;
ON TABLE HOLD AS HOLD_BIR FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'Missing County';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_COUNTY_CODE EQ MISSING;
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE EQ MISSING ) OR ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE EQ '157' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_CON FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'International Student Missing Country of Citizenship(GOAINTL)';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.NATION_CITIZEN_CODE EQ MISSING;
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.INTL_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_CIT FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'International Ind is Y but SPBPERS_CITZ_CODE is not NC or NO';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.CITIZENSHIP_CODE NE 'NO' ) AND ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.CITIZENSHIP_CODE NE 'NC' );
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.INTL_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_INT FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'Citizenship Code is Missing ';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.CITIZENSHIP_CODE EQ MISSING );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_CCM FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'No Active A1 Address';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_STREET_LINE1 EQ MISSING;
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_MA1 FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'Not Michigan County';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_STATE_CODE EQ 'MI';
WHERE SUBSTR(30, CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_COUNTY_CODE, 1, 2, 2, 'A2') NE 'MI' OR CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_COUNTY_CODE EQ MISSING ;
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_MCO FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'US State but not US Nation';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE NE '157' ) AND ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE NE MISSING ) AND ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_STATE_CODE CONTAINS 'AK' OR 'AL' OR 'AR' OR 'AZ' OR 'CA' OR 'CO' OR 'CT' OR 'DC' OR 'DE' OR 'FL' OR 'GA' OR 'HI' OR 'IA' OR 'ID' OR 'IL' OR 'IN' OR 'KS' OR 'KY' OR 'LA' OR 'MA' OR 'MD' OR 'ME' OR 'MI' OR 'MN' OR 'MO' OR 'MS' OR 'MT' OR 'NC' OR 'ND' OR 'NE' OR 'NH' OR 'NJ' OR 'NM' OR 'NV' OR 'NY' OR 'OH' OR 'OK' OR 'OR' OR 'PA' OR 'RI' OR 'SC' OR 'SD' OR 'TN' OR 'TX' OR 'UT' OR 'VA' OR 'VT' OR 'WA' OR 'WI' OR 'WV' OR 'WY' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_USN FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     COMPUTE Message/A750 = 'Phone contains special characters:  ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_AREA_CODE |' '| CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_NUMBER |' '| CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_EXT;
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_NUMBER CONTAINS '-' OR '_' OR '!' OR '@' OR '#' OR '$' OR '%' OR '^' OR '&' OR '*' OR '(' OR ')' OR '+' OR '=' OR '|' OR '{' OR '}' OR '[' OR ']' OR ':' OR '~' OR '`' OR '<' OR '>' OR ',' OR '.' OR '?' OR '/' OR '\' ) OR ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_AREA_CODE CONTAINS '-' OR '_' OR '!' OR '@' OR '#' OR '$' OR '%' OR '^' OR '&' OR '*' OR '(' OR ')' OR '+' OR '=' OR '|' OR '{' OR '}' OR '[' OR ']' OR ':' OR '~' OR '`' OR '<' OR '>' OR ',' OR '.' OR '?' OR '/' OR '\' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_SPP FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'High School GPA may be out of Range: ' | ' ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.HSCH_GPA;
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
BY TOTAL LOWEST COMPUTE Level/A2V = '  ';
WHERE (CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.HSCH_GPA NOT-FROM '0' TO '5.0000') OR (CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.HSCH_GPA CONTAINS '_' OR '''' OR '!' OR '@' OR '#' OR '$' OR '%' OR '^' OR '&' OR '*' OR '(' OR ')' OR '+' OR '=' OR '|' OR '{' OR '}' OR '[' OR ']' OR ':' OR '~' OR '`' OR '<' OR '>' OR '?' OR '\' OR 'G' OR 'g' OR '..' OR '/');

ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_HSG FORMAT ALPHA
END

-* ****************************************************************
JOIN
 CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.DM_PIDM IN CHECK_DEMOGRAPHIC TO MULTIPLE
 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_PIDM IN CHECK_STUDENT_DATA TAG J0
 AS J0
 END
JOIN
 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE IN CHECK_STUDENT_DATA
 TO MULTIPLE STVTERM.STVTERM.STVTERM_CODE IN STVTERM TAG J3 AS J3
 END
-* ****************************************************************


DEFINE FILE CHECK_DEMOGRAPHIC
AreaCodeLength/I2=IF CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_AREA_CODE NE MISSING THEN ( ARGLEN(6, CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_AREA_CODE, 'I2') ) ELSE 0;
PhoneLength/I2=IF CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_NUMBER NE MISSING THEN ( ARGLEN(12, CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_NUMBER, 'I2') ) ELSE 0;
Level/A2V = J0.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE;
END
TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     COMPUTE Message/A750 = 'AreaCode not 3 or Phone not 7, Nation is US or blank:  ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_AREA_CODE | ' ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_NUMBER | ' ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_EXT;
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
WHERE (( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.AreaCodeLength NE 0 OR 3 ) OR ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.PhoneLength NE 0 OR 7 )) AND (( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE EQ '157' ) OR ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE EQ MISSING ));
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_PHU FORMAT ALPHA
END
TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     COMPUTE Message/A750 = 'AreaCode not 3 or Phone not 7, Nation not US:  ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_AREA_CODE | ' ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_NUMBER | ' ' | CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_PHONE_EXT;
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
WHERE (( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.AreaCodeLength NE 0 OR 3 ) OR ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.PhoneLength NE 0 OR 7 )) AND (( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE NE '157' ) AND ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.A1_NATION_CODE NE MISSING ));
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_PHL FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'High School GPA missing for FTIAC: ';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.HSCH_GPA EQ MISSING ) AND ( J0.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE EQ 'F' ) AND ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.HSCH_SBGI_CODE NE '999998' OR '900100' OR '999997' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_HSM FORMAT ALPHA
END
TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
	 CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = J0.CHECK_STUDENT_DATA.SD_TERM_CODE || ' International Student (NC Citizenship) Missing Visa type';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
WHERE (CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.VISA_TYPE_CODE EQ MISSING) AND (CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.CITIZENSHIP_CODE EQ 'NC');
ON TABLE SET PAGE-NUM OFF
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_VIS FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
	 COMPUTE Message/A750 = J0.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Transfer missing Prior College code';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.PCOL_CODE_EXISTS EQ 'N' ) AND ( J0.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE EQ 'T' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_222 FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
	 COMPUTE Message/A750 = J0.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Transfer missing Prior College GPA';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
WHERE ( CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.PCOL_GPA_EXISTS EQ 'N' ) AND ( J0.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE EQ 'T' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_333 FORMAT ALPHA
END


TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'NV Hold ';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.REG_WITH_NV_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NVH FORMAT ALPHA
END

TABLE FILE CHECK_DEMOGRAPHIC
PRINT
     J0.CHECK_STUDENT_DATA.Level
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.UMID
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME
     CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME
     COMPUTE Message/A750 = 'Invalid Transfer Grade(s) ';
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.LAST_NAME NOPRINT
BY  LOWEST CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.FIRST_NAME NOPRINT
WHERE CHECK_DEMOGRAPHIC.CHECK_DEMOGRAPHIC.INVALID_TRANSFER_GRADE_IND EQ 'Y';
WHERE J0.CHECK_STUDENT_DATA.Level LIKE 'U%';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_ITG FORMAT ALPHA
END

DEFINE FILE CHECK_STUDENT_DATA
Level/A2V=CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE;
END
TABLE FILE CHECK_STUDENT_DATA
PRINT
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Missing Rate Code ';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE EQ MISSING ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE NE 'DR' OR 'D2' OR 'D3' OR 'GR' OR 'G2' OR 'G3' OR 'PT' OR 'NA' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RTM FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' UG Rate Code ';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE EQ 'UG' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RTUG FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' RNBSN NonRes without RBSN2 Rate Code';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE NE 'RBSN2' OR CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE EQ MISSING ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RESIDENCY_CODE EQ 'N' ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_MAJOR_1 EQ 'NURR' OR 'NURN' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RTN FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' UG Rate Code - not UG Level';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE EQ 'UG' OR 'UG2' OR 'NUR2' OR 'NURN2' OR 'RBSN2' ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE NE 'U3' OR 'U2' OR 'UG' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RTU FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' UG2 Rate Code - NURG major';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE EQ 'UG2') AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_MAJOR_1 EQ 'NURG');
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NUG FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' NUR2 or NURN2 Rate Code - not NURG major';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RATE_CODE EQ 'NUR2' OR 'NURN2') AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_MAJOR_1 NE 'NURG');
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NRC FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Missing Residency Code ';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.RESIDENCY_CODE EQ MISSING )AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE NE 'NA' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RSC FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' FTIAC Primary level not UG' ;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE NE 'UG') AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE EQ 'F' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_FUG FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' U2 or U3 Level with < 90 Hours' ;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'U3' OR 'U2' ) AND (( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.OVERALL_HOURS_EARNED EQ MISSING) OR (CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.OVERALL_HOURS_EARNED LT 90 ));
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_L90 FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Registered but not Active Student Status' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_STATUS_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_STATUS_CODE NE 'AS' OR CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_STATUS_CODE EQ MISSING;
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_SIG FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Missing Primary Major ';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_MAJOR_1 EQ MISSING );
ON TABLE SET PAGE-NUM OFF
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_MPM FORMAT ALPHA
END
-* Only do the following check for projected student type after the first day of a term - this way the actual student
-* type will have rolled after end-of-term processing
TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Projected Student Type does not match Actual Student Type (excluding Readmit projected types)' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE NE CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE OR CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_STATUS_CODE EQ MISSING) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE NE 'R' ) AND ( '&YYMD' GE J3.STVTERM.STVTERM_START_DATE );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_PST FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' NCFD Student with Incorrect Degree Code:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_DEGREE_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE EQ 'S' ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_DEGREE_CODE NE '000000' OR 'TC' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NID FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Undeclared College or Student Type:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_COLLEGE_CODE | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_COLLEGE_CODE EQ '00' ) OR ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE EQ '0' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_USC FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' New with Continuing Projected_Student_Type:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.NEW_CONT_IND | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.NEW_CONT_IND EQ 'N' ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE EQ 'C' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NWC FORMAT ALPHA
END

TABLE FILE CHECK_STUDENT_DATA
PRINT
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Admitted this term on SGASTDN but no Application for this term:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_ADMIT_TERM | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.ADMITTED_THIS_TERM_IND;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE EQ CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PRIMARY_ADMIT_TERM ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.ADMITTED_THIS_TERM_IND EQ MISSING );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NEWNOAPP FORMAT ALPHA
END
TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Continuing with New Projected_Student_Type:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.NEW_CONT_IND | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.NEW_CONT_IND EQ 'C' ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.PROJECTED_STUDENT_TYPE IN ('N','F','G','R','T') );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_CWN FORMAT ALPHA
END

-* Only do the following check for projected student type after the first day of a term - this way the actual student
-* type will have rolled after end-of-term processing
TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' New Student Type, No Admitted Application ';
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.STUDENT_TYPE_CODE IN ('T','N','D','G','F') ) AND ( CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.NEW_CONT_IND EQ 'C' ) AND ( '&YYMD' GE J3.STVTERM.STVTERM_START_DATE );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_NNA FORMAT ALPHA
END
DEFINE FILE CHECK_ADMISSIONS_APPLICANT
Level/A2V=CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LEVL_CODE;
END
TABLE FILE CHECK_ADMISSIONS_APPLICANT
PRINT
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.Level
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.UMID
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME
     COMPUTE Message/A750 = CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY || ' Readmit Admit Type but Not Contuining Student Type (SAAADMS) ';
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME NOPRINT
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME NOPRINT
WHERE ( CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE EQ 'RE' ) AND ( CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE NE 'C' OR CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE EQ MISSING );
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RNC FORMAT ALPHA
END
TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' In ELP course with incorrect RSTS code:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.ELP_WRONG_REG_STATUS_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_EL1 FORMAT ALPHA
END


TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' In ELP course but no ELP level:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.ELP_WRONG_STYPE_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_EL2 FORMAT ALPHA
END


TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' U2/U3/G2/G3/D2 level with UG/GR/DR course level:  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.REG_LEVEL_MISMATCH_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RGL FORMAT ALPHA
END


TABLE FILE CHECK_STUDENT_DATA
PRINT
	 CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.Level
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.UMID
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME
     CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME
     COMPUTE Message/A750 = CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE || ' Withdrawn Enr Status with Registered Course Status  ' | ' ' | CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.SD_TERM_CODE;
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.LAST_NAME NOPRINT
BY  LOWEST CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.FIRST_NAME NOPRINT
WHERE CHECK_STUDENT_DATA.CHECK_STUDENT_DATA.WITHDRAWN_WITH_REG_IND EQ 'Y';
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_WRS FORMAT ALPHA
END


TABLE FILE CHECK_ADMISSIONS_APPLICANT
PRINT
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.Level
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.UMID
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME
     COMPUTE Message/A750 = CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY || ' App Student Type does not match Stu Student Type:  ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE_STU;
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME NOPRINT
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME NOPRINT
WHERE CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE NE CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE_STU OR CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.STYP_CODE_STU EQ MISSING;
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_STN FORMAT ALPHA
END

TABLE FILE CHECK_ADMISSIONS_APPLICANT
PRINT
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.Level
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.UMID
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME
     COMPUTE Message/A750 = CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY | ' App Admit Code does not match Stu Admit Code:  ' | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE_STU;
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME NOPRINT
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME NOPRINT
WHERE ( CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE NE CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE_STU OR CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE_STU EQ MISSING ) AND (CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.ADMT_CODE NE 'CO' );
ON TABLE SET PAGE-NUM OFF
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_ADC FORMAT ALPHA
END

TABLE FILE CHECK_ADMISSIONS_APPLICANT
PRINT
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.Level
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.UMID
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME
     COMPUTE Message/A750 = CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY || ' App Residency does not match Stu Residency:  ' | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE_STU;
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME NOPRINT
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME NOPRINT
WHERE ( CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE NE CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE_STU );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RES FORMAT ALPHA
END

TABLE FILE CHECK_ADMISSIONS_APPLICANT
PRINT
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.Level
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.UMID
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME
     CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME
     COMPUTE Message/A750 = CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY || ' Residency is not R or not N ' | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE | ' ' | CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE_STU;
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.LAST_NAME NOPRINT
BY  LOWEST CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.FIRST_NAME NOPRINT
WHERE ( CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE NE 'R' OR 'N' ) OR ( CHECK_ADMISSIONS_APPLICANT.CHECK_ADMISSIONS_APPLICANT.RESD_CODE_STU NE 'R' OR 'N' );
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_RRN FORMAT ALPHA
END

-*
-* Now concatenate General error tables and display as one report
-*
TABLE FILE HOLD_GEN
PRINT
	 HOLD_GEN.HOLD_GEN.Level
     HOLD_GEN.HOLD_GEN.UMID
     HOLD_GEN.HOLD_GEN.LAST_NAME
     HOLD_GEN.HOLD_GEN.FIRST_NAME
     HOLD_GEN.HOLD_GEN.Message
ON TABLE SET PAGE-NUM OFF
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_ALL FORMAT ALPHA
MORE
FILE HOLD_BIR
MORE
FILE HOLD_CON
MORE
FILE HOLD_CIT
MORE
FILE HOLD_INT
MORE
FILE HOLD_CCM
MORE
FILE HOLD_VIS
MORE
FILE HOLD_MA1
MORE
FILE HOLD_MCO
MORE
FILE HOLD_USN
MORE
FILE HOLD_NVH
MORE
FILE HOLD_RTM
MORE
FILE HOLD_RTUG
MORE
FILE HOLD_RTN
MORE
FILE HOLD_RTU
MORE
FILE HOLD_NUG
MORE
FILE HOLD_NRC
MORE
FILE HOLD_RSC
MORE
FILE HOLD_FUG
MORE
FILE HOLD_222
MORE
FILE HOLD_333
MORE
FILE HOLD_L90
MORE
FILE HOLD_SIG
MORE
FILE HOLD_MPM
MORE
FILE HOLD_PST
MORE
FILE HOLD_NID
MORE
FILE HOLD_USC
MORE
FILE HOLD_NWC
MORE
FILE HOLD_NEWNOAPP
MORE
FILE HOLD_CWN
MORE
-*FILE HOLD_NNA
-*MORE
FILE HOLD_EL1
MORE
FILE HOLD_EL2
MORE
FILE HOLD_RGL
MORE
FILE HOLD_WRS
END
TABLE FILE HOLD_ALL
PRINT
     HOLD_ALL.HOLD_ALL.Message
BY  LOWEST HOLD_ALL.HOLD_ALL.Level
BY  LOWEST HOLD_ALL.HOLD_ALL.LAST_NAME NOPRINT
BY  LOWEST HOLD_ALL.HOLD_ALL.FIRST_NAME NOPRINT
BY  LOWEST HOLD_ALL.HOLD_ALL.UMID
BY  LOWEST HOLD_ALL.HOLD_ALL.LAST_NAME AS 'Last Name'
BY  LOWEST HOLD_ALL.HOLD_ALL.FIRST_NAME AS 'First Name'
ON TABLE SUBHEAD
"Banner Clean Up Report"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD1 FORMAT HTMTABLE
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
-MRNOEDIT INCLUDE=endeflt,$
-*     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,$
$
TYPE=REPORT,
     GRAPHCOLOR='GREEN',
$
TYPE=REPORT,
     OBJECT=MENU,
     COLOR='WHITE',
     HOVER-COLOR=RGB(66 70 73),
     BACKCOLOR=RGB(102 102 102),
     HOVER-BACKCOLOR=RGB(218 225 232),
     BORDER-COLOR='WHITE',
$
TYPE=REPORT,
     OBJECT=STATUS-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     OBJECT=CURRENT-ROW,
     HOVER-BACKCOLOR=RGB(218 225 232),
     BACKCOLOR=RGB(200 200 200),
$
TYPE=REPORT,
     OBJECT=CALC-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     COLUMN=N2,
     SQUEEZE=0.791667,
$
TYPE=REPORT,
     COLUMN=N3,
     SQUEEZE=0.555556,
$
ENDSTYLE
END
-*
-* Display Admission info separately
-*
SET LINES = 9999
TABLE FILE HOLD_RNC
PRINT
     HOLD_RNC.HOLD_RNC.UMID
     HOLD_RNC.HOLD_RNC.LAST_NAME
     HOLD_RNC.HOLD_RNC.FIRST_NAME
     HOLD_RNC.HOLD_RNC.Message
BY  LOWEST HOLD_RNC.HOLD_RNC.Level
BY  LOWEST HOLD_RNC.HOLD_RNC.LAST_NAME NOPRINT
BY  LOWEST HOLD_RNC.HOLD_RNC.FIRST_NAME NOPRINT
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_AD FORMAT ALPHA
MORE
FILE HOLD_STN
MORE
FILE HOLD_ADC
MORE
FILE HOLD_RES
MORE
FILE HOLD_RRN
MORE
FILE HOLD_ITG
MORE
FILE HOLD_HSM
MORE
FILE HOLD_HSG
END
TABLE FILE HOLD_AD
PRINT
     HOLD_AD.HOLD_AD.Message
BY  LOWEST HOLD_AD.HOLD_AD.Level
BY  HOLD_AD.HOLD_AD.LAST_NAME NOPRINT
BY  HOLD_AD.HOLD_AD.FIRST_NAME NOPRINT
BY  LOWEST HOLD_AD.HOLD_AD.UMID
BY  LOWEST HOLD_AD.HOLD_AD.LAST_NAME AS 'Last Name'
BY  LOWEST HOLD_AD.HOLD_AD.FIRST_NAME AS 'First Name'
ON TABLE SUBHEAD
"Banner Admission Info Clean up Report"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD2 FORMAT HTMTABLE
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
-MRNOEDIT INCLUDE=endeflt,$
-*     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
TYPE=REPORT,
     GRAPHCOLOR='GREEN',
$
TYPE=REPORT,
     OBJECT=MENU,
     COLOR='WHITE',
     HOVER-COLOR=RGB(66 70 73),
     BACKCOLOR=RGB(102 102 102),
     HOVER-BACKCOLOR=RGB(218 225 232),
     BORDER-COLOR='WHITE',
$
TYPE=REPORT,
     OBJECT=STATUS-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     OBJECT=CURRENT-ROW,
     HOVER-BACKCOLOR=RGB(218 225 232),
     BACKCOLOR=RGB(200 200 200),
$
TYPE=REPORT,
     OBJECT=CALC-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     COLUMN=N2,
     SQUEEZE=0.388889,
$
TYPE=REPORT,
     COLUMN=N3,
     SQUEEZE=0.402778,
$
ENDSTYLE
END
-*
-* End Admissions Info Report
-*

-*
-* Display phone report separate from other messages because this will often have 'errors' that cannot be corrected
-*

SET LINES = 9999
TABLE FILE HOLD_SPP
PRINT
     HOLD_SPP.HOLD_SPP.Level
     HOLD_SPP.HOLD_SPP.UMID
     HOLD_SPP.HOLD_SPP.LAST_NAME
     HOLD_SPP.HOLD_SPP.FIRST_NAME
     HOLD_SPP.HOLD_SPP.Message
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD_PHO FORMAT ALPHA
MORE
FILE HOLD_PHU
MORE
FILE HOLD_PHL
END
TABLE FILE HOLD_PHO
PRINT
     HOLD_PHO.HOLD_PHO.Message
BY  LOWEST HOLD_PHO.HOLD_PHO.Level
BY  LOWEST HOLD_PHO.HOLD_PHO.LAST_NAME NOPRINT
BY  LOWEST HOLD_PHO.HOLD_PHO.FIRST_NAME NOPRINT
BY  LOWEST HOLD_PHO.HOLD_PHO.UMID
BY  LOWEST HOLD_PHO.HOLD_PHO.LAST_NAME AS 'Last Name'
BY  LOWEST HOLD_PHO.HOLD_PHO.FIRST_NAME AS 'First Name'
ON TABLE SUBHEAD
"Banner PHONE NUMBER Clean up Report"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD3 FORMAT HTMTABLE
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
-MRNOEDIT INCLUDE=endeflt,$
-*     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
TYPE=REPORT,
     GRAPHCOLOR='GREEN',
$
TYPE=REPORT,
     OBJECT=MENU,
     COLOR='WHITE',
     HOVER-COLOR=RGB(66 70 73),
     BACKCOLOR=RGB(102 102 102),
     HOVER-BACKCOLOR=RGB(218 225 232),
     BORDER-COLOR='WHITE',
$
TYPE=REPORT,
     OBJECT=STATUS-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     OBJECT=CURRENT-ROW,
     HOVER-BACKCOLOR=RGB(218 225 232),
     BACKCOLOR=RGB(200 200 200),
$
TYPE=REPORT,
     OBJECT=CALC-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     COLUMN=N2,
     SQUEEZE=0.388889,
$
TYPE=REPORT,
     COLUMN=N3,
     SQUEEZE=0.402778,
$
ENDSTYLE
END
-*
-* End Phone Report

ENGINE SQLORA SET DEFAULT_CONNECTION AIMSPROD_aimsmgr
SQL SQLORA PREPARE SQLOUT FOR
select
spriden.spriden_id umid,
spriden.spriden_last_name,
spriden.spriden_first_name,
sum_stdn_sel.sgbstdn_styp_code summer_styp_code,
fall_stdn_sel.sgbstdn_styp_code fall_styp_code
from(
  select
  saradap_pidm
  from saradap
  inner join sarappd on sarappd.sarappd_pidm = saradap.saradap_pidm
                     and sarappd.sarappd_appl_no = saradap.saradap_appl_no
                     and sarappd.sarappd_seq_no = (select max(s2.sarappd_seq_no)
                                                   from sarappd s2
                                                   where s2.sarappd_pidm = sarappd.sarappd_pidm
                                                   and s2.sarappd_appl_no = sarappd.sarappd_appl_no)
  where saradap.saradap_term_code_entry = (select um_current_term.current_term from um_current_term where substr(um_current_term.current_term, 5, 2) = '10')
  and saradap.saradap_levl_code like 'UG'
  and sarappd.sarappd_apdc_code = 'CT'
  and saradap.saradap_styp_code = 'F'
)fall_sel
inner join(
  select
  saradap_pidm
  from saradap
  inner join sarappd on sarappd.sarappd_pidm = saradap.saradap_pidm
                     and sarappd.sarappd_appl_no = saradap.saradap_appl_no
                     and sarappd.sarappd_seq_no = (select max(s2.sarappd_seq_no)
                                                   from sarappd s2
                                                   where s2.sarappd_pidm = sarappd.sarappd_pidm
                                                   and s2.sarappd_appl_no = sarappd.sarappd_appl_no)
  where saradap.saradap_term_code_entry = (select um_current_term.last_term from um_current_term where substr(um_current_term.current_term, 5, 2) = '10')
  and saradap.saradap_levl_code like 'UG'
  and sarappd.sarappd_apdc_code = 'AD'
  and saradap.saradap_styp_code = 'F'
)sum_sel on sum_sel.saradap_pidm = fall_sel.saradap_pidm
inner join(
  select
  sgbstdn.sgbstdn_pidm,
  sgbstdn.sgbstdn_styp_code
  from sgbstdn
  where sgbstdn.sgbstdn_term_code_eff = (select max(s1.sgbstdn_term_code_eff)
                                         from sgbstdn s1
                                         where s1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm
                                         and s1.sgbstdn_term_code_eff <= (select um_current_term.current_term from um_current_term where substr(um_current_term.current_term, 5, 2) = '10'))
  and sgbstdn.sgbstdn_levl_code = 'UG'
  and sgbstdn.sgbstdn_stst_code = 'AS'
)fall_stdn_sel on fall_stdn_sel.sgbstdn_pidm = fall_sel.saradap_pidm
inner join(
  select
  sgbstdn.sgbstdn_pidm,
  sgbstdn.sgbstdn_styp_code
  from sgbstdn
  where sgbstdn.sgbstdn_term_code_eff = (select max(s1.sgbstdn_term_code_eff)
                                         from sgbstdn s1
                                         where s1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm
                                         and s1.sgbstdn_term_code_eff <= (select um_current_term.last_term from um_current_term where substr(um_current_term.current_term, 5, 2) = '10'))
  and sgbstdn.sgbstdn_levl_code = 'UG'
  and sgbstdn.sgbstdn_stst_code = 'AS'
)sum_stdn_sel on sum_stdn_sel.sgbstdn_pidm = fall_sel.saradap_pidm
inner join spriden on spriden.spriden_pidm = fall_sel.saradap_pidm
                   and spriden.spriden_change_ind is null
where fall_stdn_sel.sgbstdn_styp_code = 'C'
and sum_stdn_sel.sgbstdn_styp_code = 'F'
order by 2, 3
END
TABLE FILE SQLOUT
PRINT
     UMID
     SPRIDEN_LAST_NAME AS 'Last Name'
     SPRIDEN_FIRST_NAME AS 'First Name'
     SUMMER_STYP_CODE AS 'Summer STYP'
     FALL_STYP_CODE AS 'Fall STYP'
ON TABLE SUBHEAD
"MisMatch STYP Report"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD4 FORMAT HTMTABLE
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
ENDSTYLE
END

ENGINE SQLORA SET DEFAULT_CONNECTION AIMSPROD_webfocus
SQL SQLORA PREPARE SQLOUT FOR
with pop_sel as(
  select
  spriden_pidm,
  spriden_id
  from spriden
  where spriden_change_ind is null
  and not exists(select 'is_dup'
                 from sprhold
                 where sprhold.sprhold_hldd_code = 'DP'
                 and sprhold.sprhold_pidm = spriden.spriden_pidm)
),
p2 as(
  select
  spriden_id,
  count(spriden_pidm)
  from pop_sel
  having count(spriden_pidm) > 1
  group by
  spriden_id
)
select
spriden.spriden_pidm,
spriden.spriden_id,
spriden.spriden_last_name,
spriden.spriden_first_name,
spriden.spriden_activity_date,
spriden.spriden_user
from spriden
inner join p2 on p2.spriden_id = spriden.spriden_id
where spriden.spriden_change_ind is null
END
TABLE FILE SQLOUT
PRINT
     SPRIDEN_PIDM AS 'PIDM'
     SPRIDEN_ID AS 'UMID'
     SPRIDEN_LAST_NAME AS 'Last Name'
     SPRIDEN_FIRST_NAME AS 'First Name'
     SPRIDEN_ACTIVITY_DATE AS 'Activity Date'
     SPRIDEN_USER AS 'User'
BY  SPRIDEN_LAST_NAME NOPRINT
BY  SPRIDEN_FIRST_NAME NOPRINT
BY  SPRIDEN_ACTIVITY_DATE NOPRINT
ON TABLE SUBHEAD
"More that one pidm for a UMID."
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD5 FORMAT HTMTABLE
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
ENDSTYLE
END




-*
-* Output the reports as below so that ReportCaster will display both reports
-*
-HTMLFORM BEGIN

!IBI.FIL.HOLD1;

!IBI.FIL.HOLD2;

!IBI.FIL.HOLD3;

!IBI.FIL.HOLD4;

!IBI.FIL.HOLD5;

-HTMLFORM END
