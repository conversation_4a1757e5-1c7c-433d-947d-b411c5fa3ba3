--people who applied to graduate did not get a degree awarded
--located in tabmgr schema
create or replace view load_shbgapp_no_shrdgmr as
select 
shbgapp.SHBGAPP_GRAD_TERM_CODE Grad_Term,
shbgapp.SHBGAPP_LAST_NAME ||', '||shbgapp.SHBGAPP_FIRST_NAME ||' '||shbgapp.SHBGAPP_MI last_first_mi,
um.umid,
shbgapp.shbgapp_seqno

from aimsmgr.shbgapp shbgapp
inner join um_demographic um on um.dm_pidm = shbgapp.shbgapp_pidm

where 
shbgapp_grad_term_code >= '201910'
and 
not exists (
                select 'x' from aimsmgr.shrdgmr shrdgmr
                where shbgapp_pidm = shrdgmr_pidm
                and shbgapp_grad_term_code = shrdgmr_term_code_grad
              )
order by     
shbgapp.SHBGAPP_GRAD_TERM_CODE desc,
um.umid
;         