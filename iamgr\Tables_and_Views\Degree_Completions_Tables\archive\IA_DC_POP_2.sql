create or replace view IA_DC_POP_2 as (
select
IA_DC_POP.PIDM,
IA_DC_POP.GRAD_TERM_CODE,
IA_DC_POP.DEGREE_CODE,
case
    when DEGREE_CODE_PRIORITY = 1 then 'first major'
    else 'second major'
  end COMPLETION_TYPE
--count(IA_DC_POP.DEGREE_CODE)
  

from 
IA_DC_POP

where 
IA_DC_POP.GRAD_TERM_CODE in ('201440', '201510', '201520', '201530') --and
--DEGREE_CODE_PRIORITY = 1
--IA_DC_POP.PIDM in ('60058')

--group by
--IA_DC_POP.PIDM,
--IA_DC_POP.GRAD_TERM_CODE,
--IA_DC_POP.DEGREE_CODE,
--COMPLETION_TYPE

--order by IA_DC_POP.PIDM

);


