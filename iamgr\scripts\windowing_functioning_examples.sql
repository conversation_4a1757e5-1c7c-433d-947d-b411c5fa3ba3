--row_number window functioning
with term_sel as(
    select 
    s1.*,
    row_number() over(partition by s1.sgbstdn_pidm order by s1.sgbstdn_term_code_eff  desc) order_num
    from aimsmgr.sgbstdn s1
    where s1.sgbstdn_term_code_eff <= '202020'
    
)
select *
from term_sel
--where term_sel.order_num = 1
where term_sel.sgbstdn_pidm = 55342
;

select *
from aimsmgr.sgbstdn
where sgbstdn_pidm = 55342
and sgbstdn_term_code_eff = (select max(s1.sgbstdn_term_code_eff)
                             from aimsmgr.sgbstdn s1
                             where s1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm
                             and s1.sgbstdn_term_code_eff <= '202020');
                             
                             