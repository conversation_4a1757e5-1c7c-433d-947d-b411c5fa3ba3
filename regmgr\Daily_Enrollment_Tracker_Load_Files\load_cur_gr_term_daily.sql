--------------------------------------------------------
--  DDL for View LOAD_CUR_GR_TERM_DAILY
--------------------------------------------------------

  CREATE OR REPLACE VIEW REGMGR.LOAD_CUR_GR_TERM_DAILY AS 
  with t1 as(
    select 
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code degc_majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc,
    sum(um_reg_student_daily.head_count) head_count,
    sum(um_reg_student_daily.total_credit_hours) total_credit_hours
    from um_reg_student_daily
    where um_reg_student_daily.run_date = trunc(sysdate)
    and um_reg_student_daily.term_code = (select um_current_term.current_term from um_current_term)
    and um_reg_student_daily.rpt_levl_code in ('GR', 'DR')
    and um_reg_student_daily.rpt_student_type_code != 'X'
    group by
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc
)
, t2 as(
    select 
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code degc_majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc,
    sum(um_reg_student_daily.head_count) head_count,
    sum(um_reg_student_daily.total_credit_hours) total_credit_hours
    from um_reg_student_daily
    where um_reg_student_daily.day_of_term = (select distinct t1.day_of_term from t1)
    and um_reg_student_daily.term_code = (select distinct t1.term_code - 100 from t1)
    and um_reg_student_daily.rpt_levl_code in ('GR', 'DR')
    and um_reg_student_daily.rpt_student_type_code != 'X'
    group by
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc
)
, t3 as(
    select 
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code degc_majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc,
    sum(um_reg_student_daily.head_count) head_count,
    sum(um_reg_student_daily.total_credit_hours) total_credit_hours
    from um_reg_student_daily
    where um_reg_student_daily.day_of_term = (select distinct t1.day_of_term from t1)
    and um_reg_student_daily.term_code = (select distinct t1.term_code - 200 from t1)
    and um_reg_student_daily.rpt_levl_code in ('GR', 'DR')
    and um_reg_student_daily.rpt_student_type_code != 'X'
    group by
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc
)
, t4 as(
    select 
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code degc_majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc,
    sum(um_reg_student_daily.head_count) head_count,
    sum(um_reg_student_daily.total_credit_hours) total_credit_hours
    from um_reg_student_daily
    where um_reg_student_daily.day_of_term = (select distinct t1.day_of_term from t1)
    and um_reg_student_daily.term_code = (select distinct t1.term_code - 300 from t1)
    and um_reg_student_daily.rpt_levl_code in ('GR', 'DR')
    and um_reg_student_daily.rpt_student_type_code != 'X'
    group by
    um_reg_student_daily.day_of_term,
    um_reg_student_daily.term_code,
    um_reg_student_daily.term_desc,
    um_reg_student_daily.degc_code || um_reg_student_daily.majr_code,
    um_reg_student_daily.rpt_degree_major_code,
    um_reg_student_daily.rpt_degree_major_desc
)
, td as(

    select
    td_term_summary.td_term_code,
    td_term_summary.primary_degree_code || ' - ' || td_term_summary.primary_major_1 degc_majr_code,
    td_term_summary.primary_degree_code || ' - ' || td_term_summary.primary_major_1_desc degc_majr_desc,
    sum(td_term_summary.head_count) head_count,
    sum(td_term_summary.total_credit_hours) total_credit_hours
    
    from td_term_summary
    where td_term_summary.td_term_code = (select distinct t1.term_code - 100 from t1) --'202110'
    and td_term_summary.report_level_code = 'GR'
    and td_term_summary.student_type_code != 'X'

    group by
    td_term_summary.td_term_code,
    td_term_summary.primary_degree_code || ' - ' || td_term_summary.primary_major_1,
    td_term_summary.primary_degree_code || ' - ' || td_term_summary.primary_major_1_desc    

)
, pop_sel as(
    select 
    dot.day_of_term,
    dot.term_code,
    dot.term_desc,
    cds.degc_majr_code,
    cds.degc_majr_desc
    from (
        select 
        distinct sobcurr.sobcurr_degc_code || ' - ' || stvmajr.stvmajr_code degc_majr_code,
        sobcurr.sobcurr_degc_code || ' - ' || stvmajr.stvmajr_desc degc_majr_desc
        --sobcurr.*,
        --sorcmjr.*,
        --stvmajr.*
        from aimsmgr.sobcurr_ext sobcurr
        inner join aimsmgr.sorcmjr_ext sorcmjr on sorcmjr.sorcmjr_curr_rule = sobcurr.sobcurr_curr_rule
                                       and sorcmjr.sorcmjr_stu_ind = 'Y'
                                       and sorcmjr.sorcmjr_term_code_eff = (select max(s1.sorcmjr_term_code_eff)
                                                                            from aimsmgr.sorcmjr_ext s1
                                                                            where s1.sorcmjr_curr_rule = sorcmjr.sorcmjr_curr_rule
                                                                            and s1.sorcmjr_term_code_eff <= (select distinct t1.term_code from t1))
        inner join aimsmgr.stvmajr_ext stvmajr on stvmajr.stvmajr_code = sorcmjr.sorcmjr_majr_code
        where sobcurr.sobcurr_levl_code in ('GR', 'DR')
        and sobcurr.sobcurr_lock_ind = 'Y'
        and sobcurr.sobcurr_term_code_init <= (select distinct t1.term_code from t1)
        union
        select 
        t1.rpt_degree_major_code,
        t1.rpt_degree_major_desc
        from t1
        union
        select 
        t2.rpt_degree_major_code,
        t2.rpt_degree_major_desc
        from t2
        union
        select 
        t3.rpt_degree_major_code,
        t3.rpt_degree_major_desc
        from t3
        union
        select 
        t4.rpt_degree_major_code,
        t4.rpt_degree_major_desc
        from t4
        union
        select 
        td.degc_majr_code,
        td.degc_majr_desc
        from td
    )cds
    inner join(
        select
        distinct t1.day_of_term,
        t1.term_code,
        t1.term_desc
        from t1
    )dot on dot.term_code != 'XX' 
)

select
pop_sel.day_of_term,
pop_sel.term_code,
pop_sel.term_desc,
pop_sel.degc_majr_code,
pop_sel.degc_majr_desc,
nvl(t1.head_count, 0) t1_head_count,
nvl(t1.total_credit_hours, 0) t1_total_credit_hours,
nvl(t2.head_count, 0) t2_head_count,
nvl(t2.total_credit_hours, 0) t2_total_credit_hours,
nvl(t3.head_count, 0) t3_head_count,
nvl(t3.total_credit_hours, 0) t3_total_credit_hours,
nvl(t4.head_count, 0) t4_head_count,
nvl(t4.total_credit_hours, 0) t4_total_credit_hours,
nvl(td.head_count, 0) td_head_count,
nvl(td.total_credit_hours, 0) td_total_credit_hours

from pop_sel

left outer join t1 on t1.rpt_degree_major_code = pop_sel.degc_majr_code

left outer join t2 on t2.rpt_degree_major_code = pop_sel.degc_majr_code

left outer join t3 on t3.rpt_degree_major_code = pop_sel.degc_majr_code

left outer join t4 on t4.rpt_degree_major_code = pop_sel.degc_majr_code

left outer join td on td.degc_majr_code = pop_sel.degc_majr_code

order by 

pop_sel.degc_majr_code
;
