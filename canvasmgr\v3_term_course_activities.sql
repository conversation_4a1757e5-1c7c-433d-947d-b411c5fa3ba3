--------------------------------------------------------
--  File created - Wednesday-July-03-2024   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View V3_TERM_COURSE_ACTIVITIES
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CANVASMGR"."V3_TERM_COURSE_ACTIVITIES" ("PERSON_ID", "COURSE_OFFERING_ID", "LEARNER_ACTIVITY_ID", "ASSIGNMET_TITLE", "ASSIGNMENT_DUE_DATE", "ASSIGNMENT_SCORE", "MAX_ASSIGNMENT_SCORE", "ASSIGNMENT_GRADE", "AVG_ASSIGNMENT_SCORE", "ASSIGNMENT_COMMENT") AS 
  SELECT
 lar.person_id,
 la.course_offering_id,
 la.learner_activity_id,
 la.title assignmet_title,
 la.due_date assignment_due_date,
 CASE
   WHEN lar.gradebook_status = 'true' AND lar.grading_status = 'graded' THEN lar.score
 ELSE
 NULL
END
 assignment_score,
 la.points_possible AS max_assignment_score,
 CASE
   WHEN lar.gradebook_status = 'true' AND lar.grading_status = 'graded' THEN lar.grade
 ELSE
 NULL
END
 assignment_grade,
 course_assignment_avg.avg_assignment_score,
 a.body_value assignment_comment

FROM
 v1_student_course vsctc
LEFT JOIN
v2_student_course_assignment_avg course_assignment_avg
ON
 vsctc.course_offering_id = course_assignment_avg.course_offering_id
LEFT JOIN
udp_learner_activity_ext la
ON
 course_assignment_avg.learner_activity_id = la.learner_activity_id
LEFT JOIN
 udp_learner_activity_result_ext lar
ON
 vsctc.person_id = lar.person_id
 AND la.learner_activity_id = lar.learner_activity_id
LEFT JOIN
 udp_annotation_ext a
ON
 lar.learner_activity_result_id = a.learner_activity_result_id
--WHERE
-- la.status = 'published'
-- where lar.person_id = '12815'
;
