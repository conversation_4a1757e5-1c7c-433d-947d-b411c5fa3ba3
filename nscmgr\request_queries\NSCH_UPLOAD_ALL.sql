select count (*)
from IA_COHORT_POP_UNDUP_TBL;
--where 
--co_term_code_key = '200810' and
--co_ia_student_type_code = 'F' and
--co_full_part_ind_umf = 'F'
;
/*******************************************************************************
Author: Dan Getty University of Michigan-Flint
Create date:  02-18-16
Description:	Queries to gather Banner data for upload to National 
              Student Clearinhouse Student Tracker for SE request
              used in competitor dashboard.
Notes:  EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
Known issue(s):	
*******************************************************************************/
WITH BASE_SEL AS ( 
  SELECT DISTINCT 
       substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(trim(SD.FIRST_NAME))
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) first_name,
    substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(trim(SD.LAST_NAME))
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) last_name,
        substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(SD.MIDDLE_INITIAL)
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) middle_name,
    
            substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(SD.NAME_SUFFIX)
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) name_suffix,
--  SD.FIRST_NAME,
--  SD.MIDDLE_INITIAL,
--  SD.LAST_NAME,
--  SD.NAME_SUFFIX, 
  SD.BIRTHDATE,
  CO.CO_TERM_CODE_KEY,
  SD.SD_PIDM
  FROM IA_TD_STUDENT_DATA SD
  INNER JOIN IA_COHORT_POP_UNDUP_TBL CO
  ON CO.CO_PIDM = SD.SD_PIDM
  AND CO.CO_TERM_CODE_KEY = SD.SD_TERM_CODE
),

POP_SEL AS (
  --THIS SECTION CREATES A HEADER FILE.
  SELECT
  1 ORDER_NUM,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  TO_CHAR(SYSDATE, 'YYYYMMDD') "E",
  'SE' "F",
  'I' "G",
  NULL "H",
  NULL "I",
  NULL "J",
  NULL "K",
  NULL "L"
  FROM DUAL
  
  UNION ALL
  --THIS SECTION PULLS THE STUDENT RECORDS FOR THE PAYLOAD.
  SELECT
  2 ORDER_NUM,
  'D1' "A",
  NULL "B",
  CASE
  WHEN (FIRST_NAME IS NULL or FIRST_NAME = '.' OR LENGTH(FIRST_NAME) < 3) THEN 'Jon'
  ELSE  SUBSTR(FIRST_NAME,1,20) 
  END "C",
  SUBSTR(REGEXP_REPLACE(TRIM(MIDDLE_NAME), '[[:punct:] ]',NULL), 1 ,1) "D",
   CASE 
  WHEN (LAST_NAME IS NULL OR LAST_NAME = '.' OR LENGTH(FIRST_NAME) < 3) THEN 'Doe'
  ELSE  SUBSTR(LAST_NAME,1,20) 
  END "E",
  SUBSTR(REGEXP_REPLACE(TRIM(NAME_SUFFIX), '[[:punct:] ]',NULL),1,5) "F",
  TO_CHAR(BIRTHDATE, 'YYYYMMDD') "G",
  TO_CHAR(TO_NUMBER(SUBSTR(CO_TERM_CODE_KEY,1,4))-1 ||'0915') "H", 
  NULL "I",
  '002327' "J",
  '00' "K",
  TO_CHAR(SD_PIDM) "L"
  FROM BASE_SEL

  UNION ALL
    --THIS IS TO COUNT THE NUMBER OF RECORDS AND APPEND A TRAILER RECORD
  SELECT
  3 ORDER_NUM,
  'T1' "A",
  TO_CHAR(COUNT(SD_PIDM)+2) "B",
  NULL "C",
  NULL "D",
  NULL "E",
  NULL "F",
  NULL "G",
  NULL "H",
  NULL "I",
  NULL "J",
  NULL "K",
  NULL "L"
  FROM BASE_SEL
)
SELECT 
POP_SEL.A,
POP_SEL.B,
POP_SEL.C,
POP_SEL.D,
POP_SEL.E,
POP_SEL.F,
POP_SEL.G,
POP_SEL.H,
POP_SEL.I,
POP_SEL.J,
POP_SEL.K,
POP_SEL.L
FROM POP_SEL
ORDER BY ORDER_NUM ASC
;
