with dp1 as (
select 
sd_pidm pidm,
sd_term_code term,
ia_student_type_code stype
from td_student_data
where registered_ind = 'Y'
and sd_term_code = 202010
and (ia_student_type_code in ('F','T'))
minus
select 
co_pidm pidm,
co_term_code_key term,
co_ia_student_type_code stype
from IA_COHORT_POP_UNDUP
where co_term_code_key = 202010
and co_ia_student_type_code in ('F','T')
)
select dp1.*,sd.primary_level_code from dp1 
inner join ia_td_student_data sd on sd.sd_pidm = dp1.pidm
and sd.sd_term_code = dp1.term
order by sd.primary_level_code
;
--Total:1181, Transfer 577
select 
count(*) summer_rolls
from td_student_data
where registered_ind = 'Y'
and sd_term_code = 202010
and ia_student_type_code in ('T')
;
--Total:1134, Transfer 530
select 
count(*) summer_rolls
from td_student_data
where registered_ind = 'Y'
and sd_term_code = 202010
and student_type_code in ('T')
;
