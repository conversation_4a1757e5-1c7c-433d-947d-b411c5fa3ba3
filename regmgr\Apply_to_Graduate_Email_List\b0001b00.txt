TABLE FILE UM_STUDENT_DATA
PRINT
     UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE
     UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE
     UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1
     UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_PROGRAM
     UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED
     UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_GPA
     UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_DESC
BY  LOWEST UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM
WHERE ( UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE EQ '&SD_TERM_CODE' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.STUDENT_STATUS_CODE EQ 'AS' );
WHERE ( UM_STUDENT_DATA.UM_STUDENT_DATA.STUDENT_TYPE_CODE EQ 'C' );
WHERE ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1 NE '0000' OR 'NURN' OR 'NURP' OR 'PSWK' OR 'PHST' OR 'PBUS' OR 'PCCP' OR 'PEDU' OR 'PPTP' OR 'PRTT' );
WHERE (( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'UG' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 90 ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.INST_HOURS_EARNED GE 20 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'U2' OR 'U3' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 100 ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.INST_HOURS_EARNED GE 20 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'GR' OR 'G2' OR 'G3' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 15 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'EDD' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 28 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'EDS' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 20 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'PHD' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 35 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'DPT' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_PROGRAM EQ 'DPT' OR 'DPT-HS' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1 EQ 'PTPP' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 5 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'DPT' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_PROGRAM EQ 'DPT' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1 EQ 'PTP' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 50 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'CERG' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 5 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'CERG' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_PROGRAM EQ 'CERG-BUS-NET' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 5 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'DNP' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 30 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'DNP' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_PROGRAM EQ 'DNP-NR' )AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 15 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_DEGREE_CODE EQ 'DAP' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 15 )
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'DR' OR 'D2' ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 50 ));
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE NOTOTAL
ON TABLE HOLD FORMAT FOCUS INDEX 'UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM'
END
JOIN
LEFT_OUTER HOLD.SEG01.SD_PIDM AND HOLD.SEG01.PRIMARY_LEVEL_CODE IN HOLD
TO MULTIPLE SHRDGMR.SHRDGMR.SHRDGMR_PIDM AND SHRDGMR.SHRDGMR.SHRDGMR_LEVL_CODE
IN SHRDGMR TAG J0 AS J0
END
JOIN
INNER HOLD.SEG01.SD_PIDM IN HOLD TO UNIQUE
UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM IN UM_DEMOGRAPHIC TAG J2 AS J2
END
TABLE FILE HOLD
BY  LOWEST J0.SHRDGMR.SHRDGMR_PIDM
WHERE J0.SHRDGMR.SHRDGMR_GRST_CODE EQ 'PG';
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDPGS FORMAT FOCUS INDEX 'J0.SHRDGMR.SHRDGMR_PIDM'
END
MATCH FILE HOLD
  PRINT HOLD.SEG01.SD_PIDM HOLD.SEG01.PRIMARY_LEVEL_CODE HOLD.SEG01.OVERALL_HOURS_EARNED HOLD.SEG01.OVERALL_GPA HOLD.SEG01.SD_TERM_DESC HOLD.SEG01.PRIMARY_DEGREE_CODE HOLD.SEG01.PRIMARY_MAJOR_1 HOLD.SEG01.PRIMARY_PROGRAM
  BY HOLD.SEG01.SD_PIDM
RUN
FILE HOLDPGS
  PRINT HOLDPGS.SEG01.SHRDGMR_PIDM
  BY HOLDPGS.SEG01.SHRDGMR_PIDM
AFTER MATCH HOLD AS HOLDNOTPG OLD-NOT-NEW
END
JOIN
INNER HOLDNOTPG.HOLDNOTP.SD_PIDM IN HOLDNOTPG TO UNIQUE
UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM IN UM_DEMOGRAPHIC TAG J4 AS J4
END
DEFINE FILE HOLDNOTPG
Level_Sort/A1=SUBSTR(2, HOLDNOTPG.HOLDNOTP.PRIMARY_LEVEL_CODE, 1, 1, 1, 'A1');
END
SET LINES = 9999
TABLE FILE HOLDNOTPG
PRINT
     J4.UM_DEMOGRAPHIC.NAME_SUFFIX
     J4.UM_DEMOGRAPHIC.UMID
     J4.UM_DEMOGRAPHIC.CA_EMAIL
     HOLDNOTPG.HOLDNOTP.PRIMARY_LEVEL_CODE
     HOLDNOTPG.HOLDNOTP.PRIMARY_DEGREE_CODE
     HOLDNOTPG.HOLDNOTP.PRIMARY_MAJOR_1
	 HOLDNOTPG.HOLDNOTP.PRIMARY_PROGRAM
     HOLDNOTPG.HOLDNOTP.OVERALL_HOURS_EARNED
     HOLDNOTPG.HOLDNOTP.OVERALL_GPA
BY  HOLDNOTPG.HOLDNOTP.Level_Sort NOPRINT
 RANKED  AS '' BY  LOWEST J4.UM_DEMOGRAPHIC.LAST_NAME
BY  LOWEST J4.UM_DEMOGRAPHIC.FIRST_NAME
BY  LOWEST J4.UM_DEMOGRAPHIC.MIDDLE_INITIAL
ON TABLE SUBHEAD
"<HOLDNOTPG.HOLDNOTP.SD_TERM_DESC  'Apply to Graduate' Email List"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE NOTOTAL
ON TABLE PCHOLD FORMAT &WFFMT.(<HTML,HTML>,<PDF,PDF>,<Excel 2007,XLSX>,<Excel 2000,EXL2K>,<Excel 2007 Formula,XLSX FORMULA>,<Excel 2000 Formula,EXL2K FORMULA>,<HTML Active Report,AHTML>,<Active Report for Adobe Flash Player,FLEX>,<Active Report for PDF,APDF>,<PowerPoint,PPT>).Select type of display output.
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
ENDSTYLE
END
