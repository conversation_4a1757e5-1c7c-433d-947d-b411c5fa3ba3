select 
popsel.TD_TERM_CODE,
popsel.CRN_KEY,
popsel.SUBJ_CODE,
popsel.CRSE_NUMBER,
popsel.SECTION_NUMBER,
popsel.MEETING_SCHD_CODE_1,
popsel.XLST_GROUP,
popsel.instructor_name,
popsel.instructor_number,
case 
when popsel.CRSE_NUMBER <300 then 'LL-UG'
when popsel.CRSE_NUMBER <500 and popsel.CRSE_NUMBER >=300 then 'UL-UG'
else 'GR'
end crse_level,
--count (popsel.prod_pidm) as SECTION_ENROLLMENT,
--sum (popsel.XLST_ACTUAL_ENROLLMENT) as CROSSLIST_ENROLLMENT,
count (popsel.td_pidm)as TD_SECTION_ENROLLMENT,
--sum (popsel.CREDIT_HOURS) as CREDIT_HOURS,
sum (popsel.TD_credit_hours) as TD_CREDIT_HOURS

from (
--primary instructor
select
I<PERSON>_TD_REGISTRATION_DETAIL.pidm td_pidm,
--IA_UM_REGISTRATION_DETAIL.pidm prod_pidm,
IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
IA_TD_REGISTRATION_DETAIL.CRN_KEY,
IA_TD_REGISTRATION_DETAIL.SUBJ_CODE,
IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
IA_TD_REGISTRATION_DETAIL.SECTION_NUMBER,
IA_TD_REGISTRATION_DETAIL.MEETING_SCHD_CODE_1,
IA_TD_REGISTRATION_DETAIL.XLST_GROUP,
IA_UM_REGISTRATION_DETAIL.XLST_ACTUAL_ENROLLMENT,
IA_UM_REGISTRATION_DETAIL.CREDIT_HOURS credit_hours,
IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours,
IA_UM_REGISTRATION_DETAIL.PRIMARY_INS_NAME instructor_name,
'1' as instructor_number

from
IA_TD_REGISTRATION_DETAIL
full outer join IA_UM_REGISTRATION_DETAIL on 
  IA_UM_REGISTRATION_DETAIL.TERM_CODE = IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE and
  IA_UM_REGISTRATION_DETAIL.CRN_KEY = IA_TD_REGISTRATION_DETAIL.CRN_KEY  and
  IA_UM_REGISTRATION_DETAIL.pidm = IA_TD_REGISTRATION_DETAIL.pidm

union all

--secondary instructor
select
IA_TD_REGISTRATION_DETAIL.pidm td_pidm,
--IA_UM_REGISTRATION_DETAIL.pidm prod_pidm,
IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
IA_TD_REGISTRATION_DETAIL.CRN_KEY,
IA_TD_REGISTRATION_DETAIL.SUBJ_CODE,
IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
IA_TD_REGISTRATION_DETAIL.SECTION_NUMBER,
IA_TD_REGISTRATION_DETAIL.MEETING_SCHD_CODE_1,
IA_TD_REGISTRATION_DETAIL.XLST_GROUP,
IA_UM_REGISTRATION_DETAIL.XLST_ACTUAL_ENROLLMENT,
IA_UM_REGISTRATION_DETAIL.CREDIT_HOURS credit_hours,
IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours,
IA_UM_REGISTRATION_DETAIL.SECOND_INS_NAME instructor_name,
'2' as instructor_number

from
IA_TD_REGISTRATION_DETAIL
full outer join IA_UM_REGISTRATION_DETAIL on 
  IA_UM_REGISTRATION_DETAIL.TERM_CODE = IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE and
  IA_UM_REGISTRATION_DETAIL.CRN_KEY = IA_TD_REGISTRATION_DETAIL.CRN_KEY  and
  IA_UM_REGISTRATION_DETAIL.pidm = IA_TD_REGISTRATION_DETAIL.pidm

union all
--third instructor
select
IA_TD_REGISTRATION_DETAIL.pidm td_pidm,
--IA_UM_REGISTRATION_DETAIL.pidm prod_pidm,
IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
IA_TD_REGISTRATION_DETAIL.CRN_KEY,
IA_TD_REGISTRATION_DETAIL.SUBJ_CODE,
IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
IA_TD_REGISTRATION_DETAIL.SECTION_NUMBER,
IA_TD_REGISTRATION_DETAIL.MEETING_SCHD_CODE_1,
IA_TD_REGISTRATION_DETAIL.XLST_GROUP,
IA_UM_REGISTRATION_DETAIL.XLST_ACTUAL_ENROLLMENT,
IA_UM_REGISTRATION_DETAIL.CREDIT_HOURS credit_hours,
IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours,
IA_UM_REGISTRATION_DETAIL.THIRD_INS_NAME instructor_name,
'3' as instructor_number

from
IA_TD_REGISTRATION_DETAIL
full outer join IA_UM_REGISTRATION_DETAIL on 
  IA_UM_REGISTRATION_DETAIL.TERM_CODE = IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE and
  IA_UM_REGISTRATION_DETAIL.CRN_KEY = IA_TD_REGISTRATION_DETAIL.CRN_KEY  and
  IA_UM_REGISTRATION_DETAIL.pidm = IA_TD_REGISTRATION_DETAIL.pidm

)popsel

WHERE
popsel.TD_TERM_CODE in ('201440','201510','201520','201530')

group by
popsel.TD_TERM_CODE,
popsel.CRN_KEY,
popsel.SUBJ_CODE,
popsel.CRSE_NUMBER,
popsel.SECTION_NUMBER,
popsel.MEETING_SCHD_CODE_1,
popsel.XLST_GROUP,
popsel.instructor_name,
popsel.instructor_number,
case 
when popsel.CRSE_NUMBER <300 then 'LL-UG'
when popsel.CRSE_NUMBER <500 and popsel.CRSE_NUMBER >=300 then 'UL-UG'
else 'GR'
end 


;