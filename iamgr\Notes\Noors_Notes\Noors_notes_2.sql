create table ia_reg_student_daily as (
select *
from um_reg_student_daily
)

create table IA_REG_COURSE_SECTION_DAILY as (
select *
from UM_REG_COURSE_SECTION_DAILY
)


select *
from um_reg_student_daily

/*Last Year*/ 
select rpt_student_type_code, 
sum(head_count) 
from UM_REG_STUDENT_DAILY 
where run_date = (trunc(sysdate)- 365) and 

where term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
inner join IA_COHORT_POP_UNDUP_TBL on 
rpt_levl_code = 'UG' 
group by rpt_student_type_code


/*Next Fall*/ 
select rpt_student_type_code, 
sum(head_count) 
from UM_REG_STUDENT_DAILY 
where run_date = '15-JUN-15' and 
term_code = '201610' and 
rpt_levl_code = 'UG' 
group by rpt_student_type_code


select (trunc(sysdate) - 365)
from Dual
