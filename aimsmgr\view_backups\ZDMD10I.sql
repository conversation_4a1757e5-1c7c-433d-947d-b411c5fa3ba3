CREATE OR REPLACE PACKAGE "ZDMD10I" as 

/*
  University of Michigan-Flint
  Name: ZDMD10I
  Date: September 15, 2014
  Description:
    This package, ZDMD10I, is responsible for updating the tenth day table 
    umf_10d.
  Version: 2.0
  Update Log:

  Copyrights (c) 2014, All Rights reserved: http://www.umflint.edu
*/

  -- List of functions and procedures that can be found in this package. 
  procedure p_extract_umf_10d;

  procedure p_import_test_score;
  procedure p_import_demographic;
  procedure p_import_student_data;
  procedure p_import_adm_app;
  procedure p_import_reg_detail;

end zdmd10i;

/


CREATE OR REPLACE PACKAGE BODY "ZDMD10I" as

/*
  University of Michigan-Flint
  Name: ZDMD10I Body
  Date: September 15, 2014
  Description:
    This is the body file for ZDMD10I which is will create the umf_10d table.
  Version: 2.0
  Update Log:

  Copyrights (c) 2014, All Rights reserved: http://www.umflint.edu
*/

  -- ***************************************************************************
  -- This procedure will import the test scores into the tenth day table.
  procedure p_import_test_score as

    procedure p_delete_from_td_table(term_code_in varchar2) as
    cursor load_cur
    is
    select
    td_test_score.rowid
    from td_test_score
    where td_test_score.td_term_code = term_code_in
    order by td_test_score.rowid;

    type rowid_table is table of rowid index by binary_integer; 
    rowid_rec rowid_table;

    begin
      open load_cur;
      loop
        fetch load_cur bulk collect into rowid_rec limit(2000);

        forall i in 1..rowid_rec.count
        delete from td_test_score
        where td_test_score.rowid = rowid_rec(i);

        commit;
        exit when rowid_rec.count < 1;
      end loop;
      close load_cur;
    end p_delete_from_td_table; 

  begin
    p_delete_from_td_table('201330');

    insert into td_test_score(
    td_test_score.td_term_code,
    td_test_score.pidm,

    td_test_score.sat_verbal_code,
    td_test_score.sat_verbal,
--    td_test_score.sat_verbal_desc,
    td_test_score.sat_verbal_date,

    td_test_score.sat_mathematics_code,
    td_test_score.sat_mathematics,
--    td_test_score.sat_mathematics_desc,
    td_test_score.sat_mathematics_date,

    td_test_score.sat_reading_code,
    td_test_score.sat_reading,
--    td_test_score.sat_reading_desc,
    td_test_score.sat_reading_date,

    td_test_score.act_english_code,
    td_test_score.act_english,
--    td_test_score.act_english_desc,
    td_test_score.act_english_date,

    td_test_score.act_math_code,
    td_test_score.act_math,
--    td_test_score.act_math_desc,
    td_test_score.act_math_date,

    td_test_score.act_composite_code,
    td_test_score.act_composite,
--    td_test_score.act_composite_desc,
    td_test_score.act_composite_date

    )
    select
    umf_10d_adm_app.term_code_key,
    umf_10d_adm_app.pidm_key,

    umf_10d_adm_app.test_code1,
    umf_10d_adm_app.test_score1,
--    (select stvtesc.stvtesc_code from stvtesc_ext stvtesc where stvtesc.stvtesc_code = umf_10d_adm_app.test_code1),
    umf_10d_adm_app.test_date1,

    umf_10d_adm_app.test_code2,
    umf_10d_adm_app.test_score2,
--    (select stvtesc.stvtesc_code from stvtesc_ext stvtesc where stvtesc.stvtesc_code = umf_10d_adm_app.test_code2),
    umf_10d_adm_app.test_date2,

    umf_10d_adm_app.test_code3,
    umf_10d_adm_app.test_score3,
--    (select stvtesc.stvtesc_code from stvtesc_ext stvtesc where stvtesc.stvtesc_code = umf_10d_adm_app.test_code3),
    umf_10d_adm_app.test_date3,

    umf_10d_adm_app.test_code4,
    umf_10d_adm_app.test_score4,
--    (select stvtesc.stvtesc_code from stvtesc_ext stvtesc where stvtesc.stvtesc_code = umf_10d_adm_app.test_code4),
    umf_10d_adm_app.test_date4,

    umf_10d_adm_app.test_code5,
    umf_10d_adm_app.test_score5,
--    (select stvtesc.stvtesc_code from stvtesc_ext stvtesc where stvtesc.stvtesc_code = umf_10d_adm_app.test_code5),
    umf_10d_adm_app.test_date5,

    umf_10d_adm_app.test_code6,
    umf_10d_adm_app.test_score6,
--    (select stvtesc.stvtesc_code from stvtesc_ext stvtesc where stvtesc.stvtesc_code = umf_10d_adm_app.test_code6),
    umf_10d_adm_app.test_date6

    from umf_10d_adm_app_ext umf_10d_adm_app
    where umf_10d_adm_app.appl_no_key = (select max(s1.appl_no_key)
                                         from umf_10d_adm_app_ext s1
                                         where s1.pidm_key = umf_10d_adm_app.pidm_key
                                         and s1.term_code_key = umf_10d_adm_app.term_code_key)
    and umf_10d_adm_app.term_code_key = '201330';

    commit;
  end p_import_test_score;

  -- ***************************************************************************
  -- This procedure will import the demographic information.
  procedure p_import_demographic as

  cursor old_cur
  is
  select
  umf_10d_stu_identification.pidm_key,
  umf_10d_stu_identification.term_code_key,
  umf_10d_stu_identification.id,
  umf_10d_stu_identification.ssn,
  umf_10d_stu_identification.last_name,
  umf_10d_stu_identification.first_name,
  umf_10d_stu_identification.middle_initial,
  umf_10d_stu_identification.name_prefix,
  umf_10d_stu_identification.name_suffix,
  umf_10d_stu_identification.street1_line1,
  umf_10d_stu_identification.street1_line2,
  umf_10d_stu_identification.city1,
  umf_10d_stu_identification.state1,
  umf_10d_stu_identification.zip1,
  umf_10d_stu_identification.cnty_code1,
  umf_10d_stu_identification.natn_code1,
  umf_10d_stu_identification.phone_area_code1,
  umf_10d_stu_identification.phone_number1,
  sd_join.birth_date,
  sd_join.age,
  sd_join.gender,
  sd_join.deceased_ind,
  sd_join.deceased_date,
  sd_join.race_code1,
  sd_join.race_code2,
  sd_join.race_code3,
  sd_join.race_code4,
  sd_join.race_code5,
  case when sd_join.race_code_count > 1 then 'Y' else null end multi_race_ind,
  sd_join.citz_code,
--  case when sd_join.citz_code = 'RN' or sd_join.citz_code = 'RO' then 'Y' else null end intl_ind,
  sd_join.report_ethnicity,
  sd_join.ethn_cde,
  sd_join.ethn_cde_desc,
  sd_join.graduated_ind,

  adm_app_join.sbgi_code_high_school,
--  (select stvsbgi.stvsbgi_desc from stvsbgi_ext stvsbgi where stvsbgi.stvsbgi_code = adm_app_join.sbgi_code_high_school) hsch_desc,
  adm_app_join.sobsbgi_street_line1,
  adm_app_join.sobsbgi_street_line2,
  adm_app_join.sobsbgi_city,
  adm_app_join.sobsbgi_stat_code,
  adm_app_join.sobsbgi_zip,
  adm_app_join.high_school_grad_date,
  adm_app_join.high_school_rank,
  adm_app_join.high_school_size,
  adm_app_join.high_school_reported_gpa,

  adm_app_join.sbgi_code_prior_college1 pcol_code_1,
--  (select stvsbgi.stvsbgi_desc from stvsbgi_ext stvsbgi where stvsbgi.stvsbgi_code = adm_app_join.sbgi_code_prior_college1) pcol_desc_1,
  adm_app_join.sbgi_code_prior_college2 pcol_code_2,
--  (select stvsbgi.stvsbgi_desc from stvsbgi_ext stvsbgi where stvsbgi.stvsbgi_code = adm_app_join.sbgi_code_prior_college2) pcol_desc_2,
  adm_app_join.sbgi_code_prior_college3 pcol_code_3,
--  (select stvsbgi.stvsbgi_desc from stvsbgi_ext stvsbgi where stvsbgi.stvsbgi_code = adm_app_join.sbgi_code_prior_college3) pcol_desc_3,

  adm_app_join.international_birth_nation nation_birth_code,
--  (select stvnatn.stvnatn_nation from stvnatn_ext stvnatn where stvnatn.stvnatn_code = adm_app_join.international_birth_nation) nation_birth_desc,
  adm_app_join.international_legal_nation nation_citizen_code,
--  (select stvnatn.stvnatn_nation from stvnatn_ext stvnatn where stvnatn.stvnatn_code = adm_app_join.international_legal_nation) nation_citizen_desc,
  adm_app_join.international_visa_number visa_number,
  adm_app_join.international_visa_type visa_type_code --,
--  (select stvvtyp.stvvtyp_desc from stvvtyp_ext stvvtyp where stvvtyp.stvvtyp_code = adm_app_join.international_visa_type) visa_type_desc

  from umf_10d_stu_identification_ext umf_10d_stu_identification
  left outer join(
    select
    umf_10d_stu_data.pidm_key,
    umf_10d_stu_data.term_code_key,
    umf_10d_stu_data.birth_date,
    umf_10d_stu_data.age,
    umf_10d_stu_data.gender,
    umf_10d_stu_data.ethn_code,
    umf_10d_stu_data.deceased_ind,
    umf_10d_stu_data.deceased_date,
    umf_10d_stu_data.race_code1,
    umf_10d_stu_data.race_code2,
    umf_10d_stu_data.race_code3,
    umf_10d_stu_data.race_code4,
    umf_10d_stu_data.race_code5,
    umf_10d_stu_data.race_code_count,
    umf_10d_stu_data.ethn_cde,
    umf_10d_stu_data.ethn_cde_desc,
    umf_10d_stu_data.citz_code,
    umf_10d_stu_data.report_ethnicity,
    umf_10d_stu_data.graduated_ind
    from umf_10d_stu_data_ext umf_10d_stu_data
  )sd_join on sd_join.pidm_key = umf_10d_stu_identification.pidm_key
           and sd_join.term_code_key = umf_10d_stu_identification.term_code_key
  left outer join(
    select 
    umf_10d_adm_app.pidm_key,
    umf_10d_adm_app.term_code_key,
    umf_10d_adm_app.sbgi_code_high_school,
    umf_10d_adm_app.high_school_grad_date,
    umf_10d_adm_app.high_school_rank,
    umf_10d_adm_app.high_school_size,
    umf_10d_adm_app.high_school_reported_gpa,
    sobsbgi_join.sobsbgi_street_line1,
    sobsbgi_join.sobsbgi_street_line2,
    sobsbgi_join.sobsbgi_city,
    sobsbgi_join.sobsbgi_stat_code,
    sobsbgi_join.sobsbgi_zip,
    umf_10d_adm_app.sbgi_code_prior_college1,
    umf_10d_adm_app.sbgi_code_prior_college2,
    umf_10d_adm_app.sbgi_code_prior_college3,

    umf_10d_adm_app.international_birth_nation,
    umf_10d_adm_app.international_legal_nation,
    umf_10d_adm_app.international_visa_number,
    umf_10d_adm_app.international_visa_type
    from umf_10d_adm_app_ext umf_10d_adm_app
    left outer join(
      select
      sobsbgi.sobsbgi_sbgi_code,
      sobsbgi.sobsbgi_street_line1,
      sobsbgi.sobsbgi_street_line2,
      sobsbgi.sobsbgi_city,
      sobsbgi.sobsbgi_stat_code,
      sobsbgi.sobsbgi_zip
      from sobsbgi_ext sobsbgi
    ) sobsbgi_join on sobsbgi_join.sobsbgi_sbgi_code = umf_10d_adm_app.sbgi_code_high_school
    where umf_10d_adm_app.appl_no_key = (select max(s1.appl_no_key)
                                          from umf_10d_adm_app s1
                                          where s1.pidm_key = umf_10d_adm_app.pidm_key
                                          and s1.term_code_key = umf_10d_adm_app.term_code_key)
  )adm_app_join on adm_app_join.pidm_key = umf_10d_stu_identification.pidm_key
                and adm_app_join.term_code_key = umf_10d_stu_identification.term_code_key 
  where umf_10d_stu_identification.term_code_key < '201110';

  type old_table is table of old_cur%rowtype index by binary_integer;
  old_rec old_table;

  -- This procedure will delete from the tenth day table.
  procedure p_delete_from_td_table(term_code_in varchar2) 
    as
      cursor load_cur
      is
      select
      td_demographic.rowid
      from td_demographic
      where td_demographic.td_term_code = term_code_in
      order by td_demographic.rowid;

      type rowid_table is table of rowid index by binary_integer; 
      rowid_rec rowid_table;

    begin
        open load_cur;
        loop
          fetch load_cur bulk collect into rowid_rec limit(2000);
          forall i in 1..rowid_rec.count
          delete from td_demographic
          where td_demographic.rowid = rowid_rec(i);

          commit;
          exit when rowid_rec.count < 1;
        end loop;
        close load_cur;
    end p_delete_from_td_table; 

  begin
    p_delete_from_td_table('201330');

    open old_cur;
    fetch old_cur bulk collect into old_rec;
    close old_cur;

--    forall i in 1..old_rec.count
--    insert into td_demographic(
--    td_demographic.td_term_code,
----    td_demographic.td_term_desc,
--    td_demographic.dm_pidm,
--    td_demographic.umid,
--    td_demographic.last_name,
--    td_demographic.first_name,
--    td_demographic.middle_initial,
--    td_demographic.name_prefix,
--    td_demographic.name_suffix,
--    td_demographic.a1_street_line1,
--    td_demographic.a1_street_line2,
--    td_demographic.a1_city,
--    td_demographic.a1_state_code,
----    td_demographic.a1_state_desc,
--    td_demographic.a1_zip,
--    td_demographic.a1_zip5,
--    td_demographic.a1_county_code,
----    td_demographic.a1_county_desc,
--    td_demographic.a1_nation_code,
----    td_demographic.a1_nation_desc,
--    td_demographic.a1_area_code,
--    td_demographic.a1_phone_number,
--    td_demographic.birthdate,
--    td_demographic.age,
--    td_demographic.gender,
--    td_demographic.deceased_ind,
--    td_demographic.deceased_date,
--    td_demographic.race_code_1,
----    td_demographic.race_desc_1,
--    td_demographic.race_code_2,
----    td_demographic.race_desc_2,
--    td_demographic.race_code_3,
----    td_demographic.race_desc_3,
--    td_demographic.race_code_4,
----    td_demographic.race_desc_4,
--    td_demographic.race_code_5,
----    td_demographic.race_desc_5,
--    td_demographic.multi_race_ind, 
--    td_demographic.ethn_cde,
----    td_demographic.ethn_cde_desc,
--    td_demographic.citizenship_code,
----    td_demographic.citizenship_desc,
--    td_demographic.report_ethnicity,
--    td_demographic.new_ethnicity,
----    td_demographic.new_ethnicity_desc,
--    td_demographic.graduated_ind,
--
--    td_demographic.hsch_code,
----    td_demographic.hsch_desc,
--    td_demographic.hsch_street_line_1,
--    td_demographic.hsch_street_line_2,
--    td_demographic.hsch_city,
--    td_demographic.hsch_state,
--    td_demographic.hsch_zip,
--    td_demographic.hsch_grad_date,
--    td_demographic.hsch_rank,
--    td_demographic.hsch_size,
--    td_demographic.hsch_gpa,
--
--    td_demographic.pcol_code_1,
----    td_demographic.pcol_desc_1,
--    td_demographic.pcol_code_2,
----    td_demographic.pcol_desc_2,
--    td_demographic.pcol_code_3,
----    td_demographic.pcol_desc_3,
--    
--    td_demographic.nation_birth_code,
----    td_demographic.nation_birth_desc,
--    td_demographic.nation_citizen_code,
----    td_demographic.nation_citizen_desc,
--    td_demographic.visa_number,
--    td_demographic.visa_type_code --,
----    td_demographic.visa_type_desc
--    
--    )values(
--    old_rec(i).term_code_key,
----    (select stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = old_rec(i).term_code_key),
--    old_rec(i).pidm_key,
--    old_rec(i).id,
--    old_rec(i).last_name,
--    old_rec(i).first_name,
--    old_rec(i).middle_initial,
--    old_rec(i).name_prefix,
--    old_rec(i).name_suffix,
--    old_rec(i).street1_line1,
--    old_rec(i).street1_line2,
--    old_rec(i).city1,
--    old_rec(i).state1,
----    (select stvstat.stvstat_desc from stvstat_ext stvstat where stvstat.stvstat_code = old_rec(i).state1),
--    old_rec(i).zip1,
--    substr(old_rec(i).zip1, 1, 5),
--    old_rec(i).cnty_code1,
----    (select stvcnty.stvcnty_desc from stvcnty_ext stvcnty where stvcnty.stvcnty_code = old_rec(i).cnty_code1),
--    old_rec(i).natn_code1,
----    (select stvnatn.stvnatn_nation from stvnatn_ext stvnatn where stvnatn.stvnatn_code = old_rec(i).natn_code1),
--    old_rec(i).phone_area_code1,
--    old_rec(i).phone_number1,
--    old_rec(i).birth_date,
--    old_rec(i).age,
--    old_rec(i).gender,
--    old_rec(i).deceased_ind,
--    old_rec(i).deceased_date,
--    old_rec(i).race_code1,
----    (select gorrace.gorrace_desc from gorrace_ext gorrace where gorrace.gorrace_race_cde = old_rec(i).race_code1),
--    old_rec(i).race_code2,
----    (select gorrace.gorrace_desc from gorrace_ext gorrace where gorrace.gorrace_race_cde = old_rec(i).race_code2),
--    old_rec(i).race_code3,
----    (select gorrace.gorrace_desc from gorrace_ext gorrace where gorrace.gorrace_race_cde = old_rec(i).race_code3),
--    old_rec(i).race_code4,
----    (select gorrace.gorrace_desc from gorrace_ext gorrace where gorrace.gorrace_race_cde = old_rec(i).race_code4),
--    old_rec(i).race_code5,
----    (select gorrace.gorrace_desc from gorrace_ext gorrace where gorrace.gorrace_race_cde = old_rec(i).race_code5),
--    old_rec(i).multi_race_ind,
--    old_rec(i).citz_code,
----    (select stvcitz.stvcitz_code from stvcitz_ext stvcitz where stvcitz.stvcitz_code = old_rec(i).citz_code),
--    old_rec(i).report_ethnicity,
--    old_rec(i).ethn_cde,
----    old_rec(i).ethn_cde_desc,
--    old_rec(i).graduated_ind,
--
--    old_rec(i).sbgi_code_high_school,
----    old_rec(i).hsch_desc,
--    old_rec(i).sobsbgi_street_line1,
--    old_rec(i).sobsbgi_street_line2,
--    old_rec(i).sobsbgi_city,
--    old_rec(i).sobsbgi_stat_code,
--    old_rec(i).sobsbgi_zip,
--    old_rec(i).high_school_grad_date,
--    old_rec(i).high_school_rank,
--    old_rec(i).high_school_size,
--    old_rec(i).high_school_reported_gpa,
--    
--    old_rec(i).pcol_code_1,
----    old_rec(i).pcol_desc_1,
--    old_rec(i).pcol_code_2,
----    old_rec(i).pcol_desc_2,
--    old_rec(i).pcol_code_3,
----    old_rec(i).pcol_desc_3,
--
--    old_rec(i).nation_birth_code,
----    old_rec(i).nation_birth_desc,
--    old_rec(i).nation_citizen_code,
----    old_rec(i).nation_citizen_desc,
--    old_rec(i).visa_number,
--    old_rec(i).visa_type_code --,
----    old_rec(i).visa_type_desc
--    );

    commit;
  end p_import_demographic;

  -- ***************************************************************************
  -- This procedure will import the student data into the tenth day table. 
  procedure p_import_student_data as

  cursor d10_cur
  is
  select
  umf_10d_stu_data.pidm_key,
  umf_10d_stu_data.term_code_key,
  umf_10d_stu_data.enrolled_ind,
  umf_10d_stu_data.registered_ind,
  umf_10d_stu_data.term_code_last_attended,
  umf_10d_stu_data.graduated_ind,
  umf_10d_stu_data.enrollment_add_date,
  umf_10d_stu_data.birth_date,
  umf_10d_stu_data.age,
  umf_10d_stu_data.gender,
  umf_10d_stu_data.ethn_code,
  umf_10d_stu_data.deceased_ind,
  umf_10d_stu_data.deceased_date,
  umf_10d_stu_data.confidentiality_ind,
  umf_10d_stu_data.full_part_time_ind,
  umf_10d_stu_data.clas_code,
  umf_10d_stu_data.stst_code,
  umf_10d_stu_data.levl_code,
  umf_10d_stu_data.styp_code,
  umf_10d_stu_data.orig_styp_code,
  umf_10d_stu_data.term_code_admit,
  umf_10d_stu_data.camp_code,
  umf_10d_stu_data.resd_code,
  umf_10d_stu_data.rate_code,
  umf_10d_stu_data.admt_code,
  umf_10d_stu_data.coll_code,
  umf_10d_stu_data.degc_code,
  umf_10d_stu_data.majr_code,
  umf_10d_stu_data.majr_code_1_2,
  umf_10d_stu_data.majr_code_minor_1,
  umf_10d_stu_data.majr_code_minor_1_2,
  umf_10d_stu_data.majr_code_concentration_1,
  umf_10d_stu_data.majr_code_concentration_1_2,
  umf_10d_stu_data.levl_code_2,
  umf_10d_stu_data.coll_code_2,
  umf_10d_stu_data.degc_code_2,
  umf_10d_stu_data.majr_code_2,
  umf_10d_stu_data.majr_code_2_2,
  umf_10d_stu_data.majr_code_minor_2,
  umf_10d_stu_data.majr_code_minor_2_2,
  umf_10d_stu_data.majr_code_concentration_2,
  umf_10d_stu_data.majr_code_concentration_2_2,
  umf_10d_stu_data.majr_code_concentration_121,
  umf_10d_stu_data.majr_code_concentration_122,
  umf_10d_stu_data.majr_code_concentration_221,
  umf_10d_stu_data.majr_code_concentration_222,
  umf_10d_stu_data.program_1,
  umf_10d_stu_data.program_2,
  umf_10d_stu_data.term_code_intended_graduation,
  umf_10d_stu_data.intended_graduation_date,
  umf_10d_stu_data.advisor_last_name1,
  umf_10d_stu_data.advisor_first_name1,
  umf_10d_stu_data.hold_code1,
  umf_10d_stu_data.hold_reason1,
  umf_10d_stu_data.hold_code2,
  umf_10d_stu_data.hold_reason2,
  umf_10d_stu_data.hold_code3,
  umf_10d_stu_data.hold_reason3,
  umf_10d_stu_data.hold_code4,
  umf_10d_stu_data.hold_reason4,
  umf_10d_stu_data.hold_code5,
  umf_10d_stu_data.hold_reason5,
  umf_10d_stu_data.additional_holds_ind,
  umf_10d_stu_data.ar_oldest_effective_date,
  umf_10d_stu_data.egol_code,
  umf_10d_stu_data.online_courses_only,
  umf_10d_stu_data.site_code,
  umf_10d_stu_data.full_part_time_ind_umf,
  data_num_join.overall_lgpa_hours_attempted,
  data_num_join.overall_lgpa_hours_earned,
  data_num_join.overall_lgpa_hours,
  data_num_join.overall_lgpa_quality_points,
  data_num_join.overall_lgpa_gpa,
  data_num_join.overall_lgpa_hours_passed,
  data_num_join.inst_lgpa_hours_attempted,
  data_num_join.inst_lgpa_hours_earned,
  data_num_join.inst_lgpa_hours,
  data_num_join.inst_lgpa_quality_points,
  data_num_join.inst_lgpa_gpa,
  data_num_join.inst_lgpa_hours_passed,
  data_num_join.transfer_lgpa_hours_attempted,
  data_num_join.transfer_lgpa_hours_earned,
  data_num_join.transfer_lgpa_hours,
  data_num_join.transfer_lgpa_quality_points,
  data_num_join.transfer_lgpa_gpa,
  data_num_join.transfer_lgpa_hours_passed,
  data_num_join.total_credit_hours,
  data_num_join.total_billing_hours,
  data_num_join.ar_account_balance,
  data_num_join.ar_amount_due,
  data_num_join.ar_memo_balance,
  data_num_join.ar_deposit_balance,
  data_num_join.total_credit_hours_umf,
  attr_1_join.sgrsatt_atts_code stu_attr_1,
  attr_2_join.sgrsatt_atts_code stu_attr_2,
  attr_3_join.sgrsatt_atts_code stu_attr_3,
  attr_4_join.sgrsatt_atts_code stu_attr_4,
  attr_5_join.sgrsatt_atts_code stu_attr_5,
  case
    when attr_6_join.sgrsatt_atts_code is not null then 'Y'
    else null
  end stu_attr_more_ind 
  from umf_10d_stu_data_ext umf_10d_stu_data
  left outer join(
    select
    pidm_key,
    term_code_key,
    overall_lgpa_hours_attempted,
    overall_lgpa_hours_earned,
    overall_lgpa_hours,
    overall_lgpa_quality_points,
    overall_lgpa_gpa,
    overall_lgpa_hours_passed,
    inst_lgpa_hours_attempted,
    inst_lgpa_hours_earned,
    inst_lgpa_hours,
    inst_lgpa_quality_points,
    inst_lgpa_gpa,
    inst_lgpa_hours_passed,
    transfer_lgpa_hours_attempted,
    transfer_lgpa_hours_earned,
    transfer_lgpa_hours,
    transfer_lgpa_quality_points,
    transfer_lgpa_gpa,
    transfer_lgpa_hours_passed,
    total_credit_hours,
    total_billing_hours,
    ar_account_balance,
    ar_amount_due,
    ar_memo_balance,
    ar_deposit_balance,
    total_credit_hours_umf
    from umf_10d_stu_data_num_ext umf_10d_stu_data_num
  )data_num_join on data_num_join.pidm_key = umf_10d_stu_data.pidm_key
                 and data_num_join.term_code_key = umf_10d_stu_data.term_code_key    
  left outer join(
    select 
    umf_10d_stu_attr.sgrsatt_pidm,
    umf_10d_stu_attr.term_code_key,
    umf_10d_stu_attr.sgrsatt_atts_code,
    row_number() over(partition by umf_10d_stu_attr.sgrsatt_pidm, umf_10d_stu_attr.term_code_key order by umf_10d_stu_attr.sgrsatt_atts_code) order_num
    from umf_10d_stu_attr_ext umf_10d_stu_attr
  )attr_1_join on attr_1_join.sgrsatt_pidm = umf_10d_stu_data.pidm_key
               and attr_1_join.term_code_key = umf_10d_stu_data.term_code_key
               and attr_1_join.order_num = 1
  left outer join(
    select 
    umf_10d_stu_attr.sgrsatt_pidm,
    umf_10d_stu_attr.term_code_key,
    umf_10d_stu_attr.sgrsatt_atts_code,
    row_number() over(partition by umf_10d_stu_attr.sgrsatt_pidm, umf_10d_stu_attr.term_code_key order by umf_10d_stu_attr.sgrsatt_atts_code) order_num
    from umf_10d_stu_attr_ext umf_10d_stu_attr
  )attr_2_join on attr_2_join.sgrsatt_pidm = umf_10d_stu_data.pidm_key
               and attr_2_join.term_code_key = umf_10d_stu_data.term_code_key
               and attr_2_join.order_num = 2
  left outer join(
    select 
    umf_10d_stu_attr.sgrsatt_pidm,
    umf_10d_stu_attr.term_code_key,
    umf_10d_stu_attr.sgrsatt_atts_code,
    row_number() over(partition by umf_10d_stu_attr.sgrsatt_pidm, umf_10d_stu_attr.term_code_key order by umf_10d_stu_attr.sgrsatt_atts_code) order_num
    from umf_10d_stu_attr_ext umf_10d_stu_attr
  )attr_3_join on attr_3_join.sgrsatt_pidm = umf_10d_stu_data.pidm_key
               and attr_3_join.term_code_key = umf_10d_stu_data.term_code_key
               and attr_3_join.order_num = 3
  left outer join(
    select 
    umf_10d_stu_attr.sgrsatt_pidm,
    umf_10d_stu_attr.term_code_key,
    umf_10d_stu_attr.sgrsatt_atts_code,
    row_number() over(partition by umf_10d_stu_attr.sgrsatt_pidm, umf_10d_stu_attr.term_code_key order by umf_10d_stu_attr.sgrsatt_atts_code) order_num
    from umf_10d_stu_attr_ext umf_10d_stu_attr
  )attr_4_join on attr_4_join.sgrsatt_pidm = umf_10d_stu_data.pidm_key
               and attr_4_join.term_code_key = umf_10d_stu_data.term_code_key
               and attr_4_join.order_num = 4
  left outer join(
    select 
    umf_10d_stu_attr.sgrsatt_pidm,
    umf_10d_stu_attr.term_code_key,
    umf_10d_stu_attr.sgrsatt_atts_code,
    row_number() over(partition by umf_10d_stu_attr.sgrsatt_pidm, umf_10d_stu_attr.term_code_key order by umf_10d_stu_attr.sgrsatt_atts_code) order_num
    from umf_10d_stu_attr_ext umf_10d_stu_attr
  )attr_5_join on attr_5_join.sgrsatt_pidm = umf_10d_stu_data.pidm_key
               and attr_5_join.term_code_key = umf_10d_stu_data.term_code_key
               and attr_5_join.order_num = 5
  left outer join(
    select 
    umf_10d_stu_attr.sgrsatt_pidm,
    umf_10d_stu_attr.term_code_key,
    umf_10d_stu_attr.sgrsatt_atts_code,
    row_number() over(partition by umf_10d_stu_attr.sgrsatt_pidm, umf_10d_stu_attr.term_code_key order by umf_10d_stu_attr.sgrsatt_atts_code) order_num
    from umf_10d_stu_attr_ext umf_10d_stu_attr
  )attr_6_join on attr_6_join.sgrsatt_pidm = umf_10d_stu_data.pidm_key
               and attr_6_join.term_code_key = umf_10d_stu_data.term_code_key
               and attr_6_join.order_num = 6
  where umf_10d_stu_data.term_code_key < '201110';

  type d10_table is table of d10_cur%rowtype;
  d10_rec d10_table := d10_table(null);

  procedure p_delete_from_td_table(term_code_in varchar2) as
  cursor load_cur
  is
  select
  td_student_data.rowid
  from td_student_data
  where td_student_data.sd_term_code = term_code_in
  order by td_student_data.rowid;

  type rowid_table is table of rowid index by binary_integer; 
  rowid_rec rowid_table;

  begin
    open load_cur;
    loop
      fetch load_cur bulk collect into rowid_rec limit(2000);

      forall i in 1..rowid_rec.count
      delete from td_student_data
      where td_student_data.rowid = rowid_rec(i);

      commit;
      exit when rowid_rec.count < 1;
    end loop;
    close load_cur;
  end p_delete_from_td_table; 

  begin
--    p_delete_from_td_table('201330');

    open d10_cur;
    fetch d10_cur bulk collect into d10_rec;
    close d10_cur;

    forall k in 1..d10_rec.count
    insert into td_student_data_10d(
    td_student_data_10d.sd_pidm,                -- pidm_key
    td_student_data_10d.sd_term_code,           -- term_code_key
    td_student_data_10d.enrolled_ind,            -- enrolled_ind,
    td_student_data_10d.registered_ind,         -- registered_ind,
    td_student_data_10d.most_recent_term_code,  -- term_code_last_attended,
    --graduated_ind -- moved to td_demographic *******
    td_student_data_10d.enrollment_add_date,    -- enrollment_add_date
    --birth_date,   -- moved to td_demographic *******
    --age,
    --gender,
    --ethn_code,
    --deceased_ind,
    --deceased_date, -- *****************************
    td_student_data_10d.confidentiality_ind,      -- confidentiality_ind,
    td_student_data_10d.full_part_time_ind,       -- full_part_time_ind,
    td_student_data_10d.class_code,                -- clas_code,
    td_student_data_10d.student_status_code,      -- stst_code,
    td_student_data_10d.primary_level_code,       -- levl_code,
    td_student_data_10d.student_type_code,        -- styp_code,
    td_student_data_10d.orig_student_type_code,   -- orig_styp_code,
    td_student_data_10d.primary_admit_term,       -- term_code_admit,
    td_student_data_10d.primary_campus_code,      -- camp_code,
    td_student_data_10d.residency_code,           -- resd_code,
    td_student_data_10d.rate_code,                -- rate_code,
    td_student_data_10d.primary_admit_code,       -- admt_code,
    td_student_data_10d.primary_college_code,     -- coll_code,
    td_student_data_10d.primary_degree_code,      -- degc_code,
    td_student_data_10d.primary_major_1,          -- majr_code,
    td_student_data_10d.primary_major_2,          -- majr_code_1_2,
    td_student_data_10d.primary_minor_1,          -- majr_code_minor_1,
    td_student_data_10d.primary_minor_2,          -- majr_code_minor_1_2,
    td_student_data_10d.primary_conc_1,           -- majr_code_concentration_1,
    td_student_data_10d.primary_conc_1_major_attach,
    td_student_data_10d.primary_conc_2,           -- majr_code_concentration_1_2,
    td_student_data_10d.primary_conc_2_major_attach,
    td_student_data_10d.second_level_code,        -- levl_code_2,
    td_student_data_10d.second_college_code,      -- coll_code_2,
    td_student_data_10d.second_degree_code,       -- degc_code_2,
    td_student_data_10d.second_major_1,           -- majr_code_2,
    td_student_data_10d.second_major_2,           -- majr_code_2_2,
    td_student_data_10d.second_minor_1,           -- majr_code_minor_2,
    td_student_data_10d.second_minor_2,           -- majr_code_minor_2_2,
    td_student_data_10d.second_conc_1,            -- majr_code_concentration_2,
    td_student_data_10d.second_conc_1_major_attach,
    td_student_data_10d.second_conc_2,            -- majr_code_concentration_2_2,
    td_student_data_10d.second_conc_2_major_attach,
    td_student_data_10d.primary_conc_3,           -- majr_code_concentration_121,
    td_student_data_10d.primary_conc_3_major_attach,
    td_student_data_10d.primary_conc_4,           -- majr_code_concentration_122,
    td_student_data_10d.primary_conc_4_major_attach,
    td_student_data_10d.second_conc_3,            -- majr_code_concentration_221,
    td_student_data_10d.second_conc_3_major_attach,
    td_student_data_10d.second_conc_4,            -- majr_code_concentration_222,
    td_student_data_10d.second_conc_4_major_attach,
    td_student_data_10d.primary_program,          -- program_1,
    td_student_data_10d.second_program,           -- program_2,
    td_student_data_10d.exp_grad_term,            -- term_code_intended_graduation,
    td_student_data_10d.exp_grad_date,            -- intended_graduation_date,
    td_student_data_10d.non_primary_advr_1_last_name,     -- advisor_last_name1,
    td_student_data_10d.non_primary_advr_1_first_name,    -- advisor_first_name1,
    td_student_data_10d.hold_code_1,              -- hold_code1,
    td_student_data_10d.hold_reason_1,            -- hold_reason1,
    td_student_data_10d.hold_code_2,              -- hold_code2,
    td_student_data_10d.hold_reason_2,            -- hold_reason2,
    td_student_data_10d.hold_code_3,              -- hold_code3,
    td_student_data_10d.hold_reason_3,            -- hold_reason3,
    td_student_data_10d.hold_code_4,              -- hold_code4,
    td_student_data_10d.hold_reason_4,            -- hold_reason4,
    td_student_data_10d.hold_code_5,              -- hold_code5,
    td_student_data_10d.hold_reason_5,            -- hold_reason5,
    td_student_data_10d.more_holds_ind,           -- additional_holds_ind,
--    td_student_data_10d.ar_oldest_effective_date,  -- ar_oldest_effective_date
    td_student_data_10d.educ_goal,                -- egol_code,
    --race_code1, -- moved to td_demographic ***************************************
    --race_code2,
    --race_code3,
    --race_code4,
    --race_code5,
    --race_code_count,
    --ethn_cde,
    --ethn_cde_desc,
    --citz_code, -- ****************************************************************
    td_student_data_10d.online_courses_only_ind,     -- online_courses_only, 
    td_student_data_10d.site_code,
    td_student_data_10d.full_part_time_ind_umf,        -- full_part_time_ind_umf, -- ?
    -- report_ethnicity -- moved to td_demographic
    td_student_data_10d.overall_hours_attempted,  -- overall_lgpa_hours_attempted,
    td_student_data_10d.overall_hours_earned,         -- overall_lgpa_hours_earned,
    td_student_data_10d.overall_gpa_hours,            -- overall_lgpa_hours,
    td_student_data_10d.overall_quality_points,      -- overall_lgpa_quality_points,
    td_student_data_10d.overall_gpa,                  -- overall_lgpa_gpa,
    td_student_data_10d.overall_hours_passed,         -- overall_lgpa_hours_passed,
    td_student_data_10d.inst_hours_attempted,         -- inst_lgpa_hours_attempted,
    td_student_data_10d.inst_hours_earned,            -- inst_lgpa_hours_earned,
    td_student_data_10d.inst_gpa_hours,               -- inst_lgpa_hours,
    td_student_data_10d.inst_quality_points,          -- inst_lgpa_quality_points,
    td_student_data_10d.inst_gpa,                     -- inst_lgpa_gpa,
    td_student_data_10d.inst_hours_passed,            -- inst_lgpa_hours_passed,
    td_student_data_10d.transfer_hours_attempted,     -- transfer_lgpa_hours_attempted,
    td_student_data_10d.transfer_hours_earned,        -- transfer_lgpa_hours_earned,
    td_student_data_10d.transfer_gpa_hours,           -- transfer_lgpa_hours,
    td_student_data_10d.transfer_quality_points,      -- transfer_lgpa_quality_points,
    td_student_data_10d.transfer_gpa,                 -- transfer_lgpa_gpa,
    td_student_data_10d.transfer_hours_passed,        -- transfer_lgpa_hours_passed,
    td_student_data_10d.term_registered_hours,        -- total_credit_hours, -- ?
    td_student_data_10d.term_billing_hours,           -- total_billing_hours, -- ?
    td_student_data_10d.ar_account_balance,           -- ar_account_balance, -- ?
    td_student_data_10d.ar_amount_due,                -- ar_amount_due, -- ?
--    td_student_data_10d.ar_memo_balance,              -- ar_memo_balance, -- ?
--    td_student_data_10d.ar_deposit_balance,           -- ar_deposit_balance, -- ?
    td_student_data_10d.total_credit_hours_umf,        -- total_credit_hours_umf -- ?

    td_student_data_10d.stu_attr_1,
    td_student_data_10d.stu_attr_2,
    td_student_data_10d.stu_attr_3,
    td_student_data_10d.stu_attr_4,
    td_student_data_10d.stu_attr_5,
    td_student_data_10d.stu_attr_more_ind,
    td_student_data_10d.stu_attr_b2,
    td_student_data_10d.stu_attr_ho,
    td_student_data_10d.stu_attr_js,
    td_student_data_10d.stu_attr_k12a,
    td_student_data_10d.stu_attr_k12e,
    td_student_data_10d.stu_attr_k12h,
    td_student_data_10d.stu_attr_lape,
    td_student_data_10d.stu_attr_laph,
    td_student_data_10d.stu_attr_mcc, 
    td_student_data_10d.stu_attr_fyew,
    td_student_data_10d.stu_attr_libp,
    td_student_data_10d.stu_attr_lilp,
    td_student_data_10d.stu_attr_asa,
    td_student_data_10d.stu_attr_awd, 
    td_student_data_10d.stu_attr_ba,
    td_student_data_10d.stu_attr_capw,
    td_student_data_10d.stu_attr_core,
    td_student_data_10d.stu_attr_edp, 
    td_student_data_10d.stu_attr_engp,
    td_student_data_10d.stu_attr_iexc,
    td_student_data_10d.stu_attr_lang,
    td_student_data_10d.stu_attr_tr,
    td_student_data_10d.stu_attr_tc,
    td_student_data_10d.stu_attr_uti, 
    td_student_data_10d.stu_attr_lcbp,
    td_student_data_10d.stu_attr_lihp,
    td_student_data_10d.stu_attr_lipe,
    td_student_data_10d.stu_attr_utbp,
    td_student_data_10d.stu_attr_utlp,
    td_student_data_10d.stu_attr_utpe

    )values(
    d10_rec(k).pidm_key,
    d10_rec(k).term_code_key,
    d10_rec(k).enrolled_ind,
    d10_rec(k).registered_ind,
    d10_rec(k).term_code_last_attended,
    d10_rec(k).enrollment_add_date,
    d10_rec(k).confidentiality_ind,
    d10_rec(k).full_part_time_ind,
    d10_rec(k).clas_code,
    d10_rec(k).stst_code,
    d10_rec(k).levl_code,
    d10_rec(k).styp_code,
    d10_rec(k).orig_styp_code,
    d10_rec(k).term_code_admit,
    d10_rec(k).camp_code,
    d10_rec(k).resd_code,
    d10_rec(k).rate_code,
    d10_rec(k).admt_code,
    d10_rec(k).coll_code,
    d10_rec(k).degc_code,
    d10_rec(k).majr_code,
    d10_rec(k).majr_code_1_2,
    d10_rec(k).majr_code_minor_1,
    d10_rec(k).majr_code_minor_1_2,
    d10_rec(k).majr_code_concentration_1,
    case when d10_rec(k).majr_code_concentration_1 is not null then d10_rec(k).majr_code else null end,
    d10_rec(k).majr_code_concentration_1_2,
    case when d10_rec(k).majr_code_concentration_1_2 is not null then d10_rec(k).majr_code else null end,
    d10_rec(k).levl_code_2,
    d10_rec(k).coll_code_2,
    d10_rec(k).degc_code_2,
    d10_rec(k).majr_code_2,
    d10_rec(k).majr_code_2_2,
    d10_rec(k).majr_code_minor_2,
    d10_rec(k).majr_code_minor_2_2,
    d10_rec(k).majr_code_concentration_2,
    case when d10_rec(k).majr_code_concentration_2 is not null then d10_rec(k).majr_code_2 else null end,
    d10_rec(k).majr_code_concentration_2_2,
    case when d10_rec(k).majr_code_concentration_2_2 is not null then d10_rec(k).majr_code_2 else null end ,
    d10_rec(k).majr_code_concentration_121,
    case when d10_rec(k).majr_code_concentration_121 is not null then d10_rec(k).majr_code else null end,
    d10_rec(k).majr_code_concentration_122,
    case when d10_rec(k).majr_code_concentration_122 is not null then d10_rec(k).majr_code else null end,
    d10_rec(k).majr_code_concentration_221,
    case when d10_rec(k).majr_code_concentration_221 is not null then d10_rec(k).majr_code_2 else null end,
    d10_rec(k).majr_code_concentration_222,
    case when d10_rec(k).majr_code_concentration_222 is not null then d10_rec(k).majr_code_2 else null end,
    d10_rec(k).program_1,
    d10_rec(k).program_2,
    d10_rec(k).term_code_intended_graduation,
    d10_rec(k).intended_graduation_date,
    d10_rec(k).advisor_last_name1,
    d10_rec(k).advisor_first_name1,
    d10_rec(k).hold_code1,
    d10_rec(k).hold_reason1,
    d10_rec(k).hold_code2,
    d10_rec(k).hold_reason2,
    d10_rec(k).hold_code3,
    d10_rec(k).hold_reason3,
    d10_rec(k).hold_code4,
    d10_rec(k).hold_reason4,
    d10_rec(k).hold_code5,
    d10_rec(k).hold_reason5,
    d10_rec(k).additional_holds_ind,
    d10_rec(k).egol_code,
    d10_rec(k).online_courses_only,
    d10_rec(k).site_code,
    d10_rec(k).full_part_time_ind_umf,
    d10_rec(k).overall_lgpa_hours_attempted,
    d10_rec(k).overall_lgpa_hours_earned,
    d10_rec(k).overall_lgpa_hours,
    d10_rec(k).overall_lgpa_quality_points,
    d10_rec(k).overall_lgpa_gpa,
    d10_rec(k).overall_lgpa_hours_passed,
    d10_rec(k).inst_lgpa_hours_attempted,
    d10_rec(k).inst_lgpa_hours_earned,
    d10_rec(k).inst_lgpa_hours,
    d10_rec(k).inst_lgpa_quality_points,
    d10_rec(k).inst_lgpa_gpa,
    d10_rec(k).inst_lgpa_hours_passed,
    d10_rec(k).transfer_lgpa_hours_attempted,
    d10_rec(k).transfer_lgpa_hours_earned,
    d10_rec(k).transfer_lgpa_hours,
    d10_rec(k).transfer_lgpa_quality_points,
    d10_rec(k).transfer_lgpa_gpa,
    d10_rec(k).transfer_lgpa_hours_passed,
    d10_rec(k).total_credit_hours,
    d10_rec(k).total_billing_hours,
    d10_rec(k).ar_account_balance,
    d10_rec(k).ar_amount_due,
    d10_rec(k).total_credit_hours_umf,

    d10_rec(k).stu_attr_1,
    d10_rec(k).stu_attr_2,
    d10_rec(k).stu_attr_3,
    d10_rec(k).stu_attr_4,
    d10_rec(k).stu_attr_5,
    d10_rec(k).stu_attr_more_ind,
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'B2'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'HO'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'JS'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'K12A'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'K12E'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'K12H'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LAPE'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LAPH'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'MCC'), 
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'FYEW'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LIBP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LILP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'ASA'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'AWD'), 
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'BA'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'CAPW'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'CORE'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'EDP'), 
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'ENGP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'IEXC'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LANG'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'TR'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'TC'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'UTI'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LCBP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LIHP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'LIPE'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'UTBP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'UTLP'),
    (select umf_10d_stu_attr.sgrsatt_atts_code from umf_10d_stu_attr_ext umf_10d_stu_attr where umf_10d_stu_attr.sgrsatt_pidm = d10_rec(k).pidm_key and umf_10d_stu_attr.term_code_key = d10_rec(k).term_code_key and umf_10d_stu_attr.sgrsatt_atts_code = 'UTPE')
    );

    commit;
  end p_import_student_data;

  -- ***************************************************************************
  -- This procedure will import the admission applicants information into the 
  -- tenth day table.
  procedure p_import_adm_app as

  procedure p_delete_from_td_table(term_code_in varchar2) as
  cursor load_cur
  is
  select
  td_admissions_applicant.rowid
  from td_admissions_applicant
  where td_admissions_applicant.term_code_entry = term_code_in
  order by td_admissions_applicant.rowid;

  type rowid_table is table of rowid index by binary_integer; 
  rowid_rec rowid_table;

  begin
    open load_cur;
    loop
      fetch load_cur bulk collect into rowid_rec limit(2000);

      forall i in 1..rowid_rec.count
      delete from td_admissions_applicant
      where td_admissions_applicant.rowid = rowid_rec(i);

      commit;
      exit when rowid_rec.count < 1;
    end loop;
    close load_cur;
  end p_delete_from_td_table; 

  begin
    p_delete_from_td_table('201330');

    insert into td_admissions_applicant(
    td_admissions_applicant.ad_pidm,
    td_admissions_applicant.term_code_entry,
    td_admissions_applicant.term_code_entry_desc,
    td_admissions_applicant.appl_no,
    td_admissions_applicant.enrolled_ind,
    td_admissions_applicant.finaid_applicant_ind,
    td_admissions_applicant.appl_date,
    td_admissions_applicant.apst_code,
    td_admissions_applicant.apst_desc,
    td_admissions_applicant.apst_date,
    td_admissions_applicant.admt_code,
    td_admissions_applicant.admt_desc,
    td_admissions_applicant.styp_code,
    td_admissions_applicant.styp_desc,
    td_admissions_applicant.resd_code,
    td_admissions_applicant.resd_desc,
    td_admissions_applicant.rate_code,
    td_admissions_applicant.rate_desc,
    td_admissions_applicant.egol_code,
    td_admissions_applicant.egol_desc,

    td_admissions_applicant.apdc_code_1,
    td_admissions_applicant.apdc_desc_1,
    td_admissions_applicant.apdc_date_1,
    td_admissions_applicant.apdc_code_2,
    td_admissions_applicant.apdc_desc_2,
    td_admissions_applicant.apdc_date_2,
    td_admissions_applicant.apdc_code_3,
    td_admissions_applicant.apdc_desc_3,
    td_admissions_applicant.apdc_date_3,

    td_admissions_applicant.primary_program,
    td_admissions_applicant.primary_program_desc,
    td_admissions_applicant.primary_level_code,
    td_admissions_applicant.primary_level_desc,
    td_admissions_applicant.primary_college_code,
    td_admissions_applicant.primary_college_desc,
    td_admissions_applicant.primary_degree_code,
    td_admissions_applicant.primary_degree_desc,
    td_admissions_applicant.primary_campus_code,
    td_admissions_applicant.primary_campus_desc,
    td_admissions_applicant.primary_major_1,
    td_admissions_applicant.primary_major_1_desc,
    td_admissions_applicant.primary_major_1_cipc_code,
    td_admissions_applicant.primary_conc_1,
    td_admissions_applicant.primary_conc_1_desc,
    td_admissions_applicant.primary_conc_1_major_attach,
    td_admissions_applicant.primary_conc_2,
    td_admissions_applicant.primary_conc_2_desc,
    td_admissions_applicant.primary_conc_2_major_attach,

    td_admissions_applicant.second_college_code,
    td_admissions_applicant.second_college_desc,
    td_admissions_applicant.second_degree_code,
    td_admissions_applicant.second_degree_desc,

    td_admissions_applicant.second_major_1,
    td_admissions_applicant.second_major_1_desc,
    td_admissions_applicant.second_major_1_cipc_code
    )
    select
    umf_10d_adm_app.pidm_key,
    umf_10d_adm_app.term_code_key,
    (select stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = umf_10d_adm_app.term_code_key),
    umf_10d_adm_app.appl_no_key,
    umf_10d_adm_app.enrolled_ind,
    umf_10d_adm_app.finaid_applicant_ind,
    umf_10d_adm_app.application_date,
    umf_10d_adm_app.apst_code,
    (select stvapst.stvapst_desc from stvapst_ext stvapst where stvapst.stvapst_code = umf_10d_adm_app.apst_code),
    umf_10d_adm_app.application_status_date,
    umf_10d_adm_app.admt_code,
    (select stvadmt.stvadmt_desc from stvadmt_ext stvadmt where stvadmt.stvadmt_code = umf_10d_adm_app.admt_code),
    umf_10d_adm_app.styp_code,
    (select stvstyp.stvstyp_desc from stvstyp_ext stvstyp where stvstyp.stvstyp_code = umf_10d_adm_app.styp_code),
    umf_10d_adm_app.resd_code,
    (select stvresd.stvresd_desc from stvresd_ext stvresd where stvresd.stvresd_code = umf_10d_adm_app.resd_code),
    umf_10d_adm_app.rate_code,
    (select stvrate.stvrate_desc from stvrate_ext stvrate where stvrate.stvrate_code = umf_10d_adm_app.rate_code),
    umf_10d_adm_app.egol_code,
    (select stvegol.stvegol_desc from stvegol_ext stvegol where stvegol.stvegol_code = umf_10d_adm_app.egol_code),

    umf_10d_adm_app.apdc_code1,
    (select stvapdc.stvapdc_desc from stvapdc_ext stvapdc where stvapdc.stvapdc_code = umf_10d_adm_app.apdc_code1),
    umf_10d_adm_app.apdc_decision_date1,
    umf_10d_adm_app.apdc_code2,
    (select stvapdc.stvapdc_desc from stvapdc_ext stvapdc where stvapdc.stvapdc_code = umf_10d_adm_app.apdc_code2),
    umf_10d_adm_app.apdc_decision_date2,
    umf_10d_adm_app.apdc_code3,
    (select stvapdc.stvapdc_desc from stvapdc_ext stvapdc where stvapdc.stvapdc_code = umf_10d_adm_app.apdc_code3),
    umf_10d_adm_app.apdc_decision_date3,

    umf_10d_adm_app.program_1,
    (select smrprle.smrprle_program_desc from smrprle_ext smrprle where smrprle.smrprle_program = umf_10d_adm_app.program_1),
    umf_10d_adm_app.levl_code,
    (select stvlevl.stvlevl_desc from stvlevl_ext stvlevl where stvlevl.stvlevl_code = umf_10d_adm_app.levl_code),
    umf_10d_adm_app.coll_code1,
    (select stvcoll.stvcoll_desc from stvcoll_ext stvcoll where stvcoll.stvcoll_code = umf_10d_adm_app.coll_code1),
    umf_10d_adm_app.degc_code1,
    (select stvdegc.stvdegc_desc from stvdegc_ext stvdegc where stvdegc.stvdegc_code = umf_10d_adm_app.degc_code1),
    umf_10d_adm_app.camp_code,
    (select stvcamp.stvcamp_desc from stvcamp_ext stvcamp where stvcamp.stvcamp_code = umf_10d_adm_app.camp_code),
    umf_10d_adm_app.majr_code1,
    (select stvmajr.stvmajr_desc from stvmajr_ext stvmajr where stvmajr.stvmajr_code = umf_10d_adm_app.majr_code1),
    (select stvmajr.stvmajr_cipc_code from stvmajr_ext stvmajr where stvmajr.stvmajr_code = umf_10d_adm_app.majr_code1),
    umf_10d_adm_app.majr_code1_conc1,
    (select stvmajr.stvmajr_desc from stvmajr_ext stvmajr where stvmajr.stvmajr_code = umf_10d_adm_app.majr_code1_conc1),
    case when umf_10d_adm_app.majr_code1_conc1 is not null then umf_10d_adm_app.majr_code1 else null end,    
    umf_10d_adm_app.majr_code1_conc2,
    (select stvmajr.stvmajr_desc from stvmajr_ext stvmajr where stvmajr.stvmajr_code = umf_10d_adm_app.majr_code1_conc2),
    case when umf_10d_adm_app.majr_code1_conc2 is not null then umf_10d_adm_app.majr_code1 else null end,

    umf_10d_adm_app.coll_code2,
    (select stvcoll.stvcoll_desc from stvcoll_ext stvcoll where stvcoll.stvcoll_code = umf_10d_adm_app.coll_code2),
    umf_10d_adm_app.degc_code2,
    (select stvdegc.stvdegc_desc from stvdegc_ext stvdegc where stvdegc.stvdegc_code = umf_10d_adm_app.degc_code2),
    umf_10d_adm_app.majr_code2,
    (select stvmajr.stvmajr_desc from stvmajr_ext stvmajr where stvmajr.stvmajr_code = umf_10d_adm_app.majr_code2),
    (select stvmajr.stvmajr_cipc_code from stvmajr_ext stvmajr where stvmajr.stvmajr_code = umf_10d_adm_app.majr_code2)

    from umf_10d_adm_app
    where umf_10d_adm_app.term_code_key = '201330'
    ;

    commit;
  end p_import_adm_app;

  -- ***************************************************************************
  -- This procedure will import the registration detail into the tenth day 
  -- table.
  procedure p_import_reg_detail
  as

  procedure p_delete_from_td_table(term_code_in varchar2) as
  cursor load_cur
  is
  select
  td_registration_detail.rowid
  from td_registration_detail
  where td_registration_detail.td_term_code = term_code_in
  order by td_registration_detail.rowid;

  type rowid_table is table of rowid index by binary_integer; 
  rowid_rec rowid_table;

  begin
    open load_cur;
    loop
      fetch load_cur bulk collect into rowid_rec limit(2000);

      forall i in 1..rowid_rec.count
      delete from td_registration_detail
      where td_registration_detail.rowid = rowid_rec(i);

      commit;
      exit when rowid_rec.count < 1;
    end loop;
    close load_cur;
  end p_delete_from_td_table; 

  begin
    p_delete_from_td_table('201330');

    insert into td_registration_detail(
    td_registration_detail.pidm,
    td_registration_detail.td_term_code,
    td_registration_detail.td_term_desc,
    td_registration_detail.crn_key,
    td_registration_detail.reg_seq,
    td_registration_detail.rsts_code,
    td_registration_detail.rsts_desc,
    td_registration_detail.rsts_date,
    td_registration_detail.subj_code,
    td_registration_detail.crse_number,
    td_registration_detail.section_number,
    td_registration_detail.billing_hours,
    td_registration_detail.credit_hours,
    td_registration_detail.gmod_code,
    td_registration_detail.gmod_desc,
    td_registration_detail.add_date,
    td_registration_detail.levl_code,
    td_registration_detail.levl_desc,
    td_registration_detail.registration_user_id,
    td_registration_detail.coll_code,
    td_registration_detail.coll_desc,
    td_registration_detail.camp_code,
    td_registration_detail.camp_desc)
    select 
    umf_10d_stu_reg_detail.pidm_key pidm,
    umf_10d_stu_reg_detail.term_code_key td_term_code,
    (select stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = umf_10d_stu_reg_detail.term_code_key) td_term_desc,
    umf_10d_stu_reg_detail.crn_key crn_key,
    umf_10d_stu_reg_detail.registration_sequence_number reg_seq,
    umf_10d_stu_reg_detail.rsts_code rsts_code,
    (select stvrsts.stvrsts_desc from stvrsts_ext stvrsts where stvrsts.stvrsts_code = umf_10d_stu_reg_detail.rsts_code) rsts_desc,
    umf_10d_stu_reg_detail.registration_status_date rsts_date,
    umf_10d_stu_reg_detail.subj_code subj_code,
    umf_10d_stu_reg_detail.course_number crse_number,
    -- umf_10d_stu_reg_detail.course_title,
    umf_10d_stu_reg_detail.section_number section_number,
    umf_10d_stu_reg_detail.section_billing_hours billing_hours,
    umf_10d_stu_reg_detail.section_credit_hours credit_hours,
    umf_10d_stu_reg_detail.gmod_code gmod_code,
    (select stvgmod.stvgmod_desc from stvgmod_ext stvgmod where stvgmod.stvgmod_code = umf_10d_stu_reg_detail.gmod_code) gmod_desc,
    umf_10d_stu_reg_detail.section_add_date add_date,
    umf_10d_stu_reg_detail.levl_code_section levl_code,
    (select stvlevl.stvlevl_desc from stvlevl_ext stvlevl where stvlevl.stvlevl_code = umf_10d_stu_reg_detail.levl_code_section) levl_desc,
    --umf_10d_stu_reg_detail.instructor_id,
    --umf_10d_stu_reg_detail.instructor_last_name,
    --umf_10d_stu_reg_detail.instructor_first_name,
    --umf_10d_stu_reg_detail.instructor_middle_initial,
    umf_10d_stu_reg_detail.registration_user_id registration_user_id,
    umf_10d_stu_reg_detail.course_college coll_code,
    (select stvcoll.stvcoll_desc from stvcoll_ext stvcoll where stvcoll.stvcoll_code = umf_10d_stu_reg_detail.course_college) coll_desc,
    umf_10d_stu_reg_detail.camp_code_section camp_code,
    (select stvcamp.stvcamp_desc from stvcamp_ext stvcamp where stvcamp.stvcamp_code = umf_10d_stu_reg_detail.camp_code_section) camp_desc
    from umf_10d_stu_reg_detail_ext umf_10d_stu_reg_detail
    where umf_10d_stu_reg_detail.term_code_key = '201330';

    commit;
  end p_import_reg_detail;

  -- ***************************************************************************
  -- This procedure will extract the tenth day tables.
  procedure p_extract_umf_10d
  as

  type d10_array_type is varray(100) of varchar2(32);
  d10_array d10_array_type;

  begin
    d10_array := d10_array_type('umf_10d_adm_app', 
                                'umf_10d_stu_attr',
                                'umf_10d_stu_data', 
                                'umf_10d_stu_data_num',
                                'umf_10d_stu_identification', 
                                'umf_10d_stu_reg_detail');

    for i in d10_array.first..d10_array.last
    loop
      execute immediate('truncate table ' || d10_array(i) || '_ext ');
      execute immediate('insert into ' || d10_array(i) || '_ext select * from ' || d10_array(i));

      commit;
    end loop;
  end p_extract_umf_10d;

end zdmd10i;

/
