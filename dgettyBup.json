{"connections": [{"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "EpPjwdilmjMquxODliCEX40U8KKtdT35oonaxUTNwNyvdysYVMVcN2lP", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "recmgr", "ExportKeyChecksum": "STB6ZIIp4hdYMqIvcUayH/0G+CIbUgCjfjUOdFKqrEBg9zxR"}, "name": "IAPROD_recmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "PmhAwAdaDeRaCPezIddaLtSV9tBzvTt+S8chh6g5qDHRsq0=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "grmgr", "ExportKeyChecksum": "YUuXoQMevft9B//TEgST03HdBAatBHRfceKy5tHUq2+XR+q7"}, "name": "IAPROD_grmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "783OhMNmsG5rJbq5Gq9P9FUMDUFGRjBTdVncItEanzZA5uRW7g==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "regmgr", "ExportKeyChecksum": "n5NhbJiyrE2LuaY2BRNL39vPDzOjcr/ex01mIJFBxXufUGyH"}, "name": "IAPROD_regmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "YJxhmRLBCdsfJhQC4uJ+s/2TtOfKjtxMHQ9jINa0nwkXHma9tzConufG", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "chsmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "mJwbt4Wb97meelult8AJm7U3+kbN0YJ4IvqWuYTeuByDrdJn"}, "name": "IAPROD_chsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "pLt+gUN1njxxliT/QSa4XxOGYszVlI+IyZHa/ZjAEV6mHsWXtPMXPQ==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "famgr", "ExportKeyChecksum": "NAwfGhGmHZX7umpCCc13RimOndn4KLrRShA1lSwe9wsO4r0M"}, "name": "IAPROD_famgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "4J70nPysYBPKnbCKMHgGSUUcCvT05BTJi5KeDu8zdxuJ1Mxi", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "ugmgr", "ExportKeyChecksum": "NlEgpWJZOHh5e8Rxb1IMGwQxZK8M11uTNnXpmbWtPuTbpsZe"}, "name": "IAPROD_ugmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "TNS", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "ExportPasswordMode": "Key", "customUrl": "PROD", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "password": "xuplElcUztdIQ8Zn3YliOGlcaTZYmrpaNUrP6mXiWiD9r376G6+2DLO06z8=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "user": "webfocus", "PROXY_USER_NAME": "", "ExportKeyChecksum": "Oe6kg1lLCEL1YyzP7WKsy6o/rTnDhIX7PdPhukWcTpSsDE82"}, "name": "PROD_webfocus", "type": "jdbc"}, {"info": {"customUrl": "jdbc:postgresql://***************/postgres?:5432/", "hostname": "***************/postgres?", "password": "lYwUZXNUZH1UTmp3tIkHE0JweMaV5YGMS8SJ4a0i5/901JmoU0FOFg==", "driver": "org.postgresql.Driver", "subtype": "SDPostgreSQL", "port": "5432", "SavePassword": "true", "RaptorConnectionType": "SDPostgreSQL", "user": "postgres", "ExportPasswordMode": "Key", "ExportKeyChecksum": "<PERSON><PERSON><PERSON><PERSON>"}, "name": "dw1.postres.postgres", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "I2mIbjoi40mFpLI7yIx1mx+VucyJHEYVTyfw2hZxYV/M3UH8Owhp9RBh", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "system", "ExportKeyChecksum": "qx7BlAD1kHpU9t77ZoIGm56pysl1fVGBDnB41VO0/l/DM4aN"}, "name": "IAPROD_system", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "R03+yiTb+B3wF0GdiA9GnRqxUQRmIcdVDuhoaGDbdDj4lOpA", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "sscmgr", "ExportKeyChecksum": "temwkZa4lMlEVs0u5jap1TahGd1s/OQXYojEL5TL0pYtE3CR"}, "name": "IAPROD_sscmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "4n6hrcv9cFvX5LfqUmBDt73P53FK1BWMr/DGIxMzOCwEO30NPw7P1hHK9pLy", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "oelmgr", "ExportKeyChecksum": "2p+n34iyVEmnFj/bWpW7rJ1fgyfDFhQXI8CUTIQK8oXaC5vk"}, "name": "IAPROD_oelmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "serviceName": "IAPROD", "Connection-Color-For-Editors": "-16776961", "ExportPasswordMode": "Key", "customUrl": "***********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "jq4S+zLeJi60Nu3frWu9FRGKcHUxQ2U8Ok38VcsY1BfT56Q=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "canvasmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "SpwYDbpKMCQdfCDPSRhl21xZ7Y/Ql081zGsXpod88ubEMUxG"}, "name": "2_IAPROD_canvasmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "Connection-Color-For-Editors": "-16776961", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "A4N1MqbZYQ2aWhhmoz8ZtG9PnmgKZGqo35qeDqfXMjd5VJYaI/w309X/", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "contmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "S+0zjmiTMRMfF55zRaErZLtgcLtjgOMksUEIQ7QQqsK1QL6S"}, "name": "2_IAPROD_contmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "28pQUHVmy9oJ1cif/4/bmgSXCji8tJjw1pcwOMI8yy7W5+g=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "webfocus", "ExportKeyChecksum": "+LwnFVfj6liCNr3FIL+hesmf67ID0v290KVgO4lDz6RnjO3K"}, "name": "IAPROD_webfocus", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "IGJ0ZWupeJPV9QxpE1PPbdbPEZI5m6UmbHkUP52PuDm4+I9OktiDMbLv", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "targetxmgr", "ExportKeyChecksum": "tdHW57UQKBW9ss0IppoN4PkbrCHscrgOVf9AhyD2yedoeLgM"}, "name": "iaprod_targetxmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "fD3XSjrFIThOE6Ozj0/ZgIGXwJPLTuFKT9q54Y06PJFM845DSvOy+JJs", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "silmgr", "ExportKeyChecksum": "1E+/IPTJPzfDEr/wSqpCVNWW75sCjXSGnnWC4VyUgwamO2CP"}, "name": "IAPROD_silmgr", "type": "jdbc"}, {"info": {"customUrl": "jdbc:postgresql://***************/testmgr?:5432/", "hostname": "***************/testmgr?", "password": "I4qhQ0g0rC+dIOu7AytVu9Ll0HYPG8VZf/BaOXKzkdtO65Qf", "driver": "org.postgresql.Driver", "subtype": "SDPostgreSQL", "port": "5432", "SavePassword": "true", "RaptorConnectionType": "SDPostgreSQL", "user": "testmgr", "ExportPasswordMode": "Key", "ExportKeyChecksum": "<PERSON><PERSON><PERSON><PERSON>"}, "name": "dw1.testmgr.testmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "+FYkosyNrlv7+wCEX83ZDQOelUpUSklPZXPjCIf2KLhzdZJQOaRE/Ml0", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "cepmgr", "ExportKeyChecksum": "N3EZp6TyODAPgYoQcBILJDNe5XFpk5h8MCeb1E/LINnQtL5z"}, "name": "IAPROD_cepmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "tG843yWPo4ow9RIX0IHdFcqfDNGC+q02AABLBv965abqGEPX1PAOdBYl", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "salesforcemgr", "ExportKeyChecksum": "k7wNeA+WXuR6RhtJF5WUa6b5YL3AKlOhxGNF/3k1bPK9GTXF"}, "name": "IAPROD_salesforcemgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "Connection-Color-For-Editors": "-16776961", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "W1CpnSUOg36nbEwQ/hvZUQCyjE2NuLKbOTBqM35dgwZv1g==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "iamgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "AvSiSlcpGAzRI5AIMjKFF5e8VwZKJabXhedzS5kx9bfPoi7Y"}, "name": "1_IAPROD_iamgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "ipetnWjfmSdB9AX7fGtu+12uXvzWMa6NVJCndt0Ea70Do9XzY9mY1s1b", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "npsasmgr", "ExportKeyChecksum": "fpoQEGr9Nlb/sU+UC5mGf1YuAJb1qBxOJItxw8iO/KkW6qWG"}, "name": "IAPROD_npsasmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "rjO4yaCetg9g3LkZgs6bWfzSMR6TAvzNQBijLZ0Fnqmv9G9aOfERdy7m", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "talendmgr", "ExportKeyChecksum": "x667/DeLGzm8I7vp1L2CL5qEJZh4AGf8RcnoOkT3LRWOUZlm"}, "name": "IAPROD_talendmgr", "type": "jdbc"}, {"info": {"customUrl": "jdbc:postgresql://***************/dm1?:5432/", "hostname": "***************/dm1?", "password": "boUNI5P+lCsLoNxQrcdUEaTuGtqv7FDyTOM4sfDUubJMb6bEUKkn9g==", "driver": "org.postgresql.Driver", "subtype": "SDPostgreSQL", "port": "5432", "SavePassword": "true", "RaptorConnectionType": "SDPostgreSQL", "user": "d<PERSON>ty", "ExportPasswordMode": "Key", "ExportKeyChecksum": "<PERSON><PERSON><PERSON><PERSON>"}, "name": "dw1.dm1.dgetty", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "x46xttMrSt+71VStRVE0hUv8lmhhAiK6irZlkf0Ev1Gr7JTe8Rdp3fiz", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "targetxmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "w4J9OEwj8iKuwS2k29ogI0sh351VwcR9iMUOr4UOe15ZN6Wz"}, "name": "IAPROD_targetxmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "DwViKEQD5vO7EAlbVKp0917nlYkZCqO3AiWhMUORa/m4V4IlW8OtFPS8", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "nscmgr", "ExportKeyChecksum": "AWy/8JC6P5hfNyPbs+SlLY4fCDvaDheRBXD/8unvV6ZtznrW"}, "name": "IAPROD_nscmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "wf8prod", "ExportPasswordMode": "Key", "customUrl": "****************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "localhost", "password": "uJUfcr9j1un/KSw80vJfA/rKKOO5+DYBz5ZJ8bJVKb3UyXo=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "system", "ExportKeyChecksum": "ify5wN2mDQv7yqSiXn7lsId8RDHnG7U+bz00ueIaYaduT+Ub"}, "name": "WF8PROD_system", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "false", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "PROD", "customUrl": "***************************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "ban-prod-db.umflint.edu", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1548", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "dgetty0"}, "name": "Banner-Prod", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "XhzHTLKnDZm8PW2ZE/XeUk4WzswiJEuQMAnBMy2ikzU7Xp4=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "intlmgr", "ExportKeyChecksum": "grscnBKzZ5cBkQOIH4Y2fe6vG7OkTRvVtHD6eB07Xh4/LC7G"}, "name": "IAPROD_intlmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "u/tEIvelEKHrD6TEn+powdb5egt7Z4oftsHVr4/p9RrUucWFfQx2OMVr", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "campuslabsmgr", "ExportKeyChecksum": "9LhpK6FpzuQwWz7ErajfdIAvcXi4u49lPB4A5mhsVTbYlO+g"}, "name": "IAPROD_campuslabsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "Connection-Color-For-Editors": "-16776961", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "DhcDrD9UurHwKux+cbaGD+8igyCdhZzP2s8n/lFDF7dQJps=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "aimsmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "fco6HUaCV8Yyc8AiwcVH5VJg3ev723oKNGAl+A3mGUzbto1I"}, "name": "1_IAPROD_aimsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "0JhAohQhe3nF/o8qk6IEyJliW944l6a3eI3KSaVysCVrzW8+T38PxBu2", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "rbessacmgr", "ExportKeyChecksum": "DYQ8Rkl7rmQa47MfY79hfVk3dSofKoU3Df92IAER2VAXCOyx"}, "name": "IAPROD_rbessacmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "gyjPycXkTwxoqc0n6Toj2SDQkpH7uiYnFGK37CKLMruezNgg0AZk3T8B", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "contmgr", "ExportKeyChecksum": "eLd02pzc5D26732fTRrq9xHvHRLLYiarg+m9C1cs4UVTr9yV"}, "name": "IAPROD_contmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "qeTwO1U1r7g2SS6xTgB1OEaPrtBFkuhoqjjd32fsE1oHe+ePvOqdE+Uu", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "hedamgr", "ExportKeyChecksum": "3q1xV9wd3HzkJn0tUgSkTpUaMwKQ5PkCQOXunpPn5kBvZzUt"}, "name": "IAPROD_hedamgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "Ec0RcanyT4olv/mzra4jRUISxYM1sHIkwObBQHHFJ2BS+Yg=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "umfmgr", "ExportKeyChecksum": "277xRUOY+SQH7Lx+m+R3xfV/IwJkd/KEYlwPTM7vfGH4/AtI"}, "name": "IAPROD_umfmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "5kO2wNM7brscdh8RMo9SLRSPi5wHQwmdJBkUVP6s+Vh+EQ1bzfl5qIqLKQ==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "dwrksmgr", "ExportKeyChecksum": "YeER1627WRmODYS+/XmeR851IcWMyxXk2eQFRsDljtTj0hy3"}, "name": "IAPROD_dwrksmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "AO2mCIcejR/4nu8easVr6j3hTPSbfzyPXSNDbzCkX8D3GGcIskztpuy9DeSkyRxy", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "fsbmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "oKXOIeUCYfQOx5QsSR7jjNwqyYUKSgNJL2tn8PntFwr4LI/5"}, "name": "IAPROD_fsbmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "jHa0nBvTdAFFFiGuQywysSrQxWPPH5qterZi2LCux9IR/jM9Cgb+wvSW", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "hrmgr", "ExportKeyChecksum": "4SwpLxHucY8BkKNeqVPgQ2j+7YkFeNsvhPTIr2KrgMOGYTk8"}, "name": "IAPROD_hrmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "M6wW/f6IEF4+HLUxZ0ZiSDcRAM7qpFZ8wlavbLntAgMopoAJGC8Z59+j", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "BO_FLINT_STUDENT", "ExportKeyChecksum": "uxevBSt8nNXA05LET+dGwwd8yCy1Ck3Uy0pvmINOTI+j/T8C"}, "name": "IAPROD_bo_flint_student", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "LSgePXj1GBhIlE+Kt6OiucdywLkxZ7EBW4AFifzRAMEVpSNRTIE+roUB", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "heerfmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "R5432bp4N7+crz34TskX4mrX8DMNw4qVySVwDZQaQvOs1VFg"}, "name": "IAPROD_heerfmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "jSIykD37bWLqbe1dOYUdqBc7yR0F/RiAg0ngZJaw1oDlpQ==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "tabmgr", "ExportKeyChecksum": "crvVyyXf9xrsYRdsxa0ioJf38DowmjLUlv3HBwodnr26gfoX"}, "name": "IAPROD_tabmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "h2PaNP3NQDucbEIJ4qcJIcYhsYV16h7780hr79Dvy8fzX+TJQvx0Wo0p", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "cashmgr", "ExportKeyChecksum": "yhdN9FUst06bb9PXOl1YgMCeXXpzlVZED0SjGPIydHE1PJps"}, "name": "IAPROD_cashsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "false", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "PROD", "customUrl": "***************************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "ban-prod-db.umflint.edu", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1548", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "dgetty0", "PROXY_USER_NAME": ""}, "name": "Banner_Prod", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "wn76wP6JyMNFBAakXF4glaCZWu9Anc05Yp0Jqek49BM1O3MaNhTczNXc", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "rcaster", "ExportKeyChecksum": "/t/K36pW/ojI34/JBmoJgV3OoSG2k3Fklw5/1R59utGsxuBK"}, "name": "IAPROD_rcaster", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "BYjcePNPkN1lqF6bFOaSdzMEKe4RqzAnyu4Y8rU33B3jLeCAJwkIu/Ne", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "maxientmgr", "ExportKeyChecksum": "X+epKXUnii1WY2N5A908kfN8Q0rkHmLD2EpCrZCyzNZiH2Y7"}, "name": "IAPROD_maxientmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "LGNws8GHADssMTaIEgYQvwuzdNEdbQ+PPsPE4GrxRrrpcGo=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "emasmgr", "ExportKeyChecksum": "VpbccVZ1WXuMy8j2Cu/EAMSRRj5QyEJZYGAdDYiwv2rePd0w"}, "name": "IAPROD_emasmgr", "type": "jdbc"}]}