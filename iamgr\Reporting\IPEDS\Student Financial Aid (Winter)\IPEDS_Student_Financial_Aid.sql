/*

*/
SELECT
    COUNT(*)
FROM
    td_student_data sd
WHERE
        sd.registered_ind = 'Y'
    AND sd.sd_term_code = '202010'                                     --UPDATE THIS
    AND sd.report_level_code = 'UG';

SELECT
    COUNT(*)
FROM
    td_student_data sd
WHERE
        sd.registered_ind = 'Y'
    AND sd.sd_term_code = '202010'                                     --UPDATE THIS
    AND sd.report_level_code = 'UG'
    AND sd.ia_student_type_code = 'F'
    AND sd.full_part_time_ind = 'F';
/*

*/
--create table ia_fa_ipeds_temp as
WITH dp1 AS (--5862
    SELECT
        sd.sd_pidm,
        sd.residency_code,
        sd.full_part_time_ind_umf,
        sd.ia_student_type_code,
        sd.fafy,
        fapp.aid_year,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source IN('FDRL', 'STAT')
                        AND fb.fund_type IN('GRNT', 'SCHL')
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   fdrl_stat_grnt_schl_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source IN('FDRL', 'STAT')
                AND fb.fund_type IN('GRNT', 'SCHL')
        ),
            0)     fdrl_stat_grnt_schl_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'FDRL'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   title_iv_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'FDRL'
        ),
            0)     title_iv_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'FDRL'
                        AND fb.fund_type = 'GRNT'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   fdrl_grnt_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'FDRL'
                AND fb.fund_type = 'GRNT'
        ),
            0)     fdrl_grnt_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
--    AND FB.FUND_SOURCE = 'FDRL'
                            AND fb.fund_type = 'GRNT'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   grnt_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
--  AND FB.FUND_SOURCE = 'FDRL'
                  AND fb.fund_type = 'GRNT'
        ),
            0)     grnt_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_type = 'SCHL'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   schl_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_type = 'SCHL'
        ),
            0)     schl_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_code = '1PELL'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   pell_grnt_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_code = '1PELL'
        ),
            0)     pell_grnt_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'FDRL'
                        AND fb.fund_type = 'GRNT'
                        AND fb.fund_code != '1PELL'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   other_fdrl_grnt_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'FDRL'
                AND fb.fund_type = 'GRNT'
                AND fb.fund_code != '1PELL'
        ),
            0)     other_fdrl_grnt_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source IN('STAT')
                        AND fb.fund_type IN('GRNT', 'SCHL')
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   stat_locl_grnt_schl_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source IN('STAT')
                AND fb.fund_type IN('GRNT', 'SCHL')
        ),
            0)     stat_locl_grnt_schl_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'INST'
                        AND fb.fund_type IN('GRNT', 'SCHL')
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   inst_grnt_schl_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'INST'
                AND fb.fund_type IN('GRNT', 'SCHL')
        ),
            0)     inst_grnt_schl_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
--  AND FB.FUND_SOURCE = 'INST'
                          AND fb.fund_type = 'LOAN'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   loan_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
--  AND FB.FUND_SOURCE = 'INST'
                  AND fb.fund_type = 'LOAN'
        ),
            0)     loan_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'FDRL'
                        AND fb.fund_type = 'LOAN'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   fdrl_loan_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'FDRL'
                AND fb.fund_type = 'LOAN'
        ),
            0)     fdrl_loan_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'EXTN'
                        AND fb.fund_type = 'LOAN'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   other_loan_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'EXTN'
                AND fb.fund_type = 'LOAN'
        ),
            0)     other_loan_amt,
        nvl((
            SELECT
                'Y'
            FROM
                dual
            WHERE
                EXISTS(
                    SELECT
                        fb.pidm
                    FROM
                        IA_FA_AWARD_BY_AIDY fb
                    WHERE
                            fb.pidm = sd.sd_pidm
                        AND fb.aid_year = sd.fafy
                        AND fb.fund_source = 'FDRL'
                        AND fb.fund_type = 'WORK'
                        AND fb.paid_amt > 0
                        AND fb.paid_amt IS NOT NULL
                )
        ),
            'N')   fdrl_work_ind,
        nvl((
            SELECT
                SUM(paid_amt)
            FROM
                IA_FA_AWARD_BY_AIDY fb
            WHERE
                    fb.pidm = sd.sd_pidm
                AND fb.aid_year = sd.fafy
                AND fb.fund_source = 'FDRL'
                AND fb.fund_type = 'WORK'
        ),
            0)     fdrl_work_amt,
        fapp.pell_efc,
        fapp.fisap_inc
    FROM
        ia_td_student_data  sd
        LEFT JOIN (
            SELECT
                aid_year,
                pidm,
                pell_efc,
                fisap_inc
            FROM
                ia_fa_applicant
            WHERE
                curr_rec_ind = 'Y'
        )                   fapp ON sd.sd_pidm = fapp.pidm
                  AND sd.fafy = fapp.aid_year
    WHERE
            sd.registered_ind = 'Y'
        AND sd.sd_term_code = '202010'                                     --UPDATE THIS
        AND sd.report_level_code = 'UG'
), dp2 AS (
    SELECT
        dp1.*,
        CASE
            WHEN dp1.fisap_inc > 0
                 AND dp1.fisap_inc <= 30000 THEN
                '$0 - $30,000'
            WHEN dp1.fisap_inc > 30001
                 AND dp1.fisap_inc <= 48000 THEN
                '$30,001 - $48,000'
            WHEN dp1.fisap_inc > 48001
                 AND dp1.fisap_inc <= 75000 THEN
                '$48,001 - $75,000'
            WHEN dp1.fisap_inc > 75001
                 AND dp1.fisap_inc <= 110000 THEN
                '$75,001 - $110,000'
            WHEN dp1.fisap_inc > 110001 THEN
                '$110,000 or more'
        END fasap_inc_level
    FROM
        dp1

)
, g1_1 AS (
    SELECT
        count (*) g1_1_count
    FROM
        dp2
), g1_3 AS (
    SELECT
        count (*) g1_3_count,
        sum (dp2.fdrl_stat_grnt_schl_amt) 
        + sum(dp2.inst_grnt_schl_amt) g1_3_amt
    FROM
        dp2
    WHERE
        dp2.fdrl_stat_grnt_schl_ind = 'Y'
        OR dp2.inst_grnt_schl_ind = 'Y'
), g1_4 AS (
    SELECT
        count (*) g1_4_count,
        sum (dp2.pell_grnt_amt) 
        + sum(dp2.pell_grnt_amt) g1_4_amt
    FROM
        dp2
    WHERE
        dp2.pell_grnt_ind = 'Y'
), g1_5 AS (
    SELECT
        count (*) g1_5_count,
        sum (dp2.pell_grnt_amt) 
        + sum(dp2.pell_grnt_amt) g1_5_amt
    FROM
        dp2
    WHERE
        fdrl_loan_ind = 'Y'
), g2_1 AS (
    SELECT
        count (*) g2_1_count
    FROM
        dp2
    WHERE
            dp2.full_part_time_ind_umf = 'F'
        AND dp2.ia_student_type_code = 'F'
), g2_1a AS (
    SELECT
        count (*) g2_1a_count,
        sum (dp2.fdrl_work_amt) 
        + sum(dp2.loan_amt) 
        + sum(dp2.grnt_amt)
        + sum(dp2.schl_amt)
        g2_1a_amt
    FROM
        dp2
    WHERE
            dp2.full_part_time_ind_umf = 'F'
        AND dp2.ia_student_type_code = 'F'
        AND ( fdrl_work_ind = 'Y'
              OR loan_ind = 'Y'
              OR grnt_ind = 'Y'
              OR schl_ind = 'Y' )
), g2_1b AS (
    SELECT
        count (*) g2_1b_count,
        sum (dp2.fdrl_work_amt) 
        + sum(dp2.loan_amt) 
        + sum(dp2.fdrl_stat_grnt_schl_amt)
        + sum(dp2.inst_grnt_schl_amt)
        g2_1b_amt
    FROM
        dp2
    WHERE
            dp2.full_part_time_ind_umf = 'F'
        AND dp2.ia_student_type_code = 'F'
        AND ( loan_ind = 'Y'
              OR fdrl_stat_grnt_schl_ind = 'Y'
              OR inst_grnt_schl_ind = 'Y' )
), g3 AS (
    SELECT
        *
    FROM
        dp2
    WHERE
            dp2.full_part_time_ind_umf = 'F'
        AND dp2.ia_student_type_code = 'F'
        AND dp2.residency_code = 'R'
        AND ( fdrl_stat_grnt_schl_ind = 'Y'
              OR inst_grnt_schl_ind = 'Y' )
), g4 AS (
    SELECT
        *
    FROM
        dp2
    WHERE
            dp2.full_part_time_ind_umf = 'F'
        AND dp2.ia_student_type_code = 'F'
        AND ( dp2.title_iv_ind = 'Y' )
), g5 AS (
    SELECT
        *
    FROM
        dp2
    WHERE
            dp2.full_part_time_ind_umf = 'F'
        AND dp2.ia_student_type_code = 'F'
        AND ( dp2.fdrl_loan_ind = 'Y' )
)
--select * from g4

SELECT
    'UNITID=171146'  a,
    'SURVSECT=SFA'   b,
    'PART=A'         c,
    'LINETYPE=1'     d,
    'COUNT='
    || (
        SELECT
            g1_1_count
        FROM
            g1_1
    )                e,
    ''               f,
    ''               g,
    ''               h,
    'All undergraduate students Fall 19' tech_notes
FROM
    dual
UNION ALL
SELECT
    'UNITID=171146'  a,
    'SURVSECT=SFA'   b,
    'PART=A'         c,
    'LINETYPE=3'     d,
    'COUNT='
    || (
        SELECT
            g1_3_count
        FROM
            g1_3
    )                e,
    'AMOUNT='
    || (
        SELECT
            round(g1_3.g1_3_amt, 0)
        FROM
            g1_3
    )                f,
    ''               g,
    ''               h     
    ,'All UG Grant or scholorship from fed, state/local, inst' tech_notes
FROM
    dual
UNION ALL
SELECT
    'UNITID=171146'  a,
    'SURVSECT=SFA'   b,
    'PART=A'         c,
    'LINETYPE=4'     d,
    'COUNT='
    || (
        SELECT
            g1_4_count
        FROM
            g1_4
    )                e,
    'AMOUNT='
    || (
        SELECT
            round(g1_4.g1_4_amt, 0)
        FROM
            g1_4
    )                f,
    ''               g,
    ''               h     
    ,'All UG Pell Grant' tech_notes
FROM
    dual
UNION ALL
SELECT
    'UNITID=171146'  a,
    'SURVSECT=SFA'   b,
    'PART=A'         c,
    'LINETYPE=5'     d,
    'COUNT='
    || (
        SELECT
            g1_5_count
        FROM
            g1_5
    )                e,
    'AMOUNT='
    || (
        SELECT
            round(g1_5.g1_5_amt, 0)
        FROM
            g1_5
    )                f,
    ''               g,
    ''               h     
    ,'All UG Fed student loans' tech_notes
FROM
    dual
UNION ALL
SELECT
    'UNITID=171146'  a,
    'SURVSECT=SFA'   b,
    'PART=B'         c,
    'LINETYPE=1'     d,
    'COUNT='
    || (
        SELECT
            g2_1_count
        FROM
            g2_1
    )                e,
    ''               f,
    ''               g,
    ''               h     
    ,'FT FTIAC' tech_notes
FROM
    dual
--UNION ALL
--SELECT
--    'UNITID=171146'  a,
--    'SURVSECT=SFA'   b,
--    'PART=B'         c,
--    'LINETYPE=3'     d,
--    'COUNT='
--    || (
--        SELECT
--            COUNT(*)
--        FROM
--            g2_1
--    )                e,
----    'AMOUNT='        f,
--'AMOUNT='||(SELECT round(SUM(G1_5.fdrl_loan_amt),0) FROM G1_5) F,
--    ''               g,
--    ''               h     
--,'' tech_notes
--FROM
--    dual    
    
    
    ;