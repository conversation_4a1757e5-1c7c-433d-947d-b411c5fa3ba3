--INSERT INTO IA_CW_FY_TERM
WITH DP1 AS (
SELECT 
STVTERM_CODE FY_TERM_CODE,
STVTERM_DESC FY_TERM_DESC,
STVTERM_FA_PROC_YR FAFY
FROM 
AIMSMGR.stvterm_ext
WHERE
STVTERM_CODE NOT IN ('000000','999999')
AND STVTERM_CODE > '200040'

MINUS

SELECT
FY_TERM_CODE,
FY_TERM_DESC,
FAFY
FROM
IA_CW_FY_TERM
)
SELECT 
'' FY,
DP1.FY_TERM_CODE,
DP1.FY_TERM_DESC,
'' FY_CO_TERM_CODE,
DP1.FAFY
FROM DP1
;
COMMIT;

DROP TABLE IA_CW_FY_TERM_BAC;

CREATE TABLE IA_CW_FY_TERM_BAC AS
SELECT * FROM IA_CW_FY_TERM;
COMMIT;