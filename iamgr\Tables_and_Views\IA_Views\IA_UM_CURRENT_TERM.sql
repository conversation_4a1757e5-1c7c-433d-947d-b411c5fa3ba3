create or replace view ia_um_current_term as
select 
*
--last_term_desc,
--last_term,
--current_term_desc,
--current_term,
--CURRENT_TERM_START_DATE_1 CURRENT_TERM_START_DATE, 
--CURRENT_TERM_END_DATE_1 CURRENT_TERM_END_DATE,
--CURRENT_TERM_CENSUS_DATE_1 CURRENT_TERM_CENSUS_DATE,
--CURRENT_TERM_CENSUS_DOT, 
--CURRENT_TERM_DOT,
--next_term_desc,
--next_term,
--next_term_census_date_1 next_term_census_data
from um_current_term;

SELECT * FROM IA_UM_CURRENT_TERM;