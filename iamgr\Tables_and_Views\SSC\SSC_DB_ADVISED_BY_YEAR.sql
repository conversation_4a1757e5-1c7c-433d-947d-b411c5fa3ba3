create or replace view SSC_DB_ADVISED_BY_YEAR as (

select
extract(day from ADVSWIPE_TIME_IN) as Day,
extract(month from ADVSWIPE_TIME_IN) as Mon,
extract(Year from ADVSWIPE_TIME_IN) as YR,
ADVSWIPE.*,
UM_CATALOG_SCHEDULE.SUBJ_CODE,
UM_CATALOG_SCHEDULE.CRSE_NUMBER,
um_demographic.HSCH_GPA,
  CASE
        WHEN um_demographic.HSCH_GPA < 2.000
        THEN 'Below 2.0'
        WHEN um_demographic.HSCH_GPA >= 2.000
        AND um_demographic.HSCH_GPA   < 3.000
        THEN '2.00-2.99'
        WHEN um_demographic.HSCH_GPA >= 3.000
        AND um_demographic.HSCH_GPA   < 3.500
        THEN '3.00-3.49'
        WHEN um_demographic.HSCH_GPA >= 3.500
        THEN '3.5+'
        ELSE 'No Earned GPA'
      END HS_gpa_bin_desc,
um_student_data.FULL_PART_TIME_IND_UMF

from
ADVSWIPE
left join um_demographic on um_demographic.dm_pidm = ADVSWIPE.ADVSWIPE_ADV_PIDM
left join UM_CATALOG_SCHEDULE on UM_CATALOG_SCHEDULE.crn_key = ADVSWIPE.ADVSWIPE_TUTOR_CRN and
                                 UM_CATALOG_SCHEDULE.TERM_CODE_KEY = ADVSWIPE.ADVSWIPE_TERM
left join um_student_data on um_student_data.sd_pidm = ADVSWIPE.ADVSWIPE_ADV_PIDM and
                             um_student_data.sd_term_code =  ADVSWIPE.ADVSWIPE_TERM
);                     
