create or replace view ia_um_holds as(
select 
um_holds.pidm, 
um_holds.HLDD_CODE,
um_holds.DESCR,
um_holds.FROM_DATE,
um_holds.TO_DATE,
um_holds.REASON,
amount_owed.balance,
--um_holds.AMOUNT_OWED,
um_holds.HOLD_USER,
um_holds.ORIG_CODE,
um_holds.REG_HOLD_IND,
um_holds.TRANS_HOLD_IND,
um_holds.GRAD_HOLD_IND,
um_holds.AR_HOLD_IND,
um_holds.ACTIVITY_DATE,
um_holds.HOLD_STATUS_IND
from um_holds
inner join(
select 
aimsmgr.tbraccd.TBRACCD_PIDM,
sum(to_number(aimsmgr.tbraccd.TBRACCD_BALANCE))balance,

from aimsmgr.tbraccd
inner join aimsmgr.tbbdetc on aimsmgr.tbbdetc.TBBDETC_DETAIL_CODE = aimsmgr.tbraccd.TBRACCD_DETAIL_CODE
--where TBRACCD_PIDM = 110170 and TBRACCD_TERM_CODE = '201610'
group by
aimsmgr.tbraccd.TBRACCD_PIDM
)amount_owed on um_holds.pidm = amount_owed.TBRACCD_PIDM
where um_holds.HOLD_STATUS_IND = 'CURRENT'

)

