/******************************************************************************
The purpose of this query is to create a view that will return an indicator if
the student was enrolled in a gate keeper course, first year experience, or a 
Civic Engagement course.
Note: When a student repeats a course 
******************************************************************************/
TRUNCATE TABLE IA_COHORT_TRANS_POP_BAC;

INSERT INTO IA_COHORT_TRANS_POP_BAC AS
SELECT * FROM IA_COHORT_TRANS_POP_TBL;
COMMIT;

SELECT COUNT (*) FROM IA_COHORT_TRANS_POP_BAC;
SELECT COUNT (*) FROM IA_COHORT_TRANS_POP_TBL;

TRUNCATE TABLE IA_COHORT_TRANS_POP_TBL;
--DROP TABLE IA_COHORT_TRANS_POP_TBL;


INSERT INTO IA_COHORT_TRANS_POP_TBL

--Create TABLE IA_COHORT_TRANS_POP_TBL AS
with bigcel as(
    select 
    /*+ MATERIALIZE */
--    IA_COHORT_POP_UNDUP.*
    CO_PIDM,
    CO_TERM_CODE_KEY
    FROM  
    IA_COHORT_POP_UNDUP 
)
, POPCEL1 as(
    select 
    DISTINCT pidm, 
    umid , 
    First_name, 
    last_name, 
    Type, 
    Type_desc,
    term, 
    subject, 
    course, 
    title, 
    completed_ind, 
    grade,
    CASE 
        WHEN ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR ATTRIBUTE_4 = 'CE' THEN 'CE' 
        ELSE NULL 
    END CIVIC_IND
    from UM_STUDENT_TRANSCRIPT 
    where (ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR ATTRIBUTE_4 = 'CE')OR
          (
           ((subject = 'BIO' and course in ('111','113', '167')) or 
            (subject = 'CHM' and course in ('140', '150', '260')) or
            (subject = 'MTH' and course in ('090', '111', '118', '120')) or
            (subject = 'PHY' and course in ('143', '145', '243', '245')) or
            (subject = 'UNV' and course = '100')) AND 
            (repeat_ind = 'I' OR repeat_ind IS NULL)
           )--FINAL WERE 2 GRADES IN THE SAME TERM
)
, POPCEL2 as(
    SELECT 
    MAX(TERM) AS MAXTERM,
    PIDM AS MAXPIDM, 
    SUBJECT,
    COURSE  
    FROM UM_STUDENT_TRANSCRIPT
    where (ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR ATTRIBUTE_4 = 'CE')OR
          (
           (subject = 'BIO' and course in ('111','113', '167')) or
            (subject = 'CHM' and course in ('140', '150', '260')) or
            (subject = 'MTH' and course in ('090', '111', '118', '120')) or
            (subject = 'PHY' and course in ('143', '145', '243', '245'))or
            (subject = 'UNV' and course = '100')
    ) 
    GROUP BY 
    PIDM, 
    SUBJECT,COURSE 
)
, POPCEL3 as(
    SELECT 
    count(*) AS NUMOFTRIALS,
    PIDM AS TPIDM, 
    SUBJECT as TSUBJECT,
    COURSE as TCOURSE  
    FROM UM_STUDENT_TRANSCRIPT
    where ( TYPE <> 'W' or TYPE <> 'T') 
    AND -- WE ONLY WANT INSTITUTIONAL GRADES
        ((ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR ATTRIBUTE_4 = 'CE') OR
         (subject = 'BIO' and course in ('111','113', '167')) or
         (subject = 'CHM' and course in ('140', '150', '260')) or
         (subject = 'MTH' and course in ('090', '111', '118', '120')) or
         (subject = 'PHY' and course in ('143', '145', '243', '245'))or
         (subject = 'UNV' and course = '100')
         )   
    GROUP BY 
    PIDM, 
    SUBJECT,COURSE  
)
, POPCEL4 as(
    select  
    maxpidm, 
    POPCEL1.SUBJECT, 
    POPCEL1.COURSE, 
    POPCEL1.COMPLETED_IND, 
    POPCEL1.TYPE , 
    POPCEL2.maxterm, 
    popcel1.title , 
    POPCEL3.NUMOFTRIALS,
    GRADE,
    CIVIC_IND 
    FROM POPCEL1
    INNER JOIN POPCEL2 on POPCEL1.PIDM = POPCEL2.MAXPIDM 
                       AND POPCEL1.SUBJECT = POPCEL2.SUBJECT 
                       AND POPCEL1.COURSE = POPCEL2.COURSE 
                       AND POPCEL1.TERM = POPCEL2.MAXTERM
    LEFT JOIN POPCEL3 on POPCEL2.MAXPIDM = POPCEL3.TPIDM 
                      AND POPCEL2.SUBJECT = POPCEL3.TSUBJECT 
                      AND POPCEL2.COURSE = POPCEL3.TCOURSE

)
select 
bigcel.CO_PIDM, 
POPCEL4.SUBJECT, 
POPCEL4.COURSE, 
POPCEL4.COMPLETED_IND, 
POPCEL4.TYPE as I_type,
POPCEL4.NUMOFTRIALS,
bigcel.CO_TERM_CODE_KEY,
POPCEL4.maxterm, 
popcel4.title, 
POPCEL4.GRADE, 
POPCEL4.CIVIC_IND 
FROM bigcel --IA_COHORT_POP_UNDUP 
left join POPCEL4 on bigcel.co_pidm = popcel4.maxpidm

;




-- select 
-- CO_PIDM, 
-- POPCEL4.SUBJECT, 
-- POPCEL4.COURSE, 
-- POPCEL4.COMPLETED_IND, 
-- POPCEL4.TYPE as I_type ,
-- NUMOFTRIALS,
-- CO_TERM_CODE_KEY,
-- POPCEL4.maxterm, 
-- popcel4.title, 
-- GRADE, 
-- CIVIC_IND 

-- FROM  
-- IA_COHORT_POP_UNDUP left join 

-- (
-- select  maxpidm, POPCEL1.SUBJECT, POPCEL1.COURSE, POPCEL1.COMPLETED_IND, 
-- POPCEL1.TYPE , POPCEL2.maxterm, popcel1.title , 
-- POPCEL3.NUMOFTRIALS,GRADE,CIVIC_IND FROM  

-- (select DISTINCT pidm, umid , First_name, last_name, Type, Type_desc, 
-- term, subject, course, title, completed_ind, grade ,
-- CASE WHEN ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR 
-- ATTRIBUTE_4 = 'CE' THEN 'CE' ELSE NULL END CIVIC_IND

-- from UM_STUDENT_TRANSCRIPT 
-- where 
-- (ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR 
-- ATTRIBUTE_4 = 'CE')OR
-- (((subject = 'BIO' and course in ('111','113', '167'))or
-- (subject = 'CHM' and course in ('140', '150', '260')) or
-- (subject = 'MTH' and course in ('090', '111', '118', '120')) or
-- (subject = 'PHY' and course in ('143', '145', '243', '245'))or
-- (subject = 'UNV' and course = '100')) AND 
-- (repeat_ind = 'I' OR repeat_ind IS NULL))--FINAL WERE 2 GRADES IN THE SAME TERM
-- )POPCEL1
-- INNER JOIN 
-- (SELECT MAX(TERM) AS MAXTERM,PIDM AS MAXPIDM, SUBJECT,COURSE  
-- FROM UM_STUDENT_TRANSCRIPT
-- where
-- (ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR 
-- ATTRIBUTE_4 = 'CE')OR
-- ((subject = 'BIO' and course in ('111','113', '167')) or
-- (subject = 'CHM' and course in ('140', '150', '260')) or
-- (subject = 'MTH' and course in ('090', '111', '118', '120')) or
-- (subject = 'PHY' and course in ('143', '145', '243', '245'))or
-- (subject = 'UNV' and course = '100')) 

-- GROUP BY PIDM, SUBJECT,COURSE )POPCEL2
-- on POPCEL1.PIDM = POPCEL2.MAXPIDM AND POPCEL1.SUBJECT = POPCEL2.SUBJECT AND 
-- POPCEL1.COURSE = POPCEL2.COURSE AND POPCEL1.TERM = POPCEL2.MAXTERM

-- LEFT JOIN 
-- (SELECT count(*) AS NUMOFTRIALS,PIDM AS TPIDM, SUBJECT as TSUBJECT,COURSE as 
-- TCOURSE  FROM UM_STUDENT_TRANSCRIPT
-- where ( TYPE <> 'W' or TYPE <> 'T') AND -- WE ONLY WANT INSTITUTIONAL GRADES
-- ((ATTRIBUTE_1 = 'CE' OR ATTRIBUTE_2 = 'CE' OR ATTRIBUTE_3 = 'CE' OR 
-- ATTRIBUTE_4 = 'CE')OR
-- (subject = 'BIO' and course in ('111','113', '167')) or
-- (subject = 'CHM' and course in ('140', '150', '260')) or
-- (subject = 'MTH' and course in ('090', '111', '118', '120')) or
-- (subject = 'PHY' and course in ('143', '145', '243', '245'))or
-- (subject = 'UNV' and course = '100'))   
-- GROUP BY PIDM, SUBJECT,COURSE  )POPCEL3
-- on POPCEL2.MAXPIDM = POPCEL3.TPIDM AND POPCEL2.SUBJECT = POPCEL3.TSUBJECT AND 
-- POPCEL2.COURSE = POPCEL3.TCOURSE)POPCEL4

-- on IA_COHORT_POP_UNDUP.CO_PIDM = POPCEL4.maxPIDM;
