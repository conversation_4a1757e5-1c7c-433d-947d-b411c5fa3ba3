create or replace view load_sch_window as
with dp1 as (
SELECT DISTINCT
-- dt_date,
-- dt_desc,
-- start_date,
-- end_date,
-- begin_time,
-- end_time,
-- camp_code,
-- camp_desc,
-- schd_code,
-- bldg_code,
 bldg_schd_desc,
um_crn_group_key_desc,
 dt_window_key,
-- dt_term_code,
 dt_term_desc,
-- dt_key,
-- schd_desc,
-- bldg_desc,
 dt_time_slot,
  max(xlst_maximum_enrollment) xlst_maximum_enrollment,
 max(xlst_actual_enrollment) xlst_actual_enrollment
FROM
 sch_window_sel
 where dt_term_code = (select current_term from um_current_term)
 group by
--  dt_date,
-- dt_desc,
-- start_date,
-- end_date,
-- begin_time,
-- end_time,
-- camp_code,
-- camp_desc,
-- schd_code,
-- bldg_code,
 bldg_schd_desc,
um_crn_group_key_desc,
 dt_window_key,
-- dt_term_code,
 dt_term_desc,
-- dt_key,
-- schd_desc,
-- bldg_desc,
 dt_time_slot
 
 union
 
 SELECT DISTINCT
-- dt_date,
-- dt_desc,
-- start_date,
-- end_date,
-- begin_time,
-- end_time,
-- camp_code,
-- camp_desc,
-- schd_code,
-- bldg_code,
 bldg_schd_desc,
um_crn_group_key_desc,
 dt_window_key,
-- dt_term_code,
 dt_term_desc,
-- dt_key,
-- schd_desc,
-- bldg_desc,
 dt_time_slot,
  max(xlst_maximum_enrollment) xlst_maximum_enrollment,
 max(xlst_actual_enrollment) xlst_actual_enrollment
FROM
 sch_window_sel
 where dt_term_code = (select next_term from um_current_term)
 group by
--  dt_date,
-- dt_desc,
-- start_date,
-- end_date,
-- begin_time,
-- end_time,
-- camp_code,
-- camp_desc,
-- schd_code,
-- bldg_code,
 bldg_schd_desc,
um_crn_group_key_desc,
 dt_window_key,
-- dt_term_code,
 dt_term_desc,
-- dt_key,
-- schd_desc,
-- bldg_desc,
 dt_time_slot
 )
 select 
 * 
 from dp1;