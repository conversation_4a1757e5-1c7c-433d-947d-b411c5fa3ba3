drop table CO_GR_TEN_YR_NSC_TBL;
drop table CO_GR_TEN_YR_NSC_TBL_BAC;
create table CO_GR_TEN_YR_NSC_TBL_BAC as select * from CO_GR_TEN_YR_NSC_TBL;
truncate table CO_GR_TEN_YR_NSC_TBL;
create table CO_GR_TEN_YR_NSC_TBL as

-- INSERT INTO CO_GR_TEN_YR_NSC_TBL

SELECT DISTINCT
  co.co_gr_pidm,
  co.co_gr_term_code_key,
  co.start_date,
    co.CO_GR_IA_STUDENT_TYPE_CODE, 
  co.CO_GR_FULL_PART_IND_UMF, 
  co.CO_GR_ETHNICITY, 
  co.CO_GR_GENDER, 
  co.REPORT_LEVEL_CODE, 
  co.PRIMARY_LEVEL_CODE, 
  co.PRIMARY_DEGREE_CODE,
  co.PRIMARY_MAJOR_1_CIPC_CODE, 
  co.CITIZENSHIP_CODE, 
  co.CITIZENSHIP_DESC, 
  co.TRANS_GPA, PCOL_DESC_1, 
  co.PRIMARY_MAJOR_1, 
  co.PRIMARY_PROGRAM,
  co.PRIMARY_CONC_1, 
  co.UMID,
(SELECT MAX(TWOYEAR_FOURYEAR)
  FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.CO_GR_PIDM = CO.CO_GR_PIDM
  )TWOFOUR_NSC_ENROLLED,-- ENROLLED AT TWO YEAR OR FOUR YEAR OTHER THAN UMF
  (SELECT MAX(TWOYEAR_FOURYEAR)
  FROM CO_GR_NSC_DEGREE_TBL NSCD
  WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
  )TWOFOUR_NSC_DEGREE,-- GRADUATED FROM OTHER COLLEGE


  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 100))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   scnd_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 100))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END scnd_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
  FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 100))
  AND NSC.CO_GR_PIDM                         = CO.CO_GR_PIDM
  )SCND_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE,

  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_GR_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_GR_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 100)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
----------------------------Third-----------------------------------------------
-- Third Fall Set **************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 200))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   thrd_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 200))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END thrd_fall_grad_ind,

    (SELECT NSC.REGISTERED_IND
  FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 200))
  AND NSC.CO_GR_PIDM                         = CO.CO_GR_PIDM
  )THRD_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE,

  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_GR_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_GR_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 200)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
----------------------------fourth---------------------------------------------------
-- Fourth Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 300))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   frth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 300))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END frth_fall_grad_ind,

      (SELECT NSC.REGISTERED_IND
  FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 300))
  AND NSC.CO_GR_PIDM                         = CO.CO_GR_PIDM
  )FRTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE,

  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_GR_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_GR_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 300)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
-----------------------------Fifth--------------------------------------------------
-- Fifth Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 400))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   ffth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 400))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END ffth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 400))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) FFTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 400))) > 0
    THEN 'Y'
    ELSE NULL
  END FFTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE

-----------------------------Sixth--------------------------------------------------
-- Sixth Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 500))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   sxth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 500))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END sxth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 500))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) SXTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 500))) > 0
    THEN 'Y'
    ELSE NULL
  END SXTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE

---------------------------Seventh--------------------------------------------------
-- Seventh Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 600))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   svnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 600))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END svnth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 600))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) SVNTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 600))) > 0
    THEN 'Y'
    ELSE NULL
  END SVNTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE

---------------------------Eighth--------------------------------------------------
-- Eighth Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 700))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   eigth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 700))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END eigth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 700))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) EIGTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 700))) > 0
    THEN 'Y'
    ELSE NULL
  END EIGTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE

---------------------------Ninth--------------------------------------------------
-- Ninth Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 800))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   nnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 800))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END nnth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 800))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) NNTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 800))) > 0
    THEN 'Y'
    ELSE NULL
  END NNTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE

---------------------------Tenth--------------------------------------------------
-- Tenth Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 900))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   tnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 900))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END tnth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 900))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) TNTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 900))) > 0
    THEN 'Y'
    ELSE NULL
  END TNTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE

---------------------------Eleventh--------------------------------------------------
-- Eleventh Fall Set ******************************************************************
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 1000))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   elvnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 1000))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END elvnth_fall_grad_ind,

  (SELECT NSC.REGISTERED_IND
   FROM CO_GR_NSC_STUDENT_DATA_TBL NSC
   WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 1000))
     AND NSC.CO_GR_PIDM    = CO.CO_GR_PIDM
  ) ELVNTH_FALL_TERM_REG_NSC, -- REGISTERED AT ANOTHER COLLEGE

  CASE
    WHEN (SELECT COUNT(*)
            FROM CO_GR_NSC_DEGREE_TBL NSCD
           WHERE NSCD.CO_PIDM = CO.CO_GR_PIDM
             AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_GR_TERM_CODE_KEY) + 1000))) > 0
    THEN 'Y'
    ELSE NULL
  END ELVNTH_FALL_GRAD_NSC, -- GRADUATED FROM OTHER COLLEGE
---------------------------Grad Date---------------------------------------
--Grad Date***********************************************************  
  (
    SELECT
      MAX(grad_date)
    FROM
      um_degree umd
    WHERE
        umd.pidm = CO.co_gr_pidm
      AND umd.degree_status = 'AW'
      AND umd.degree_code = CO.primary_degree_code
      AND umd.grad_date > CO.start_date
  )   grad_date 
-- Cohort Demographic Table joins and Where Clause ************************
FROM
       td_student_data sd
  INNER JOIN CO_GR_POP_TBL CO ON CO.co_gr_pidm = sd.sd_pidm
                                 AND CO.co_gr_term_code_key = sd.sd_term_code
                                 AND CO.primary_degree_code = sd.primary_degree_code
where co.co_gr_term_code_key like '%10'
;