WITH IA_GRADES AS
(SELECT UM_STUDENT_TRANSCRIPT.pidm,
UM_STUDENT_TRANSCRIPT.term,
UM_DEMOGRAPHIC.HSCH_DESC,
UM_DEMOGRAPHIC.HSCH_GPA,
UM_DEMOGRAPHIC.hsch_code,
UM_STUDENT_TRANSCRIPT.subject,
UM_STUDENT_TRANSCRIPT.course,
UM_STUDENT_TRANSCRIPT.section,
UM_STUDENT_TRANSCRIPT.CRN,
UM_STUDENT_TRANSCRIPT.Title,
CASE
WHEN UM_STUDENT_TRANSCRIPT.Grade IN (
'A-','.A-','IA-','.IA-','A','.A','IA','.IA','A+','.A+','IA+','.IA+',
'P','IP','S','IS','.IS','.S','.Y','Y',
'B-','.B-','IB-','.IB-','B','.B','IB','.IB','B+','.B+','IB+','.IB+',
'.-C-','-C-','C-','.C-','IC-','.IC-','C','.C','IC','.IC','C+','.C+','IC+',
'D','.D','ID','.ID','D+','.D+','ID+',
'ED','.ED'
)
THEN '1'
ELSE '2'
END PDFW_CODE,    
CASE
WHEN UM_STUDENT_TRANSCRIPT.Grade IN (
'A-','.A-','IA-','.IA-','A','.A','IA','.IA','A+','.A+','IA+','.IA+',
'P','IP','S','IS','.IS','.S','.Y','Y',
'B-','.B-','IB-','.IB-','B','.B','IB','.IB','B+','.B+','IB+','.IB+',
'.-C-','-C-','C-','.C-','IC-','.IC-','C','.C','IC','.IC','C+','.C+','IC+',
'D','.D','ID','.ID','D+','.D+','ID+',
'ED','.ED'
)
THEN 'pass'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('.W','W','YW')
THEN 'Withdrawl'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('.I','I','*','N','IN')
THEN 'Drop'
ELSE 'Fail'
END PDFW_DESC,
CASE
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('A-','.A-','IA-','.IA-','A','.A','IA','.IA','A+','.A+','IA+','.IA+','P','IP','S','IS','.IS','.S','.Y','Y')
THEN 'A'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('B-','.B-','IB-','.IB-','B','.B','IB','.IB','B+','.B+','IB+','.IB+')
THEN 'B'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('.-C-','-C-','C-','.C-','IC-','.IC-','C','.C','IC','.IC','C+','.C+','IC+')
THEN 'C'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('D-','.D-','ID-','D','.D','ID','.ID','D+','.D+','ID+','ED','.ED')
THEN 'D'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('E-','.E-','IE-','E','.E','IE','.IE','E+','.E+','IE+','IF','NE','IU')
THEN 'E'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('.W','W','YW')
THEN 'W'
WHEN UM_STUDENT_TRANSCRIPT.Grade IN ('.I','I','*','N','IN')
THEN 'I'
ELSE Grade
END Grade_Dist,
UM_STUDENT_TRANSCRIPT.Grade,
UM_STUDENT_TRANSCRIPT.COMPLETED_IND,
UM_CATALOG_SCHEDULE.PRIMARY_INSTRUCTOR_ID,
UM_CATALOG_SCHEDULE.PRIMARY_INSTRUCTOR_LAST_NAME
|| ', '
|| UM_CATALOG_SCHEDULE.PRIMARY_INSTRUCTOR_FIRST_NAME AS INS_NAME,
UM_STUDENT_TRANSCRIPT.subject
|| ' '
||UM_STUDENT_TRANSCRIPT.course AS sub_crs
FROM UM_STUDENT_TRANSCRIPT
INNER JOIN UM_CATALOG_SCHEDULE
ON UM_STUDENT_TRANSCRIPT.term = UM_CATALOG_SCHEDULE.term_code_key
AND UM_STUDENT_TRANSCRIPT.CRN = UM_CATALOG_SCHEDULE.CRN_KEY
INNER JOIN um_demographic
ON UM_STUDENT_TRANSCRIPT.pidm = um_demographic.dm_pidm
INNER JOIN um_student_data
ON UM_STUDENT_TRANSCRIPT.term    = um_student_data.sd_term_code
AND UM_STUDENT_TRANSCRIPT.pidm   = um_student_data.sd_pidm
WHERE UM_STUDENT_TRANSCRIPT.TYPE = 'I'
AND UM_STUDENT_TRANSCRIPT.term  = '201710'
and UM_STUDENT_TRANSCRIPT.course <500
and UM_STUDENT_TRANSCRIPT.grade not in ('N','IN','Y','*')
--  and um_student_data.ia_student_type_code = 'F'
    
  )
,
dfw as (
select
subject,
course,
count (pidm) DFW
from IA_GRADES
where PDFW_CODE = 2
group by
subject,
course
--order by count (pidm) desc
),
total_students as (
select
subject,
course,
count (pidm) headcount
from IA_GRADES
group by
subject,
course
order by count (pidm) desc
)
select 
total_students.*,
nvl(dfw.dfw,0) dfw,
round((nvl(dfw.dfw,0)/total_students.headcount)*100,2) percent_dfw
from total_students
left join dfw on dfw.subject = total_students.subject and
dfw.course = total_students.course
order by nvl(dfw.dfw,0) desc
;