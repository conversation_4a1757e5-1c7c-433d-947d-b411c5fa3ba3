--------------------------------------------------------
--  DDL for Procedure P_COL_CONT
--------------------------------------------------------
set define off;

  CREATE OR REPLACE PROCEDURE "CONTMGR"."P_COL_CONT" as 
BEGIN
EXECUTE IMMEDIATE ('TRUNCATE TABLE COL_CONT_ONE_YR');
--EXECUTE IMMEDIATE ('TRUNCATE TABLE COL_CONT_TWO_YR');

/******************************************************************************* 
Build COL_CONT_ONE_YR Table
*******************************************************************************/ 

INSERT INTO COL_CONT_ONE_YR
--CREATE TABLE COL_CONT_ONE_YR AS
  WITH cur_term AS (
  SELECT
   td.sd_pidm,
   td.sd_term_code          cur_term_code,
   td.sd_term_desc          cur_term_desc,
    CASE
    WHEN td.primary_college_code IN ( 'HP', 'HS' ) THEN
     'HS'
    WHEN td.primary_college_code IN ( 'RK', 'AS' ) THEN
     'AS'
    WHEN td.primary_major_1 = '0000' THEN
     '00'
    ELSE
     td.primary_college_code
   END                      cur_col_code,
   td.primary_major_1       cur_major_1,
   td.primary_level_code    cur_level_code,
   td.report_level_code     cur_report_level,
   td.primary_degree_code,
      CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = td.sd_pidm
      AND umd.degree_status = 'AW'
     -- AND umd.degree_code = td.primary_degree_code
           AND umd.level_code = td.primary_level_code
      AND umd.grad_term_code <= (td.sd_term_code + 100)
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                      cur_reg_term_grad_ind
  FROM
   td_student_data  td
   LEFT OUTER JOIN td_demographic      dm ON td.sd_term_code = dm.td_term_code
                                        AND td.sd_pidm = dm.dm_pidm
  WHERE
    sd_term_code = (select substr(current_term,1,4)- 1 || '10' cur_fall 
                    from um_current_term)    
   AND td.registered_ind = 'Y'
   AND td.primary_college_code IS NOT NULL
   AND td.ia_student_type_code NOT IN ( 'D', 'E', 'G', 'S', 'X' )
--   AND educ_goal NOT IN ( 'E2', 'E3' )
--  AND td.primary_major_1 != '0000'
 ),
/******************************************************************************* 
Current Reg Term Set
*******************************************************************************/ 
cur_reg_term AS (
  SELECT
--  cur_term.*,
   sd.sd_pidm,
   sd.sd_term_desc          cur_reg_term_desc,
   sd.registered_ind        cur_reg_term_enrolled_ind,
   sd.primary_level_code    cur_reg_level_code,
--  sd.primary_college_code    cur_reg_term_col_code,
    CASE
    WHEN sd.primary_college_code IN ( 'HP', 'HS' ) THEN
     'HS'
    WHEN sd.primary_college_code IN ( 'RK', 'AS' ) THEN
     'AS'
    WHEN sd.primary_major_1 = '0000' THEN
     '00'
    ELSE
     sd.primary_college_code
   END                      cur_reg_term_col_code,
   sd.primary_major_1       cur_reg_term_major_1
  FROM
   cur_term
   LEFT JOIN td_student_data sd ON cur_term.sd_pidm  = sd.sd_pidm
   and sd.sd_term_code = ( cur_term.cur_term_code + 100 )
--  WHERE
   
 )
 SELECT
  cur_term.sd_pidm,
  cur_term.cur_term_code,
  cur_term.cur_term_desc,
  cur_term.cur_col_code,
  cur_term.cur_major_1,
  cur_term.cur_level_code,
  cur_term.cur_report_level,
  cur_term.primary_degree_code,
  cur_reg_term_desc,
  cur_reg_term_enrolled_ind,
  cur_reg_level_code,
  cur_reg_term_col_code,
  cur_reg_term_major_1,
  cur_term.cur_reg_term_grad_ind

 FROM
  cur_term
  LEFT JOIN cur_reg_term ON cur_term.sd_pidm = cur_reg_term.sd_pidm;
  
/******************************************************************************* 
BUILD COL_CONT_TWO_YR Table
*******************************************************************************/ 

--INSERT INTO COL_CONT_TWO_YR 
--  WITH cur_term AS (
--  SELECT
--   td.sd_pidm,
--   td.sd_term_code          cur_term_code,
--   td.sd_term_desc          cur_term_desc,
--    CASE
--    WHEN td.primary_college_code IN ( 'HP', 'HS' ) THEN
--     'HS'
--    WHEN td.primary_college_code IN ( 'RK', 'AS' ) THEN
--     'AS'
--    WHEN td.primary_major_1 = '0000' THEN
--     '00'
--    ELSE
--     td.primary_college_code
--   END                      cur_col_code,
--   td.primary_major_1       cur_major_1,
--   td.primary_level_code    cur_level_code,
--   td.report_level_code     cur_report_level,
--   td.primary_degree_code
--  FROM
--   td_student_data  td
--   LEFT OUTER JOIN td_demographic      dm ON td.sd_term_code = dm.td_term_code
--                                        AND td.sd_pidm = dm.dm_pidm
--  WHERE
--    sd_term_code = (select substr(current_term,1,4)- 2 || '10' cur_fall 
--                    from um_current_term)    
--   AND td.registered_ind = 'Y'
--   AND td.primary_college_code IS NOT NULL
--   AND td.ia_student_type_code NOT IN ( 'D', 'E', 'G', 'S', 'X' )
----   AND educ_goal NOT IN ( 'E2', 'E3' )
----  AND td.primary_major_1 != '0000'
-- ),
--/******************************************************************************* 
--Current Reg Term Set
--*******************************************************************************/ 
--cur_reg_term AS (
--  SELECT
--   sd.sd_pidm,
--   sd.sd_term_desc          cur_reg_term_desc,
--   sd.registered_ind        cur_reg_term_enrolled_ind,
--   sd.primary_level_code    cur_reg_level_code,
--    CASE
--    WHEN sd.primary_college_code IN ( 'HP', 'HS' ) THEN
--     'HS'
--    WHEN sd.primary_college_code IN ( 'RK', 'AS' ) THEN
--     'AS'
--    WHEN sd.primary_major_1 = '0000' THEN
--     '00'
--    ELSE
--     sd.primary_college_code
--   END                      cur_reg_term_col_code,
--   sd.primary_major_1       cur_reg_term_major_1,
--   CASE
--    WHEN (
--     SELECT
--      COUNT(*)
--     FROM
--      um_degree umd
--     WHERE
--       umd.pidm = cur_term.sd_pidm
--      AND umd.degree_status = 'AW'
--     -- AND umd.degree_code = cur_term.primary_degree_code
--           AND umd.level_code = cur_term.cur_level_code
--      AND umd.grad_term_code <= sd.sd_term_code
--    ) > 0 THEN
--     'Y'
--    ELSE
--     'N'
--   END                      cur_reg_term_grad_ind
--  FROM
--   cur_term
--   LEFT JOIN td_student_data sd ON cur_term.sd_pidm = sd.sd_pidm
--   and sd.sd_term_code = ( cur_term.cur_term_code + 100 )
----  WHERE
--   
-- ),
--/******************************************************************************* 
--Next Reg Term Set
--*******************************************************************************/ 
--next_reg_term AS (
--  SELECT
--   sd.sd_pidm,
--   sd.sd_term_desc          next_reg_term_desc,
--   sd.registered_ind        next_reg_term_enrolled_ind,
--   sd.primary_level_code    next_reg_level_code,
----  sd.primary_college_code    next_reg_term_col_code,
--    CASE
--    WHEN sd.primary_college_code IN ( 'HP', 'HS' ) THEN
--     'HS'
--    WHEN sd.primary_college_code IN ( 'RK', 'AS' ) THEN
--     'AS'
--    WHEN sd.primary_major_1 = '0000' THEN
--     '00'
--    ELSE
--     sd.primary_college_code
--   END                      next_reg_term_col_code,
--   sd.primary_major_1       next_reg_term_major_1,
--   CASE
--    WHEN (
--     SELECT
--      COUNT(*)
--     FROM
--      um_degree umd
--     WHERE
--       umd.pidm = cur_term.sd_pidm
--      AND umd.degree_status = 'AW'
--     -- AND umd.degree_code = cur_term.primary_degree_code
--           AND umd.level_code = cur_term.cur_level_code
--      AND umd.grad_term_code <= sd.sd_term_code
--    ) > 0 THEN
--     'Y'
--    ELSE
--     'N'
--   END                      next_reg_term_grad_ind
--  FROM
--   cur_term
--   LEFT JOIN td_student_data sd ON cur_term.sd_pidm = sd.sd_pidm
--  WHERE
--   sd.sd_term_code = ( cur_term.cur_term_code + 200 )
-- )
-- SELECT
--  cur_term.*,
--  cur_reg_term_desc,
--  cur_reg_term_enrolled_ind,
--  cur_reg_level_code,
--  cur_reg_term_col_code,
--  cur_reg_term_major_1,
--  cur_reg_term_grad_ind,
--  next_reg_term_desc,
--  next_reg_term_enrolled_ind,
--  next_reg_level_code,
--  next_reg_term_col_code,
--  next_reg_term_major_1,
--  next_reg_term_grad_ind
-- FROM
--  cur_term
--  LEFT JOIN cur_reg_term ON cur_term.sd_pidm = cur_reg_term.sd_pidm
--  LEFT JOIN next_reg_term ON cur_term.sd_pidm = next_reg_term.sd_pidm
--;
--
 COMMIT;
--
END p_col_cont;

/
