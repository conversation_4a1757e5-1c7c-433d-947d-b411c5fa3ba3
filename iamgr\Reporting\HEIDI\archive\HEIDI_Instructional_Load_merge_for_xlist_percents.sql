WITH DP1 AS (
select
distinct
td_term_code,
crn_key,
COURSE,
SECTION_NUMBER,
XLST_GROUP
from
IA_TD_REGISTRATION_DETAIL RD
WHERE
RD.fy = (
   SELECT
    substr(current_aidy_code, 1, 2)
    || '-'
    || substr(current_aidy_code, 3, 4) fy
   FROM
    um_current_term
  ) 
),DP2 AS(
SELECT DP1.*,
CASE WHEN XLST_GROUP IS NULL THEN 1
ELSE ROW_NUMBER() OVER (PARTITION BY td_term_code, XLST_GROUP ORDER BY SECTION_NUMBER )
END
ROW_COUNT
FROM DP1
)

,MAX_COUNT AS(
SELECT 
td_term_code,
XLST_GROUP,
ROUND(1/MAX(ROW_COUNT),3)PERCENT_OF_XLIST
FROM DP2
GROUP BY
td_term_code,
XLST_GROUP
)

,DP3 AS(
SELECT DP2.*,
NVL(MAX_COUNT.PERCENT_OF_XLIST,1)PERCENT_OF_XLIST
FROM DP2
LEFT JOIN MAX_COUNT ON MAX_COUNT.td_term_code = DP2.td_term_code
and MAX_COUNT.XLST_GROUP = DP2.XLST_GROUP
ORDER BY DP2.XLST_GROUP
)
SELECT * FROM dp3 
--where td_term_code = '202110'


;