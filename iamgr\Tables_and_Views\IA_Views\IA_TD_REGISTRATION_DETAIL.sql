--------------------------------------------------------
--  DDL for View IA_TD_REGISTRATION_DETAIL
--------------------------------------------------------

  CREATE OR REPLACE VIEW ia_td_registration_detail AS
 ( SELECT 
--student info
            rd.pidm,
  dm.umid,
  dm.last_name
  || ','
  || dm.first_name                            AS stud_name,
  dm.last_name
  || ','
  || dm.first_name
  || ','
  || dm.middle_initial
  || ','
  || dm.pprn_desc                             AS pprn_name,
  dm.ca_email,
  dm.a1_area_code || dm.a1_phone_number       AS phone_number,
  rd.levl_code,
  rd.levl_desc,
  rd.td_term_code,
  rd.td_term_desc,
  CASE
   WHEN rd.td_term_code LIKE ( '%40' ) THEN
    'Su'
   WHEN rd.td_term_code LIKE ( '%10' ) THEN
    'Fa'
   WHEN rd.td_term_code LIKE ( '%20' ) THEN
    'Wi'
   WHEN rd.td_term_code LIKE ( '%30' ) THEN
    'Sp'
  END                                         season,
  CASE
   WHEN rd.section_number LIKE 'W%' THEN
    'online'
   WHEN rd.section_number LIKE 'M%' THEN
    'mixied_mode'
   ELSE
    'face_to_face'
  END                                         modality,
  fy.fy,
  rd.rsts_date,
  sd.ia_student_type_code,
  sd.ia_student_type_desc,
  sd.full_part_time_ind_umf,
  sd.residency_code,
  sd.residency_desc,
  sd.primary_degree_code,
  sd.primary_program,
  sd.primary_major_1,
  sd.primary_conc_1,
  sd.class_code,
  sd.class_desc,
  sd.va_cert_date,
  sd.va_cert_hours,
  sd.va_cert_term,
  sd.va_cert_term_ind,
  sd.va_cert_type,
  sd.va_cert_type_desc,
  sd.primary_college_code,
  sd.primary_college_desc,
  sd.overall_hours_earned,

--instructor info
    cs.primary_instructor_id,
  cs.primary_instructor_first_name,
  cs.primary_instructor_last_name,
  cs.primary_instructor_last_name
  || ', '
  || cs.primary_instructor_first_name         AS ins_name,
  cs.primary_instructor_email,
  cs.instructor_id2,
  cs.instructor_first_name2,
  cs.instructor_last_name2,
  cs.instructor_last_name2
  || ', '
  || cs.instructor_first_name2                AS second_ins_name,
  cs.instructor_email2,
  cs.instructor_id3,
  cs.instructor_last_name3
  || ', '
  || cs.instructor_first_name3                AS third_ins_name,
  cs.instructor_email3,

--course info
  rd.crn_key,
  rd.subj_code,
  rd.crse_number,
  rd.subj_code
  || ' '
  || rd.crse_number                           AS course,
  rd.subj_code
  || ' '
  || rd.crse_number
  || ' '
  || rd.section_number                        AS selected_course,
  rd.section_number,
  cs.cat_title,
  cs.course_text,
  rd.rsts_code,
  rd.rsts_desc,
  cs.meeting_start_date_1,
  cs.meeting_end_date_1,
  cs.meeting_days_1,
  cs.meeting_times_1,
  cs.meeting_schd_code_1,
  cs.meeting_room_bldg_1,
  case 
when MEETING_ROOM_BLDG_1 = ' OSYNC' THEN 'Osy'
when MEETING_ROOM_BLDG_1 = ' OASYNC' THEN 'Async'
else 'Flex'
end course_type,
  cs.xlst_group,
  cs.xlst_courses,
  cs.xlst_actual_enrollment,
  cs.cat_civic_engage_ind,
  cs.sec_civic_engage_ind,
  rd.billing_hours,
  rd.camp_code,
  rd.camp_desc,
  rd.coll_code,
  rd.coll_desc,
  cs.dept_code,
  cs.dept_desc,
  rd.credit_hours
 FROM
       td_registration_detail rd
  INNER JOIN td_catalog_schedule  cs ON cs.term_code_key = rd.td_term_code
                                       AND cs.crn_key = rd.crn_key
  INNER JOIN td_demographic       dm ON dm.dm_pidm = rd.pidm
                                  AND dm.td_term_code = rd.td_term_code
  INNER JOIN td_student_data      sd ON sd.sd_pidm = rd.pidm
                                   AND sd.sd_term_code = rd.td_term_code
  INNER JOIN ia_cw_fy_term        fy ON fy.fy_term_code = rd.td_term_code
 WHERE
  rd.rsts_code IN ( 'RW', 'RE' )
  AND rd.subj_code NOT IN ( 'MCC' )
 );