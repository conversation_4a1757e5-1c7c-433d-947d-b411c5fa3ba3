--------------------------------------------------------
--  DDL for View SIL_SMP_CONT
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "SILMGR"."SIL_SMP_CONT" ("UMID", "SD_PIDM", "SD_TERM_CODE", "SD_TERM_DESC", "FULL_PART_TIME_IND_UMF", "IA_STUDENT_TYPE_CODE", "TERM_GPA", "ONLINE_COURSES_ONLY_IND", "SMP_ROLE", "NEXT_WIN_CONT_IND", "NEXT_WIN_GRAD_IND", "NEXT_FALL_CONT_IND", "NEXT_FALL_GRAD_IND", "NEXT_WIN_CONT_OR_GRAD_IND", "NEXT_WIN_CONT_OR_GRAD_CODE", "NEXT_FALL_CONT_OR_GRAD_IND", "NEXT_FALL_CONT_OR_GRAD_CODE", "PARTICIPATED_IND") AS 
  WITH dp1 AS (
  SELECT
   sd.umid,
   sd.sd_pidm,
   sd.sd_term_code,
   sd.sd_term_desc,
   sd.full_part_time_ind_umf,
   sd.ia_student_type_code,
   um.term_gpa,
   decode (sd.online_courses_only_ind,'Y','Yes','No') online_courses_only_ind,
   nvl(sil.SMP_ROLE,'Non-SMP')SMP_ROLE,
   (
    SELECT
     td1.registered_ind
    FROM
     td_student_data td1
    WHERE
      td1.sd_term_code = to_char((to_number(sd.sd_term_code) + 10))
     AND td1.sd_pidm = sd.sd_pidm
     AND sd.sd_term_code LIKE '%10'
   )                       next_win_cont_ind, --REGISTERED AT UMF FOR WINTER
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = sd.sd_pidm
      AND umd.degree_status = 'AW'
      AND umd.level_code = sd.primary_level_code
      AND umd.grad_term_code < to_char((to_number(sd.sd_term_code) + 10))
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                     next_win_grad_ind, --REGISTERED AT UMF FOR WINTER
   (
    SELECT
     td1.registered_ind
    FROM
     td_student_data td1
    WHERE
      td1.sd_term_code = to_char((to_number(sd.sd_term_code) + 100))
     AND td1.sd_pidm = sd.sd_pidm
     AND sd.sd_term_code LIKE '%10'
   )                       next_fall_cont_ind,--REGISTERED AT UMF FOR FALL
   CASE
    WHEN (
     SELECT
      COUNT(*)
     FROM
      um_degree umd
     WHERE
       umd.pidm = sd.sd_pidm
      AND umd.degree_status = 'AW'
      AND umd.level_code = sd.primary_level_code
      AND umd.grad_term_code < to_char((to_number(sd.sd_term_code) + 100))
    ) > 0 THEN
     'Y'
    ELSE
     'N'
   END                     next_fall_grad_ind
  FROM
   td_student_data sd
   LEFT JOIN um_student_data um ON sd.sd_pidm = um.sd_pidm
                                   AND sd.sd_term_code = um.sd_term_code
   LEFT JOIN sil_smp         sil ON sd.sd_term_code = sil.SMP_TERM
                            AND sd.umid = sil.SMP_UMID 

  WHERE
   sd.sd_term_code in (select distinct  smp_term from sil_smp)-->= ( '202210' )
   AND sd.sd_term_code like '%10'
   AND sd.registered_ind = 'Y'
   AND sd.report_level_code = 'UG'
 )
 SELECT DISTINCT
  dp1."UMID",
  dp1."SD_PIDM",
  dp1."SD_TERM_CODE",
  dp1."SD_TERM_DESC",
  dp1."FULL_PART_TIME_IND_UMF",
  dp1."IA_STUDENT_TYPE_CODE",
  dp1."TERM_GPA",
  dp1."ONLINE_COURSES_ONLY_IND",
  dp1."SMP_ROLE",
  nvl(dp1."NEXT_WIN_CONT_IND",'N') NEXT_WIN_CONT_IND,
  nvl(dp1."NEXT_WIN_GRAD_IND",'N') NEXT_WIN_GRAD_IND,
  nvl(dp1."NEXT_FALL_CONT_IND",'N') NEXT_FALL_CONT_IND,
  nvl(dp1."NEXT_FALL_GRAD_IND",'N') NEXT_FALL_GRAD_IND,
  CASE
   WHEN next_win_cont_ind = 'Y'
        OR next_win_grad_ind = 'Y' THEN
    'Yes'
   ELSE
    'No'
  END next_win_cont_or_grad_ind,
  CASE
   WHEN next_win_cont_ind = 'Y'
        OR next_win_grad_ind = 'Y' THEN
    1
   ELSE
    0
  END next_win_cont_or_grad_code,
  CASE
   WHEN next_fall_cont_ind = 'Y'
        OR next_fall_grad_ind = 'Y' THEN
    'Yes'
   ELSE
    'No'
  END next_fall_cont_or_grad_ind,
  CASE
   WHEN next_fall_cont_ind = 'Y'
        OR next_fall_grad_ind = 'Y' THEN
    1
   ELSE
    0
  END next_fall_cont_or_grad_code,
  case
  when smp_role = 'Non-SMP' then 'No'
  else 'Yes'
  end participated_ind

 FROM
  dp1
;
