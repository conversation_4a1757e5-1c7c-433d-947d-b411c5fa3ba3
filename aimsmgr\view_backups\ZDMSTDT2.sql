CREATE OR REPLACE PACKAGE "ZDMSTDT2" as 

/*
  University of Michigan-Flint
  Name: ZDMSTDT
  Date: August 13, 2014
  Description:
    This is the header file for student transcripts.

    The grain of this table is one row per pidm, per course. 
  Version: 1.0
  Update Log:

  Copyrights (c) 2014, All Rights reserved: http://www.umflint.edu    
*/

  -- The lists of procedures that are found in the body. 
  procedure p_trans_transfer;
  procedure p_trans_inst;
  procedure p_trans_in_progress;

  procedure p_trans_student_transcript;
  procedure p_load_um_student_transcript;

end zdmstdt2;

/


CREATE OR REPLACE PACKAGE BODY "ZDMSTDT2" as

/*
  University of Michigan-Flint
  Name: zdmstdt2 Body
  Date: August 13, 2014
  Description:
    This is the body file for student transcript, this will build each students 
    transcript. 
  Version: 1.0
  Update Log:

  Copyrights (c) 2014, All Rights reserved: http://www.umflint.edu    
*/

  -- The lists of variables that are found in this package. 
  seq_no number := 0;
  error_message varchar2(512) := null;
  rows_before number := 0;
  rows_found number := null;
  rows_after number := 0;
  row_counter number := 0;
  max_cores number := 3;

  table_name varchar2(128) := null;

  -- ***************************************************************************
  -- This function will count the number of rows in the student transcript 
  -- table. 
  function f_count_rows(table_name_in varchar2)
  return number
  as

  query_string varchar2(512) := null;
  count_out number := 0;

  begin
    query_string :=  'select count(*) counter from ' || table_name_in;
    execute immediate query_string into count_out;
    return count_out;
  end f_count_rows;

  -- ***************************************************************************
  -- This procedure will transform the student data information, and place it 
  -- into the load_student_transcript table.
  procedure p_trans_student_transcript as

  i number := 0;
  job_label_prefix varchar2(10) := 'TRANS_STD2';
  running_jobs number := 0;

  -- This will begin the process of truncating the current table and then begin 
  -- to fill that table with the proper informtaion. 
  begin
    table_name := 'load_student_transcript2';
    seq_no := zdmctrl.f_get_new_seq_no(table_name, 'TRAN');
    zdmctrl.p_start_tran_log(seq_no, table_name);
    rows_before := f_count_rows(table_name);
    row_counter := 0;

    execute immediate('truncate table ' || table_name);

    -- This will start the transfer portion of the transcript genereation.
    begin  
      sys.dbms_scheduler.create_job (
      job_name => job_label_prefix || '_TRANSFER',
      job_type => 'PLSQL_BLOCK',
      job_action => 'begin zdmstdt2.p_trans_transfer; end;',
      enabled => true);
    end;

    -- This will start the institutional portion of the transcript generation.
    begin  
      sys.dbms_scheduler.create_job (
      job_name => job_label_prefix || '_INST',
      job_type => 'PLSQL_BLOCK',
      job_action => 'begin zdmstdt2.p_trans_inst; end;',
      enabled => true);
    end;

    -- This will start the in progress portion of the transcript generation.
    begin  
      sys.dbms_scheduler.create_job (
      job_name => job_label_prefix || '_IN_PROGRESS',
      job_type => 'PLSQL_BLOCK',
      job_action => 'begin zdmstdt2.p_trans_in_progress; end;',
      enabled => true);
    end;

    running_jobs := 3;

    while running_jobs > 0 
    loop
      begin
        dbms_lock.sleep(15);

        select count(*) into running_jobs
        from user_scheduler_jobs
        where substr(user_scheduler_jobs.job_name, 1, 10) = job_label_prefix;
      end;
    end loop;

    rows_after := f_count_rows(table_name);

    -- Error checking.
    if error_message is not null then
      zdmctrl.p_stop_tran_log(seq_no, table_name, 'BIG FAIL', error_message);
    else
      zdmctrl.p_stop_tran_log(seq_no, table_name, 'COMPLETE', null, rows_before, rows_found, rows_after);
    end if;
  end p_trans_student_transcript;

  -- *****************************************************************************
  -- This procedure will load all the students transfer credits into thier 
  -- transcript. 
  procedure p_trans_transfer as

  begin

    insert into load_student_transcript2(
      load_student_transcript2.pidm,
      load_student_transcript2.umid,
      load_student_transcript2.last_name,
      load_student_transcript2.first_name,
      load_student_transcript2.middle_initial,
      load_student_transcript2.name_suffix,
      load_student_transcript2.type,
      load_student_transcript2.type_desc,
      load_student_transcript2.term,
      load_student_transcript2.term_desc,
      load_student_transcript2.levl,
      load_student_transcript2.levl_desc,
      load_student_transcript2.subject,
      load_student_transcript2.course,
      load_student_transcript2.section,
      load_student_transcript2.crn,
      load_student_transcript2.title,
      load_student_transcript2.grade,
      load_student_transcript2.hours,
      load_student_transcript2.completed_ind,
      load_student_transcript2.grade_mode,
      load_student_transcript2.grade_change_code,
      load_student_transcript2.grade_activity_date,
      load_student_transcript2.grade_user,
      load_student_transcript2.repeat_ind,
      load_student_transcript2.attribute_1,
      load_student_transcript2.attribute_2,
      load_student_transcript2.attribute_3,
      load_student_transcript2.attribute_4,
      load_student_transcript2.attribute_more_ind,
      load_student_transcript2.transfer_institution_code,
      load_student_transcript2.transfer_institution,
      load_student_transcript2.transfer_subject,
      load_student_transcript2.transfer_course,
      load_student_transcript2.transfer_title,
      load_student_transcript2.transfer_hours,
      load_student_transcript2.transfer_grade,
      load_student_transcript2.transfer_group,
      load_student_transcript2.last_attend,
      load_student_transcript2.grade_extension_date,
      load_student_transcript2.grade_quality_points,
      load_student_transcript2.grade_numeric_value)

      with pop_sel as(
        select
        /*+ MATERIALIZE */
        spriden.spriden_pidm,
        spriden.spriden_id,
        spriden.spriden_last_name,
        spriden.spriden_mi,
        spriden.spriden_first_name,
        spbpers_join.spbpers_name_suffix
        from spriden_ext spriden
        inner join(
          select
          spbpers.spbpers_pidm,
          spbpers.spbpers_name_suffix
          from spbpers_ext spbpers
        )spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
        where spriden.spriden_change_ind is null
      --  and spriden.spriden_pidm = 60500 --55342 --91183
        order by spriden.spriden_pidm
      ),
      trans_attr_sel as(
        select
        /*+ MATERIALIZE */
        shrtatt.shrtatt_pidm,
        shrtatt.shrtatt_trit_seq_no,
        shrtatt.shrtatt_tram_seq_no,
        shrtatt.shrtatt_trcr_seq_no,
        shrtatt.shrtatt_trce_seq_no,
        shrtatt.shrtatt_attr_code,
        row_number() over(partition by shrtatt.shrtatt_pidm, shrtatt.shrtatt_trit_seq_no, shrtatt.shrtatt_tram_seq_no,
                                       shrtatt.shrtatt_trcr_seq_no, shrtatt.shrtatt_trce_seq_no 
                          order by shrtatt.shrtatt_attr_code) order_num
        from shrtatt_ext shrtatt 
      )
      select
      /*+ USE_HASH(pop_sel, trans_attr_sel)*/
      pop_sel.spriden_pidm pidm,
      pop_sel.spriden_id umid,
      pop_sel.spriden_last_name last_name,
      pop_sel.spriden_first_name first_name,
      substr(pop_sel.spriden_mi, 1, 1) middle_initial,
      pop_sel.spbpers_name_suffix name_suffix,
      'T' type,
      'Transfer' type_desc,
      shrtrce_join.shrtrce_term_code_eff term,
      (select /*+ FIRST_ROWS */ stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = shrtrce_join.shrtrce_term_code_eff) term_desc,
      shrtrce_join.shrtrce_levl_code levl,
      (select /*+ FIRST_ROWS */ stvlevl.stvlevl_desc from stvlevl_ext stvlevl where stvlevl.stvlevl_code = shrtrce_join.shrtrce_levl_code) levl_desc,
      shrtrce_join.shrtrce_subj_code subject,
      shrtrce_join.shrtrce_crse_numb course,
      null section,
      null crn,
      shrtrce_join.shrtrce_crse_title title,
      shrtrce_join.shrtrce_grde_code grade,
      shrtrce_join.shrtrce_credit_hours hours,
      (select 
       shrgrde.shrgrde_completed_ind
       from shrgrde_ext shrgrde
       where shrgrde.shrgrde_levl_code = shrtrce_join.shrtrce_levl_code
       and shrgrde.shrgrde_code = shrtrce_join.shrtrce_grde_code
       and shrgrde.shrgrde_term_code_effective = (select max(s2.shrgrde_term_code_effective)
                                                  from shrgrde_ext s2
                                                  where s2.shrgrde_levl_code = shrtrce_join.shrtrce_levl_code
                                                  and s2.shrgrde_code = shrtrce_join.shrtrce_grde_code
                                                  and s2.shrgrde_term_code_effective <= shrtrce_join.shrtrce_term_code_eff)
      )completed_ind,
      shrtrce_join.shrtrce_gmod_code grade_mode,
      null grade_change_code,
      shrtrce_join.shrtrce_activity_date activity_date,
      null grade_user,
      shrtrce_join.shrtrce_repeat_course repeat_ind,
      (select 
       trans_attr_sel.shrtatt_attr_code
       from trans_attr_sel
       where trans_attr_sel.shrtatt_pidm = pop_sel.spriden_pidm
       and trans_attr_sel.shrtatt_trit_seq_no = shrtrce_join.shrtrce_trit_seq_no
       and trans_attr_sel.shrtatt_tram_seq_no = shrtrce_join.shrtrce_tram_seq_no
       and trans_attr_sel.shrtatt_trcr_seq_no = shrtrce_join.shrtrce_trcr_seq_no
       and trans_attr_sel.shrtatt_trce_seq_no = shrtrce_join.shrtrce_seq_no
       and trans_attr_sel.order_num = 1
      ) attribute_1,
      (select 
       trans_attr_sel.shrtatt_attr_code
       from trans_attr_sel
       where trans_attr_sel.shrtatt_pidm = pop_sel.spriden_pidm
       and trans_attr_sel.shrtatt_trit_seq_no = shrtrce_join.shrtrce_trit_seq_no
       and trans_attr_sel.shrtatt_tram_seq_no = shrtrce_join.shrtrce_tram_seq_no
       and trans_attr_sel.shrtatt_trcr_seq_no = shrtrce_join.shrtrce_trcr_seq_no
       and trans_attr_sel.shrtatt_trce_seq_no = shrtrce_join.shrtrce_seq_no
       and trans_attr_sel.order_num = 2
      ) attribute_2,
      (select 
       trans_attr_sel.shrtatt_attr_code
       from trans_attr_sel
       where trans_attr_sel.shrtatt_pidm = pop_sel.spriden_pidm
       and trans_attr_sel.shrtatt_trit_seq_no = shrtrce_join.shrtrce_trit_seq_no
       and trans_attr_sel.shrtatt_tram_seq_no = shrtrce_join.shrtrce_tram_seq_no
       and trans_attr_sel.shrtatt_trcr_seq_no = shrtrce_join.shrtrce_trcr_seq_no
       and trans_attr_sel.shrtatt_trce_seq_no = shrtrce_join.shrtrce_seq_no
       and trans_attr_sel.order_num = 3
      ) attribute_3,
      (select 
       trans_attr_sel.shrtatt_attr_code
       from trans_attr_sel
       where trans_attr_sel.shrtatt_pidm = pop_sel.spriden_pidm
       and trans_attr_sel.shrtatt_trit_seq_no = shrtrce_join.shrtrce_trit_seq_no
       and trans_attr_sel.shrtatt_tram_seq_no = shrtrce_join.shrtrce_tram_seq_no
       and trans_attr_sel.shrtatt_trcr_seq_no = shrtrce_join.shrtrce_trcr_seq_no
       and trans_attr_sel.shrtatt_trce_seq_no = shrtrce_join.shrtrce_seq_no
       and trans_attr_sel.order_num = 4
      ) attribute_4,
      (select 
       case 
        when count(trans_attr_sel.shrtatt_attr_code) > 4 then 'Y'
        else null
       end
       from trans_attr_sel
       where trans_attr_sel.shrtatt_pidm = pop_sel.spriden_pidm
       and trans_attr_sel.shrtatt_trit_seq_no = shrtrce_join.shrtrce_trit_seq_no
       and trans_attr_sel.shrtatt_tram_seq_no = shrtrce_join.shrtrce_tram_seq_no
       and trans_attr_sel.shrtatt_trcr_seq_no = shrtrce_join.shrtrce_trcr_seq_no
       and trans_attr_sel.shrtatt_trce_seq_no = shrtrce_join.shrtrce_seq_no
       and trans_attr_sel.order_num = 5
      ) attribute_more_ind,
      shrtrit_join.shrtrit_sbgi_code transfer_institution_code,
      (select /*+ FIRST_ROWS */ stvsbgi.stvsbgi_desc from stvsbgi_ext stvsbgi where stvsbgi.stvsbgi_code = shrtrit_join.shrtrit_sbgi_code) transfer_institution,
      shrtrcr_join.shrtrcr_trans_course_name transfer_subject,
      shrtrcr_join.shrtrcr_trans_course_numbers transfer_course,
      shrtrcr_join.shrtrcr_tcrse_title transfer_title,
      shrtrcr_join.shrtrcr_trans_credit_hours transfer_hours,
      shrtrcr_join.shrtrcr_trans_grade transfer_grade,
      shrtrcr_join.shrtrcr_group transfer_group,
      null last_attend,
      null grade_extension_date,
      (select 
       shrgrde.shrgrde_quality_points
       from shrgrde_ext shrgrde
       where shrgrde.shrgrde_levl_code = shrtrce_join.shrtrce_levl_code
       and shrgrde.shrgrde_code = shrtrce_join.shrtrce_grde_code
       and shrgrde.shrgrde_term_code_effective = (select max(s2.shrgrde_term_code_effective)
                                                  from shrgrde_ext s2
                                                  where s2.shrgrde_levl_code = shrtrce_join.shrtrce_levl_code
                                                  and s2.shrgrde_code = shrtrce_join.shrtrce_grde_code
                                                  and s2.shrgrde_term_code_effective <= shrtrce_join.shrtrce_term_code_eff)
      )grade_quality_points,
      (select 
       shrgrde.shrgrde_numeric_value
       from shrgrde_ext shrgrde
       where shrgrde.shrgrde_levl_code = shrtrce_join.shrtrce_levl_code
       and shrgrde.shrgrde_code = shrtrce_join.shrtrce_grde_code
       and shrgrde.shrgrde_term_code_effective = (select max(s2.shrgrde_term_code_effective)
                                                  from shrgrde_ext s2
                                                  where s2.shrgrde_levl_code = shrtrce_join.shrtrce_levl_code
                                                  and s2.shrgrde_code = shrtrce_join.shrtrce_grde_code
                                                  and s2.shrgrde_term_code_effective <= shrtrce_join.shrtrce_term_code_eff)
      )grade_numeric_value
      from pop_sel
      inner join(
        select
        shrtrce.shrtrce_pidm,
        shrtrce.shrtrce_term_code_eff,
        shrtrce.shrtrce_levl_code,
        shrtrce.shrtrce_subj_code,
        shrtrce.shrtrce_crse_numb,
        shrtrce.shrtrce_crse_title,
        shrtrce.shrtrce_grde_code,
        shrtrce.shrtrce_credit_hours,
        shrtrce.shrtrce_gmod_code,
        shrtrce.shrtrce_activity_date,
        shrtrce.shrtrce_seq_no,
        shrtrce.shrtrce_trit_seq_no,
        shrtrce.shrtrce_tram_seq_no,
        shrtrce.shrtrce_trcr_seq_no,
        shrtrce.shrtrce_repeat_course
        from shrtrce_ext shrtrce
      )shrtrce_join on shrtrce_join.shrtrce_pidm = pop_sel.spriden_pidm
      inner join(
        select 
        shrtrcr.shrtrcr_pidm,
        shrtrcr.shrtrcr_trit_seq_no,
        shrtrcr.shrtrcr_tram_seq_no,
        shrtrcr.shrtrcr_seq_no,
        shrtrcr.shrtrcr_trans_course_name,
        shrtrcr.shrtrcr_trans_course_numbers,
        shrtrcr.shrtrcr_tcrse_title,
        shrtrcr.shrtrcr_trans_credit_hours,
        shrtrcr.shrtrcr_trans_grade,
        shrtrcr.shrtrcr_group
        from shrtrcr_ext shrtrcr
      )shrtrcr_join on shrtrcr_join.shrtrcr_pidm = pop_sel.spriden_pidm
                    and shrtrcr_join.shrtrcr_trit_seq_no = shrtrce_join.shrtrce_trit_seq_no
                    and shrtrcr_join.shrtrcr_tram_seq_no = shrtrce_join.shrtrce_tram_seq_no
                    and shrtrcr_join.shrtrcr_seq_no = shrtrce_join.shrtrce_trcr_seq_no
      inner join(
        select
        shrtrit.shrtrit_pidm,
        shrtrit.shrtrit_seq_no,
        shrtrit.shrtrit_sbgi_code
        from shrtrit_ext shrtrit
      )shrtrit_join on shrtrit_join.shrtrit_pidm = shrtrce_join.shrtrce_pidm
                    and shrtrit_join.shrtrit_seq_no = shrtrce_join.shrtrce_trit_seq_no;

    commit;
  end p_trans_transfer;

  -- *****************************************************************************
  -- This procedure will get each students institutional credits and place them 
  -- in thier transcript. 
  procedure p_trans_inst as

  begin

    insert into load_student_transcript2(
      load_student_transcript2.pidm,
      load_student_transcript2.umid,
      load_student_transcript2.last_name,
      load_student_transcript2.first_name,
      load_student_transcript2.middle_initial,
      load_student_transcript2.name_suffix,
      load_student_transcript2.type,
      load_student_transcript2.type_desc,
      load_student_transcript2.term,
      load_student_transcript2.term_desc,
      load_student_transcript2.levl,
      load_student_transcript2.levl_desc,
      load_student_transcript2.subject,
      load_student_transcript2.course,
      load_student_transcript2.section,
      load_student_transcript2.crn,
      load_student_transcript2.title,
      load_student_transcript2.grade,
      load_student_transcript2.hours,
      load_student_transcript2.completed_ind,
      load_student_transcript2.grade_mode,
      load_student_transcript2.grade_change_code,
      load_student_transcript2.grade_activity_date,
      load_student_transcript2.grade_user,
      load_student_transcript2.repeat_ind,
      load_student_transcript2.attribute_1,
      load_student_transcript2.attribute_2,
      load_student_transcript2.attribute_3,
      load_student_transcript2.attribute_4,
      load_student_transcript2.attribute_more_ind,
      load_student_transcript2.transfer_institution_code,
      load_student_transcript2.transfer_institution,
      load_student_transcript2.transfer_subject,
      load_student_transcript2.transfer_course,
      load_student_transcript2.transfer_title,
      load_student_transcript2.transfer_hours,
      load_student_transcript2.transfer_grade,
      load_student_transcript2.transfer_group,
      load_student_transcript2.last_attend,
      load_student_transcript2.grade_extension_date,
      load_student_transcript2.grade_quality_points,
      load_student_transcript2.grade_numeric_value)

      with pop_sel as(
        select
        /*+ MATERIALIZE */
        spriden.spriden_pidm,
        spriden.spriden_id,
        spriden.spriden_last_name,
        spriden.spriden_mi,
        spriden.spriden_first_name,
        spbpers_join.spbpers_name_suffix
        from spriden_ext spriden
        inner join(
          select
          spbpers.spbpers_pidm,
          spbpers.spbpers_name_suffix
          from spbpers_ext spbpers
        )spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
        where spriden.spriden_change_ind is null
      --  and spriden.spriden_pidm = 60500 --55342 --91183
        order by spriden.spriden_pidm
      ),
      inst_attr_sel as(
        select 
        /*+ MATERIALIZE */
        attr_sel.pidm,
        attr_sel.term_code,
        attr_sel.tckn_seq_no,
        attr_sel.crn,
        attr_sel.attr_code,
        row_number() over(partition by attr_sel.pidm, attr_sel.term_code, attr_sel.crn order by attr_order_num, attr_code) order_num
        from (
          select 
          /*+ MATERIALIZE */
          shrtckn.shrtckn_pidm pidm,
          shrtckn.shrtckn_crn crn,
          shrtckn.shrtckn_term_code term_code,
          shrtckn.shrtckn_seq_no tckn_seq_no,
          shrattc_join.shrattc_attr_code attr_code,
          1 attr_order_num
          from shrtckn_ext shrtckn
          inner join(
            select *
            from shrattc_ext shrattc
          )shrattc_join on shrattc_join.shrattc_crn = shrtckn.shrtckn_crn
                        and shrattc_join.shrattc_term_code = shrtckn.shrtckn_term_code
          union 
          select 
          /*+ MATERIALIZE */
          shrtckn.shrtckn_pidm,
          shrtckn.shrtckn_crn,
          shrtckn.shrtckn_term_code,
          shrtckn.shrtckn_seq_no,
          shrattr_join.shrattr_attr_code,
          2 attr_order_num
          from shrtckn_ext shrtckn
          inner join(
            select *
            from shrattr_ext shrattr
          )shrattr_join on shrattr_join.shrattr_pidm = shrtckn.shrtckn_pidm
                        and shrattr_join.shrattr_term_code = shrtckn.shrtckn_term_code
                        and shrattr_join.shrattr_tckn_seq_no = shrtckn.shrtckn_seq_no
        )attr_sel
      )
      select
      /*+ USE_HASH(pop_sel, inst_attr_sel) */
      pop_sel.spriden_pidm pidm,
      pop_sel.spriden_id umid,
      pop_sel.spriden_last_name last_name,
      pop_sel.spriden_first_name first_name,
      substr(pop_sel.spriden_mi, 1, 1) middle_initial,
      pop_sel.spbpers_name_suffix name_suffix,
      'I' type,
      'Institutional' type_desc,
      shrtckn_join.shrtckn_term_code term,
      (select /*+ FIRST_ROWS */ stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = shrtckn_join.shrtckn_term_code) term_desc,
      shrtckl_join.shrtckl_levl_code levl,
      (select /*+ FIRST_ROWS */ stvlevl.stvlevl_desc from stvlevl_ext stvlevl where stvlevl.stvlevl_code = shrtckl_join.shrtckl_levl_code) levl_desc,
      shrtckn_join.shrtckn_subj_code subject,
      shrtckn_join.shrtckn_crse_numb course,
      shrtckn_join.shrtckn_seq_numb section,
      shrtckn_join.shrtckn_crn crn,
      shrtckn_join.shrtckn_crse_title title,
      shrtckg_join.shrtckg_grde_code_final grade,
      shrtckg_join.shrtckg_credit_hours,
      (select shrgrde.shrgrde_completed_ind
       from shrgrde_ext shrgrde
       where shrgrde.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code
       and shrgrde.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
       and shrgrde.shrgrde_term_code_effective = (select max(shrgrde_1.shrgrde_term_code_effective)
                                                  from shrgrde_ext shrgrde_1
                                                  where shrgrde_1.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code
                                                  and shrgrde_1.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
                                                  and shrgrde_1.shrgrde_term_code_effective <= shrtckn_join.shrtckn_term_code)
      ) completed_ind,
      shrtckg_join.shrtckg_gmod_code,
      shrtckg_join.shrtckg_gchg_code,
      shrtckg_join.shrtckg_activity_date,
      shrtckg_join.shrtckg_final_grde_chg_user,
      shrtckn_join.shrtckn_repeat_course_ind,
      (select inst_attr_sel.attr_code
       from inst_attr_sel
       where inst_attr_sel.pidm = pop_sel.spriden_pidm
       and inst_attr_sel.term_code = shrtckn_join.shrtckn_term_code
       and inst_attr_sel.tckn_seq_no = shrtckn_join.shrtckn_seq_no
       and inst_attr_sel.order_num = 1) attr_1,
      (select inst_attr_sel.attr_code
       from inst_attr_sel
       where inst_attr_sel.pidm = pop_sel.spriden_pidm
       and inst_attr_sel.term_code = shrtckn_join.shrtckn_term_code
       and inst_attr_sel.tckn_seq_no = shrtckn_join.shrtckn_seq_no
       and inst_attr_sel.order_num = 2) attr_2,
       (select inst_attr_sel.attr_code
       from inst_attr_sel
       where inst_attr_sel.pidm = pop_sel.spriden_pidm
       and inst_attr_sel.term_code = shrtckn_join.shrtckn_term_code
       and inst_attr_sel.tckn_seq_no = shrtckn_join.shrtckn_seq_no
       and inst_attr_sel.order_num = 3) attr_3,
       (select inst_attr_sel.attr_code
       from inst_attr_sel
       where inst_attr_sel.pidm = pop_sel.spriden_pidm
       and inst_attr_sel.term_code = shrtckn_join.shrtckn_term_code
       and inst_attr_sel.tckn_seq_no = shrtckn_join.shrtckn_seq_no
       and inst_attr_sel.order_num = 4) attr_4,
      (select
        case
          when inst_attr_sel.attr_code > 4 then 'Y'
          else ''
        end
        from inst_attr_sel
        where inst_attr_sel.pidm = pop_sel.spriden_pidm
        and inst_attr_sel.term_code = shrtckn_join.shrtckn_term_code
        and inst_attr_sel.tckn_seq_no = shrtckn_join.shrtckn_seq_no
        and inst_attr_sel.order_num = 5
      ) more_ind,
      null transfer_institution_code,
      null transfer_institution,
      null transfer_subject,
      null transfer_course,
      null transfer_title,
      null transfer_hours,
      null transfer_grade,
      null transfer_group,
      sfrstcr_join.sfrstcr_last_attend last_attend,
      shrtckg_join.shrtckg_incomplete_ext_date,
      (select shrgrde.shrgrde_quality_points
       from shrgrde_ext shrgrde
       where shrgrde.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code
       and shrgrde.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
       and shrgrde.shrgrde_term_code_effective = (select max(shrgrde_1.shrgrde_term_code_effective)
                                                  from shrgrde_ext shrgrde_1
                                                  where shrgrde_1.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code
                                                  and shrgrde_1.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
                                                  and shrgrde_1.shrgrde_term_code_effective <= shrtckn_join.shrtckn_term_code)
      )grade_quality_points,
      (select shrgrde.shrgrde_numeric_value
       from shrgrde_ext shrgrde
       where shrgrde.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code
       and shrgrde.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
       and shrgrde.shrgrde_term_code_effective = (select max(shrgrde_1.shrgrde_term_code_effective)
                                                  from shrgrde_ext shrgrde_1
                                                  where shrgrde_1.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code
                                                  and shrgrde_1.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
                                                  and shrgrde_1.shrgrde_term_code_effective <= shrtckn_join.shrtckn_term_code)
      )grade_numeric_value
      from pop_sel
      inner join(
        with attr_sel as(
          select
          shrattc.shrattc_crn,
          shrattc.shrattc_term_code,
          shrattc.shrattc_attr_code,
          row_number() over(partition by shrattc.shrattc_crn, shrattc.shrattc_term_code
                            order by shrattc.shrattc_attr_code) order_num
          from shrattc_ext shrattc
        )
        select
        /*+ USE_HASH(attr_sel) */
        shrtckn.shrtckn_pidm,
        shrtckn.shrtckn_seq_no,
        shrtckn.shrtckn_term_code,
        shrtckn.shrtckn_subj_code,
        shrtckn.shrtckn_crse_numb,
        shrtckn.shrtckn_seq_numb,
        shrtckn.shrtckn_crn,
        shrtckn.shrtckn_crse_title,
        shrtckn.shrtckn_repeat_course_ind,
        (select
          attr_sel.shrattc_attr_code
          from attr_sel
          where attr_sel.shrattc_crn = shrtckn.shrtckn_crn
          and attr_sel.shrattc_term_code = shrtckn.shrtckn_term_code
          and attr_sel.order_num = 1) shrattc_attr_code_1,
        (select
          attr_sel.shrattc_attr_code
          from attr_sel
          where attr_sel.shrattc_crn = shrtckn.shrtckn_crn
          and attr_sel.shrattc_term_code = shrtckn.shrtckn_term_code
          and attr_sel.order_num = 2) shrattc_attr_code_2,
        (select
          attr_sel.shrattc_attr_code
          from attr_sel
          where attr_sel.shrattc_crn = shrtckn.shrtckn_crn
          and attr_sel.shrattc_term_code = shrtckn.shrtckn_term_code
          and attr_sel.order_num = 3) shrattc_attr_code_3,
        (select
          attr_sel.shrattc_attr_code
          from attr_sel
          where attr_sel.shrattc_crn = shrtckn.shrtckn_crn
          and attr_sel.shrattc_term_code = shrtckn.shrtckn_term_code
          and attr_sel.order_num = 4) shrattc_attr_code_4,
        (select
          attr_sel.shrattc_attr_code
          from attr_sel
          where attr_sel.shrattc_crn = shrtckn.shrtckn_crn
          and attr_sel.shrattc_term_code = shrtckn.shrtckn_term_code
          and attr_sel.order_num = 5) shrattc_attr_code_5
        from shrtckn_ext shrtckn
      )shrtckn_join on shrtckn_join.shrtckn_pidm = pop_sel.spriden_pidm
      inner join(
        select
        shrtckl.shrtckl_pidm,
        shrtckl.shrtckl_term_code,
        shrtckl.shrtckl_tckn_seq_no,
        shrtckl.shrtckl_levl_code
        from shrtckl_ext shrtckl
      )shrtckl_join on shrtckl_join.shrtckl_pidm = pop_sel.spriden_pidm
                    and shrtckl_join.shrtckl_term_code = shrtckn_join.shrtckn_term_code
                    and shrtckl_join.shrtckl_tckn_seq_no = shrtckn_join.shrtckn_seq_no
      inner join(
        select
        shrtckg.shrtckg_pidm,
        shrtckg.shrtckg_term_code,
        shrtckg.shrtckg_tckn_seq_no,
        shrtckg.shrtckg_grde_code_final,
        shrtckg.shrtckg_gmod_code,
        shrtckg.shrtckg_gchg_code,
        shrtckg.shrtckg_activity_date,
        shrtckg.shrtckg_final_grde_chg_user,
        shrtckg.shrtckg_credit_hours,
        shrtckg.shrtckg_incomplete_ext_date,
        row_number() over(partition by shrtckg.shrtckg_pidm, shrtckg.shrtckg_term_code, shrtckg.shrtckg_tckn_seq_no
                          order by shrtckg.shrtckg_seq_no desc) order_num
        from shrtckg_ext shrtckg
      )shrtckg_join on shrtckg_join.shrtckg_pidm = pop_sel.spriden_pidm
                    and shrtckg_join.shrtckg_term_code = shrtckn_join.shrtckn_term_code
                    and shrtckg_join.shrtckg_tckn_seq_no = shrtckn_join.shrtckn_seq_no
                    and shrtckg_join.order_num = 1
      left outer join(
        select
        sfrstcr.sfrstcr_pidm,
        sfrstcr.sfrstcr_crn,
        sfrstcr.sfrstcr_term_code,
        sfrstcr.sfrstcr_last_attend
        from sfrstcr_ext sfrstcr
      )sfrstcr_join on sfrstcr_join.sfrstcr_pidm = pop_sel.spriden_pidm
                    and sfrstcr_join.sfrstcr_crn = shrtckn_join.shrtckn_crn
                    and sfrstcr_join.sfrstcr_term_code = shrtckn_join.shrtckn_term_code;

    commit; 
  end p_trans_inst;

  -- *****************************************************************************
  -- This procedure will get each students in progress courses and place them on 
  -- their transcript. 
  procedure p_trans_in_progress as

  begin

    insert into load_student_transcript2(
      load_student_transcript2.pidm,
      load_student_transcript2.umid,
      load_student_transcript2.last_name,
      load_student_transcript2.first_name,
      load_student_transcript2.middle_initial,
      load_student_transcript2.name_suffix,
      load_student_transcript2.type,
      load_student_transcript2.type_desc,
      load_student_transcript2.term,
      load_student_transcript2.term_desc,
      load_student_transcript2.levl,
      load_student_transcript2.levl_desc,
      load_student_transcript2.subject,
      load_student_transcript2.course,
      load_student_transcript2.section,
      load_student_transcript2.crn,
      load_student_transcript2.title,
      load_student_transcript2.grade,
      load_student_transcript2.hours,
      load_student_transcript2.completed_ind,
      load_student_transcript2.grade_mode,
      load_student_transcript2.grade_change_code,
      load_student_transcript2.grade_activity_date,
      load_student_transcript2.grade_user,
      load_student_transcript2.repeat_ind,
      load_student_transcript2.attribute_1,
      load_student_transcript2.attribute_2,
      load_student_transcript2.attribute_3,
      load_student_transcript2.attribute_4,
      load_student_transcript2.attribute_more_ind,
      load_student_transcript2.transfer_institution_code,
      load_student_transcript2.transfer_institution,
      load_student_transcript2.transfer_subject,
      load_student_transcript2.transfer_course,
      load_student_transcript2.transfer_title,
      load_student_transcript2.transfer_hours,
      load_student_transcript2.transfer_grade,
      load_student_transcript2.transfer_group,
      load_student_transcript2.last_attend,
      load_student_transcript2.grade_extension_date,
      load_student_transcript2.grade_quality_points,
      load_student_transcript2.grade_numeric_value)

      with pop_sel as(
        select
        spriden.spriden_pidm,
        spriden.spriden_id,
        spriden.spriden_last_name,
        spriden.spriden_mi,
        spriden.spriden_first_name,
        spbpers_join.spbpers_name_suffix
        from spriden_ext spriden
        inner join(
          select
          spbpers.spbpers_pidm,
          spbpers.spbpers_name_suffix
          from spbpers_ext spbpers
        )spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
        where spriden.spriden_change_ind is null
      --  and spriden.spriden_pidm = 60500 --55342 --91183
        order by spriden.spriden_pidm
      ),
      progress_attr_sel as(
        select
        ssrattr.ssrattr_term_code,
        ssrattr.ssrattr_crn,
        ssrattr.ssrattr_attr_code,
        row_number() over (partition by ssrattr.ssrattr_term_code, ssrattr.ssrattr_crn order by ssrattr.ssrattr_attr_code) order_num
        from ssrattr_ext ssrattr
      )
      select
      /*+ USE_HASH(pop_sel, progress_attr_sel)*/
      pop_sel.spriden_pidm,
      pop_sel.spriden_id,
      pop_sel.spriden_last_name,
      pop_sel.spriden_first_name,
      substr(pop_sel.spriden_mi, 1, 1) middle_initial,
      pop_sel.spbpers_name_suffix,
      'W' type,
      'In Progress' type_desc,
      sfrstcr_join.sfrstcr_term_code,
      (select /*+ FIRST_ROWS */ stvterm.stvterm_desc from stvterm stvterm where stvterm.stvterm_code = sfrstcr_join.sfrstcr_term_code) term_desc,
      sfrstcr_join.sfrstcr_levl_code,
      (select /*+ FIRST_ROWS */ stvlevl.stvlevl_desc from stvlevl stvlevl where stvlevl.stvlevl_code = sfrstcr_join.sfrstcr_levl_code) levl_desc,
      ssbsect_join.ssbsect_subj_code,
      ssbsect_join.ssbsect_crse_numb,
      ssbsect_join.ssbsect_seq_numb,
      sfrstcr_join.sfrstcr_crn,
      --ssbsect_join.scbcrse_title,
      case 
        when length(ssbsect_join.ssbsect_crse_title) > 0 then ssbsect_join.ssbsect_crse_title
        else ssbsect_join.scbcrse_title 
      end scbcrse_title,
      'Reg' grade,
      sfrstcr_join.sfrstcr_credit_hr hours,
      null complete_ind,
      sfrstcr_join.sfrstcr_gmod_code grade_mode,
      null grade_change_code,
      null grade_activity_date,
      null grade_user,
      null repeat_ind,
      (select
        progress_attr_sel.ssrattr_attr_code
        from progress_attr_sel
        where progress_attr_sel.ssrattr_term_code = sfrstcr_join.sfrstcr_term_code
        and progress_attr_sel.ssrattr_crn = sfrstcr_join.sfrstcr_crn
        and progress_attr_sel.order_num = 1) attribute_1,
      (select
        progress_attr_sel.ssrattr_attr_code
        from progress_attr_sel
        where progress_attr_sel.ssrattr_term_code = sfrstcr_join.sfrstcr_term_code
        and progress_attr_sel.ssrattr_crn = sfrstcr_join.sfrstcr_crn
        and progress_attr_sel.order_num = 2) attribute_2,
      (select
        progress_attr_sel.ssrattr_attr_code
        from progress_attr_sel
        where progress_attr_sel.ssrattr_term_code = sfrstcr_join.sfrstcr_term_code
        and progress_attr_sel.ssrattr_crn = sfrstcr_join.sfrstcr_crn
        and progress_attr_sel.order_num = 3) attribute_3,
      (select
        progress_attr_sel.ssrattr_attr_code
        from progress_attr_sel
        where progress_attr_sel.ssrattr_term_code = sfrstcr_join.sfrstcr_term_code
        and progress_attr_sel.ssrattr_crn = sfrstcr_join.sfrstcr_crn
        and progress_attr_sel.order_num = 4) attribute_4,
      (select
        case
          when count(progress_attr_sel.ssrattr_attr_code) > 4 then 'Y'
          else ''
        end
        from progress_attr_sel
        where progress_attr_sel.ssrattr_term_code = sfrstcr_join.sfrstcr_term_code
        and progress_attr_sel.ssrattr_crn = sfrstcr_join.sfrstcr_crn
        and progress_attr_sel.order_num = 5) attribute_more_ind,
      null transfer_institution_code,
      null transfer_institution,
      null transfer_subject,
      null transfer_course,
      null transfer_title,
      null transfer_hours,
      null transfer_grade,
      null transfer_group,
      null last_attend,
      null grade_extension_date,
      null grade_quality_points,
      null grade_numeric_value
      from pop_sel
      inner join(
        select
        sfrstcr.sfrstcr_pidm,
        sfrstcr.sfrstcr_term_code,
        sfrstcr.sfrstcr_levl_code,
        sfrstcr.sfrstcr_crn,
        sfrstcr.sfrstcr_grde_date,
        sfrstcr.sfrstcr_rsts_code,
        sfrstcr.sfrstcr_credit_hr,
        sfrstcr.sfrstcr_gmod_code
        from sfrstcr_ext sfrstcr
      )sfrstcr_join on sfrstcr_join.sfrstcr_pidm = spriden_pidm
                    and sfrstcr_join.sfrstcr_grde_date is null
      inner join(
        select
        stvrsts.stvrsts_code,
        stvrsts.stvrsts_incl_sect_enrl
        from stvrsts_ext stvrsts
      )stvrsts_join on stvrsts_join.stvrsts_code = sfrstcr_join.sfrstcr_rsts_code
                    and stvrsts_join.stvrsts_incl_sect_enrl = 'Y'
      inner join(
        select
        ssbsect.ssbsect_crn,
        ssbsect.ssbsect_term_code,
        ssbsect.ssbsect_subj_code,
        ssbsect.ssbsect_crse_numb,
        ssbsect.ssbsect_seq_numb,
        ssbsect.ssbsect_crse_title,
        scbcrse_join.scbcrse_title
        from ssbsect_ext ssbsect
        inner join(
          select
          scbcrse.scbcrse_subj_code,
          scbcrse.scbcrse_crse_numb,
          scbcrse.scbcrse_eff_term,
          scbcrse.scbcrse_title
          from scbcrse_ext scbcrse
        )scbcrse_join on scbcrse_join.scbcrse_subj_code = ssbsect.ssbsect_subj_code
                      and scbcrse_join.scbcrse_crse_numb = ssbsect.ssbsect_crse_numb
                      and scbcrse_join.scbcrse_eff_term = (select max(s2.scbcrse_eff_term)
                                                           from scbcrse_ext s2
                                                           where s2.scbcrse_subj_code = ssbsect.ssbsect_subj_code
                                                           and s2.scbcrse_crse_numb = ssbsect.ssbsect_crse_numb
                                                           and s2.scbcrse_eff_term <= ssbsect.ssbsect_term_code)
      )ssbsect_join on ssbsect_join.ssbsect_crn = sfrstcr_join.sfrstcr_crn
                  and ssbsect_join.ssbsect_term_code = sfrstcr_join.sfrstcr_term_code;

    commit;
  end p_trans_in_progress;

  -- ***************************************************************************
  -- This procedure will load the student transcript table, by holding and 
  -- truncating the current front room table. 
  procedure p_load_um_student_transcript as

  hold_and_trunc varchar2(32) := null;

  begin
    table_name := 'um_student_transcript2';
    seq_no := zdmctrl.f_get_new_seq_no(table_name, 'LOAD');
    zdmctrl.p_start_load_log(seq_no, table_name);
    rows_before := f_count_rows(table_name);

    begin  
      hold_and_trunc := zdmctrl.f_hold_and_trunc(table_name);    

      if hold_and_trunc = 'SUCCESS' then
        insert into um_student_transcript2(
        um_student_transcript2.pidm,
        um_student_transcript2.umid,
        um_student_transcript2.last_name,
        um_student_transcript2.first_name,
        um_student_transcript2.middle_initial,
        um_student_transcript2.name_suffix,
        um_student_transcript2.type,
        um_student_transcript2.type_desc,
        um_student_transcript2.term,
        um_student_transcript2.term_desc,
        um_student_transcript2.levl,
        um_student_transcript2.levl_desc,
        um_student_transcript2.subject,
        um_student_transcript2.course,
        um_student_transcript2.section,
        um_student_transcript2.crn,
        um_student_transcript2.title,
        um_student_transcript2.grade,
        um_student_transcript2.hours,
        um_student_transcript2.COMPLETEd_ind,
        um_student_transcript2.grade_mode,
        um_student_transcript2.grade_change_code,
        um_student_transcript2.grade_activity_date,
        um_student_transcript2.grade_user,
        um_student_transcript2.repeat_ind,
        um_student_transcript2.attribute_1,
        um_student_transcript2.attribute_2,
        um_student_transcript2.attribute_3,
        um_student_transcript2.attribute_4,
        um_student_transcript2.attribute_more_ind,
        um_student_transcript2.transfer_institution_code,
        um_student_transcript2.transfer_institution,
        um_student_transcript2.transfer_subject,
        um_student_transcript2.transfer_course,
        um_student_transcript2.transfer_title,
        um_student_transcript2.transfer_hours,
        um_student_transcript2.transfer_grade,
        um_student_transcript2.transfer_group,
        um_student_transcript2.last_attend,
        um_student_transcript2.grade_extension_date,
        um_student_transcript2.grade_quality_points,
        um_student_transcript2.grade_numeric_value
        )
        select 
        load_student_transcript2.pidm,
        load_student_transcript2.umid,
        load_student_transcript2.last_name,
        load_student_transcript2.first_name,
        load_student_transcript2.middle_initial,
        load_student_transcript2.name_suffix,
        load_student_transcript2.type,
        load_student_transcript2.type_desc,
        load_student_transcript2.term,
        load_student_transcript2.term_desc,
        load_student_transcript2.levl,
        load_student_transcript2.levl_desc,
        load_student_transcript2.subject,
        load_student_transcript2.course,
        load_student_transcript2.section,
        load_student_transcript2.crn,
        load_student_transcript2.title,
        load_student_transcript2.grade,
        load_student_transcript2.hours,
        load_student_transcript2.completed_ind,
        load_student_transcript2.grade_mode,
        load_student_transcript2.grade_change_code,
        load_student_transcript2.grade_activity_date,
        load_student_transcript2.grade_user,
        load_student_transcript2.repeat_ind,
        load_student_transcript2.attribute_1,
        load_student_transcript2.attribute_2,
        load_student_transcript2.attribute_3,
        load_student_transcript2.attribute_4,
        load_student_transcript2.attribute_more_ind,
        load_student_transcript2.transfer_institution_code,
        load_student_transcript2.transfer_institution,
        load_student_transcript2.transfer_subject,
        load_student_transcript2.transfer_course,
        load_student_transcript2.transfer_title,
        load_student_transcript2.transfer_hours,
        load_student_transcript2.transfer_grade,
        load_student_transcript2.transfer_group,
        load_student_transcript2.last_attend,
        load_student_transcript2.grade_extension_date,
        load_student_transcript2.grade_quality_points,
        load_student_transcript2.grade_numeric_value
        from load_student_transcript2;

        commit;
      end if; 
    exception when others then
      error_message := sqlerrm;
    end;

    rows_after := f_count_rows(table_name);

    -- Error checking. 
    if error_message is not null then
      zdmctrl.p_stop_load_log(seq_no, table_name, 'BIG FAIL', error_message);
    else
      zdmctrl.p_stop_load_log(seq_no, table_name, 'COMPLETE', null, rows_before, rows_found, rows_after);
    end if;
  end p_load_um_student_transcript;

end zdmstdt2;

/
