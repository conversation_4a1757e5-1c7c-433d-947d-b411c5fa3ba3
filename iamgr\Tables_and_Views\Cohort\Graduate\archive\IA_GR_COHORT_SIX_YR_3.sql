CREATE OR REPLACE VIEW IA_GR_COHORT_SIX_YR
AS
  /*******************************************************************************
  Table grain: 1 row / student
  Purpose: pull student data projected out 6 years on a student and create view
  Primary keys: IA_GR_COHORT_POP.CO_GR_PIDM
  Foreign keys: TD_STUDENT_DATA.SD_PIDM
  using report_level_code because the primary_level_code was changed in the 
  system for many of the programs.  Tried to link on Primary_Major_1 and 
  primary_program but many of these had been altered in the system as well.
  *******************************************************************************/
  SELECT DISTINCT CO_GR_PIDM,
    --First Fall********************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
    ) frst_fall_term_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
    ) frst_fall_reg_ind,
    --------------------------Second----------------------------------------------
    --Second Fall Set *************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) Scnd_fall_term_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) Scnd_fall_term_reg_ind,
    ( SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
    )scnd_fall_grad_ind,-- Second Year Graduation Indicator
    ----------------------------Third-----------------------------------------------
    -- Third Fall Set **************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) thrd_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) thrd_fall_term_reg_ind,
    ( SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
    )thrd_fall_grad_ind,-- Third Year Graduation Indicator
    ----------------------------fourth---------------------------------------------------
    -- Fourth Fall Set ******************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) frth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) frth_fall_term_reg_ind,
    (SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
    )frth_fall_grad_ind,-- Fourth Year Graduation Indicator
    -----------------------------Fifth--------------------------------------------------
    -- Fifth Fall Set ******************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) ffth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) ffth_fall_term_reg_ind,
    (SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
    )ffth_fall_grad_ind,-- Five Year Graduation Indicator
    -----------------------------Sixth--------------------------------------------
    -- Sixth Fall Set **************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) sxth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) sxth_fall_term_reg_ind,
    (SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
    )sxth_fall_grad_ind,-- sixth Year Graduation Indicator
    -----------------------------Seventh-------------------------------------------
    -- Seventh Fall Set ************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) svnth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) svnth_fall_term_reg_ind,
    (SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
    )svnth_fall_grad_ind,-- Seventh Year Graduation Indicator
    -----------------------------Eigth----------------------------------------------
    -- Eight Fall Set ************************************************************
    (
    SELECT td1.sd_term_code
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) eigth_fall_code,
    (SELECT td1.registered_ind
    FROM td_student_data td1
    WHERE td1.sd_term_code     = TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
    AND td1.report_level_code = IA_GR_COHORT_POP.report_level_code
    AND td1.sd_pidm            = IA_GR_COHORT_POP.CO_GR_PIDM
      --AND td1.student_type_code IN ('C')
    ) eigth_fall_term_reg_ind,
    (SELECT DISTINCT 'Y'
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or ia_um_degree.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or ia_um_degree.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
    )eigth_fall_grad_ind, -- eighth Year Graduation Indicator
    (SELECT max(GRAD_DATE)
    FROM ia_um_degree
    WHERE ia_um_degree.pidm         = IA_GR_COHORT_POP.CO_GR_PIDM
    AND ia_um_degree.degree_status  = 'AW'
    AND (ia_um_degree.level_code     = IA_GR_COHORT_POP.report_level_code
    or IA_UM_DEGREE.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1
    or IA_UM_DEGREE.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM)
    AND ia_um_degree.grad_term_code < TO_CHAR((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
    )grad_date --Second Year Graduation Indicator
    -- Cohort Demographic Table joins and Where Clause *****************************
  FROM TD_student_data
  INNER JOIN IA_GR_COHORT_POP
  ON IA_GR_COHORT_POP.CO_GR_PIDM = TD_student_data.sd_pidm;
