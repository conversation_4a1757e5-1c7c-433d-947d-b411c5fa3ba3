/*The purpose of this query is to remove the duplicate values from the ia_cohort_pop table.
All of the duplicates that exist are because of miss coded transfer students.  Many of the the
Transfer students denoted as CO_IA_STUDENT_TYPE_CODE = 'T' are actually readmits and belong do a
previous year cohort and have been reported as a FTIAC or Transfer in an earlier year.
Miss Coded Students:
co_pidm = 76842, co_term_code_key = '200910' Student Coded as U2 and FTIAC, the student was placed back into the
cohort in the IA_COHORT_POP_VIEW script.
co_pidm = 65383, co_term_code_key = '200910' Student coded as Transfer in Banner and should be a Continuing
Student was removed from IA_CO_ALL_2008_2010 by <PERSON> on 6/1/15
Transfer Students with primary admit code = CH; this is only supposed to be applied to FTIAC's
108163, 104372, 116503
*/

create table ia_cohort_pop_undup_bac as (
select * from IA_COHORT_POP_UNDUP_TBL
);

insert into ia_cohort_pop_undup_tbl (
 SELECT * FROM IA_COHORT_POP_UNDUP
 where CO_TERM_CODE_KEY = 202010
);

select count(*)
from ia_cohort_pop_undup_tbl
;

--Students pulled from Cohorts
with dp1 as (
select sd_pidm co_pidm,
sd_term_code co_term_code_key

from ia_td_student_data
where registered_ind = 'Y'
and sd_term_code = 202010
and ia_student_type_code in ('F','T')
and PRIMARY_LEVEL_CODE = 'UG'
minus
 SELECT CO_PIDM,
 CO_TERM_CODE_KEY
 FROM IA_COHORT_POP_UNDUP
 where CO_TERM_CODE_KEY = 202010
 )
 select dp1.*,
 sd.umid,
 sd.primary_level_code,
 sd.sd_term_code,
 sd.ia_student_type_code 
 from dp1 
 left join ia_td_student_data sd on sd.sd_pidm = dp1.co_pidm
-- and sd.SD_TERM_CODE = dp1.co_term_code_key
;

