with dp0 as (
select
distinct
sd.sd_term_desc,
sd.sd_term_code,
sd.first_name,
sd.last_name,
sd.umid,
sd.sd_pidm,
sd.ca_email,
sd.primary_major_1,
sd.registered_ind,
fund_type,
fund_desc,
fa.offer_amt,
fa.accept_amt,
fa.paid_amt

from ia_um_student_data sd
left join ia_fa_award_by_term fa
on sd.sd_pidm = fa.pidm and sd.sd_term_code = fa.term_code
where 
--last_name = 'Getty'
sd.sd_pidm in ('246984','126975')
and sd.sd_term_code >= (select current_term from um_current_term)
)
select 
distinct
dp0.*
--,
--rd.COURSE,
--rd.SECTION_NUMBER,
--MEETING_DAYS_1,
--MEETING_TIMES_1,
--rd.PRIMARY_INSTRUCTOR_FIRST_NAME,
--rd.PRIMARY_INSTRUCTOR_LAST_NAME,
--rd.PRIMARY_INSTRUCTOR_EMAIL

from dp0 
left join ia_um_registration_detail rd on dp0.sd_pidm = rd.pidm
and dp0.sd_term_code = rd.term_code
order by
3,2
;


desc ia_um_registration_detail;