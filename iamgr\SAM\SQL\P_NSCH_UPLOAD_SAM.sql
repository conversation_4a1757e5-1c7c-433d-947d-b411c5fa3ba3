CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE P_SAM_REQUEST(termCode_in IN VARCHAR2, stype_in IN VARCHAR2, fullPart_in IN VARCHAR2, nscQueryType_in IN VARCHAR2) IS

    l_line VARCHAR2(4000);  -- Variable to hold the file path for the output
    TYPE L_TAB_TYPE IS TABLE OF VARCHAR2(4000);
    l_table L_TAB_TYPE := L_TAB_TYPE();  -- Initialize the collection

BEGIN
    -- Open the output file in write mode
    DBMS_OUTPUT.PUT_LINE('-- BEGIN SCRIPT');
    DBMS_OUTPUT.PUT_LINE('');

    -- Execute the query and fetch the results
    FOR rec IN (WITH BASE_SEL AS (
        SELECT DISTINCT 
            SD.FIRST_NAME,
            SD.MIDDLE_INITIAL,
            SD.LAST_NAME,
            SD.NAME_SUFFIX, 
            SD.BIRTHDATE,
            CO.CO_TERM_CODE_KEY,
            SD_PIDM 
        FROM IA_TD_STUDENT_DATA SD
        INNER JOIN IA_COHORT_POP_UNDUP_TBL CO
            ON SD.SD_PIDM = CO.CO_PIDM
            AND SD.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
        WHERE CO.CO_TERM_CODE_KEY = termCode_in --'201910'  --INSTERT COHORT TERM 
        AND CO.CO_IA_STUDENT_TYPE_CODE = stype_in     --'F'  -- STUDENT TYPE (F OR T)
        AND CO.CO_FULL_PART_IND_UMF = fullPart_in --'F'  -- FULL PART TIME STATUS (F OR P)
    ),
    
    POP_SEL AS (
        --THIS SECTION CREATES A HEADER FILE.
        SELECT
            1 ORDER_NUM,
            'H1' "A",
            '002327' "B",
            '00' "C",
            'UNIVERSITY OF MICHIGAN FLINT' "D",
            TO_CHAR(SYSDATE, 'YYYYMMDD') "E",
            nscQueryType_in "F",
            'I' "G",
            NULL "H",
            NULL "I",
            NULL "J",
            NULL "K",
            NULL "L"
        FROM DUAL
        UNION ALL
        --THIS SECTION PULLS THE STUDENT RECORDS FOR THE PAYLOAD.
        SELECT
            2 ORDER_NUM,
            'D1' "A",
            NULL "B",
            CASE
                WHEN (FIRST_NAME IS NULL OR FIRST_NAME = '.') THEN 'Jon'
                ELSE  SUBSTR(FIRST_NAME,1,20) 
            END "C",
            SUBSTR(REGEXP_REPLACE(TRIM(MIDDLE_INITIAL), '[[:punct:] ]',NULL), 1 ,1) "D",
            CASE 
                WHEN (LAST_NAME IS NULL OR LAST_NAME = '.') THEN 'Doe'
                ELSE  SUBSTR(LAST_NAME,1,20) 
            END "E",
            SUBSTR(REGEXP_REPLACE(TRIM(NAME_SUFFIX), '[[:punct:] ]',NULL),1,5) "F",
            TO_CHAR(BIRTHDATE, 'YYYYMMDD') "G",
            TO_CHAR(TO_NUMBER(SUBSTR(CO_TERM_CODE_KEY,1,4))-1 ||'0915') "H", 
            NULL "I",
            '002327' "J",
            '00' "K",
            TO_CHAR(SD_PIDM) "L"
        FROM BASE_SEL

        UNION ALL
          --THIS IS TO COUNT THE NUMBER OF RECORDS AND APPEND A TRAILER RECORD
        SELECT
            3 ORDER_NUM,
            'T1' "A",
            TO_CHAR(COUNT(SD_PIDM)+2) "B",
            NULL "C",
            NULL "D",
            NULL "E",
            NULL "F",
            NULL "G",
            NULL "H",
            NULL "I",
            NULL "J",
            NULL "K",
            NULL "L"
        FROM BASE_SEL
    )
    SELECT 
        POP_SEL.A,
        POP_SEL.B,
        POP_SEL.C,
        POP_SEL.D,
        POP_SEL.E,
        POP_SEL.F,
        POP_SEL.G,
        POP_SEL.H,
        POP_SEL.I,
        POP_SEL.J,
        POP_SEL.K,
        POP_SEL.L
    FROM POP_SEL
    ORDER BY ORDER_NUM ASC)
    LOOP
        -- Append each line to the file without enclosing quotes
        l_line := rec.A || '\t' ||
                   rec.B || '\t' ||
                   rec.C || '\t' ||
                   rec.D || '\t' ||
                   rec.E || '\t' ||
                   rec.F || '\t' ||
                   rec.G || '\t' ||
                   rec.H || '\t' ||
                   rec.I || '\t' ||
                   rec.J || '\t' ||
                   rec.K || '\t' ||
                   rec.L;
        
        DBMS_OUTPUT.PUT_LINE(l_line);
        
        -- Add the line to the collection
        l_table.EXTEND;
        l_table(l_table.COUNT) := l_line;
    END LOOP;

    -- Close the output file
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('-- END SCRIPT');

    -- Create a table with the desired name
EXECUTE IMMEDIATE 'CREATE TABLE temp_' || termCode_in || '_' || fullPart_in || '_' || 
stype_in || '_' || nscQueryType_in ||' AS SELECT * FROM L_TAB_TYPE';
                       
    DBMS_OUTPUT.PUT_LINE('Table created successfully.');
END P_SAM_REQUEST;
