WITH curr_sel AS
  (SELECT
    sorcmjr.sorcmjr_term_code_eff,
    sobcurr.SOBCURR_ACTIVITY_DATE,
    sobcurr.sobcurr_curr_rule,
    sobcurr.sobcurr_levl_code,
    sobcurr.sobcurr_coll_code,
    sobcurr.sobcurr_degc_code,
    sobcurr.sobcurr_program,
    sorcmjr.sorcmjr_cmjr_rule,
    sorcmjr.sorcmjr_majr_code,
    sorcmjr.sorcmjr_dept_code,
    (SELECT stvmajr.stvmajr_desc
    FROM stvmajr
    WHERE stvmajr.stvmajr_code = sorcmjr.sorcmjr_majr_code
    ) stvmajr_desc,
    sorcmjr.sorcmjr_disp_web_ind,
    sorcmjr.sorcmjr_desc,
    row_number() over(partition BY sobcurr.sobcurr_curr_rule, 
      sobcurr.sobcurr_levl_code, sobcurr.sobcurr_coll_code, 
      sobcurr.sobcurr_degc_code, sobcurr.sobcurr_program, 
      sorcmjr.sorcmjr_majr_code, sorcmjr.sorcmjr_dept_code 
      order by sorcmjr.sorcmjr_term_code_eff DESC) order_num
  FROM sobcurr
  INNER JOIN sorcmjr
  ON sorcmjr.sorcmjr_curr_rule = sobcurr.sobcurr_curr_rule
  WHERE 
--  sorcmjr.sorcmjr_term_code_eff = 201910
--  and 
  sorcmjr.sorcmjr_adm_ind        = 'Y'
  AND sorcmjr.sorcmjr_disp_web_ind = 'Y'
  AND sobcurr.SOBCURR_ACTIVITY_DATE >= '01-JUL-18'
  )
SELECT
    curr_sel.sorcmjr_term_code_eff,
    curr_sel.SOBCURR_ACTIVITY_DATE,
    curr_sel.sobcurr_levl_code,
    curr_sel.sobcurr_coll_code,
    curr_sel.sobcurr_degc_code,
    curr_sel.sobcurr_program,
    curr_sel.sorcmjr_dept_code,
    curr_sel.sorcmjr_majr_code,
    curr_sel.stvmajr_desc
--    ,
--    curr_sel.order_num
FROM curr_sel
WHERE curr_sel.order_num = 1
ORDER BY
    curr_sel.sorcmjr_term_code_eff,
    curr_sel.SOBCURR_ACTIVITY_DATE
;
