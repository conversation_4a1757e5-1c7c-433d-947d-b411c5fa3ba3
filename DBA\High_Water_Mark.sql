--Show data file block sizes
select
   a.tablespace_name,
   a.file_name,
   ceil( (nvl(hwm,1)*8192)/1024/1024 ) "Mo"
from dba_data_files a,
     ( select file_id, max(block_id+blocks-1) hwm
       from dba_extents
       group by file_id
     ) b
where a.file_id = b.file_id(+)
order by tablespace_name, file_name;


--Show High Water Mark (HGWTR) for specific table
set verify off
column owner format a10
column alcblks heading 'Allocated|Blocks' just c
column usdblks heading 'Used|Blocks'      just c
column hgwtr heading 'High|Water'         just c
break on owner skip page
 
select
    a.owner,
    a.table_name,
    b.blocks                        alcblks,
    a.blocks                        usdblks,
    (b.blocks-a.empty_blocks-1)     hgwtr
from
    dba_tables a,
    dba_segments b
where
    a.table_name=b.segment_name
    and a.owner=b.owner
    and a.owner not in('SYS','SYSTEM')
    and a.blocks <> (b.blocks-a.empty_blocks-1)
    and a.owner like upper('&owner')||'%'
    and a.table_name like upper('&table_name')||'%'
order by 1,2;

DESC RCRAPP3_EXT;