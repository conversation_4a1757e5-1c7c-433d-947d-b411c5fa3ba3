CREATE OR REPLACE VIEW IA_SECTION_PERFORMANCE AS
select 
pidm, 
term, 
subject, 
course , 
section, 
nvl(SEC_ATTRIBUTE_1,'No Attribute') Sec_Attribute,
CRN, 
Title, 
Grade, 
COMPLETED_IND,PRIMARY_INSTRUCTOR_ID,
PRIMARY_INSTRUCTOR_LAST_NAME || ', ' || PRIMARY_INSTRUCTOR_FIRST_NAME as INS_NAME, 
subject || ' ' ||course as sub_crs

from UM_STUDENT_TRANSCRIPT 
inner join UM_CATALOG_SCHEDULE on UM_STUDENT_TRANSCRIPT.term = UM_CATALOG_SCHEDULE.term_code_key AND 
                                  UM_STUDENT_TRANSCRIPT.CRN = UM_CATALOG_SCHEDULE.CRN_KEY
where 
type = 'I' and --institutional courses only T is the other option for transfer course credit
((subject = 'BIO' and course in ('111','113', '167'))or
(subject = 'CHM' and course in ('140', '150', '260')) or
(subject = 'MTH' and course in ('090', '111', '118', '120')) or
(subject = 'PHY' and course in ('143', '145', '243', '245'))or
(subject = 'UNV' and course = '100') or
(subject = 'EGR')


)
;
SELECT DISTINCT SUBJECT FROM UM_STUDENT_TRANSCRIPT;