--select count (*)
--from IA_COHORT_POP_UNDUP_TBL
--where co_term_code_key = '200810' and
--co_ia_student_type_code = 'F' and
--co_full_part_ind_umf = 'F'
/*
This query is designed to pull the Transfer cohort and format the file for upload to
the NSCH.

EXPORT DATA AS TAB DILIMETERED TXT WITH NONE ENCLOSURE
*/

with pop_sel as(
  --This section creates a header file.
  select
  1 order_num,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  to_char(sysdate, 'YYYYMMDD') "E",
  'CO' "F",
  'I' "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from dual
  
  union
  --This section pulls the student records for the payload.
  select
  2 order_num,
  'D1' "A",
  null "B",
  case
  when demo_join.first_name is NULL then demo_join.last_name
  when demo_join.first_name = '.' then demo_join.last_name
  else  substr(first_name,1,20) 
  end "C",
  substr(demo_join.middle_name, 1 ,1) "D",
  case 
  when last_name is NULL then 'Doe'
  when last_name = '.' then 'Doe'
  else  substr(demo_join.last_name,1,20) 
  end "E",
  substr(demo_join.name_suffix,1,5) "F",
  to_char(demo_join.birthdate, 'YYYYMMDD') "G",
  to_char(to_number(SUBSTR(IA_COHORT_POP_UNDUP_TBL.co_term_code_key,1,4))-1 ||'0901') "H", 
  null "I",
  '002327' "J",
  '00' "K",
  to_char(demo_join.dm_pidm) "L"
    
  from IA_COHORT_POP_UNDUP_TBL
  inner join(
    select
    *
    from um_demographic
   )demo_join on demo_join.dm_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  where IA_COHORT_POP_UNDUP_TBL.co_term_code_key = '200810'  --Instert Cohort term ie 201010
  and IA_COHORT_POP_UNDUP_TBL.co_ia_student_type_code = 'F'  -- Student Type (F or T)
  and IA_COHORT_POP_UNDUP_TBL.co_full_part_ind_umf = 'F'  -- full part time status (F or P)
    
  union
    --This is to count the number of records and append a trailer record
  select
  3 order_num,
  'T1' "A",
  to_char(count(IA_COHORT_POP_UNDUP_TBL.CO_PIDM)+2) "B",
  null "C",
  null "D",
  null "E",
  null "F",
  null "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from IA_COHORT_POP_UNDUP_TBL
  inner join(
    select
    dm_pidm
    from um_demographic
  )demo_join on demo_join.dm_pidm = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  where IA_COHORT_POP_UNDUP_TBL.co_term_code_key = '200810'  --Instert Cohort term ie 201010
  and IA_COHORT_POP_UNDUP_TBL.co_ia_student_type_code = 'F'  -- Student Type (F or T)
  and IA_COHORT_POP_UNDUP_TBL.co_full_part_ind_umf = 'F'  -- full part time status (F or P)
  order by order_num
)
select 
pop_sel.A,
pop_sel.B,
pop_sel.C,
pop_sel.D,
pop_sel.E,
pop_sel.F,
pop_sel.G,
pop_sel.H,
pop_sel.I,
pop_sel.J,
pop_sel.K,
pop_sel.L
from pop_sel
;
