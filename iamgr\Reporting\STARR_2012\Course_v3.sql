select
spriden.spriden_id as "SchoolAssignedID",

case
  when (current_sgbstdn_join.sgbstdn_levl_code = 'GR' or
        current_sgbstdn_join.sgbstdn_levl_code = 'PT') then 
    case
      when current_sgbstdn_join.sgbstdn_degc_code_1 = '000000' then 'GraduateNonDegree'
      when current_sgbstdn_join.sgbstdn_degc_code_1 like 'D%' then 'Doctoral'
      else 'Masters'
    end
  else
    case
    when (current_sgbstdn_join.sgbstdn_styp_code = 'S' or
          current_sgbstdn_join.sgbstdn_styp_code = 'X') then 'NonDegree'
    else
      case 
      when (current_sgbstdn_join.class_standing = 'FR' and
            current_sgbstdn_join.sgbstdn_styp_code = 'F') then 'CollegeFirstYear'
      when current_sgbstdn_join.class_standing = 'FR' then 'CollegeFirstYearAttendedBefore'
      when current_sgbstdn_join.class_standing = 'SO' then 'CollegeSophomore'
      when current_sgbstdn_join.class_standing = 'JR' then 'CollegeJunior'
      when current_sgbstdn_join.class_standing = 'SR' then 'CollegeSenior'
      end
    end
end as "StudentLevel",

(select to_char(term2.stvterm_start_date, 'yyyy-mm') 
 from stvterm term2
 where term2.stvterm_code = (select min(shrttrm.shrttrm_term_code)
                             from shrttrm
                             where shrttrm.shrttrm_pidm = spriden.spriden_pidm)
) as "EntryDate",

case
when substr(shrtgpa_join.shrtgpa_term_code, 5, 2) = '10' then substr(to_char(to_number(substr(shrtgpa_join.shrtgpa_term_code, 1, 4)) - 1), 1, 4)
else substr(shrtgpa_join.shrtgpa_term_code, 1, 4) 
end as "SessionDesignator",

case substr(shrtgpa_join.shrtgpa_term_code, 5, 2)
  when '10' then 'Fall'
  when '20' then 'Winter'
  when '30' then 'Spring'
  when '40' then 'Summer'
  else 'Other'
end as "SessionName",

'Semester' as "SessionType",

shrtgpa_join.shrtckn_subj_code as "CourseSubjectAbbreviation",

shrtgpa_join.shrtckn_crse_numb as "CourseNumber",

shrtgpa_join.scbcrse_cipc_code as "CourseCIPCode",

substr(shrtgpa_join.shrtckn_crse_title, 1, 60) as "CouseTitle",

case
when shrtgpa_join.shrtckn_subj_code = 'MTH' and shrtgpa_join.shrtckn_crse_numb = '090' then 'Remedial'
else 'Regular'
end as "CourseCreditBasis",

case
when shrtgpa_join.shrgrde_completed_ind = 'Y' then shrtgpa_join.shrtckg_credit_hours
else 0
end as "CourseCreditEarned",

case
when shrtgpa_join.shrtckg_grde_code_final = 'V' then 'AuditedCourse'
when shrtgpa_join.shrtckg_grde_code_final = 'I' then 'Incomplete'
when (shrtgpa_join.shrtckg_grde_code_final = 'IE' or 
      shrtgpa_join.shrtckg_grde_code_final = 'IF' or
      shrtgpa_join.shrtckg_grde_code_final = 'IN' or
      shrtgpa_join.shrtckg_grde_code_final = 'IU' or
      shrtgpa_join.shrtckg_grde_code_final = 'IW' or
      shrtgpa_join.shrtckg_grde_code_final = 'NE' or
      shrtgpa_join.shrtckg_grde_code_final = 'YW') then 'IncompleteNotResolvedFail'
when shrtgpa_join.shrtckg_grde_code_final = 'Y' then 'InProgress'
when (shrtgpa_join.shrtckg_grde_code_final = '*' or 
      shrtgpa_join.shrtckg_grde_code_final = '**') then 'NotYetReported'
when (shrtgpa_join.shrtckg_grde_code_final = 'N' or 
      shrtgpa_join.shrtckg_grde_code_final = 'E') then 'OtherFail'
when (shrtgpa_join.shrtckg_grde_code_final = '+A+' or
      shrtgpa_join.shrtckg_grde_code_final = 'A' or
      shrtgpa_join.shrtckg_grde_code_final = 'A-' or
      shrtgpa_join.shrtckg_grde_code_final = 'A+' or
      shrtgpa_join.shrtckg_grde_code_final = 'B' or
      shrtgpa_join.shrtckg_grde_code_final = 'B-' or
      shrtgpa_join.shrtckg_grde_code_final = 'B+' or
      shrtgpa_join.shrtckg_grde_code_final = 'C' or
      shrtgpa_join.shrtckg_grde_code_final = 'C-' or
      shrtgpa_join.shrtckg_grde_code_final = '-C-' or
      shrtgpa_join.shrtckg_grde_code_final = 'C+' or
      shrtgpa_join.shrtckg_grde_code_final = 'CBE' or
      shrtgpa_join.shrtckg_grde_code_final = 'D' or
      shrtgpa_join.shrtckg_grde_code_final = 'D-' or
      shrtgpa_join.shrtckg_grde_code_final = 'D+' or
      shrtgpa_join.shrtckg_grde_code_final = 'IA' or
      shrtgpa_join.shrtckg_grde_code_final = 'IA-' or
      shrtgpa_join.shrtckg_grde_code_final = 'IA+' or
      shrtgpa_join.shrtckg_grde_code_final = 'IB' or
      shrtgpa_join.shrtckg_grde_code_final = 'IB-' or
      shrtgpa_join.shrtckg_grde_code_final = 'IB+' or
      shrtgpa_join.shrtckg_grde_code_final = 'IC' or
      shrtgpa_join.shrtckg_grde_code_final = 'IC-' or
      shrtgpa_join.shrtckg_grde_code_final = 'IC+' or
      shrtgpa_join.shrtckg_grde_code_final = 'ID' or
      shrtgpa_join.shrtckg_grde_code_final = 'ID-' or
      shrtgpa_join.shrtckg_grde_code_final = 'ID+') then 'OtherPass'
when (shrtgpa_join.shrtckg_grde_code_final = 'F' or 
      shrtgpa_join.shrtckg_grde_code_final = 'U') then 'PassFailFail'
when (shrtgpa_join.shrtckg_grde_code_final = 'IP' or
      shrtgpa_join.shrtckg_grde_code_final = 'IS' or
      shrtgpa_join.shrtckg_grde_code_final = 'P' or
      shrtgpa_join.shrtckg_grde_code_final = 'S') then 'PassFailPass'
when (shrtgpa_join.shrtckg_grde_code_final = '.A' or
      shrtgpa_join.shrtckg_grde_code_final = '.A-' or
      shrtgpa_join.shrtckg_grde_code_final = '.A+' or
      shrtgpa_join.shrtckg_grde_code_final = '.B' or
      shrtgpa_join.shrtckg_grde_code_final = '.B-' or
      shrtgpa_join.shrtckg_grde_code_final = '.B+' or
      shrtgpa_join.shrtckg_grde_code_final = '.C' or
      shrtgpa_join.shrtckg_grde_code_final = '.C-' or
      shrtgpa_join.shrtckg_grde_code_final = '.C+' or
      shrtgpa_join.shrtckg_grde_code_final = '.D' or
      shrtgpa_join.shrtckg_grde_code_final = '.D-' or
      shrtgpa_join.shrtckg_grde_code_final = '.D+' or
      shrtgpa_join.shrtckg_grde_code_final = '.E' or
      shrtgpa_join.shrtckg_grde_code_final = '.F' or
      shrtgpa_join.shrtckg_grde_code_final = '.I' or
      shrtgpa_join.shrtckg_grde_code_final = '.N' or
      shrtgpa_join.shrtckg_grde_code_final = '.P' or
      shrtgpa_join.shrtckg_grde_code_final = '.V' or
      shrtgpa_join.shrtckg_grde_code_final = '.W' or
      shrtgpa_join.shrtckg_grde_code_final = '''A' or
      shrtgpa_join.shrtckg_grde_code_final = '''A-' or
      shrtgpa_join.shrtckg_grde_code_final = '''A+' or
      shrtgpa_join.shrtckg_grde_code_final = '''B' or
      shrtgpa_join.shrtckg_grde_code_final = '''B-' or
      shrtgpa_join.shrtckg_grde_code_final = '''B+' or
      shrtgpa_join.shrtckg_grde_code_final = '''C' or
      shrtgpa_join.shrtckg_grde_code_final = '''C-' or
      shrtgpa_join.shrtckg_grde_code_final = '''C+' or
      shrtgpa_join.shrtckg_grde_code_final = '''D' or
      shrtgpa_join.shrtckg_grde_code_final = '''D-' or
      shrtgpa_join.shrtckg_grde_code_final = '''D+' or
      shrtgpa_join.shrtckg_grde_code_final = '''E' or
      shrtgpa_join.shrtckg_grde_code_final = '''F' or
      shrtgpa_join.shrtckg_grde_code_final = '''I' or
      shrtgpa_join.shrtckg_grde_code_final = 'T' or
      shrtgpa_join.shrtckg_grde_code_final = 'TR' or
      shrtgpa_join.shrtckg_grde_code_final = 'TX' or
      shrtgpa_join.shrtckg_grde_code_final = '''W') then 'TransferNoGrade'
when shrtgpa_join.shrtckg_grde_code_final = 'W' then 'Withdrew'
end as "CourseAcademicGradeStatusCode",

trunc(shrtgpa_join.shrgrde_quality_points, 2) as "CourseAcademicGrade",

case 
when shrtgpa_join.sgbstdn_levl_code = 'GR' then '15'
else
  case
  when shrtgpa_join.shrtckg_gmod_code = '0' then '01'
  when shrtgpa_join.shrtckg_gmod_code = '6' then '06'
  when (shrtgpa_join.shrtckg_gmod_code = '1' or
        shrtgpa_join.shrtckg_gmod_code = '2' or
        shrtgpa_join.shrtckg_gmod_code = '4' or
        shrtgpa_join.shrtckg_gmod_code = '5' or
        shrtgpa_join.shrtckg_gmod_code = '7' or
        shrtgpa_join.shrtckg_gmod_code = 'C') then '10'
  when shrtgpa_join.shrtckg_gmod_code = 'P' then '60'
  when (shrtgpa_join.shrtckg_gmod_code = 'A' or
        shrtgpa_join.shrtckg_gmod_code = 'M' or
        shrtgpa_join.shrtckg_gmod_code = 'N' or
        shrtgpa_join.shrtckg_gmod_code = 'T') then '62'
  when shrtgpa_join.shrtckg_gmod_code = 'S' then '63'
  end
end as "CourseAcademicGradeScale",

shrtgpa_join.shrtckg_grde_code_final as "AcademicLetterGrade",

shrtgpa_join.shrtckn_cont_hr as "ContactHours"

from spriden
-- start term
inner join(
  select 
  max(stvterm.STVTERM_CODE) as start_term_code
  from stvterm
  where stvterm.STVTERM_CODE = '201130'
) start_term on start_term.start_term_code <> '999999'
-- end term
inner join(
  select max(stvterm.STVTERM_CODE) as end_term_code
  from stvterm
  where stvterm.STVTERM_CODE = '201220'
) end_term on end_term.end_term_code <> '999999'
-- our begining of time (first banner term)
inner join(
  select 
  stvterm.stvterm_code as bobt_term_code
  from stvterm
  where stvterm.stvterm_code = '199910'
) begining_of_banner_term on begining_of_banner_term.bobt_term_code <> '999999'
-- current term sgbstdn
inner join(
  select
  sgbstdn.sgbstdn_pidm,
  sgbstdn.sgbstdn_term_code_eff,
  sgbstdn.sgbstdn_levl_code,
  sgbstdn.sgbstdn_styp_code,
  sgbstdn.sgbstdn_degc_code_1,
  f_class_calc_fnc(sgbstdn.sgbstdn_pidm, sgbstdn.sgbstdn_levl_code, sgbstdn.sgbstdn_term_code_eff) as class_standing
  from 
  sgbstdn
) current_sgbstdn_join on current_sgbstdn_join.sgbstdn_pidm = spriden.spriden_pidm
                       and current_sgbstdn_join.sgbstdn_term_code_eff = (select max(s2.sgbstdn_term_code_eff)
                                                                         from sgbstdn s2
                                                                         where s2.sgbstdn_pidm = current_sgbstdn_join.sgbstdn_pidm
                                                                         and s2.sgbstdn_term_code_eff <= end_term.end_term_code)
-- course join
inner join(
  select
  inst_trans_union.pidm shrtgpa_pidm,
  inst_trans_union.term_code shrtgpa_term_code,
  inst_trans_union.levl_code sgbstdn_levl_code,
  inst_trans_union.credit_hours shrtckg_credit_hours,
  inst_trans_union.grde_code_final shrtckg_grde_code_final,
  inst_trans_union.cont_hr shrtckn_cont_hr,
  inst_trans_union.subj_code shrtckn_subj_code,
  inst_trans_union.crse_numb shrtckn_crse_numb,
  inst_trans_union.gmod_code shrtckg_gmod_code,
  inst_trans_union.quality_points shrgrde_quality_points,
  inst_trans_union.shrgrde_completed_ind shrgrde_completed_ind,
  inst_trans_union.crse_title shrtckn_crse_title,
  inst_trans_union.cipc_code scbcrse_cipc_code
  from
   (select
    shrtgpa.shrtgpa_pidm pidm,
    shrtgpa.shrtgpa_term_code term_code,
    shrtckn_join.shrtckl_levl_code levl_code,
    shrtckn_join.shrtckg_credit_hours credit_hours,
    shrtckn_join.shrtckg_grde_code_final grde_code_final,
    shrtckn_join.shrtckn_cont_hr cont_hr,
    shrtckn_join.shrtckn_subj_code subj_code,
    shrtckn_join.shrtckn_crse_numb crse_numb,
    shrtckn_join.shrtckg_gmod_code gmod_code,
    shrtckn_join.shrgrde_quality_points quality_points,
    shrtckn_join.shrgrde_completed_ind,
    shrtckn_join.shrtckn_crse_title crse_title,
    shrtckn_join.scbcrse_cipc_code cipc_code
    from shrtgpa
    inner join(
      select
      shrtckn.shrtckn_pidm,
      shrtckn.shrtckn_term_code,
      shrtckl_join.shrtckl_levl_code,
      shrtckg_join.shrtckg_credit_hours,
      shrtckn.shrtckn_cont_hr,
      shrtckn.shrtckn_subj_code,
      shrtckn.shrtckn_crse_numb,
      shrtckg_join.shrtckg_grde_code_final,
      shrtckg_join.shrtckg_gmod_code,
      shrgrde_join.shrgrde_quality_points,
      shrgrde_join.shrgrde_completed_ind,
      shrtckn.shrtckn_crse_title,
      null scbcrse_cipc_code --scbcrse_join.scbcrse_cipc_code
      from shrtckn
      inner join(
        select 
        shrtckg.shrtckg_pidm,
        shrtckg.shrtckg_term_code,
        shrtckg.shrtckg_tckn_seq_no,
        shrtckg.shrtckg_credit_hours,
        shrtckg.shrtckg_grde_code_final,
        shrtckg.shrtckg_gmod_code
        from shrtckg
        where shrtckg.shrtckg_seq_no = (select max(shrtckg_1.shrtckg_seq_no)
                                        from shrtckg shrtckg_1
                                        where shrtckg_1.shrtckg_pidm = shrtckg.shrtckg_pidm
                                        and shrtckg_1.shrtckg_term_code = shrtckg.shrtckg_term_code
                                        and shrtckg_1.shrtckg_tckn_seq_no = shrtckg.shrtckg_tckn_seq_no)
      ) shrtckg_join on shrtckg_join.shrtckg_pidm = shrtckn.shrtckn_pidm
                     and shrtckg_join.shrtckg_term_code = shrtckn.shrtckn_term_code
                     and shrtckg_join.shrtckg_tckn_seq_no = shrtckn.shrtckn_seq_no
      inner join(
        select
        shrtckl.shrtckl_pidm,
        shrtckl.shrtckl_term_code,
        shrtckl.shrtckl_tckn_seq_no,
        shrtckl.shrtckl_levl_code
        from shrtckl
      ) shrtckl_join on shrtckl_join.shrtckl_pidm = shrtckn.shrtckn_pidm
                     and shrtckl_join.shrtckl_term_code = shrtckn.shrtckn_term_code
                     and shrtckl_join.shrtckl_tckn_seq_no = shrtckn.shrtckn_seq_no
      inner join (
        select
        shrgrde.shrgrde_code,
        shrgrde.shrgrde_levl_code,
        shrgrde.shrgrde_term_code_effective,
        shrgrde.shrgrde_quality_points,
        shrgrde.shrgrde_completed_ind
        from shrgrde
      ) shrgrde_join on shrgrde_join.shrgrde_code = shrtckg_join.shrtckg_grde_code_final
                     and shrgrde_join.shrgrde_levl_code = shrtckl_join.shrtckl_levl_code 
                     and shrgrde_join.shrgrde_term_code_effective = (select max(shrgrde_1.shrgrde_term_code_effective)
                                                                     from shrgrde shrgrde_1
                                                                     where shrgrde_1.shrgrde_code = shrgrde_join.shrgrde_code
                                                                     and shrgrde_1.shrgrde_levl_code = shrgrde_join.shrgrde_levl_code
                                                                     and shrgrde_1.shrgrde_term_code_effective <= shrtckn.shrtckn_term_code)
    ) shrtckn_join on shrtckn_join.shrtckn_pidm = shrtgpa.shrtgpa_pidm
                   and shrtckn_join.shrtckn_term_code = shrtgpa.shrtgpa_term_code
    where shrtgpa.shrtgpa_gpa_type_ind = 'I'
    and exists (select
                'matchs on primary levl'
                from sgbstdn
                where sgbstdn.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                and sgbstdn.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
                and sgbstdn.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                     from sgbstdn sgbstdn_1
                                                     where sgbstdn_1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm
                                                     and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code))
    union all
    select
    shrtrce_join.shrtrce_pidm pidm,
    shrtrce_join.shrtrce_term_code_eff term_code,
    shrtrce_join.shrtrce_levl_code levl_code,
    shrtrce_join.shrtrce_credit_hours credit_hours,
    shrtrce_join.shrtrce_grde_code grde_code_final,
    shrtrce_join.shrtckn_cont_hr cont_hr,
    shrtrce_join.shrtrce_subj_code subj_code,
    shrtrce_join.shrtrce_crse_numb crse_numb,
    shrtrce_join.shrtrce_gmod_code gmod_code,
    shrtrce_join.shrgrde_quality_points quality_points,
    null completed_ind, 
    shrtrce_join.shrtrce_crse_title crse_title,
    shrtrce_join.cipc_code cipc_code
    from shrtgpa
    inner join(
      select
      shrtrce.shrtrce_pidm,
      shrtrce.shrtrce_term_code_eff,
      shrtrce.shrtrce_levl_code,
      shrtrce.shrtrce_credit_hours,
      shrtrce.shrtrce_grde_code,
      null shrtckn_cont_hr,
      shrtrce.shrtrce_subj_code,
      upper(shrtrce.shrtrce_crse_numb) shrtrce_crse_numb,
      shrtrce.shrtrce_gmod_code,
      shrgrde_join.shrgrde_quality_points,
      nvl(shrtrce.shrtrce_crse_title, 'Departmental Credit') shrtrce_crse_title,
      null cipc_code,
      shrtrce.shrtrce_trit_seq_no,
      shrtrce.shrtrce_tram_seq_no
      from shrtrce
      inner join(
        select
        shrgrde.shrgrde_code,
        shrgrde.shrgrde_levl_code,
        shrgrde.shrgrde_term_code_effective,
        shrgrde.shrgrde_quality_points
        from shrgrde
      ) shrgrde_join on shrgrde_join.shrgrde_code = shrtrce.shrtrce_grde_code
                     and shrgrde_join.shrgrde_levl_code = shrtrce.shrtrce_levl_code
                     and shrgrde_join.shrgrde_term_code_effective = (select max(shrgrde_1.shrgrde_term_code_effective)
                                                                     from shrgrde shrgrde_1
                                                                     where shrgrde_1.shrgrde_code = shrgrde_join.shrgrde_code
                                                                     and shrgrde_1.shrgrde_levl_code = shrgrde_join.shrgrde_levl_code
                                                                     and shrgrde_1.shrgrde_term_code_effective <= shrtrce.shrtrce_term_code_eff)
    ) shrtrce_join on shrtrce_join.shrtrce_pidm = shrtgpa.shrtgpa_pidm
                   and shrtrce_join.shrtrce_term_code_eff = shrtgpa.shrtgpa_term_code
                   and shrtrce_join.shrtrce_levl_code = shrtgpa.shrtgpa_levl_code
                   and shrtrce_join.shrtrce_trit_seq_no = shrtgpa.shrtgpa_trit_seq_no
                   and shrtrce_join.shrtrce_tram_seq_no = shrtgpa.shrtgpa_tram_seq_no
    where shrtgpa_gpa_type_ind = 'T'
    and exists (select 'matches on the primary levl'
                from sgbstdn
                where sgbstdn.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                and sgbstdn.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
                and sgbstdn.sgbstdn_term_code_eff = (select min(sgbstdn_1.sgbstdn_term_code_eff)
                                                     from sgbstdn sgbstdn_1
                                                     where sgbstdn_1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm
                                                     and sgbstdn_1.sgbstdn_term_code_eff >= shrtgpa.shrtgpa_term_code))
  )inst_trans_union
  order by inst_trans_union.pidm, inst_trans_union.term_code
)shrtgpa_join on shrtgpa_join.shrtgpa_pidm = spriden.spriden_pidm
              and shrtgpa_join.shrtgpa_term_code >= begining_of_banner_term.bobt_term_code
              and shrtgpa_join.shrtgpa_term_code <= end_term.end_term_code
-- has to have a UID
inner join(
  select 
  goradid.GORADID_PIDM,
  goradid.GORADID_ADDITIONAL_ID
  from goradid
  where goradid.GORADID_ADID_CODE = 'UIC'
  and goradid.GORADID_ADDITIONAL_ID is not null
) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
where spriden.spriden_change_ind is null
and exists (select
            'not dead'
            from spbpers
            where spbpers.SPBPERS_DEAD_IND is null
            and spbpers.spbpers_pidm = spriden.spriden_pidm)
and exists (select
            'has course work in their primary level in these terms'
            from shrtgpa
            inner join(
              select
              sgbstdn.sgbstdn_pidm,
              sgbstdn.sgbstdn_term_code_eff,
              sgbstdn.sgbstdn_levl_code
              from sgbstdn
            ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                           and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                     from sgbstdn sgbstdn_1
                                                                     where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                     and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                           and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
            where shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
            and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
            and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
--and spriden.SPRIDEN_PIDM in (87604, 12578, 10420, 32853)
--and spriden.SPRIDEN_PIDM in (102494)


