create table IA_HLC_STUDY_TBL as (

select 

/***************************************
Cohort Un-duped
***************************************/
IA_COHORT_POP_UNDUP_TBL.CO_PIDM,
IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY,
IA_COHORT_POP_UNDUP_TBL.CO_IA_STUDENT_TYPE_CODE,
IA_COHORT_POP_UNDUP_TBL.CO_FULL_PART_IND_UMF,
IA_COHORT_POP_UNDUP_TBL.CO_ETHNICITY,
IA_COHORT_POP_UNDUP_TBL.CO_GENDER,

/***************************************
Cohort Demographic
***************************************/
td_demographic.hsch_gpa,
case
when td_demographic.hsch_gpa <  2.7 then 1
when td_demographic.hsch_gpa >= 2.7 and td_demographic.hsch_gpa <=3.0   then 2
when td_demographic.hsch_gpa >= 3.01 and td_demographic.hsch_gpa <=3.25 then 3
when td_demographic.hsch_gpa >= 3.26 and td_demographic.hsch_gpa <=3.50 then 4
when td_demographic.hsch_gpa >= 3.51 and td_demographic.hsch_gpa <=3.6 then 5
when td_demographic.hsch_gpa > 3.6 then 6
else 7
end hsch_gpa_bin,
case
when td_demographic.hsch_gpa <  2.7 then 'Below 2.7'
when td_demographic.hsch_gpa >= 2.7 and td_demographic.hsch_gpa <=3.0   then '2.7-3.0'
when td_demographic.hsch_gpa >= 3.01 and td_demographic.hsch_gpa <=3.25 then '3.01-3.25'
when td_demographic.hsch_gpa >= 3.26 and td_demographic.hsch_gpa <=3.50 then '3.26-3.50'
when td_demographic.hsch_gpa >= 3.51 and td_demographic.hsch_gpa <=3.6 then '3.51-3.6'
when td_demographic.hsch_gpa > 3.6 then 'Above 3.6'
else 'No Record'
end hsch_gpa_bin_desc,
td_demographic.PCOL_GPA_TRANSFERRED_1 as PCOL_GPA, 
td_demographic.PCOL_DEGC_CODE_1 as PCOL_DEGC_CODE, 
td_demographic.PCOL_HOURS_TRANSFERRED_1 as PCOL_HOURS,
td_demographic.VETERAN_IND,
/***************************************
Student Data
***************************************/
--td_student_data.ORIG_STUDENT_TYPE_CODE, --these are already in the IA_COHORT_SIX_YR
td_student_data.CLASS_CODE,
td_student_data.primary_admit_code,
td_student_data.residency_code, 
td_student_data.primary_major_1,
td_student_data.HOUSING_IND,
td_student_data.TOTAL_CREDIT_HOURS_UMF,

/***************************************
Student Success Variables 
join to IA_COHORT_SIX_YR for projected
values  inner join on pidm
***************************************/
case when popcel2.overall_gpa >= 2.0 then 'Yes' else 'No' end success_after_1st_term,
popcel4.MOST_RECENT_ASTD_CODE, 
popcel4.overall_gpa, 
popcel4.STUDENT_STATUS_CODE,
popcel3.act_composite, 
popcel3.act_math, 
popcel3.act_english,

/***************************************
CSI Qualitative Variables - cohort term
***************************************/

ia_nl_csi_2014.WORK_DURING_SCHOOL, 
ia_nl_csi_2014.DESIRE_TO_TRANSFER,
ia_nl_csi_2014.MOTHERS_EDU, 
ia_nl_csi_2014.FATHERS_EDU, 

-- calculate First Generation student
case when (ia_nl_csi_2014.MOTHERS_EDU is NULL and ia_nl_csi_2014.FATHERS_EDU is NULL) then NULL    
     when (ia_nl_csi_2014.MOTHERS_EDU = 'High School Diploma' or ia_nl_csi_2014.MOTHERS_EDU ='Some High School' or ia_nl_csi_2014.MOTHERS_EDU = 'Elementary')and 
          (ia_nl_csi_2014.FATHERS_EDU is NULL)                                      then 'Yes'
     when (ia_nl_csi_2014.MOTHERS_EDU is NULL)and 
          (ia_nl_csi_2014.FATHERS_EDU = 'High School Diploma' or ia_nl_csi_2014.FATHERS_EDU ='Some High School' or ia_nl_csi_2014.FATHERS_EDU = 'Elementary')
                                                                                  then 'Yes'
     when (ia_nl_csi_2014.MOTHERS_EDU = 'High School Diploma' or ia_nl_csi_2014.MOTHERS_EDU ='Some High School' or ia_nl_csi_2014.MOTHERS_EDU = 'Elementary')and 
          (ia_nl_csi_2014.FATHERS_EDU = 'High School Diploma' or ia_nl_csi_2014.FATHERS_EDU ='Some High School' or ia_nl_csi_2014.FATHERS_EDU = 'Elementary')
                                                                                  then 'Yes' 
                                                                                  else 'No' 
     end Frst_Gen_Stud,
-- end first generatin student calculation    
     
/************************************************
CSI Stanine Variables - cohort term
Summary scores are expressed on a stanine scale:
1 = very low, 5 = average, 9 = very high
Calculating Stanines
Result Ranking	4%	7%	12%	17%	20%	17%	12%	7%	4%
Stanine		      1	  2	  3	  4	  5	  6	  7	  8	  9
**************************************************/
ia_nl_csi_2014.DROPOUT_PRONENESS_STANINE, 
ia_nl_csi_2014.EDUCATIONAL_STRESS_STANINE,
ia_nl_csi_2014.RECEPTIVITY_TO_HELP_STANINE,
ia_nl_csi_2014.PREDICTED_ACAD_DIFF_STANINE

/***************************************
From and Joins
***************************************/
from 
IA_COHORT_POP_UNDUP_TBL 

inner join td_demographic on td_demographic.dm_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm and 
                             td_demographic.td_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY

inner join td_student_data on td_student_data.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm and 
                              td_student_data.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
 
-- Pull First Fall GPA 
--inner join
--( select sd_pidm, 
--         TERM_GPA, 
--         sd_term_code 
--         from um_student_data ) popcel1 on popcel1.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm and 
--                                          popcel1.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY

--Pull First Winter GPA
inner join
( select sd_pidm, 
         TERM_GPA,
         registered_ind, 
         sd_term_code, 
         um_student_data.overall_gpa,
         umid 
         from um_student_data ) popcel2 on popcel2.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm and 
                                           popcel2.sd_term_code = to_char((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))

--pull most recent academic standing
inner join
( select sd_pidm, 
         TERM_GPA, 
         sd_term_code, 
         OVERALL_GPA,
         MOST_RECENT_ASTD_CODE, 
         STUDENT_STATUS_CODE,
         UMID 
         from um_student_data ) popcel4 on popcel4.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm and 
                                           popcel4.sd_term_code = '201510' -- THIS NEEDDS TO BE UPDATED EACH FALL TERM

-- Pull ACT Composite
left join 
( select pidm, 
         act_composite,
         act_math, 
         act_english,  
         td_term_code 
         from td_test_score ) popcel3 on popcel3.pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm and 
                                         popcel3.td_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
     
left join ia_nl_csi_2014 on ia_nl_csi_2014.student_ID = popcel4.umid
)

