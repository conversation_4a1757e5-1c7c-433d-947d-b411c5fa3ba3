WITH HOLD1 AS (
select 
distinct
case
when sd.primary_campus_code = 'GBS' THEN 'GBEC'
ELSE 'Non-GBEC'
end SCHOOLTYPE,
case
when sd.overall_gpa_hours < 55 then 'Lower'
ELSE 'Upper'
end CASHIERLEVEL,
case
when sd.residency_code = 'R' then 'In-State'
else 'Out-of-State'
end STUDENTRESIDENCEY,
case
when sd.primary_campus_code = 'GBS' then 'GBS'
else 'NotGBS'
end CAMPUSCODE,
case
when sd.overall_gpa_hours < 55 then '<55'
ELSE '>=55'
end OVERALLCRHRTHRESHOLDLEVELDESC,
substr(sd.sd_term_code,1,4)REGTERM4DIGIT,
dm.last_name,
dm.first_name,
dm.middle_initial,
sd.sd_pidm,
dm.umid,
sd.sd_term_code,
sd.sd_term_desc,
dm.intl_ind,
sd.registered_ind,
sd.full_part_time_ind_umf,
sd.student_status_code,
sd.STUDENT_STATUS_DESC,
sd.STUDENT_TYPE_CODE,
sd.STUDENT_TYPE_DESC,
sd.CLASS_CODE,
sd.CLASS_DESC,
sd.RATE_CODE,
sd.RATE_DESC,
sd.RESIDENCY_CODE,
sd.RESIDENCY_DESC,
sd.PRIMARY_ADMIT_CODE,
sd.PRIMARY_ADMIT_DESC,
sd.PRIMARY_ADMIT_TERM,
sd.PRIMARY_ADMIT_TERM_DESC,
sd.TERM_REGISTERED_HOURS,
sd.TERM_BILLING_HOURS,
sd.TERM_HOURS_ATTEMPTED,
sd.TERM_HOURS_EARNED,
sd.TERM_GPA_HOURS,
sd.TERM_GPA,
sd.TERM_HOURS_PASSED,
sd.TERM_ASTD_CODE,
sd.TERM_ASTD_DESC,
sd.OVERALL_HOURS_ATTEMPTED,
sd.OVERALL_HOURS_EARNED,
sd.OVERALL_GPA_HOURS,
sd.OVERALL_GPA,
sd.OVERALL_HOURS_PASSED,
sd.INST_HOURS_EARNED,
sd.INST_GPA,
sd.INST_HOURS_PASSED,
sd.INST_GPA_HOURS,
sd.INST_HOURS_ATTEMPTED,
sd.ENROLLED_IND,
sd.ENROLLMENT_ADD_DATE,
sd.TOTAL_CREDIT_HOURS_UMF,
sd.DEEP_IND,
dm.HSCH_CODE,
dm.HSCH_DESC,
sd.PRIMARY_CAMPUS_CODE,
sd.PRIMARY_CAMPUS_DESC,
sd.PRIMARY_COLLEGE_CODE,
sd.PRIMARY_COLLEGE_DESC,
sd.PRIMARY_DEGREE_CODE,
sd.PRIMARY_DEGREE_DESC,
sd.PRIMARY_MAJOR_1,
sd.PRIMARY_MAJOR_1_DESC
-- ,

 

from um_student_data sd 
inner join um_demographic dm on sd.sd_pidm = dm.pidm_key
--inner join tbraccd on tbraccd.TBRACCD_PIDM = sd.sd_pidm
--and tbraccd.tbraccd_term_code = sd.sd_term_code
--INNER JOIN
where sd.sd_term_code = 202110 --&sd.sd_term_code
AND (sd.STUDENT_STATUS_CODE = 'AS') 
AND (sd.TERM_BILLING_HOURS > 0)
AND (((sd.STUDENT_TYPE_CODE = 'D') AND (sd.DEEP_IND = 'Y')) OR (sd.STUDENT_TYPE_CODE = 'E')) 
--550

)
select 
hold1.*,
TBRACCD.TBRACCD_DETAIL_CODE,
TBRACCD.TBRACCD_AMOUNT,
TBBDETC.TBBDETC_DCAT_CODE
--,
--CASE when (select tbraccd.TBRACCD_DETAIL_CODE 
--from tbraccd where tbraccd.TBRACCD_PIDM = hold1.sd_pidm
--and tbraccd.tbraccd_term_code = hold1.sd_term_code) = 'TPRT' 
--THEN (select sum(tbraccd.TBRACCD_AMOUNT) 
--from tbraccd where tbraccd.TBRACCD_PIDM = hold1.sd_pidm
--and tbraccd.tbraccd_term_code = hold1.sd_term_code) 
--ELSE 0
--END "SOMPremium"
-- tbraccd.TBRACCD_DETAIL_CODE
--,
--dect.
from hold1 --2001
INNER JOIN deep_early_college_tuition dect ON 
hold1.sd_term_code = dect.TERMCODE
and hold1.SCHOOLTYPE = dect.SCHOOLTYPE
and hold1.CASHIERLEVEL = dect.CASHIERLEVEL
and hold1.STUDENTRESIDENCEY = dect.STUDENTRESIDENCEY
and hold1.CAMPUSCODE = dect.CAMPUSCODE
and hold1.OVERALLCRHRTHRESHOLDLEVELDESC = dect.OVERALLCRHRTHRESHOLDLEVELDESC
INNER JOIN tbraccd on hold1.sd_term_code = tbraccd.tbraccd_term_code
and hold1.sd_pidm = tbraccd.tbraccd_pidm
INNER JOIN TBBDETC on tbraccd.TBRACCD_DETAIL_CODE = TBBDETC.TBBDETC_DETAIL_CODE
LEFT JOIN SGRSATT on SGRSATT.SGRSATT_PIDM = hold1.sd_pidm
LEFT JOIN STVATTS on SGRSATT.SGRSATT_ATTS_CODE = STVATTS.STVATTS_CODE
;