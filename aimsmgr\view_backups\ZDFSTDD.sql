CREATE OR REPLACE PACKAGE "ZDFSTDD" as 

  procedure p_trans_fa_student_data; 

end zdfstdd;

/


CREATE OR REPLACE PACKAGE BODY "ZDFSTDD" as

  procedure p_trans_fa_student_data as
  
  degree_here_counter number := 0;
  degree_else_counter number := 0;
  max_loan_amount number := 0;
  max_pell number := 0;

  no_award_ind varchar2(1) := null;
  loan_available_spsu number := 0;

  spsu_appl_ind varchar2(1) := null;
  spsu_appl_on_time_ind varchar2(3) := null;
  restricted_to_loans_only varchar2(1) := null;
  sd_term_code varchar2(6) := null;
  used_up_all_money_fawi_ind varchar2(1) := null;

  umid varchar2(9) := null;
  student_type_code varchar2(1) := null;
  student_type_desc varchar2(30) := null;
  primary_level_code varchar2(2) := null;
  primary_level_desc varchar2(30) := null;
  registered_ind varchar2(1) := null;
  class_code varchar2(2) := null;
  class_desc varchar2(30) := null;

  cursor term_cur
  is    
  select distinct rzbfaset.aidy_code aidy_code
  from rzbfaset
--  where aidy_code = '1011'
  where aidy_code = '1213'
  order by rzbfaset.aidy_code;

  type term_table is table of term_cur%rowtype index by binary_integer;
  term_rec term_table;

  cursor rorstat_cur(aidy_code_in varchar2)
  is
  select 
  rorstat.rorstat_pidm,
  rorstat.rorstat_aidy_code,
  rpratrm_join.rpratrm_term_code,
  rpratrm_join.rpratrm_fund_code,
  rpratrm_join.rfrbase_fund_title,
  rpratrm_join.rfrbase_ftyp_code,
  nvl(rpratrm_join.loan_ftyp_count_by_term, 0) loan_ftyp_count_by_term,
  nvl(rpratrm_join.schl_ftyp_count_by_term, 0) schl_ftyp_count_by_term,
  nvl(rpratrm_join.grnt_ftyp_count_by_term, 0) grnt_ftyp_count_by_term,
  rpratrm_join.loan_awards_only_ind,
  rpratrm_join.grant_awards_only_ind,
  rpratrm_join.scholarship_awards_only_ind,
  rpratrm_join.loan_and_grant_awards_only_ind,
  rpratrm_join.rpratrm_offer_amt,
  rpratrm_join.rpratrm_paid_amt,
  rrrareq_join.rrrareq_treq_code,
  rrrareq_join.rrrareq_est_date,
  rpratrm_join.rpratrm_orig_offer_amt,
  rpratrm_join.rpratrm_total_orig_offer_amt,
  rpratrm_join.rpratrm_total_offer_amount,
  rpratrm_join.rpratrm_total_paid_amount,
  rcrapp_join.rcrapp2_pell_pgi,
  rcrapp_join.rcrapp2_c_depend_status,
  rpratrm_join.rorsapr_sapr_code,
  rcrlds4_join.rcrlds4_pell_leu,
  rcrlds4_join.rcrlds4_pell_leu_limit_flag,
  rzbspsu_join.cut_off_spsu_date,
  rzbspsu_join.efc_set_amt,
  rcrapp_join.rcrapp1_degree_by_july,
  rcrapp_join.rcrapp3_grad_or_prof,
  rcrapp_join.rcrapp3_degree_type,
  rzbmaxpell_join.efc,
  rzbmaxpell_join.maxpell,
  pell_fall_winter_paid_join.pell_fall_winter_paid_amt,
  loan_fall_winter_paid_join.loan_fall_winter_offer_amt,
  loan_fall_winter_paid_join.loan_fall_winter_paid_amt
  from rorstat_ext rorstat
  -- rrrareq join (aidy_code based)
  left outer join(
    select
    rrrareq.rrrareq_pidm,
    rrrareq.rrrareq_aidy_code,
    rrrareq.rrrareq_treq_code,
    rrrareq.rrrareq_est_date
    from rrrareq_ext rrrareq 
  )rrrareq_join on rrrareq_join.rrrareq_pidm = rorstat.rorstat_pidm
                and rrrareq_join.rrrareq_aidy_code = rorstat.rorstat_aidy_code
                and rrrareq_join.rrrareq_treq_code = 'SPSU' || substr(rorstat.rorstat_aidy_code, 3, 2)
  -- rpratrm join (term_code based)
  left outer join(
    select
    rpratrm.rpratrm_pidm,
    rpratrm.rpratrm_aidy_code,
    rpratrm.rpratrm_term_code,
    rpratrm.rpratrm_fund_code,
    rfrbase_join.rfrbase_ftyp_code,
    rfrbase_join.rfrbase_fund_title,
    rpratrm.rpratrm_offer_amt,
    rpratrm.rpratrm_paid_amt,
    rpratrm.rpratrm_orig_offer_amt,
    loan_count_join. ftyp_counter loan_ftyp_count_by_term,
    schl_count_join. ftyp_counter schl_ftyp_count_by_term,
    grnt_count_join. ftyp_counter grnt_ftyp_count_by_term,
    case
      when nvl(loan_count_join.ftyp_counter, 0) > 0 and 
           nvl(schl_count_join.ftyp_counter, 0) < 1 and 
           nvl(grnt_count_join.ftyp_counter, 0) < 1 then 'Y'
      else 'N'
    end loan_awards_only_ind,
    case
      when nvl(loan_count_join.ftyp_counter, 0) < 1 and 
           nvl(schl_count_join.ftyp_counter, 0) < 1 and 
           nvl(grnt_count_join.ftyp_counter, 0) > 0 then 'Y'
      else 'N'
    end grant_awards_only_ind,
    case
      when nvl(loan_count_join.ftyp_counter, 0) < 1 and 
           nvl(schl_count_join.ftyp_counter, 0) > 0 and 
           nvl(grnt_count_join.ftyp_counter, 0) < 1 then 'Y'
      else 'N'
    end scholarship_awards_only_ind,
    case
      when nvl(loan_count_join.ftyp_counter, 0) > 0 and
           nvl(grnt_count_join.ftyp_counter, 0) > 0 and 
           nvl(schl_count_join.ftyp_counter, 0) < 1 then 'Y'
      else 'N'
    end loan_and_grant_awards_only_ind,
    (select sum(rpratrm_2.rpratrm_orig_offer_amt) from rpratrm_ext rpratrm_2 where rpratrm_2.rpratrm_pidm = rpratrm.rpratrm_pidm and rpratrm_2.rpratrm_term_code = rpratrm.rpratrm_term_code) rpratrm_total_orig_offer_amt,
    (select sum(rpratrm_2.rpratrm_offer_amt) from rpratrm_ext rpratrm_2 where rpratrm_2.rpratrm_pidm = rpratrm.rpratrm_pidm and rpratrm_2.rpratrm_term_code = rpratrm.rpratrm_term_code) rpratrm_total_offer_amount,
    (select sum(nvl(rpratrm_2.rpratrm_paid_amt, 0)) from rpratrm_ext rpratrm_2 where rpratrm_2.rpratrm_pidm = rpratrm.rpratrm_pidm and rpratrm_2.rpratrm_term_code = rpratrm.rpratrm_term_code) rpratrm_total_paid_amount,
    rorsapr_join.rorsapr_sapr_code

    from rpratrm_ext rpratrm
    inner join(
      select
      rfrbase.rfrbase_fund_code,
      rfrbase.rfrbase_ftyp_code,
      rfrbase.rfrbase_fund_title
      from rfrbase_ext rfrbase
    )rfrbase_join on rfrbase_join.rfrbase_fund_code = rpratrm.rpratrm_fund_code
    left outer join(
      select
      rorsapr.rorsapr_pidm,
      rorsapr.rorsapr_term_code,
      rorsapr.rorsapr_sapr_code
      from rorsapr_ext rorsapr
    )rorsapr_join on rorsapr_join.rorsapr_pidm = rpratrm.rpratrm_pidm
                  and rorsapr_join.rorsapr_term_code = rpratrm.rpratrm_term_code
    left outer join(

      select 
      rpratrm.rpratrm_pidm,
      rpratrm.rpratrm_term_code,
      rfrbase_join.rfrbase_ftyp_code,
      count(rfrbase_join.rfrbase_ftyp_code) ftyp_counter 
      from rpratrm_ext rpratrm
      inner join(
        select
        rfrbase.rfrbase_fund_code,
        rfrbase.rfrbase_ftyp_code
        from rfrbase_ext rfrbase
      )rfrbase_join on rfrbase_join.rfrbase_fund_code = rpratrm.rpratrm_fund_code
      where (rpratrm.rpratrm_offer_amt > 0 or rpratrm.rpratrm_orig_offer_amt > 0) 
      group by   rpratrm.rpratrm_pidm, rpratrm.rpratrm_term_code, rfrbase_join.rfrbase_ftyp_code

    )loan_count_join on loan_count_join.rpratrm_pidm = rpratrm.rpratrm_pidm
                     and loan_count_join.rpratrm_term_code = rpratrm.rpratrm_term_code
                     and loan_count_join.rfrbase_ftyp_code = 'LOAN'
    left outer join(
      select 
      rpratrm.rpratrm_pidm,
      rpratrm.rpratrm_term_code,
      rfrbase_join.rfrbase_ftyp_code,
      count(rfrbase_join.rfrbase_ftyp_code) ftyp_counter 
      from rpratrm_ext rpratrm
      inner join(
        select
        rfrbase.rfrbase_fund_code,
        rfrbase.rfrbase_ftyp_code
        from rfrbase_ext rfrbase
      )rfrbase_join on rfrbase_join.rfrbase_fund_code = rpratrm.rpratrm_fund_code
      where (rpratrm.rpratrm_offer_amt > 0 or rpratrm.rpratrm_orig_offer_amt > 0) 
      group by   rpratrm.rpratrm_pidm, rpratrm.rpratrm_term_code, rfrbase_join.rfrbase_ftyp_code
    )schl_count_join on schl_count_join.rpratrm_pidm = rpratrm.rpratrm_pidm
                     and schl_count_join.rpratrm_term_code = rpratrm.rpratrm_term_code
                     and schl_count_join.rfrbase_ftyp_code = 'SCHL'
    left outer join(
      select 
      rpratrm.rpratrm_pidm,
      rpratrm.rpratrm_term_code,
      rfrbase_join.rfrbase_ftyp_code,
      count(rfrbase_join.rfrbase_ftyp_code) ftyp_counter 
      from rpratrm_ext rpratrm
      inner join(
        select
        rfrbase.rfrbase_fund_code,
        rfrbase.rfrbase_ftyp_code
        from rfrbase_ext rfrbase
      )rfrbase_join on rfrbase_join.rfrbase_fund_code = rpratrm.rpratrm_fund_code
      where (rpratrm.rpratrm_offer_amt > 0 or rpratrm.rpratrm_orig_offer_amt > 0) 
      group by   rpratrm.rpratrm_pidm, rpratrm.rpratrm_term_code, rfrbase_join.rfrbase_ftyp_code
    )grnt_count_join on grnt_count_join.rpratrm_pidm = rpratrm.rpratrm_pidm
                     and grnt_count_join.rpratrm_term_code = rpratrm.rpratrm_term_code
                     and grnt_count_join.rfrbase_ftyp_code = 'GRNT'
  )rpratrm_join on rpratrm_join.rpratrm_pidm = rorstat.rorstat_pidm
                and rpratrm_join.rpratrm_aidy_code = rorstat.rorstat_aidy_code
  left outer join(
    select
    rcrapp1.rcrapp1_pidm,
    rcrapp1.rcrapp1_aidy_code,
    rcrapp1.rcrapp1_infc_code,
    rcrapp1.rcrapp1_degree_by_july,
    rcrapp2_join.rcrapp2_pell_pgi,
    rcrapp2_join.rcrapp2_c_depend_status,
    rcrapp3_join.rcrapp3_grad_or_prof,
    rcrapp3_join.rcrapp3_degree_type,
    rcrapp1.rcrapp1_curr_rec_ind
    from rcrapp1_ext rcrapp1
    inner join(
      select
      rcrapp2.rcrapp2_pidm,
      rcrapp2.rcrapp2_aidy_code,
      rcrapp2.rcrapp2_seq_no,
      rcrapp2.rcrapp2_infc_code,
      rcrapp2.rcrapp2_pell_pgi,
      rcrapp2.rcrapp2_c_depend_status
      from rcrapp2_ext rcrapp2
    )rcrapp2_join on rcrapp2_join.rcrapp2_pidm = rcrapp1.rcrapp1_pidm
                  and rcrapp2_join.rcrapp2_aidy_code = rcrapp1.rcrapp1_aidy_code
                  and rcrapp2_join.rcrapp2_seq_no = rcrapp1.rcrapp1_seq_no
                  and rcrapp2_join.rcrapp2_infc_code = rcrapp1.rcrapp1_infc_code
    inner join(
      select
      rcrapp3.rcrapp3_pidm,
      rcrapp3.rcrapp3_aidy_code,
      rcrapp3.rcrapp3_seq_no,
      rcrapp3.rcrapp3_infc_code,
      rcrapp3.rcrapp3_grad_or_prof,
      rcrapp3.rcrapp3_degree_type
      from rcrapp3_ext rcrapp3
    )rcrapp3_join on rcrapp3_join.rcrapp3_pidm = rcrapp1.rcrapp1_pidm
                  and rcrapp3_join.rcrapp3_aidy_code = rcrapp1.rcrapp1_aidy_code
                  and rcrapp3_join.rcrapp3_seq_no = rcrapp1.rcrapp1_seq_no
                  and rcrapp3_join.rcrapp3_infc_code = rcrapp1.rcrapp1_infc_code
  )rcrapp_join on rcrapp_join.rcrapp1_pidm = rorstat.rorstat_pidm
               and rcrapp_join.rcrapp1_aidy_code = rorstat.rorstat_aidy_code
               and rcrapp_join.rcrapp1_curr_rec_ind = 'Y'
  left outer join(
    select
    rcrlds4.rcrlds4_pidm,
    rcrlds4.rcrlds4_aidy_code,
    rcrlds4.rcrlds4_curr_rec_ind,
    rcrlds4.rcrlds4_pell_leu,
    rcrlds4.rcrlds4_pell_leu_limit_flag
    from rcrlds4_ext rcrlds4
  )rcrlds4_join on rcrlds4_join.rcrlds4_pidm = rorstat.rorstat_pidm
                and rcrlds4_join.rcrlds4_aidy_code = rorstat.rorstat_aidy_code
                and rcrlds4_join.rcrlds4_curr_rec_ind = 'Y'
  inner join(
    select
    rzbspsu_cutoff_date.aidy_code,
    rzbspsu_cutoff_date.cut_off_spsu_date,
    rzbspsu_cutoff_date.efc_set_amt  
    from rzbspsu_cutoff_date
  )rzbspsu_join on rzbspsu_join.aidy_code = rorstat.rorstat_aidy_code
  left outer join(
    select
    rzbmaxpell.aidy_code,
    rzbmaxpell.efc,
    rzbmaxpell.maxpell
    from rzbmaxpell
  )rzbmaxpell_join on rzbmaxpell_join.aidy_code = rorstat.rorstat_aidy_code
                   and rzbmaxpell_join.efc = rcrapp_join.rcrapp2_pell_pgi
  left outer join(
    select
    rpratrm.rpratrm_pidm,
    rpratrm.rpratrm_aidy_code,
    sum(rpratrm.rpratrm_paid_amt) pell_fall_winter_paid_amt
    from rpratrm_ext rpratrm
    where rpratrm.rpratrm_term_code in (
      select 
      stvterm.stvterm_code
      from stvterm_ext stvterm
      where (substr(stvterm.stvterm_code, 5, 2) = '10' or
             substr(stvterm.stvterm_code, 5, 2) = '20')
      and stvterm.stvterm_fa_proc_yr = rpratrm.rpratrm_aidy_code
    )
    and rpratrm.rpratrm_fund_code = '1PELL'
    group by rpratrm.rpratrm_pidm, rpratrm.rpratrm_aidy_code
  )pell_fall_winter_paid_join on pell_fall_winter_paid_join.rpratrm_pidm = rorstat.rorstat_pidm
                              and pell_fall_winter_paid_join.rpratrm_aidy_code = rorstat.rorstat_aidy_code
  left outer join(
    select
    sub_4.rpratrm_pidm,
    sub_4.rpratrm_aidy_code,
    sum(sub_4.rpratrm_offer_amt) loan_fall_winter_offer_amt,
    sum(sub_4.rpratrm_paid_amt) loan_fall_winter_paid_amt
    from (
      select 
      sub_3.rpratrm_pidm,
      sub_3.rpratrm_aidy_code,
      sub_3.rpratrm_offer_amt,
      sub_3.rpratrm_paid_amt  
      from (
        select
        rpratrm.rpratrm_pidm,
        rpratrm.rpratrm_aidy_code,
        rpratrm.rpratrm_term_code,
        rpratrm.rpratrm_fund_code,
        nvl(rpratrm.rpratrm_offer_amt, 0) rpratrm_offer_amt,
        nvl(rpratrm.rpratrm_paid_amt, 0) rpratrm_paid_amt
        from rpratrm_ext rpratrm
        where rpratrm.rpratrm_term_code in (
          select 
          stvterm.stvterm_code
          from stvterm_ext stvterm
          where (substr(stvterm.stvterm_code, 5, 2) = '10' or
                 substr(stvterm.stvterm_code, 5, 2) = '20')
          and stvterm.stvterm_fa_proc_yr = rpratrm.rpratrm_aidy_code
        )
        and (rpratrm.rpratrm_fund_code = '1SUBLN' or rpratrm.rpratrm_fund_code = '1UNSUB')
--        and rpratrm_pidm = 178958
--        and rpratrm_term_code = '201110'
      )sub_3
      where sub_3.rpratrm_paid_amt > 0
    )sub_4
    group by sub_4.rpratrm_pidm, sub_4.rpratrm_aidy_code
  )loan_fall_winter_paid_join on loan_fall_winter_paid_join.rpratrm_pidm = rorstat.rorstat_pidm
                              and loan_fall_winter_paid_join.rpratrm_aidy_code = rorstat.rorstat_aidy_code
  where rorstat.rorstat_aidy_code = aidy_code_in
--  and rorstat.rorstat_pidm = 210653
  ;

  type rorstat_table is table of rorstat_cur%rowtype index by binary_integer;
  rorstat_rec rorstat_table;

  cursor sd_cur(pidm_in number, term_code_in varchar2)
  is
  select
  um_student_data.umid,
  um_student_data.student_type_code,
  um_student_data.student_type_desc,
  um_student_data.primary_level_code,
  um_student_data.primary_level_desc,
  um_student_data.registered_ind,
  um_student_data.class_code,
  um_student_data.class_desc  
  from um_student_data
  where um_student_data.sd_pidm = pidm_in
  and um_student_data.sd_term_code = term_code_in;

  type sd_table is table of sd_cur%rowtype index by binary_integer;
  sd_rec sd_table;

  cursor min_term_less_cur(pidm_in number, term_code_in varchar2)
  is
  select
  um_student_data.umid,
  um_student_data.student_type_code,
  um_student_data.student_type_desc,
  um_student_data.primary_level_code,
  um_student_data.primary_level_desc,
  um_student_data.registered_ind,
  um_student_data.class_code,
  um_student_data.class_desc  
  from um_student_data
  where um_student_data.sd_pidm = pidm_in
  and um_student_data.sd_term_code = (select min(s1.sd_term_code)
                                      from um_student_data s1
                                      where s1.sd_pidm = um_student_data.sd_pidm
                                      and s1.sd_term_code >= term_code_in);

  cursor max_term_greater_cur(pidm_in number, term_code_in varchar2)
  is
  select
  um_student_data.umid,
  um_student_data.student_type_code,
  um_student_data.student_type_desc,
  um_student_data.primary_level_code,
  um_student_data.primary_level_desc,
  um_student_data.registered_ind,
  um_student_data.class_code,
  um_student_data.class_desc  
  from um_student_data
  where um_student_data.sd_pidm = pidm_in
  and um_student_data.sd_term_code = (select max(s1.sd_term_code)
                                      from um_student_data s1
                                      where s1.sd_pidm = um_student_data.sd_pidm
                                      and s1.sd_term_code <= term_code_in);

--  cursor sgbstdn_cur(pidm_in number, term_code_in varchar2)
--  is
--  select
--  spriden_join.spriden_id umid,
--  sgbstdn.sgbstdn_styp_code student_type_code,
--  (select stvstyp.stvstyp_desc from stvstyp_ext stvstyp where stvstyp.stvstyp_code = sgbstdn.sgbstdn_styp_code) student_type_desc,
--  sgbstdn.sgbstdn_levl_code primary_level_code,
--  (select stvlevl.stvlevl_desc from stvlevl_ext stvlevl where stvlevl.stvlevl_code = sgbstdn.sgbstdn_levl_code) primary_level_desc,
--  f_registered_this_term(pidm_in, term_code_in) registered_ind,
--  f_class_calc_fnc(pidm_in, sgbstdn.sgbstdn_levl_code, term_code_in) class_code,
--  (select stvclas.stvclas_desc from stvclas_ext stvclas where stvclas.stvclas_code = f_class_calc_fnc(pidm_in, sgbstdn.sgbstdn_levl_code, term_code_in)) class_desc
--  from sgbstdn_ext sgbstdn
--  inner join(
--    select
--    spriden.spriden_pidm,
--    spriden.spriden_id,
--    spriden.spriden_change_ind
--    from spriden_ext spriden
--  )spriden_join on spriden_join.spriden_pidm = sgbstdn.sgbstdn_pidm
--                and spriden_join.spriden_change_ind is null
--  where sgbstdn.sgbstdn_pidm = pidm_in
--  and sgbstdn.sgbstdn_term_code_eff = (select max(s1.sgbstdn_term_code_eff)
--                                       from sgbstdn_ext s1
--                                       where s1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm
--                                       and s1.sgbstdn_term_code_eff <= term_code_in);
--  
--  type sgbstdn_table is table of sgbstdn_cur%rowtype index by binary_integer;
--  sgbstdn_rec sgbstdn_table;

  begin
--    execute immediate('truncate table load_fa_student_data');

    open term_cur;
    fetch term_cur bulk collect into term_rec;
    close term_cur;

    for i in 1..term_rec.count
    loop
      open rorstat_cur(term_rec(i).aidy_code);
      fetch rorstat_cur bulk collect into rorstat_rec;
      close rorstat_cur;

      for j in 1..rorstat_rec.count
      loop

        if rorstat_rec(j).rrrareq_est_date is not null then
          spsu_appl_ind := 'Y';
        else
          spsu_appl_ind := 'N';
        end if;

        if rorstat_rec(j).rrrareq_est_date is null then
          spsu_appl_on_time_ind := 'N/A';
        elsif rorstat_rec(j).rrrareq_est_date <= rorstat_rec(j).cut_off_spsu_date then 
          spsu_appl_on_time_ind := 'Y';
        else
          spsu_appl_on_time_ind := 'N';
        end if;

        if rorstat_rec(j).rpratrm_term_code is not null then
          sd_term_code := rorstat_rec(j).rpratrm_term_code;
        else
          begin
            select min(stvterm.stvterm_code) into sd_term_code
            from stvterm_ext stvterm
            where stvterm.stvterm_fa_proc_yr = rorstat_rec(i).rorstat_aidy_code;
          end;
        end if;

        open sd_cur(rorstat_rec(j).rorstat_pidm, sd_term_code);
        fetch sd_cur bulk collect into sd_rec limit 1;
        close sd_cur;

        dbms_output.enable(null);
        dbms_output.put_line('pidm = ' || rorstat_rec(j).rorstat_pidm);
        dbms_output.put_line('term_code = ' || sd_term_code);

        if sd_rec.count < 1 then
          open min_term_less_cur(rorstat_rec(j).rorstat_pidm, sd_term_code);
          fetch min_term_less_cur bulk collect into sd_rec limit 1;
          close min_term_less_cur;

          if sd_rec.count < 1 then
            open max_term_greater_cur (rorstat_rec(j).rorstat_pidm, sd_term_code);
            fetch max_term_greater_cur bulk collect into sd_rec limit 1;
            close max_term_greater_cur;
          end if;
        end if;

--          open sgbstdn_cur(rorstat_rec(j).rorstat_pidm, sd_term_code);
--          fetch sgbstdn_cur bulk collect into sgbstdn_rec limit 1;
--          close sgbstdn_cur;

--          umid  := sgbstdn_rec(1).umid;
--          student_type_code := sgbstdn_rec(1).student_type_code;
--          student_type_desc := sgbstdn_rec(1).student_type_desc;
--          primary_level_code := sgbstdn_rec(1).primary_level_code;
--          primary_level_desc := sgbstdn_rec(1).primary_level_desc;
--          registered_ind := sgbstdn_rec(1).registered_ind;
--          class_code := sgbstdn_rec(1).class_code;
--          class_desc := sgbstdn_rec(1).class_desc;

        if sd_rec.count > 0 then
          umid  := sd_rec(1).umid;
          student_type_code := sd_rec(1).student_type_code;
          student_type_desc := sd_rec(1).student_type_desc;
          primary_level_code := sd_rec(1).primary_level_code;
          primary_level_desc := sd_rec(1).primary_level_desc;
          registered_ind := sd_rec(1).registered_ind;
          class_code := sd_rec(1).class_code;
          class_desc := sd_rec(1).class_desc;
        else
          umid := null;
          student_type_code := null;
          student_type_desc := null;
          primary_level_code := null;
          primary_level_desc := null;
          registered_ind := null;
          class_code := null;
          class_desc := null;
        end if;        

        if primary_level_code != 'UG' then
          restricted_to_loans_only := 'Y';
        elsif rorstat_rec(j).rcrapp1_degree_by_july = 1 then 
          restricted_to_loans_only := 'Y';
        elsif rorstat_rec(j).rcrapp3_grad_or_prof = 1 then
          restricted_to_loans_only := 'Y';
        elsif rorstat_rec(j).rcrapp3_degree_type = 2 then
          restricted_to_loans_only := 'Y';
        elsif rorstat_rec(j).rcrapp3_degree_type = 7 then 
          restricted_to_loans_only := 'Y';
        elsif rorstat_rec(j).rcrapp3_degree_type = 8 then 
          restricted_to_loans_only := 'Y';
        elsif rorstat_rec(j).rcrapp2_pell_pgi > rorstat_rec(j).efc_set_amt then 
          restricted_to_loans_only := 'Y';
        else
          degree_here_counter := 0;
          degree_else_counter := 0;

          begin
            select 
            count(shrdgmr.shrdgmr_pidm) into degree_here_counter 
            from shrdgmr_ext shrdgmr
            where shrdgmr_degs_code = 'AW'
            and shrdgmr.shrdgmr_levl_code like 'U%'
            and shrdgmr.shrdgmr_pidm = rorstat_rec(j).rorstat_pidm
            and shrdgmr.shrdgmr_term_code_grad < (select min(stvterm.stvterm_code) stvterm_code
                                                  from stvterm_ext stvterm
                                                  where stvterm.stvterm_fa_proc_yr = rorstat_rec(j).rorstat_aidy_code);
          end;

          begin
            select count(sordegr.sordegr_pidm) into degree_else_counter
            from sordegr_ext sordegr
            where sordegr.sordegr_degc_code like 'B%'
            and sordegr.sordegr_sbgi_code not in ('999999', '002327')
            and sordegr.sordegr_pidm = rorstat_rec(j).rorstat_pidm;
          end;

          if degree_here_counter > 0 or degree_else_counter > 0 then
            restricted_to_loans_only := 'Y';
          else 
           restricted_to_loans_only := 'N';
          end if;
        end if;

        max_loan_amount := 0;

        if (primary_level_code = 'GR' or primary_level_code = 'G2' or primary_level_code = 'G3' or primary_level_code = 'DR' or primary_level_code = 'D2' or primary_level_code = 'D3' or primary_level_code = 'PT') and
           rorstat_rec(j).rcrapp2_c_depend_status = 1 and class_code = 'GR' then 
          max_loan_amount := 20500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 1 and class_code = 'SR' then 
          max_loan_amount := 12500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 1 and class_code = 'JR' then 
          max_loan_amount := 12500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 1 and class_code = 'SO' then 
          max_loan_amount := 10500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 1 and class_code = 'FR' then 
          max_loan_amount := 9500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 2 and class_code = 'SR' then 
          max_loan_amount := 7500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 2 and class_code = 'JR' then
          max_loan_amount := 7500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 2 and class_code = 'SO' then 
          max_loan_amount := 6500;
        elsif (primary_level_code = 'UG' or primary_level_code = 'U2' or primary_level_code = 'U3'or primary_level_code = 'UT') and
              rorstat_rec(j).rcrapp2_c_depend_status = 2 and class_code = 'FR' then 
          max_loan_amount := 5500;
        else 
          max_loan_amount := null;
        end if;

        if restricted_to_loans_only = 'Y' then
          max_pell := 0;
        else
          max_pell := rorstat_rec(j).maxpell;
        end if;

        if rorstat_rec(j).rpratrm_total_orig_offer_amt < 1 and rorstat_rec(j).rpratrm_total_offer_amount < 1 then
          no_award_ind := 'Y';
        else
          no_award_ind := 'N';
        end if;

        if rorstat_rec(j).loan_fall_winter_offer_amt > 0 then
          loan_available_spsu := max_loan_amount - rorstat_rec(j).loan_fall_winter_offer_amt;
        else
          loan_available_spsu := max_loan_amount;
        end if;

        if (max_pell - nvl(rorstat_rec(j).pell_fall_winter_paid_amt , 0) + max_loan_amount - rorstat_rec(j).loan_fall_winter_paid_amt) < 1  then
          used_up_all_money_fawi_ind := 'Y';
        else
          used_up_all_money_fawi_ind := 'N';
        end if;

        begin
          insert into load_fa_student_data(    
          load_fa_student_data.pidm,
          load_fa_student_data.aidy_code,
          load_fa_student_data.term_code,
          load_fa_student_data.umid,
          load_fa_student_data.student_type_code,
          load_fa_student_data.student_type_desc,
          load_fa_student_data.level_code,
          load_fa_student_data.level_desc,
          load_fa_student_data.registered_ind,
          load_fa_student_data.class_standing,
          load_fa_student_data.class_desc,
          load_fa_student_data.term_desc,
          load_fa_student_data.fund_code,
          load_fa_student_data.fund_title,
          load_fa_student_data.fund_type,
          load_fa_student_data.fund_type_desc,
          load_fa_student_data.loan_ftyp_count_by_term,
          load_fa_student_data.schl_ftyp_count_by_term,
          load_fa_student_data.grnt_ftyp_count_by_term,
          load_fa_student_data.loan_awards_only_ind,
          load_fa_student_data.grant_awards_only_ind,
          load_fa_student_data.scholarship_awards_only_ind,
          load_fa_student_data.loan_and_grant_awards_only_ind,
          load_fa_student_data.no_awards_ind,
          load_fa_student_data.orig_offer_amt,
          load_fa_student_data.offer_amount,
          load_fa_student_data.paid_amount,
          load_fa_student_data.total_orig_offer_amt,
          load_fa_student_data.total_offer_amount,
          load_fa_student_data.total_paid_amount,
          load_fa_student_data.pell_pgi,
          load_fa_student_data.depend_status,
          load_fa_student_data.depend_status_desc,
          load_fa_student_data.sapr_code,
          load_fa_student_data.sapr_desc,
          load_fa_student_data.pell_leu,
          load_fa_student_data.pell_leu_limit_flag,
          load_fa_student_data.pell_leu_limit_flag_desc,
          load_fa_student_data.spsu_applied_date,
          load_fa_student_data.cut_off_spsu_date,
          load_fa_student_data.spsu_appl_ind,
          load_fa_student_data.spsu_appl_on_time_ind,
          load_fa_student_data.restricted_to_loans_only,
          load_fa_student_data.max_pell_amount,
          load_fa_student_data.pell_fall_winter_paid_amt,
          load_fa_student_data.pell_grant_availible_spsu,
          load_fa_student_data.max_loan_amount,
          load_fa_student_data.loan_fall_winter_paid_amt,
          load_fa_student_data.loan_fall_winter_offer_amt,
          load_fa_student_data.loan_available_spsu,
          load_fa_student_data.used_up_all_money_fawi_ind


          )values(
          rorstat_rec(j).rorstat_pidm,
          rorstat_rec(j).rorstat_aidy_code,
          rorstat_rec(j).rpratrm_term_code,
          umid,
          student_type_code,
          student_type_desc,
          primary_level_code,
          primary_level_desc,
          registered_ind,
          class_code,
          class_desc,
          (select stvterm.stvterm_desc from stvterm_ext stvterm where stvterm.stvterm_code = rorstat_rec(j).rpratrm_term_code),
          rorstat_rec(j).rpratrm_fund_code,
          rorstat_rec(j).rfrbase_fund_title,
          rorstat_rec(j).rfrbase_ftyp_code,
          (select rtvftyp.rtvftyp_desc from rtvftyp_ext rtvftyp where rtvftyp.rtvftyp_code = rorstat_rec(j).rfrbase_ftyp_code),
          rorstat_rec(j).loan_ftyp_count_by_term,
          rorstat_rec(j).schl_ftyp_count_by_term,
          rorstat_rec(j).grnt_ftyp_count_by_term,
          rorstat_rec(j).loan_awards_only_ind,
          rorstat_rec(j).grant_awards_only_ind,
          rorstat_rec(j).scholarship_awards_only_ind,
          rorstat_rec(j).loan_and_grant_awards_only_ind,
          no_award_ind,
          nvl(rorstat_rec(j).rpratrm_orig_offer_amt, 0),
          nvl(rorstat_rec(j).rpratrm_offer_amt, 0),
          nvl(rorstat_rec(j).rpratrm_paid_amt, 0),
          nvl(rorstat_rec(j).rpratrm_total_orig_offer_amt, 0),
          nvl(rorstat_rec(j).rpratrm_total_offer_amount, 0),
          nvl(rorstat_rec(j).rpratrm_total_paid_amount, 0),
          rorstat_rec(j).rcrapp2_pell_pgi,
          rorstat_rec(j).rcrapp2_c_depend_status,
          decode(rorstat_rec(j).rcrapp2_c_depend_status, 1, 'Independent', 2, 'Dependent'),
          rorstat_rec(j).rorsapr_sapr_code,
          (select rtvsapr.rtvsapr_desc from rtvsapr_ext rtvsapr where rtvsapr.rtvsapr_code = rorstat_rec(j).rorsapr_sapr_code),
          (rorstat_rec(j).rcrlds4_pell_leu * 100),
          rorstat_rec(j).rcrlds4_pell_leu_limit_flag,
          decode(rorstat_rec(j).rcrlds4_pell_leu_limit_flag, 'C', 'Close to Pell limit', 'E', 'Met or Exceeded Pell limit', 'H', 'High Pell Percent', 'N', 'No Problem'),
          rorstat_rec(j).rrrareq_est_date,
          rorstat_rec(j).cut_off_spsu_date,
          spsu_appl_ind,
          spsu_appl_on_time_ind,
          restricted_to_loans_only,
          max_pell,
          rorstat_rec(j).pell_fall_winter_paid_amt,
          (rorstat_rec(j).maxpell - nvl(rorstat_rec(j).pell_fall_winter_paid_amt , 0)),
          max_loan_amount,
          rorstat_rec(j).loan_fall_winter_paid_amt,
          rorstat_rec(j).loan_fall_winter_offer_amt,
          loan_available_spsu,
          used_up_all_money_fawi_ind
          );

        end;

      end loop;

      commit;
    end loop;


  end p_trans_fa_student_data;

end zdfstdd;
/
