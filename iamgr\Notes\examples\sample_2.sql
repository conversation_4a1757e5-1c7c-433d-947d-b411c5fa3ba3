with pop_sel as(
select
um_student_data.umid,
um_student_data.registered_ind,
um_student_data.student_status_code,
um_student_data.student_status_desc,
demo_join.gender,
case
  when test_join.act_composite >= '18' then '18+'
  when test_join.act_composite < '18' and test_join.act_composite >= '0' then '18-'
    else 'N'
end act_ind
from um_student_data
inner join(
select *
from um_test_score
)test_join on test_join.pidm = um_student_data.sd_pidm
inner join(
select
um_demographic.dm_pidm,
um_demographic.gender
from um_demographic
)demo_join on demo_join.dm_pidm = um_student_data.sd_pidm
where um_student_data.sd_term_code = '201510'
and um_student_data.student_status_code = 'AS'
)
select 
pop_sel.registered_ind,
pop_sel.student_status_code,
pop_sel.act_ind,
count(*) counter
from pop_sel
group by pop_sel.registered_ind,
         pop_sel.student_status_code,
         pop_sel.act_ind
order by counter desc

--inner and left outer example

select
 um_demographic.last_name,
 um_demographic.umid,
 sd_join.primary_level_code,
 sd_join.primary_major_1,
 sd_join.primary_major_1_desc,
 degree_join.degree_status_desc,
 degree_join.grad_status_desc
 
from
 um_demographic
 inner join(
  select *
  from um_student_data
  )sd_join on sd_join.sd_pidm = um_demographic.dm_pidm
           and sd_join.sd_term_code = '201420'
           and sd_join.registered_ind = 'Y'
           and (sd_join.primary_major_1 = 'CSC' or
                sd_join.primary_major_1 = 'CIS')
 left outer join(
  select *
  from um_degree
 )degree_join on degree_join.pidm = um_demographic.dm_pidm
where
 um_demographic.last_name like 'H%'
 order by um_demographic.last_name;