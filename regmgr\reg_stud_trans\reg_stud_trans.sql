CREATE OR REPLACE VIEW reg_stud_trans AS
SELECT --84
 st.umid,
 st.pidm,
 st.term_desc,
 st.term,
 st.first_name
 || ' '
 || st.middle_initial
 || ' '
 || st.last_name
 || ' '
 || st.name_suffix                                 stud_name,
 dm.a1_street_line1                                street,
 dm.a1_city                                        city,
 dm.a1_state_code                                  state,
 dm.a1_zip                                         zip,
 decode(st.levl, 'UG', '1', 'U2', '2',
        'U3', '3', 'GR', '4', 'G2',
        '5', 'DR', '6', '7')                       levl_order,
 decode(st.levl, 'UG', 'UG', 'U2', 'UG',
        'U3', 'UG', 'GR', 'GR', 'G2',
        'GR', 'DR')                                levl_rpt,
 st.levl,
 st.levl_desc,
 st.subject
 || ' '
 || st.course                                      course,
 st.title,
 st.grade,
 st.grade_quality_points,
 st.grade_numeric_value,
 st.type,
 st.type_desc,
 st.hours,
 st.attribute_1
 || ' '
 || st.attribute_2
 || ' '
 || st.attribute_3
 || st.attribute_4                                 attr,
 st.repeat_ind,
 st.transfer_institution,
 st.transfer_subject ||' '|| st.transfer_course transfer_course,
 st.transfer_hours,
 st.transfer_grade,
 st.completed_ind,
 
 ahs.astd_desc_most_recent                         acad_desc,
 ahs.inst_lgpa_hours_earned                        inst_hours_earned,
 ahs.transfer_lgpa_hours_earned                    trans_hours_earned,
 ahs.overall_lgpa_hours_earned                     overall_hours_earned,
 ahs.overall_lgpa_gpa_hours                        overall_gpa_hours,
 ahs.overall_lgpa_quality_points                   overall_points,
 ahs.inst_lgpa_gpa                                 overall_gpa,
 
 /*****************************************************
 Degree 1
 *****************************************************/  
 ahs.DEGREE_SEQ_NO1,  
 CAST(ahs.DEGREE_GRADUATION_DATE1 AS VARCHAR2(10))DEGREE_GRADUATION_DATE1,
 ahs.degc_code1,
 ahs.degc_desc1,
 ahs.degs_code1,
 ahs.degs_desc1,
 ahs.LEVL_CODE_DEGREE1,                       
 ahs.LEVL_DESC_DEGREE1,                     
 ahs.MAJR_CODE1_DEGREE1,                     
 ahs.MAJR_DESC1_DEGREE1,                    
 ahs.MAJR_CODE_MINOR1_DEGREE1,               
 ahs.MAJR_DESC_MINOR1_DEGREE1,                  
 ahs.MAJR_CODE_MINOR1_2_DEGREE1,             
 ahs.MAJR_DESC_MINOR1_2_DEGREE1,            
 ahs.MAJR_CODE1_2_DEGREE1,                   
 ahs.MAJR_DESC1_2_DEGREE1,  
 
 /*****************************************************
 Degree 2
 *****************************************************/      
 ahs.DEGREE_SEQ_NO2,
 CAST(ahs.DEGREE_GRADUATION_DATE2 AS VARCHAR2(10)) DEGREE_GRADUATION_DATE2,
 ahs.degc_code2,
 ahs.degc_desc2,
 ahs.degs_code2,
 ahs.degs_desc2,
 ahs.LEVL_CODE_DEGREE2,                       
 ahs.LEVL_DESC_DEGREE2,                     
 ahs.MAJR_CODE1_DEGREE2,                     
 ahs.MAJR_DESC1_DEGREE2,                   
 ahs.MAJR_CODE_MINOR1_DEGREE2,               
 ahs.MAJR_DESC_MINOR1_DEGREE2,                    
 ahs.MAJR_CODE_MINOR1_2_DEGREE2,             
 ahs.MAJR_DESC_MINOR1_2_DEGREE2,            
 ahs.MAJR_CODE1_2_DEGREE2,                   
 ahs.MAJR_DESC1_2_DEGREE2,  

 /*****************************************************
 Degree 3
 *****************************************************/  
 ahs.DEGREE_SEQ_NO3,    
 CAST(ahs.DEGREE_GRADUATION_DATE3 AS VARCHAR2(10)) DEGREE_GRADUATION_DATE3,
 ahs.degc_code3,
 ahs.degc_desc3,
 ahs.degs_code3,
 ahs.degs_desc3,
 ahs.LEVL_CODE_DEGREE3,                       
 ahs.LEVL_DESC_DEGREE3,                     
 ahs.MAJR_CODE1_DEGREE3,                     
 ahs.MAJR_DESC1_DEGREE3,                    
 ahs.MAJR_CODE_MINOR1_DEGREE3,               
 ahs.MAJR_DESC_MINOR1_DEGREE3,                   
 ahs.MAJR_CODE_MINOR1_2_DEGREE3,             
 ahs.MAJR_DESC_MINOR1_2_DEGREE3,            
 ahs.MAJR_CODE1_2_DEGREE3,                   
 ahs.MAJR_DESC1_2_DEGREE3,  
 
-- ROW_NUMBER()
-- OVER(PARTITION BY st.pidm, ahs.degc_code1
--      ORDER BY
--       term DESC
-- )                                                 max_degc

 ROW_NUMBER()
 OVER(PARTITION BY st.pidm, ahs.degree_seq_no1
      ORDER BY
       term DESC
 )                                                 max_degc
FROM
 um_student_transcript st
 INNER JOIN um_demographic                      dm ON st.pidm = dm.dm_pidm
 INNER JOIN aimsmgr.as_academic_history_summary ahs ON st.pidm = ahs.pidm_key
                                                       AND st.levl = ahs.levl_code_key
--WHERE
-- st.umid = '12805441'
-- st.pidm in ('126975','73169')
-- st.pidm = '91280'
-- st.pidm = '91280'
--st.pidm = '83'
;
--SELECT
-- *
--FROM
-- reg_stud_trans_mv
--WHERE
-- pidm = '5466';

--GRANT SELECT ON shrttrm_ext TO iamgr;
--
--DESC aimsmgr.stvastd_ext;
--
--DESC um_student_transcript;

--DESC aimsmgr.as_academic_history_summary;
--
--select * from um_student_transcript where pidm = '126975';
--grant select on aimsmgr.as_academic_history_summary to regmgr;

--SELECT
-- *
--FROM
-- aimsmgr.as_academic_history_summary
--WHERE
-- pidm_key = '126975';
-- 
-- select * from um_degree
-- where pidm = '5466';

--select dm_pidm from AIMSMGR.um_demographic where umid = '06683838';

--DESC reg_stud_trans;
--DESC reg_stud_trans_mv;
--DESC um_student_transcript;