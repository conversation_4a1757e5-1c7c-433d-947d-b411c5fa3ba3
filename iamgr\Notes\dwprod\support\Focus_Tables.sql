/*

tnsnames:
dwprod.world=(DESCRIPTION=
   (SOURCE_ROUTE=yes)
   (ADDRESS_LIST=
     (FAILOVER=on)
     (LOAD_BALANCE=on)
     (ADDRESS=(PROTOCOL=tcp)(HOST=oraconnp1.dsc.umich.edu)(PORT=30421))
     (ADDRESS=(PROTOCOL=tcp)(HOST=oraconnp2.dsc.umich.edu)(port=30421)))
   (ADDRESS=(PROTOCOL=tcp)(HOST=prd1-scan.dsc.umich.edu)(PORT=1521))
   (CONNECT_DATA=(SERVICE_NAME=dw_dwprod)))


HR DataMart

SELECT 
  
  M_HRDW1.PERSONAL_DATA,
  M_HRDW1.JOB,
  M_HRDW1.PERSONAL_DEMOGRAPHICS,
  M_HRDW1.ETHNICITY_DTL,
  M_HRDW1.DEPT_BUDGET_ERN

Finance DataMart
 --Bal_Sheet
 SELECT
  M_GLDW1.BAL_SHEET_ACT_LEDGER.ACCOUNT,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.FUND_CODE,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.DEPTID,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.PROGRAM_CODE,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.FISCAL_YEAR,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.ACCOUNTING_PERIOD,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.DOLLAR_AMOUNT,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.CLASS,
  M_GLDW1.BAL_SHEET_ACT_LEDGER.PROJECT_GRANT
FROM
  M_GLDW1.BAL_SHEET_ACT_LEDGER

--Budget_JRNL_HDR
 SELECT
  M_GLDW1.BUDGET_JRNL_HDR.BUD_JOURNAL_ID,
  M_GLDW1.BUDGET_JRNL_HDR.BUD_JOURNAL_DATE,
  M_GLDW1.BUDGET_JRNL_HDR.LEDGER_GROUP,
  M_GLDW1.BUDGET_JRNL_HDR.BUDG_TRANS_TYPE,
  M_GLDW1.BUDGET_JRNL_HDR.BUDG_TRANS_TYPE_DESCR,
  M_GLDW1.BUDGET_JRNL_HDR.POSTED_DATE,
  M_GLDW1.BUDGET_JRNL_HDR.BUD_JOURNAL_DESCR,
  M_GLDW1.BUDGET_JRNL_HDR.JOURNAL_ENTERED_OPRID
FROM
  M_GLDW1.BUDGET_JRNL_HDR

--BUDGET_JRNL_LN
 SELECT
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_ID,
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_DATE,
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_LINE,
  M_GLDW1.BUDGET_JRNL_LN.LEDGER_GROUP,
  M_GLDW1.BUDGET_JRNL_LN.ACCOUNT,
  M_GLDW1.BUDGET_JRNL_LN.FUND_CODE,
  M_GLDW1.BUDGET_JRNL_LN.DEPTID,
  M_GLDW1.BUDGET_JRNL_LN.PROGRAM_CODE,
  M_GLDW1.BUDGET_JRNL_LN.BUDGET_PERIOD,
  M_GLDW1.BUDGET_JRNL_LN.BUD_JRNL_LN_MONETARY_AMOUNT,
  M_GLDW1.BUDGET_JRNL_LN.BUD_JRNL_LN_DESCR
FROM
  M_GLDW1.BUDGET_JRNL_LN

--JRNL_LN
SELECT
  M_GLDW1.JRNL_LN.JOURNAL_ID,
  M_GLDW1.JRNL_LN.JOURNAL_DATE,
  M_GLDW1.JRNL_LN.JOURNAL_LINE,
  M_GLDW1.JRNL_LN.ACCOUNT,
  M_GLDW1.JRNL_LN.FUND_CODE,
  M_GLDW1.JRNL_LN.DEPTID,
  M_GLDW1.JRNL_LN.PROGRAM_CODE,
  M_GLDW1.JRNL_LN.CLASS,
  M_GLDW1.JRNL_LN.PROJECT_GRANT,
  M_GLDW1.JRNL_LN.JRNL_LN_REF,
  M_GLDW1.JRNL_LN.DOLLAR_AMOUNT,
  M_GLDW1.JRNL_LN.JOURNAL_LINE_DESCR
FROM
  M_GLDW1.JRNL_LN
 
--REV_EXP_ACT_LEDGER
SELECT
  M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNT,
  M_GLDW1.REV_EXP_ACT_LEDGER.FUND_CODE,
  M_GLDW1.REV_EXP_ACT_LEDGER.DEPTID,
  M_GLDW1.REV_EXP_ACT_LEDGER.PROGRAM_CODE,
  M_GLDW1.REV_EXP_ACT_LEDGER.CLASS,
  M_GLDW1.REV_EXP_ACT_LEDGER.PROJECT_GRANT,
  M_GLDW1.REV_EXP_ACT_LEDGER.FISCAL_YEAR,
  M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNTING_PERIOD,
  M_GLDW1.REV_EXP_ACT_LEDGER.DOLLAR_AMOUNT
FROM
  M_GLDW1.REV_EXP_ACT_LEDGER

  --CURR_ACCOUNT
SELECT
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFFDT,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_EFF_STATUS,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_DESCR,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_TYPE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE,
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_GRP_SRC_USE_DESCR
FROM
  M_GLDW1.CURR_ACCOUNT_VW


--REV_EX_ACT_LEDGER_CURR_FUND
SELECT
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_CODE,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_EFFDT,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_EFF_STATUS,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_DESCR,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_GRP,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_GRP_DESCR,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_DETAIL_GRP,
  REV_EX_ACT_LEDGER_CURR_FUND_VW.FUND_DETAIL_GRP_DESCR
FROM
  M_GLDW1.CURR_FUND_VW  REV_EX_ACT_LEDGER_CURR_FUND_VW

--REV_EXP_ACT_CURR_FN_DEPT

SELECT
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPTID,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_EFFDT,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_EFF_STATUS,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_DESCR,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP_DESCR,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP_VP_AREA_DESCR,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS,
  REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS_DESCR
FROM
  M_GLDW1.CURR_FN_DEPT_VW  REV_EXP_ACT_CURR_FN_DEPT_VW

--CURR_PROGRAM

SELECT
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_CODE,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFFDT,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_EFF_STATUS,
  M_GLDW1.CURR_PROGRAM_VW.PROGRAM_DESCR
FROM
  M_GLDW1.CURR_PROGRAM_VW

--REV_EXP_ACT_CURR_CLASS_CF

SELECT
  REV_EXP_ACT_CURR_CLASS_CF_VW.CLASS,
  REV_EXP_ACT_CURR_CLASS_CF_VW.CLASS_EFFDT,
  REV_EXP_ACT_CURR_CLASS_CF_VW.CLASS_EFF_STATUS,
  REV_EXP_ACT_CURR_CLASS_CF_VW.CLASS_DESCR,
  REV_EXP_ACT_CURR_CLASS_CF_VW.CLASS_GRP,
  REV_EXP_ACT_CURR_CLASS_CF_VW.CLASS_GRP_DESCR
FROM
  M_GLDW1.CURR_CLASS_CF_VW  REV_EXP_ACT_CURR_CLASS_CF_VW

--CURR_PROJECT_GRANT
  
SELECT
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFFDT,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_EFF_STATUS,
  M_GLDW1.CURR_PROJECT_GRANT_VW.PROJECT_GRANT_TITLE
FROM
  M_GLDW1.CURR_PROJECT_GRANT_VW

--REV_EXP_LED_ENDOW_DIST2

SELECT
  REV_EXP_LED_ENDOW_DIST2.FUND_CODE,
  REV_EXP_LED_ENDOW_DIST2.DEPTID,
  REV_EXP_LED_ENDOW_DIST2.CLASS,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_EFFDT,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_LN,
  REV_EXP_LED_ENDOW_DIST2.ENDOWMENT_EFF_STATUS,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_FUND_CODE,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_DEPTID,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_CLASS,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_PROGRAM_CODE,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_PROJECT_GRANT,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_IND,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_IND_DESCRSHORT,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_PCT,
  REV_EXP_LED_ENDOW_DIST2.ENDW_DIST_AMT
FROM
  M_GLDW1.ENDOW_DIST  REV_EXP_LED_ENDOW_DIST2

*/
