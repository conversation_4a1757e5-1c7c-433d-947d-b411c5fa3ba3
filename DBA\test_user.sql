/*******************************************************************************
New Schema and Datamart Grants: testuser (725 grants total)
*******************************************************************************/
BEGIN
EXECUTE IMMEDIATE 'CREATE USER testuser IDENTIFIED BY testpass'; --creates user and sets password
EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO testuser'; --grants user permissions to create table
EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO testuser'; --grants user permissions to create view
EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO testuser'; --allows unimited tablespaces on all tablespaces
EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO testuser'; --allows account to connect to the database
EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO testuser'; --grants user permissions to create proceedure
EXECUTE IMMEDIATE 'ALTER USER testuser DEFAULT ROLE ALL'; --grants user all rolls
EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO testuser'; --grats user reat write object privileges and allows user to grant priveleges to other users
EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO testuser'; --grants user permissions to create MV
EXECUTE IMMEDIATE 'GRANT CREATE JOB TO testuser'; --grants user permissions to create job
EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO testuser'; --grants user create, alter, drop job classe.
EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO testuser'; --grants user permissions to create dblink
P_DM_DBA_GRANT_SELECT_TABLE ('testuser'); -- proceedure to grant select on DM tables
P_DM_DBA_GRANT_SELECT_TABLE_EXT ('testuser'); -- proceedure to grant select on DM table extracts
P_DM_DBA_GRANT_SELECT_VIEW ('testuser'); -- proceedure to grant select on DM views
END;
/
/*******************************************************************************
Remove schema: testuser
*******************************************************************************/
drop user testuser;

/*******************************************************************************
Table and View queries used in proceedures 
*******************************************************************************/
--list DM tables: P_DM_DBA_GRANT_SELECT_TABLE (70 tables)
SELECT
  table_name
 FROM
  all_tables
 WHERE
   owner = 'AIMSMGR'
  AND ( table_name LIKE 'TD_%'
        OR table_name LIKE 'UM\_%'  escape '\');
        
--list DM extracts: P_DM_DBA_GRANT_SELECT_TABLE_EXT (188 tables)
SELECT
  table_name
 FROM
  all_tables
 WHERE
   owner = 'AIMSMGR'
  AND ( table_name LIKE '%\_EXT' escape '\')
  AND ( table_name NOT LIKE 'R%\_EXT' escape '\');

--list DM views: P_DM_DBA_GRANT_SELECT_TABLE_EXT  (455 views)
SELECT
view_name
FROM
 all_views
WHERE
owner LIKE 'AIMSMGR'
and (view_name like 'GER%'
OR view_name like 'GJ%'
OR view_name like 'GL%'
OR view_name like 'GT%'
OR view_name like 'GU%'
OR view_name like 'SA%'
OR view_name like 'SB%'
OR view_name like 'SC%'
OR view_name like 'SF%'
OR view_name like 'SG%'
OR view_name like 'SH%'
OR view_name like 'SI%'
OR view_name like 'SL%'
OR view_name like 'SM%'
OR view_name like 'SO%'
OR view_name like 'SR%'
OR view_name like 'SS%'
OR view_name like 'ST%'
OR view_name like 'TB%'
OR view_name like 'TG%'
OR view_name like 'TT%'
OR view_name like 'SS%')
;  