with pop_sel as(
    select
    sd.sd_pidm,
    sd.sd_term_code,
    ts.hours crs_credit_hours,
    case 
        when ts.shrgrde_gpa_ind = 'Y' then ts.hours
        else 0
    end crs_gpa_hrs,
    case
        when ts.shrgrde_gpa_ind = 'Y' then (ts.hours * ts.shrgrde_quality_points)
        else 0
    end crs_quality_points
    from um_student_data sd
    inner join (
        select 
        ts.*,
        shrgrde.*
        from um_student_transcript ts
        inner join aimsmgr.shrgrde on shrgrde.shrgrde_code = ts.grade
                           and shrgrde.shrgrde_levl_code = ts.levl
    )ts on ts.pidm = sd.sd_pidm
                           and ts.levl = sd.primary_level_code
                           and ts.type = 'I'
                           and ts.subject = '&subject'--'ECN'
                           and ts.grade not in ('YW', 'Y', '*', 'I', 'W', 'IW')
                           and (ts.repeat_ind is null or ts.repeat_ind in ('I', 'A'))
    
    where sd.sd_term_code = '&Current_Term_Code'
    and sd.report_level_code = 'UG'
    and sd.registered_ind = 'Y'
    and sd.overall_gpa >= 3.0
--    and sd.overall_hours_earned >= 12
)
, pop_sel_1 as(
    select
    pop_sel.sd_pidm,
    pop_sel.sd_term_code,
    sum(pop_sel.crs_credit_hours) crs_credit_hours,
    sum(pop_sel.crs_gpa_hrs) crs_gpa_hrs,
    sum(pop_sel.crs_quality_points) crs_quality_points
    from pop_sel

    group by
    pop_sel.sd_pidm,
    pop_sel.sd_term_code
)
, pop_sel_2 as(
    select
    pop_sel_1.sd_pidm,
    pop_sel_1.sd_term_code,
    pop_sel_1.crs_credit_hours,
    pop_sel_1.crs_gpa_hrs,
    pop_sel_1.crs_quality_points,
    case
        when pop_sel_1.crs_gpa_hrs != 0 then trunc((pop_sel_1.crs_quality_points / pop_sel_1.crs_gpa_hrs), 2) 
        else 0
    end crs_gpa 
    from pop_sel_1
)
select
dm.umid,
dm.last_name,
dm.first_name,
dm.name_suffix,
dm.a1_street_line1,
dm.a1_street_line2,
dm.a1_city,
dm.a1_state_code,
dm.a1_zip,
dm.a1_area_code,
dm.a1_phone_number,
dm.ca_email,
sd2.primary_level_code,
sd2.primary_major_1,
sd2.primary_major_1_desc,
sd2.primary_major_2,
sd2.primary_major_2_desc,
sd2.overall_hours_earned,
sd2.overall_gpa,
pop_sel_2.crs_credit_hours,
pop_sel_2.crs_gpa_hrs,
pop_sel_2.crs_gpa
from pop_sel_2
inner join um_student_data sd2 on sd2.sd_pidm = pop_sel_2.sd_pidm
                           and sd2.sd_term_code = pop_sel_2.sd_term_code
inner join um_demographic dm on dm.dm_pidm = pop_sel_2.sd_pidm
                          and dm.deceased_ind is null
where pop_sel_2.crs_credit_hours >= &Required_crs_credit_hours 
and pop_sel_2.crs_gpa >= &Requred_Min_crs_GPA 

order by 
last_name,
first_name
;