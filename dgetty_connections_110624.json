{"connections": [{"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "p1qTRbCEeJYwK+FaYhvmt2d8FIAexsAlmSF6TQqDnt6JHUobrEY93/0m", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "recmgr", "ExportKeyChecksum": "N+g8ps+029nWvDFVq42jd0aVtJUHKbukQPUKfD6N2VHGc9Zl"}, "name": "IAPROD_recmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "XLM1bXl9o2JYxoB+j2S2lODjckKefR963WxXx2pGSQU0xkE=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "grmgr", "ExportKeyChecksum": "oTpYtzzHBtjgfwEJFWGr62kq3mfv+7glFRgI87ykmEMlHav2"}, "name": "IAPROD_grmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "xe3EvSg92SiewXoT76vMs1XJhwgfIz69l41FyNIiDXPBl7ACsA==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "regmgr", "ExportKeyChecksum": "B5uHaLNp2G3Q1Afj7HH5vDcUxHnaqmoUug7e9UqQcdQ0+scA"}, "name": "IAPROD_regmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "QY6oqBpmXxubhtetfx1OTJyXHG4Jun4zJzqHviyZX83iRJG9J+GfRLc7", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "chsmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "quXzfDSnXxKEiTz8Vj3UR3IM4GrLYTnCPvd3iGkXbp7azROD"}, "name": "IAPROD_chsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "Ag1jlCZe+7JQtF0ryg1fe29lHPxFyeWgZwrxV0N+TBpNGivlF8QhbA==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "famgr", "ExportKeyChecksum": "pQjNtj9Md+t7gZM9/jgfLGR2syBqj4JfvIW/5MoqGxm0/Vef"}, "name": "IAPROD_famgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "qz5OAwLkSjVrsIB+gJpl3NNP5IZpsaRpX8WtRRGAxt53IkwV", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "ugmgr", "ExportKeyChecksum": "TB6j505mG+8Wde1OaM/yEBDQ5zSVlD8GBA5GUyrcOA/S66VL"}, "name": "IAPROD_ugmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "TNS", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "ExportPasswordMode": "Key", "customUrl": "PROD", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "password": "zRogMszb9eskncAQVh35KPSAYZHGx1IseA9D0jfOvHFoJYCcjpRqIuseYCE=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "user": "webfocus", "PROXY_USER_NAME": "", "ExportKeyChecksum": "hdmhSNbt0MsoMlhGi3VAWHNm2szaRO2P82n1kYGBovulUEhG"}, "name": "PROD_webfocus", "type": "jdbc"}, {"info": {"customUrl": "************************************************/", "hostname": "***************/postgres?", "password": "J8yc8i6wNdIFYEsK5G//XDR7DXMgFoB/1QA1zkce5QdnwCOy8Q5GUw==", "driver": "org.postgresql.Driver", "subtype": "SDPostgreSQL", "port": "5432", "SavePassword": "true", "RaptorConnectionType": "SDPostgreSQL", "user": "postgres", "ExportPasswordMode": "Key", "ExportKeyChecksum": "<PERSON><PERSON><PERSON><PERSON>"}, "name": "dw1.postres.postgres", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "h0BJjbXGo2sIEzaC82zklcAgWyU2Jg4fmDeKt5j+Glm7zecE8W8NT/PW", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "system", "ExportKeyChecksum": "EXdHn1SuS+PXtQcaMX2l/n9qybxqrNxNM9O5cGOdAkRQ63fs"}, "name": "IAPROD_system", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "VyLlg3MXiDI7zQQuP+cirhOf5xaw6iOhsUHL4tZdO6cSUxqi", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "sscmgr", "ExportKeyChecksum": "rrJOjyDtpQGFtlC3lYgwcZlZh3HMpXpddQwAiyeYlt+w7DLS"}, "name": "IAPROD_sscmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "IbJELxqg0wWMXjtrAX8c3il2Ne1mOGW0euZaLYNPhLQHsROrX4GwoUm2D0Vt", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "oelmgr", "ExportKeyChecksum": "AeiX1lTefliTg9MlqdfU1hwwz7fhZ1N5wtDX/EUdlyXpn7sf"}, "name": "IAPROD_oelmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "serviceName": "IAPROD", "Connection-Color-For-Editors": "-16776961", "ExportPasswordMode": "Key", "customUrl": "***********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "heP/71uQtD1dX2hXtt3N61lDG/De9//VAGZAcIwkng1w5yA=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "canvasmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "h14y93UJHNjjl5V6dBLgfjCqJc5teLngBIq1pRPJ5mPpz6AD"}, "name": "2_IAPROD_canvasmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "Connection-Color-For-Editors": "-16776961", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "sRTCWO1u5G5t9u8rgJ70SUadRjlieRC7i6NEzPc6B4oXQhxKH4a9YA/T", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "contmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "DxegoLqYw6N9WEupqL/g0rUEjcMU180/8TY5IxcKsNFrp1D+"}, "name": "2_IAPROD_contmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "5Gj8RpdUeyqUTj4UBdG2lqzUwT4mD0faOtJkhTrIqU4RPws=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "webfocus", "ExportKeyChecksum": "CovnrXCPHLoKBRdkSSxD3awV6K0UljkfiXJctTTfgZPnbgzF"}, "name": "IAPROD_webfocus", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "pXm25JX+PlJIVSgBo/ZnykliMsys8F0bN06J9jvAci3LS6AWUWfRoGCd", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "targetxmgr", "ExportKeyChecksum": "79pHA1bXJOmD5lID2nTtOEjyD9iPe/8Hh3Pk3Y8H3sliMIdu"}, "name": "iaprod_targetxmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "qAVLJZiPTCsRatqI50RtqH2VFnqMbKY4raM7V1s4WZnmJ/5kiwQBRRlK", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "silmgr", "ExportKeyChecksum": "SLM1eIl9C5vpUstW4uEvZw29/XNuhUPdea95mQPX4L5FePCr"}, "name": "IAPROD_silmgr", "type": "jdbc"}, {"info": {"customUrl": "***********************************************/", "hostname": "***************/testmgr?", "password": "KZp6KeSnORP8uxm/5HgjvhVFnHBeL8AUrGkjX+nsBP6s6yFt", "driver": "org.postgresql.Driver", "subtype": "SDPostgreSQL", "port": "5432", "SavePassword": "true", "RaptorConnectionType": "SDPostgreSQL", "user": "testmgr", "ExportPasswordMode": "Key", "ExportKeyChecksum": "<PERSON><PERSON><PERSON><PERSON>"}, "name": "dw1.testmgr.testmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "RDRDEJ9FXQJkptheDG/P55aUGl7CsbC/mDRvg74MxqTNEHKuSukVxg2y", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "cepmgr", "ExportKeyChecksum": "la3hbRI+2uBVljHA0lM7N0cNPtWOuMHouqN0ZofyVTsVDwX4"}, "name": "IAPROD_cepmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "8d7RMsgvZbhjhAzdxVzCl/KQClWUUJgDXnMF7Yb5OaM+s4E1p2mdeNWv", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "salesforcemgr", "ExportKeyChecksum": "PoDLhKCVa2/vCIG4gx3r1bRVKj2VFKaoAlU9+4A3Kzsx+o/8"}, "name": "IAPROD_salesforcemgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "Connection-Color-For-Editors": "-16776961", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "AAMow/LPp8PQz2fh2vpaXqDg5bMb4arV05k74VXhQaL5BQ==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "iamgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "jEMeV/wGWPtyb1dAZ3upvgOvw5WhrQsVq3PRSW3oLfrW2fyk"}, "name": "1_IAPROD_iamgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "uGGZ8u6TtDhhC12QjXsXVy0YG9ySTRnXA8p50+sVmN0DXBLzsnXRIjVF", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "npsasmgr", "ExportKeyChecksum": "AbBuIahcXvgxppUaQtcrG3UuXjBlaIOuHlBO05BolgO9RLZj"}, "name": "IAPROD_npsasmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "8uxOt6R8JirpsqMKl3o5UX3KI831KLWCRm68O9PMg3FnZmqW/WCb+icv", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "talendmgr", "ExportKeyChecksum": "cWWRwrhSKer4AVlb8HMvhopvEC2+W2pWFcugI62YGaA4K27D"}, "name": "IAPROD_talendmgr", "type": "jdbc"}, {"info": {"customUrl": "jdbc:postgresql://***************/dm1?:5432/", "hostname": "***************/dm1?", "password": "eDU0qxVaIfxoaVu9Gga20SY1/4Uop6kgB0WdsgA8M10mSQ77Aa8Vew==", "driver": "org.postgresql.Driver", "subtype": "SDPostgreSQL", "port": "5432", "SavePassword": "true", "RaptorConnectionType": "SDPostgreSQL", "user": "d<PERSON>ty", "ExportPasswordMode": "Key", "ExportKeyChecksum": "<PERSON><PERSON><PERSON><PERSON>"}, "name": "dw1.dm1.dgetty", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "MCPZ1caY+m6mXq/G8xXTMP88BKuGy8IS9V5EaLJxu4dwOl8vH5Z4iUQ/", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "targetxmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "FYvqDB0SW+JCz2zj0X1fjEJN8cHhJDntQcD8gx9ym/3gi4oX"}, "name": "IAPROD_targetxmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "zr4qjhhHcAZltDhMELwWsh7AaHdnZb3ibzqWxmXxevhXRD4kIDYyftYq", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "nscmgr", "ExportKeyChecksum": "6vTacKJsXvy7gG4pxFHSPIGDGue0b4VbuwBHveRE7WK9Jxi3"}, "name": "IAPROD_nscmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "wf8prod", "ExportPasswordMode": "Key", "customUrl": "****************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "localhost", "password": "cQSszGXZE66kOBHHgcj7mgUufJ9b7tKZiQFh8pbSZuOtOWg=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "system", "ExportKeyChecksum": "FOi4uFdeQxLHbvc6nVQDxKFDr5B2Fw7v79as3ILtEf7nF0kf"}, "name": "WF8PROD_system", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "false", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "PROD", "customUrl": "***************************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "ban-prod-db.umflint.edu", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1548", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "dgetty0"}, "name": "Banner-Prod", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "EnX6QIHYmuNTlXPWl/L3S9vnxJpYZMI3FX2RAtZH9VSaSB0=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "intlmgr", "ExportKeyChecksum": "pzLH5fFvummhkHc67IuTwG2NX2aKJSJZl+n5zSSk2aeCtT5c"}, "name": "IAPROD_intlmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "6B3jbX0lNN/5fuKqhKIzDwGDVu7G5aC8rRUoKwjGcFcNurXOU16Upgwy", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "campuslabsmgr", "ExportKeyChecksum": "DqzRoIHgZc+DV88/2DgCFktX7PhHslZgMyYzkDeOWAaLRvJa"}, "name": "IAPROD_campuslabsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "Connection-Color-For-Editors": "-16776961", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "svbhgzPFI8PEfav8OUwY3M04S9IFpZPoaZ84Yvswp2jsKqc=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "aimsmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "mUp7qEiwh/TE/xZlxPbHImP+GQ4Uqh+xjJyJ4KImX2FJb54B"}, "name": "1_IAPROD_aimsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "WMU4iMpmJwd5w9Ln5lG5KA0Hm3VwORX9qlpWgWlWk1iEk72Qj1zyc1A1", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "rbessacmgr", "ExportKeyChecksum": "zDDA5TaG3ml36xW9+z0f8ynmeDluGu792TKNeWlK6M099X4B"}, "name": "IAPROD_rbessacmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "TwbJ4+z/OyUwhRC/k8DvGQqLdbQFp4Q452btJrbNP+QVpgPgXXIm0fCX", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "contmgr", "ExportKeyChecksum": "5v3J9ZUbN9iPEW8/o01nas4R49e43OVM7/stq2FbxhLpfsoA"}, "name": "IAPROD_contmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "Z1xSINWr7GPqwsiMxnwJADs6JJeIEmWC2wXVSxUQGcn/zdsRYZUbzlvY", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "hedamgr", "ExportKeyChecksum": "jxyG0pE2uZka2x5j05OCfKxZDnD3CCNMvw2eAqfMnl/QdbBm"}, "name": "IAPROD_hedamgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "jOSvZ2WUWwQ/P7zJ7Wp6bXGDt8sqXrDEW6kwGTPrWjXCVks=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "umfmgr", "ExportKeyChecksum": "cj2ySV+n46vk0wTIgv9Xgl6wowQRUNgdC4QOVOBODxLmG1ch"}, "name": "IAPROD_umfmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "104HHOSEdxR3OAtqi0tMTFwBhpTucfvtxug6SpUqvQdwvE9+oxCdGUup/A==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "dwrksmgr", "ExportKeyChecksum": "e4HcfHp7mayxzuoqpwYKZOS9fd34AxQEzLa7Nc2S6iU4B37g"}, "name": "IAPROD_dwrksmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "7bFnCAvkKHhiJpq1m4Fh6oa4iuSF2SL5sf/PoYDFcQ30C1Z5CnUV3Y9dx13/GJPJ", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "fsbmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "70Q4rlLBf6XGJUQ4ohjC4RMM71LEbULMNjZXwjjANqz39dlA"}, "name": "IAPROD_fsbmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "zBW8faheK+n2jOdNpvo0pipFhOiCpQ9tAfRNKelbhczoCHexAdcsQD/Z", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "hrmgr", "ExportKeyChecksum": "HLzeGZok7gGZRfp1cnhLVPTOoBkYUpgbVuah5JjROwcw9xCl"}, "name": "IAPROD_hrmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "dx6zxLlk5ozSwl5nZYM5zQ1UYD1ihKm8U+GnypjSDJz3cR3Agh8j74w0", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "BO_FLINT_STUDENT", "ExportKeyChecksum": "s0VN8HRLcBaEI4Qa6JJ7xBq860OY2dSc6L+JbTrnwtoHyVhP"}, "name": "IAPROD_bo_flint_student", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "i<PERSON><PERSON>", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "q9EC/1ovEPBVRzZYJt4uzUICNMe3Kc7pB0emEXfptNA3EZ0r4hcBoNUJ", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "heerfmgr", "PROXY_USER_NAME": "", "ExportKeyChecksum": "1P/iAawyDXoGH1UBr/CFIjK8TFfyudYfV6BCrAx/buvKeSxj"}, "name": "IAPROD_heerfmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "OiFzmpWYhLEpNoRo3DLcBtrBBSbQgQPfT3qwVG/55mAn8A==", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "tabmgr", "ExportKeyChecksum": "Lul9OSP838lu7gI62Is4gK6eGOeNc+Pta+kGGwAJ3JhWvSCe"}, "name": "IAPROD_tabmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "SFURKiCwVIckHPdV0MXzKrKXYxu9ICjKL5LFbdOYgelI7igtGaTv4sRG", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "cashmgr", "ExportKeyChecksum": "GcxQX+CbHRqOufGTE4RQA15ptkvZmx+d7TGkqLUyAw7jw0C9"}, "name": "IAPROD_cashsmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "false", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "PROD", "customUrl": "***************************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "ban-prod-db.umflint.edu", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1548", "OS_AUTHENTICATION": "false", "IS_PROXY": "false", "KERBEROS_AUTHENTICATION": "false", "user": "dgetty0", "PROXY_USER_NAME": ""}, "name": "Banner_Prod", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "r0W8Dt6t4JkKGOBsg3at3VcPdqM0T/rMCEpx+0LFISCXzX3o/s7PdsEJ", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "rcaster", "ExportKeyChecksum": "Fbd2oGaFW6JKizPFsr3ZJTGWLLfdr6245tvuTGwuOwspVLwb"}, "name": "IAPROD_rcaster", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "cMj+gQJwn1nVRyPQN8YS+k62exPWJ2LO2YHp42lEpJho59goG+gf9djv", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "maxientmgr", "ExportKeyChecksum": "Qw/Tkl4UHg8EAV6NOKy9sF8Zxb51URopHFHWuQ01ybvzRPxd"}, "name": "IAPROD_maxientmgr", "type": "jdbc"}, {"info": {"role": "", "SavePassword": "true", "OracleConnectionType": "BASIC", "PROXY_TYPE": "USER NAME", "RaptorConnectionType": "Oracle", "sid": "IAPROD", "ExportPasswordMode": "Key", "customUrl": "*********************************************", "oraDriverType": "thin", "NoPasswordConnection": "TRUE", "hostname": "dm3.umflint.edu", "password": "4gLoRgXngWu9pbCyfS84IO2i4tU+25GK5LDsGHx72j4kjkA=", "driver": "oracle.jdbc.OracleDriver", "subtype": "oraJDBC", "port": "1521", "IS_PROXY": "false", "OS_AUTHENTICATION": "false", "KERBEROS_AUTHENTICATION": "false", "PROXY_USER_NAME": "", "user": "emasmgr", "ExportKeyChecksum": "8LgQEe09siqZNhK60EVJGXQFM96jRKX+sCDxy5qMH9icq0eE"}, "name": "IAPROD_emasmgr", "type": "jdbc"}]}