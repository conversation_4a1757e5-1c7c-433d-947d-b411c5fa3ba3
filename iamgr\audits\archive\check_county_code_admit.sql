SELECT 
TERM_CODE_ENTRY,
COUNT(*)
FROM TD_ADMISSIONS_APPLICANT
WHERE TERM_CODE_ENTRY > '200740'
AND TD_ADMISSIONS_APPLICANT.COUNTY_CODE_ADMIT IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
GROUP BY TERM_CODE_ENTRY
ORDER BY TERM_CODE_ENTRY
;
SELECT 
TERM_CODE_ENTRY,
COUNT(*)
FROM UM_ADMISSIONS_APPLICANT
WHERE TERM_CODE_ENTRY > '200740'
AND UM_ADMISSIONS_APPLICANT.COUNTY_CODE_ADMIT IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
GROUP BY TERM_CODE_ENTRY
ORDER BY TERM_CODE_ENTRY
;

SELECT 
distinct COUNTY_CODE_ADMIT,
TERM_CODE_ENTRY,
COUNTY_DESC_ADMIT,
COUNT(*)
FROM TD_ADMISSIONS_APPLICANT
--WHERE COUNTY_CODE_ADMIT is not NULL
--AND TD_ADMISSIONS_APPLICANT.COUNTY_CODE_ADMIT IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
GROUP BY TERM_CODE_ENTRY, COUNTY_CODE_ADMIT,COUNTY_DESC_ADMIT
ORDER BY TERM_CODE_ENTRY asc
;
select
distinct AP_COUNTY_CODE,
AP_COUNTY_DESC,
count (*)
from
TD_AP_ADDRESS_AT_APPL_DATE
where AP_COUNTY_CODE is not NULL
group by AP_COUNTY_CODE,
AP_COUNTY_DESC
order by TERM_CODE_ENTRY_KEY asc
;