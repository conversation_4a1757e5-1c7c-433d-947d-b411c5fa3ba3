TABLE FILE UM_STUDENT_DATA
-*WHERE (( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'U2' OR 'U3' OR 'UG' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_GPA GE 3.0 ) AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1 IN ('ENSL','ENGW','ENG','ENGS','ENGT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_2 IN ('ENSL','ENGW','ENG','ENGS','ENGT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MAJOR_1 IN ('ENSL','ENGW','ENG','ENGS','ENGT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MAJOR_2 IN ('ENSL','ENGW','ENG','ENGS','ENGT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MAJOR_1 IN ('ENSL','ENGW','ENG','ENGS','ENGT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MAJOR_2 IN ('ENSL','ENGW','ENG','ENGS','ENGT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MINOR_1 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MINOR_2 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MINOR_3 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MINOR_1 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MINOR_2 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MINOR_3 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MINOR_1 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MINOR_2 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MINOR_3 IN ('ALIT','BLIT','CWIN','CWRT','ENGT','LIN','TWRT','WRT') )) OR (( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_GPA GE 7.5 ) AND (( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1 EQ 'ENGL' ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_2 EQ 'ENGL' ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MAJOR_1 EQ 'ENGL' ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MAJOR_2 EQ 'ENGL' ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MAJOR_1 EQ 'ENGL' ) OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MAJOR_2 EQ 'ENGL' )));
PRINT
     UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE
     UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE
BY  LOWEST UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM
WHERE (( UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE EQ '&SD_TERM_CODE.Registered for Term: .' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.REGISTERED_IND EQ 'Y' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.STUDENT_TYPE_CODE IN ('C','F','N','R','T') ));
WHERE (( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'U2' OR 'U3' OR 'UG' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_GPA GE 3.0 )) 
OR (( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_GPA GE 3.3 ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_1 EQ 'ENGL' ) 
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_MAJOR_2 EQ 'ENGL' ) 
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MAJOR_1 EQ 'ENGL' ) 
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.SECOND_MAJOR_2 EQ 'ENGL' ) 
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MAJOR_1 EQ 'ENGL' ) 
OR ( UM_STUDENT_DATA.UM_STUDENT_DATA.THIRD_MAJOR_2 EQ 'ENGL' ));
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD1 FORMAT ALPHA
END
JOIN
 LEFT_OUTER HOLD1.HOLD1.SD_PIDM AND HOLD1.HOLD1.PRIMARY_LEVEL_CODE IN HOLD1
 TO MULTIPLE SHRTGPA.SHRTGPA.SHRTGPA_PIDM AND SHRTGPA.SHRTGPA.SHRTGPA_LEVL_CODE
 IN SHRTGPA TAG J0 AS J0
 END
TABLE FILE HOLD1
SUM
     CNT.J0.SHRTGPA.SHRTGPA_TERM_CODE AS 'UMF_TERMS'
BY  LOWEST HOLD1.HOLD1.SD_PIDM
BY  LOWEST HOLD1.HOLD1.SD_TERM_CODE
BY  LOWEST HOLD1.HOLD1.PRIMARY_LEVEL_CODE
WHERE J0.SHRTGPA.SHRTGPA_GPA_TYPE_IND EQ 'I';
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLD2 FORMAT ALPHA
END
TABLE FILE HOLD2
PRINT
     HOLD2.HOLD2.SD_PIDM
     HOLD2.HOLD2.SD_TERM_CODE
     HOLD2.HOLD2.PRIMARY_LEVEL_CODE
     HOLD2.HOLD2.SHRTGPA_TERM_CODE AS 'UMF_Terms'
WHERE ( HOLD2.HOLD2.SHRTGPA_TERM_CODE GT 2 ) 
AND ( HOLD2.HOLD2.PRIMARY_LEVEL_CODE IN ('UG','U2','U3') ) 
OR ( HOLD2.HOLD2.PRIMARY_LEVEL_CODE IN ('GR','G2','G3') );
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDUG FORMAT ALPHA
END
JOIN
 INNER HOLDUG.HOLDUG.SD_PIDM AND HOLDUG.HOLDUG.PRIMARY_LEVEL_CODE IN HOLDUG
 TO MULTIPLE UM_STUDENT_TRANSCRIPT.UM_STUDENT_TRANSCRIPT.PIDM
 AND UM_STUDENT_TRANSCRIPT.UM_STUDENT_TRANSCRIPT.LEVL IN UM_STUDENT_TRANSCRIPT
 TAG J1 AS J1
 END
JOIN
 INNER J1.UM_STUDENT_TRANSCRIPT.GRADE AND J1.UM_STUDENT_TRANSCRIPT.LEVL IN
HOLDUG TO UNIQUE SHRGRDE.SHRGRDE.SHRGRDE_CODE
 AND SHRGRDE.SHRGRDE.SHRGRDE_LEVL_CODE IN SHRGRDE TAG J2 AS J2
 END
DEFINE FILE HOLDUG
ENG_GPA_Hours/D12.3=IF J2.SHRGRDE.SHRGRDE_GPA_IND EQ 'Y' THEN J1.UM_STUDENT_TRANSCRIPT.HOURS ELSE 0;
ENG_Quality_Points/D12.3=IF J2.SHRGRDE.SHRGRDE_GPA_IND EQ 'Y' THEN ( J1.UM_STUDENT_TRANSCRIPT.HOURS * J2.SHRGRDE.SHRGRDE_QUALITY_POINTS ) ELSE 0;
ENG_Counter/P2=IF J1.UM_STUDENT_TRANSCRIPT.COURSE GT '112' AND J1.UM_STUDENT_TRANSCRIPT.COURSE NE '150' THEN 1 ELSE 0;
END
TABLE FILE HOLDUG
SUM 
     HOLDUG.HOLDUG.SD_TERM_CODE
     HOLDUG.HOLDUG.PRIMARY_LEVEL_CODE
     HOLDUG.HOLDUG.SHRTGPA_TERM_CODE AS 'UMF_Terms'
     J1.UM_STUDENT_TRANSCRIPT.HOURS AS 'ENG_Credit_Hours'
     J2.SHRGRDE.ENG_GPA_Hours
     J2.SHRGRDE.ENG_Quality_Points
     J1.UM_STUDENT_TRANSCRIPT.ENG_Counter AS 'ENG_Course_Count'
BY  HOLDUG.HOLDUG.SD_PIDM
WHERE ( J1.UM_STUDENT_TRANSCRIPT.TYPE EQ 'I' ) 
AND ( J1.UM_STUDENT_TRANSCRIPT.SUBJECT EQ 'ENG' );
WHERE ( J1.UM_STUDENT_TRANSCRIPT.GRADE NE 'YW' 
OR 'Y' OR '*' OR 'I' OR 'W' OR 'IW' );
WHERE ( J1.UM_STUDENT_TRANSCRIPT.REPEAT_IND EQ MISSING ) 
OR ( J1.UM_STUDENT_TRANSCRIPT.REPEAT_IND EQ 'I' OR 'A' );
ON TABLE SET PAGE-NUM NOLEAD 
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDUG2 FORMAT FOCUS INDEX 'HOLDUG.HOLDUG.SD_PIDM' 'HOLDUG.HOLDUG.SD_TERM_CODE' 
END
DEFINE FILE HOLDUG2
ENG_GPA/P12.3=IF ENG_GPA_Hours NE 0 THEN ( ENG_Quality_Points / ENG_GPA_Hours ) ELSE 0;
END
TABLE FILE HOLDUG2
PRINT
     HOLDUG2.SEG01.SD_PIDM
     HOLDUG2.SEG01.SD_TERM_CODE
     HOLDUG2.SEG01.ENG_CREDIT_HOURS
     HOLDUG2.SEG01.ENG_GPA_Hours
     HOLDUG2.SEG01.ENG_Quality_Points
     HOLDUG2.SEG01.ENG_GPA
     HOLDUG2.SEG01.UMF_TERMS
     HOLDUG2.SEG01.ENG_COURSE_COUNT
WHERE (( HOLDUG2.SEG01.ENG_GPA GE 3.5 ) 
AND ( HOLDUG2.SEG01.ENG_COURSE_COUNT GE 2 ) 
AND ( HOLDUG2.SEG01.PRIMARY_LEVEL_CODE IN ('UG','U2','U3') )) 
OR (( HOLDUG2.SEG01.PRIMARY_LEVEL_CODE IN ('G2','G3','GR') ) 
AND ( HOLDUG2.SEG01.ENG_CREDIT_HOURS GE 6 ));
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDUGGPA FORMAT FOCUS INDEX 'HOLDUG2.SEG01.SD_PIDM' 'HOLDUG2.SEG01.SD_TERM_CODE'
END
SET EMPTYREPORT = ON
SET NODATA = ''
JOIN
 HOLDUGGPA.SEG01.SD_PIDM AND HOLDUGGPA.SEG01.SD_TERM_CODE IN HOLDUGGPA TO UNIQUE
 UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM
 AND UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE IN UM_STUDENT_DATA TAG J5
 AS J5
 END
JOIN
 J5.UM_STUDENT_DATA.SD_PIDM IN HOLDUGGPA TO UNIQUE
 UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM IN UM_DEMOGRAPHIC TAG J6 AS J6
 END
TABLE FILE HOLDUGGPA
PRINT
     J6.UM_DEMOGRAPHIC.NAME_SUFFIX AS 'Suffix'
     J6.UM_DEMOGRAPHIC.UMID
     J6.UM_DEMOGRAPHIC.A1_STREETADDRESS
     J6.UM_DEMOGRAPHIC.A1_CITY AS 'City'
     J6.UM_DEMOGRAPHIC.A1_STATE_CODE AS 'State'
     J6.UM_DEMOGRAPHIC.A1_ZIP AS 'Zip'
     J6.UM_DEMOGRAPHIC.A1_AREAPHONE
     J6.UM_DEMOGRAPHIC.CA_EMAIL AS 'Campus,Email'
     J5.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE AS 'Primary,Level'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_1 AS 'Primary,Major1'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_1_DESC AS 'Primary,Major1 Desc'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_2 AS 'Primary,Major2'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_2_DESC AS 'Primary,Major2 Desc'
     J5.UM_STUDENT_DATA.SECOND_MAJOR_1 AS 'Second,Major1'
     J5.UM_STUDENT_DATA.SECOND_MAJOR_2 AS 'Second,Major2'
     J5.UM_STUDENT_DATA.PRIMARY_MINOR_1 AS 'Primary,Minor1'
     J5.UM_STUDENT_DATA.PRIMARY_MINOR_2 AS 'Primary,Minor2'
     J5.UM_STUDENT_DATA.OVERALL_HOURS_EARNED AS 'Overall Hours,Earned'
     J5.UM_STUDENT_DATA.OVERALL_GPA AS 'Overall,GPA'
     HOLDUGGPA.SEG01.ENG_COURSE_COUNT AS 'ENG Courses,(beyond 112)'
     HOLDUGGPA.SEG01.ENG_CREDIT_HOURS AS 'ENG,Cr Hrs'
     HOLDUGGPA.SEG01.ENG_GPA_Hours AS 'ENG,GPA Hrs'
     HOLDUGGPA.SEG01.ENG_GPA AS 'ENG GPA'
     HOLDUGGPA.SEG01.UMF_TERMS AS 'UM-F Terms,Completed'
BY  HIGHEST J5.UM_STUDENT_DATA.REPORT_LEVEL_DESC NOPRINT  AS 'Level'
BY  LOWEST J6.UM_DEMOGRAPHIC.LAST_NAME AS 'Last Name'
BY  LOWEST J6.UM_DEMOGRAPHIC.FIRST_NAME AS 'First Name'
BY  LOWEST J6.UM_DEMOGRAPHIC.MIDDLE_INITIAL AS 'MI'

ON J5.UM_STUDENT_DATA.REPORT_LEVEL_DESC SUBHEAD
"<J5.UM_STUDENT_DATA.REPORT_LEVEL_DESC "
ON TABLE SUBHEAD
"English Honor Society Eligibility List"
"Sigma Tau Delta"
"<J5.UM_STUDENT_DATA.SD_TERM_DESC "
ON TABLE SUBFOOT
"Undergraduate Level Criteria:  Registered for selected term, "
"Overall GPA 3.0 or higher, ENG GPA 3.5 or higher, completed 3 or more terms at UM-Flint, completed 2 English courses beyond ENG 112."
" "
"Graduate Level Criteria: Registered for selected term, ENGL major, Overall GPA 3.3 or higher, 6 or more graduate ENG credits completed"
" "
"s0064s00 English Honor Society Eligibility List  Run on: <+0>&DATEMDYY <+0> "
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE SET BYDISPLAY ON
ON TABLE NOTOTAL
ON TABLE PCHOLD FORMAT &WFFMT.(<HTML,HTML>,<PDF,PDF>,<Excel 2000,EXL2K>,<Excel Formula,EXL2K FORMULA>,<HTML Active Report,AHTML>,<Active Report for Adobe Flash Player,FLEX>,<Active Report for PDF,APDF>,<PowerPoint,PPT>).Select type of display output.
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
TYPE=REPORT,
     GRAPHCOLOR='GREEN',
$
TYPE=DATA,
     COLUMN=N24,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N1,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N28,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N18,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N19,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N20,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N21,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N2,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N3,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N4,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N5,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N6,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N7,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N8,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N9,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N10,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N11,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N12,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N13,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N14,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N15,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N16,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N17,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N22,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N23,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N25,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N26,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N27,
     BACKCOLOR='NONE',
$
TYPE=TABHEADING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABHEADING,
     LINE=2,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABHEADING,
     LINE=3,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABHEADING,
     LINE=3,
     OBJECT=FIELD,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABFOOTING,
     LINE=1,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=2,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=3,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=2,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=2,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=3,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=3,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=4,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=4,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=4,
     OBJECT=TEXT,
     ITEM=2,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=4,
     OBJECT=TEXT,
     ITEM=3,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=5,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=5,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
$
TYPE=TABFOOTING,
     LINE=5,
     OBJECT=TEXT,
     ITEM=2,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=5,
     OBJECT=TEXT,
     ITEM=3,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=6,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=6,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=6,
     OBJECT=TEXT,
     ITEM=2,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=6,
     OBJECT=TEXT,
     ITEM=3,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=SUBHEAD,
     BY=1,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
$
TYPE=SUBHEAD,
     BY=1,
     LINE=1,
     OBJECT=FIELD,
     ITEM=1,
     SIZE=12,
$
TYPE=REPORT,
     OBJECT=MENU,
     COLOR='WHITE',
     HOVER-COLOR=RGB(66 70 73),
     BACKCOLOR=RGB(102 102 102),
     HOVER-BACKCOLOR=RGB(218 225 232),
     BORDER-COLOR='WHITE',
$
TYPE=REPORT,
     OBJECT=STATUS-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     OBJECT=CURRENT-ROW,
     HOVER-BACKCOLOR=RGB(218 225 232),
     BACKCOLOR=RGB(200 200 200),
$
TYPE=REPORT,
     OBJECT=CALC-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
ENDSTYLE
END
 