/*******************************************************************************
Author: Dan Getty University of Michigan-Flint
Create date:  02-18-16
Description:	Queries to gather Banner data for upload to National 
              Student Clearinhouse Student Tracker for SE request
              used in competitor dashboard.
Notes:  EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
Known issue(s):	
*******************************************************************************/
WITH DP1 AS ( 
 SELECT
  sd.sd_pidm,
  MIN(sd.sd_term_code) AS min_term
 FROM
  um_student_mega_data sd
  inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
 WHERE
   sd.sd_term_code >= 200810
  AND sd.sd_term_code < (
   SELECT
    current_term
   FROM
    um_current_term
  )
    AND sd.student_status_code = 'IS'
    AND sd.report_level_code = 'UG'
    AND sd.primary_college_code = 'MG'
    AND dm.deceased_ind is null
    and not exists
       (select pidm, umid,degree_status, level_code 
       from um_degree 
       where GRAD_TERM_CODE >= sd.sd_term_code
       and degree_status IN ('AW')
       and pidm = sd.sd_pidm
       AND DEGREE_CODE = sd.PRIMARY_DEGREE_CODE
       )
    AND not exists
      (select 'Has Hold'
      from um_holds_mv um_holds
      where um_holds.hold_status_ind = 'CURRENT'
      and (um_holds.reg_hold_ind = 'Y' or um_holds.ar_hold_ind = 'Y')
      and um_holds.pidm = sd.sd_pidm)
    and not exists
     (select 'Has current reg' 
     from um_student_data sd2
     where sd2.sd_pidm = sd.sd_pidm
     and sd2.sd_term_code >= (select to_number(current_term) -100 last_term from um_current_term)
     and sd2.registered_ind = 'Y'
          )
 GROUP BY
  sd.sd_pidm
  ), BASE_SEL AS (
  SELECT DISTINCT 
       substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(trim(DM.FIRST_NAME))
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) first_name,
    substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(trim(DM.LAST_NAME))
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) last_name,
        substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(DM.MIDDLE_INITIAL)
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) middle_name,
    
            substr(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    regexp_replace(
    initcap(DM.NAME_SUFFIX)
    ,'[.]', '') 
    ,'[à]', 'a') 
    ,'[è]', 'e') 
    ,'[ì]', 'i') 
    ,'[ò]', 'o') 
    ,'[ù]', 'u') 
    ,'[á]', 'a') 
    ,'[é]', 'e') 
    ,'[í]', 'i') 
    ,'[ó]', 'o') 
    ,'[ú]', 'u') 
    ,'[ý]', 'y') 
    ,'[ã]', 'a') 
    ,'[ñ]', 'n') 
    ,'[õ]', 'o') 
    ,'[`]') 
    ,'[(]') 
    ,'[)]') 
    , 'n/a')
    , 'N/A')
    , '[^a-zA-Z0-9., ''-]', '')
    , 1, 35) name_suffix,
  DM.BIRTHDATE,
  DP1.MIN_TERM,
  DP1.SD_PIDM
  FROM DP1
  INNER JOIN UM_DEMOGRAPHIC DM
  ON DM.DM_PIDM = DP1.SD_PIDM

), POP_SEL AS (
  --THIS SECTION CREATES A HEADER FILE.
  SELECT
  1 ORDER_NUM,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  TO_CHAR(SYSDATE, 'YYYYMMDD') "E",
  'SE' "F",
  'I' "G",
  NULL "H",
  NULL "I",
  NULL "J",
  NULL "K",
  NULL "L"
  FROM DUAL
  
  UNION ALL
  --THIS SECTION PULLS THE STUDENT RECORDS FOR THE PAYLOAD.
  SELECT
  2 ORDER_NUM,
  'D1' "A",
  NULL "B",
  FIRST_NAME "C",
  SUBSTR(REGEXP_REPLACE(TRIM(MIDDLE_NAME), '[[:punct:] ]',NULL), 1 ,1) "D", 
  LAST_NAME "E",
  SUBSTR(REGEXP_REPLACE(TRIM(NAME_SUFFIX), '[[:punct:] ]',NULL),1,5) "F",
  TO_CHAR(BIRTHDATE, 'YYYYMMDD') "G",
  TO_CHAR(TO_NUMBER(SUBSTR(MIN_TERM,1,4))-1 ||'0915') "H", 
  NULL "I",
  '002327' "J",
  '00' "K",
  TO_CHAR(SD_PIDM) "L"
  FROM BASE_SEL

  UNION ALL
    --THIS IS TO COUNT THE NUMBER OF RECORDS AND APPEND A TRAILER RECORD
  SELECT
  3 ORDER_NUM,
  'T1' "A",
  TO_CHAR(COUNT(SD_PIDM)+2) "B",
  NULL "C",
  NULL "D",
  NULL "E",
  NULL "F",
  NULL "G",
  NULL "H",
  NULL "I",
  NULL "J",
  NULL "K",
  NULL "L"
  FROM BASE_SEL
)
SELECT 
POP_SEL.A,
POP_SEL.B,
POP_SEL.C,
POP_SEL.D,
POP_SEL.E,
POP_SEL.F,
POP_SEL.G,
POP_SEL.H,
POP_SEL.I,
POP_SEL.J,
POP_SEL.K,
POP_SEL.L
FROM POP_SEL
ORDER BY ORDER_NUM ASC
;
