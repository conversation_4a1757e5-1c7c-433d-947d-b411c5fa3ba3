/*
HEALTH PROFESSIONS AND RELATED PROGRAMS (CIP code 51)
*/
truncate table IA_CW_HP_BAC;

insert into IA_CW_HP_BAC
SELECT * FROM IA_CW_HP;

INSERT INTO IA_CW_HP (
SELECT 
DISTINCT 
DEGREE_CODE,
PRIMARY_PROGRAM,
--substr(primary_major_1_cipc_code,1,2)cip_fam,
--primary_major_1_cipc_code,
'Y'  HP_IND
FROM IA_UM_DEGREE
WHERE 
CATALOG_TERM_CODE >= 201110
and substr(primary_major_1_cipc_code,1,2) in ('51')

minus

select 
DEGREE_CODE, 
PRIMARY_PROGRAM, 
HP_IND
from IA_CW_HP_BAC
)
;
commit;

select * from IA_CW_HP
minus
select * from IA_CW_HP_BAC;
