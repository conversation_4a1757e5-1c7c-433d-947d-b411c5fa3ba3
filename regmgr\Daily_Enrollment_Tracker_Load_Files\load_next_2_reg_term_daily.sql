--------------------------------------------------------
--  DDL for View LOAD_NEXT_2_REG_TERM_DAILY
--------------------------------------------------------


  CREATE OR REPLACE VIEW REGMGR.LOAD_NEXT_2_REG_TERM_DAILY  AS 
  with t1 as(
    select
    ct.day_of_term,
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.run_date = trunc(sysdate)
    and ct.term_code = (select um_current_term.next_2_bridge_term from um_current_term)
    group by
    ct.day_of_term,
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind
)
, t2 as(
    select 
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.day_of_term = (select distinct t1.day_of_term from t1)
    and ct.term_code = (select distinct (t1.term_code - 100) from t1) --'202030'
    group by
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind
)
, t3 as(
    select 
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.day_of_term = (select distinct t1.day_of_term from t1)
    and ct.term_code = (select distinct (t1.term_code - 200) from t1) --'202030'
    group by
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind
)
, t4 as(
    select 
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.day_of_term = (select distinct t1.day_of_term from t1)
    and ct.term_code = (select distinct (t1.term_code - 300) from t1) --'202030'
    group by
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    ct.full_part_ind,
    ct.rpt_ethnicity,
    ct.college_code,
    ct.veteran_ind
)
, td as(
    select 
    td_term_code,
    report_level_code,
    report_level_desc,
    student_type_code,
    student_type_desc,
    full_part_time_ind full_part_ind,
    report_ethnicity rpt_ethnicity,
    primary_college_code college_code,
    nvl(veteran_ind,'N') veteran_ind,
    sum(head_count) head_count,
    sum(total_credit_hours) total_credit_hours
    from td_term_summary
    where td_term_code = (select distinct (t1.term_code - 100) from t1)
    group by
    td_term_code,
    report_level_code,
    report_level_desc,
    student_type_code,
    student_type_desc,
    full_part_time_ind,
    report_ethnicity,
    primary_college_code,
    veteran_ind
)
, pop_sel as(
    select
    dot.day_of_term,
    dot.term_code,
    dot.term_desc,
    cds.level_code,
    cds.level_desc,
    cds.student_type,
    cds.student_desc,
    cds.full_part_ind,
    cds.rpt_ethnicity,
    cds.college_code,
    cds.veteran_ind
    from (
        select
        t1.rpt_levl_code level_code,
        t1.rpt_level_desc level_desc,
        t1.rpt_student_type_code student_type,
        t1.rpt_student_type_desc student_desc,
        t1.full_part_ind,
        t1.rpt_ethnicity,
        t1.college_code,
        t1.veteran_ind
        from t1
        union
        select
        t2.rpt_levl_code,
        t2.rpt_level_desc,
        t2.rpt_student_type_code,
        t2.rpt_student_type_desc,
        t2.full_part_ind,
        t2.rpt_ethnicity,
        t2.college_code,
        t2.veteran_ind
        from t2    
        union
        select
        t3.rpt_levl_code,
        t3.rpt_level_desc,
        t3.rpt_student_type_code,
        t3.rpt_student_type_desc,
        t3.full_part_ind,
        t3.rpt_ethnicity,
        t3.college_code,
        t3.veteran_ind
        from t3    
        union
        select
        t4.rpt_levl_code,
        t4.rpt_level_desc,
        t4.rpt_student_type_code,
        t4.rpt_student_type_desc,
        t4.full_part_ind,
        t4.rpt_ethnicity,
        t4.college_code,
        t4.veteran_ind
        from t4    
        union
        select
        td.report_level_code,
        td.report_level_desc,
        td.student_type_code,
        td.student_type_desc,
        td.full_part_ind,
        td.rpt_ethnicity,
        td.college_code,
        td.veteran_ind
        from td
    ) cds
    inner join(
        select
        distinct t1.day_of_term,
        t1.term_code,
        t1.term_desc
        from t1
    ) dot on dot.day_of_term != 9999999
)
select
pop_sel.day_of_term,
pop_sel.term_code,
pop_sel.term_desc,
pop_sel.level_code,
pop_sel.level_desc,
pop_sel.student_type,
pop_sel.student_desc,
pop_sel.full_part_ind,
pop_sel.rpt_ethnicity,
pop_sel.college_code,
pop_sel.veteran_ind,
nvl(t1.head_count, 0) t1_head_count,
nvl(t1.total_credit_hours, 0) t1_total_credit_hours,
nvl(t2.head_count, 0) t2_head_count,
nvl(t2.total_credit_hours, 0) t2_total_credit_hours,
nvl(t3.head_count, 0) t3_head_count,
nvl(t3.total_credit_hours, 0) t3_total_credit_hours,
nvl(t4.head_count, 0) t4_head_count,
nvl(t4.total_credit_hours, 0) t4_total_credit_hours,
nvl(td.head_count, 0) td_head_count,
nvl(td.total_credit_hours, 0) td_total_credit_hours

from pop_sel
left outer join t1 on t1.rpt_levl_code = pop_sel.level_code
                   and t1.rpt_student_type_code = pop_sel.student_type
                   and t1.full_part_ind = pop_sel.full_part_ind
                   and t1.rpt_ethnicity = pop_sel.rpt_ethnicity
                   and t1.college_code = pop_sel.college_code                  
                   and t1.veteran_ind = pop_sel.veteran_ind 
left outer join t2 on t2.rpt_levl_code = pop_sel.level_code
                   and t2.rpt_student_type_code = pop_sel.student_type
                   and t2.full_part_ind = pop_sel.full_part_ind
                   and t2.rpt_ethnicity = pop_sel.rpt_ethnicity
                   and t2.college_code = pop_sel.college_code
                   and t2.veteran_ind = pop_sel.veteran_ind 
left outer join t3 on t3.rpt_levl_code = pop_sel.level_code
                   and t3.rpt_student_type_code = pop_sel.student_type
                   and t3.full_part_ind = pop_sel.full_part_ind
                   and t3.rpt_ethnicity = pop_sel.rpt_ethnicity
                   and t3.college_code = pop_sel.college_code
                   and t3.veteran_ind = pop_sel.veteran_ind 
left outer join t4 on t4.rpt_levl_code = pop_sel.level_code
                   and t4.rpt_student_type_code = pop_sel.student_type
                   and t4.full_part_ind = pop_sel.full_part_ind
                   and t4.rpt_ethnicity = pop_sel.rpt_ethnicity
                   and t4.college_code = pop_sel.college_code
                   and t4.veteran_ind = pop_sel.veteran_ind 
left outer join td on td.report_level_code = pop_sel.level_code
                   and td.student_type_code = pop_sel.student_type
                   and td.full_part_ind = pop_sel.full_part_ind
                   and td.rpt_ethnicity = pop_sel.rpt_ethnicity
                   and td.college_code = pop_sel.college_code
                   and td.veteran_ind = pop_sel.veteran_ind 
order by
4, 6, 8, 9, 10, 11
;