with tbraccd_hold as(
    select 
    tbraccd.tbraccd_pidm,
    tbraccd.tbraccd_term_code,
    tbraccd.tbraccd_amount
    from tbraccd_ext tbraccd
    inner join tbbdetc_ext tbbdetc on tbbdetc.tbbdetc_detail_code = tbraccd.tbraccd_detail_code
                       and tbbdetc.tbbdetc_dcat_code = 'TUI'
  )
  select
  /*+ ORDERED USE_HASH(tbraccd_hold) */
  nvl(sum(tbraccd.tbraccd_amount), 0) amount
  from tbraccd_hold tbraccd
  where tbraccd.tbraccd_pidm = pidm_in
  and tbraccd.tbraccd_term_code = term_code_in;
  
  type ar_tui_table is table of ar_tui_cur%rowtype index by binary_integer;
  ar_tui_rec ar_tui_table;

  cursor ar_fee_cur(pidm_in number, term_code_in varchar2)
  is
  with tbraccd_hold as(
    select 
    tbraccd.tbraccd_pidm,
    tbraccd.tbraccd_term_code,
    tbraccd.tbraccd_amount
    from tbraccd_ext tbraccd
    inner join tbbdetc_ext tbbdetc on tbbdetc.tbbdetc_detail_code = tbraccd.tbraccd_detail_code
                       and tbbdetc.tbbdetc_dcat_code = 'FEE'
  )
  select
  /*+ ORDERED USE_HASH(tbraccd_hold) */
  nvl(sum(tbraccd.tbraccd_amount), 0) amount
  from tbraccd_hold tbraccd
  where tbraccd.tbraccd_pidm = pidm_in
  and tbraccd.tbraccd_term_code = term_code_in;
