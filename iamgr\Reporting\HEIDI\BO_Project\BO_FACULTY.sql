CREATE OR REPLACE VIEW bo_faculty AS
 WITH bo_sel AS (
  SELECT
   (
    SELECT
     MIN(stvterm.stvterm_code)
    FROM
     stvterm
    WHERE
     stvterm.stvterm_end_date > bo_fac_hr_ext.job_effdt
   )  term_code,
       ROW_NUMBER()
   OVER(PARTITION BY bo_fac_hr_ext.emplid,(
    SELECT
     MIN(stvterm.stvterm_code)
    FROM
     stvterm
    WHERE
     stvterm.stvterm_end_date = bo_fac_hr_ext.job_effdt
   )
        ORDER BY bo_fac_hr_ext.job_effdt
   )  order_num,
       bo_fac_hr_ext.*
  FROM
   bo_fac_hr_ext
 )
     SELECT
  bo_sel.*
 FROM
  bo_sel  
     WHERE
  bo_sel.order_num = 1;
  
  SELECT COUNT (*) FROM BO_FAC_HR_EXT;
  SELECT COUNT (*) FROM BO_FACULTY;

  SELECT 
  TERM_CODE,
  ORDER_NUM,
  job_effdt
  FROM BO_FACULTY;
  
 SELECT * FROM BO_COURSE;