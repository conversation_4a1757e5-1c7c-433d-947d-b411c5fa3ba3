/*******************************************************************************
Update Ten year registered or graduated table
*******************************************************************************/
--Process the IA_ALGORITHM_CO_UPDATE
--Process the IA_ALGORITHM_NSC_UPDATE

--Drop backup file
DROP TABLE IA_COHORT_TEN_YR_TBL_NSC_BAC PURGE;

--Create Backup
CREATE TABLE IA_COHORT_TEN_YR_TBL_NSC_BAC AS
SELECT * FROM IA_COHORT_TEN_YR_TBL_NSC;

--Count rows to varify backup
SELECT COUNT (*) FROM IA_COHORT_TEN_YR_TBL_NSC;
SELECT COUNT (*) FROM IA_COHORT_TEN_YR_TBL_NSC_BAC;

--Drop Table
DROP TABLE IA_COHORT_TEN_YR_TBL_NSC PURGE;

--Run IA_CO_TEN_YEAR_TBL_NSC.SQL and wait for it to complete....

/*******************************************************************************
Update cohort persistence table
*******************************************************************************/
--Drop backup file
DROP TABLE IA_COHORT_PERSIST_TBL_NSC_BAC PURGE;

--Create Backup
CREATE TABLE IA_COHORT_PERSIST_TBL_NSC_BAC AS
SELECT * FROM IA_COHORT_PERSIST_TBL_NSC;

--Count rows to varify backup
SELECT COUNT (*) FROM IA_COHORT_PERSIST_TBL_NSC;
SELECT COUNT (*) FROM IA_COHORT_PERSIST_TBL_NSC_BAC;

--Drop Table
DROP TABLE IA_COHORT_PERSIST_TBL_NSC PURGE;

--Run IA_CO_PERSIST_NSC.SQL and wait for it to complete

/*******************************************************************************
Update HLC study table 
This provides filter data for analysis of persistence data.
*******************************************************************************/
--Drop backup file
DROP TABLE IA_HLC_STUDY_BAC PURGE;

--Create Backup
CREATE TABLE IA_HLC_STUDY_BAC AS
SELECT * FROM IA_HLC_STUDY_TBL;

--Count rows to varify backup
SELECT COUNT (*) FROM IA_HLC_STUDY_TBL;
SELECT COUNT (*) FROM IA_HLC_STUDY_BAC;

--Drop Table
DROP TABLE IA_HLC_STUDY_TBL PURGE;

--Run IA_CO_PERSIST_NSC.SQL and wait for it to complete

/*******************************************************************************
Update Cohort Financial Aid Table
This provides filter data for analysis of persistence data.
*******************************************************************************/
--Drop backup file
DROP TABLE IA_COHORT_FA_BAC PURGE;

--Create Backup
CREATE TABLE IA_COHORT_FA_BAC AS
SELECT * FROM IA_COHORT_FA;

--Count rows to varify backup
SELECT COUNT (*) FROM IA_COHORT_FA;
SELECT COUNT (*) FROM IA_COHORT_FA_BAC;

--Drop Table
DROP TABLE IA_COHORT_FA PURGE;

--Run IA_COHORT_FA.SQL and wait for it to complete