--must update the HR Data pulled from Business Objects
with dp1 as (
SELECT 
DISTINCT
to_date(JOB_EFFDT, 'YYYY/MM/DD HH24:MI:SS') job_effdt,
to_date(APPT_START_DATE, 'YYYY/MM/DD HH24:MI:SS') APPT_START_DATE,
to_date(DEPT_BUDGET_ERN_EFFDT, 'YYYY/MM/DD HH24:MI:SS') DEPT_BUDGET_ERN_EFFDT,
to_date(DEPT_BUDGET_ERN_END_DT, 'YYYY/MM/DD HH24:MI:SS') DEPT_BUDGET_ERN_END_DT,
empl_rcd,
JOB_EFFSEQ,
DIST_PCT,
PERCENT_EFFORT,
FUNDING_DEPT_DESCR,
SHORTCODE,
ANNUAL_FTR,
HR.IPEDSSCODE,
IP.IPEDSSCODE_DESCR,
LAST_NAME,
FIRST_NAME,
EMPLID,
SEX,
ETHNIC_GROUP_DESCR,
HIGHEST_EDUC_LVL_DESCRSHORT,
CITIZENSHIP_STATUS_DESCR,
APPT_DEPT_DESCR,
JOBCODE_DESCR,
FTE,
COMPRATE,
CLASS_INDC_DESCRSHORT,
APPT_PERIOD_DESCR,
TENURE_STATUS,
FTR_RATE,
STD_HOURS,
SHORTCODE_DESCR,
EMPL_STATUS
FROM IA_HR_FIL HR
LEFT JOIN IA_CW_IPEDS_JOB_CODES IP ON IP.IPEDSSCODE = HR.IPEDSSCODE
where HR.IPEDSSCODE in ('E','2')
and job_effdt >= '01-JUL-16' AND  job_effdt <= '30-JUN-17'
), 

rc as (
select emplid, count (*)record_count 
from dp1

group by emplid
order by
count (*)desc
)

select distinct job_effdt, emplid, FUNDING_DEPT_DESCR, shortcode, APPT_PERIOD_DESCR,PERCENT_EFFORT, FTR_RATE from 
--rc
DP1
where emplid = '60074963'
;
--SELECT DISTINCT EMPLID, FIRST_NAME, LAST_NAME, IPEDSSCODE_DESCR,JOBCODE_DESCR  FROM IA_HR_TBL
--WHERE EMPLID = '79956475'
--;
--SELECT DISTINC IPEDSSCODE_DESCR FROM IA_HR_TBL
--;