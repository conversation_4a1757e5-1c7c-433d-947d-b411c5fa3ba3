--1851 student
create or replace view REG_APLY_TO_GRAD_EMAIL AS 
with dp1 as (
SELECT
distinct
    um_student_data.sd_term_desc,
    um_student_data.sd_term_code,
    um_demographic.last_name,
    um_demographic.first_name,
    nvl(um_demographic.middle_initial,'.') middle_initial,
    nvl(um_demographic.name_suffix,'.') name_suffix,
    um_demographic.umid,
    um_demographic.ca_email,
    um_student_data.primary_level_code,
    um_student_data.primary_degree_code,
    um_student_data.primary_major_1,
    um_student_data.primary_program,
    um_student_data.inst_hours_earned,
    um_student_data.overall_hours_earned,
    um_student_data.overall_gpa
    
FROM
         um_student_data
    INNER JOIN um_demographic ON um_demographic.dm_pidm = um_student_data.sd_pidm
    LEFT OUTER JOIN um_degree ON um_degree.pidm = um_student_data.sd_pidm
                                 AND um_degree.level_code = um_student_data.primary_level_code
   
WHERE
    um_student_data.sd_term_code = (select current_term from um_current_term)--'202310'
    AND nvl(um_degree.grad_status,'xxx') != 'PG'
    AND um_student_data.student_status_code = 'AS'
    AND um_student_data.student_type_code = 'C'
    AND um_student_data.primary_major_1 NOT IN ( '0000', 'NURN', 'NURP', 'PSWK', 'PHST',
                                                 'PBUS', 'PCCP', 'PEDU', 'PPTP', 'PRTT' )
    AND (            
           ( um_student_data.primary_degree_code = 'EDD' )
          AND ( um_student_data.overall_hours_earned >= 28 )
          
          OR ( um_student_data.primary_degree_code = 'EDS' )
          AND ( um_student_data.overall_hours_earned >= 20 )
          
          OR ( um_student_data.primary_degree_code = 'PHD' )
          AND ( um_student_data.overall_hours_earned >= 35 )
          
          OR ( um_student_data.primary_degree_code = 'DPT' )
          AND ( um_student_data.primary_program IN ( 'DPT', 'DPT-HS' ) )
          AND ( um_student_data.primary_major_1 = 'PTPP' )
          AND ( um_student_data.overall_hours_earned >= 5 )
          
          OR ( um_student_data.primary_degree_code = 'DPT' )
          AND ( um_student_data.primary_program = 'DPT' )
          AND ( um_student_data.primary_major_1 = 'PTP' )
          AND ( um_student_data.overall_hours_earned >= 50 )
          
          OR ( um_student_data.primary_degree_code = 'CERG' )
          AND ( um_student_data.overall_hours_earned >= 5 )
          
          OR ( um_student_data.primary_degree_code = 'DNP' )
          AND ( um_student_data.primary_program = 'DNP-NR')
          AND ( um_student_data.overall_hours_earned >= 40 )
          
          OR ( um_student_data.primary_degree_code = 'DNP' )
          AND ( um_student_data.primary_program = 'DNP-NR-NP')
          AND ( um_student_data.overall_hours_earned >= 15 )

          OR ( um_student_data.primary_degree_code = 'DNP' )
          AND ( um_student_data.overall_hours_earned >= 30 )

          OR ( um_student_data.primary_degree_code = 'DAP' )
          AND ( um_student_data.overall_hours_earned >= 15 )
  
          OR ( um_student_data.primary_level_code IN ( 'DR', 'D2' ) )
          AND ( um_student_data.overall_hours_earned >= 50 ) 
          
          OR ( um_student_data.primary_level_code = 'UG'
             AND um_student_data.overall_hours_earned >= 90
             AND um_student_data.inst_hours_earned >= 20 )
            
          OR ( um_student_data.primary_level_code IN ( 'U2', 'U3' )
             AND um_student_data.overall_hours_earned >= 100
             AND um_student_data.inst_hours_earned >= 20 )
               
          OR ( um_student_data.primary_level_code IN ( 'GR', 'G2', 'G3' ) )
             AND ( um_student_data.overall_hours_earned >= 15 )
          )
--  order by 1
   )
   select 
   ROW_NUMBER() OVER (PARTITION BY umid ORDER BY sd_term_code) row_count,
   dp1.* from dp1
    ;

select distinct um_degree.grad_status, um_degree.grad_status_desc from um_degree;
describe um_degree;
describe um_holds;