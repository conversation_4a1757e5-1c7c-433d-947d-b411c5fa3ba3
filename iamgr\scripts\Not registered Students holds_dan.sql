create table holdstemp2 as (select PIDM ,
HLDD_CODE ,
DESCR ,
FROM_DATE ,
TO_DATE ,
REASON ,
amount_balance,
--BA<PERSON><PERSON><PERSON> ,
HOLD_USER ,
ORIG_CODE ,
REG_HOLD_IND ,
TRANS_HOLD_IND ,
GRAD_HOLD_IND ,
AR_HOLD_IND ,
ACTIVITY_DATE ,
HOLD_STATUS_IND
from 
--ia_um_holds 
UM_HOLDS_W_BALANCE
where hldd_code = 'AR' and hold_status_ind = 'CURRENT' );
SELECT sd_pidm,
  um_student_data.umid,
  first_name,
  Last_name,
  CA_EMAIL,
  A1_AREA_CODE,
  A1_PHONE_NUMBER,
  TERM_REGISTERED_HOURS,
  REASON,
  MOST_RECENT_HOURS_EARNED,
  overall_gpa,
  CLASS_DESC,
  HLDD_CODE,
  STUDENT_TYPE_Desc,
  Holdstemp2.amount_balance
  --Holdstemp2.balance
FROM um_student_data
LEFT JOIN holdstemp2
ON um_student_data.sd_pidm = Holdstemp2.Pidm
INNER JOIN um_demographic
ON um_student_data.sd_pidm = Um_Demographic.Dm_Pidm
WHERE STUDENT_TYPE_CODE   IN ('F', 'T', 'R', 'C')
AND STUDENT_STATUS_Code    = 'AS'
AND sd_term_code           = 201610
AND Report_Level_Code      = 'UG'
AND pidm                  IS NOT NULL
AND registered_ind         = 'Y'
AND sd_pidm NOT           IN
  (SELECT sd_pidm
  FROM um_student_data
  WHERE registered_ind = 'Y'
  AND sd_term_code     = 201620
  ) ;
