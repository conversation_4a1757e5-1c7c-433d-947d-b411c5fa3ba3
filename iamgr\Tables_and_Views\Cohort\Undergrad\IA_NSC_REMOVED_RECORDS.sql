/******************************************************************************
The purpose of the query is to id the records that are pulled from the 
IA_COHORT_NSC_RAW table  and are not loaded into the IA_NSC_STUDENT_DATA
table because there term codes does not coinside with UMF Banner term codes.  
******************************************************************************/
create OR REPLACE view  IA_NSC_REMOVED_RECORDS as( 
select * from IA_COHORT_NSC_RAW 
where to_Char(to_date(ENROLLMENT_BEGIN,'YYYYMMDD'), 'MM') 
in ('11','03', '02', '10')
--('11','12','03', '02', '10')
)