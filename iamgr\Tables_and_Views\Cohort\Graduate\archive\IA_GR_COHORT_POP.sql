create or replace view IA_GR_COHORT_POP AS (

select
ia_td_student_data.sd_pidm as CO_GR_PIDM,
--ia_td_student_data.sd_term_code,
ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
to_date('01-SEP-'||substr(ia_td_student_data.sd_term_code,3,2),'DD-MON-YY') start_date,
ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
ia_td_student_data.report_ethnicity as CO_GR_ETHNICITY,
ia_td_student_data.gender as CO_GR_GENDER,
ia_td_student_data.PRIMARY_MAJOR_1,
ia_td_student_data.PRIMARY_PROGRAM,
ia_td_student_data.PRIMARY_CONC_1,
ia_td_student_data.primary_level_code


from ia_td_student_data
  
where 
report_level_code in ('GR') and
registered_ind = 'Y' and 
ia_student_type_code in ('N') and 
sd_term_code like '%10' and 
sd_term_code >= '201110'  /*filters the term code to pull only 
														fall terms greater than or equal to 
														fall 2011*/
--union all
--
--select
--ia_td_student_data.sd_pidm as CO_GR_PIDM,
--ia_td_student_data.sd_term_code,
--SUBSTR(ia_td_student_data.sd_term_code, 1, 4) || '10' CO_GR_TERM_CODE_KEY, -- Winter Term modified to Fall term
--ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
--ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
--ia_td_student_data.report_ethnicity as CO_GR_ETHNICITY,
--ia_td_student_data.gender as CO_GR_GENDER
--
--from ia_td_student_data
--  
--where 
--report_level_code in ('GR') and
--registered_ind = 'Y' and 
--ia_student_type_code in ('N') and 
--sd_term_code like '%20' and 
--sd_term_code >= '201120' and  /*filters the term code to pull only fall terms greater than or equal to  fall 2011*/
--PRIMARY_MAJOR_1 = 'NUR' and
--PRIMARY_PROGRAM like 'MSN%'

);                          
                            
--
--select 
--CO_GR_TERM_CODE_KEY,
--count (*)
--from IA_GR_COHORT_POP
--group by
--CO_GR_TERM_CODE_KEY
--order by
--CO_GR_TERM_CODE_KEY desc

--select distinct primary_level_code, PRIMARY_LEVEL_DESC
--from ia_td_student_data
--
--select distinct sd_term_code, sd_term_desc, count (sd_pidm)
--from ia_td_student_data
--where primary_level_code = 'UT' and
--registered_ind = 'Y'
--group by
--sd_term_code, sd_term_desc
--order by
--sd_term_code desc
