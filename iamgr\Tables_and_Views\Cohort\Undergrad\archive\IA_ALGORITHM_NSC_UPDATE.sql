/*******************************************************************************
Update NSC Raw Data Table from detail return file
*******************************************************************************/

--Drop old NSC Raw Data Table backup file
TRUNCATE TABLE IA_COHORT_NSC_RAW_BAC;

--Create Backup
CREATE TABLE IA_COHORT_NSC_RAW_BAC AS
SELECT * FROM IA_COHORT_NSC_RAW;

--Count rows to varify backed up
SELECT COUNT (*) FROM IA_COHORT_NSC_RAW;
SELECT COUNT (*) FROM IA_COHORT_NSC_RAW_BAC;

--Truncate NSC Raw Data Table
TRUNCATE TABLE IA_COHORT_NSC_RAW;

--Rt click IA_COHORT_NSC_RAW.SQL and insert the data from the detail file

/*******************************************************************************
Update NSC Student Data Table
*******************************************************************************/

--Drop old NSC Student Data Table backup file
DROP TABLE IA_NSC_STUDENT_DATA_BAC PURGE;

--Create Backup
CREATE TABLE IA_NSC_STUDENT_DATA_BAC AS
SELECT * FROM IA_NSC_STUDENT_DATA;

--Count rows to varify backed up
SELECT COUNT (*) FROM IA_NSC_STUDENT_DATA;
SELECT COUNT (*) FROM IA_NSC_STUDENT_DATA_BAC;

--Truncate NSC Student Table
TRUNCATE TABLE IA_NSC_STUDENT_DATA;

--Run IA_NSC_STUDENT_DATA.SQL Script

/*******************************************************************************
Update NSC Degrees Table
*******************************************************************************/

--Drop old NSC Degrees Table backup file
DROP TABLE IA_NSC_DEGREES_BAC PURGE;

--Create Backup
CREATE TABLE IA_NSC_DEGREES_BAC AS
SELECT * FROM IA_NSC_DEGREES;

--Count rows to varify backed up
SELECT COUNT (*) FROM IA_NSC_DEGREES;
SELECT COUNT (*) FROM IA_NSC_DEGREES_BAC;

--Truncate NSC Student Table
TRUNCATE TABLE IA_NSC_DEGREES;

--Run IA_NSC_DEGREES.SQL Script


