/*******************************************************************************
DBA Scripts and Datamart Grants
*******************************************************************************/
--Create user and grant create table, and tablespace
CREATE USER bo_flint_student IDENTIFIED BY GoBlue022321;
GRANT CREATE TABLE TO BO_FLINT_STUDENT;
GRANT CREATE VIEW TO bo_flint_student;
ALTER USER bo_flint_student QUOTA 100M ON DEVELOPMENT ;
GRA<PERSON> UNLIMITED TABLESPACE TO bo_flint_student;
ALTER USER bo_flint_student DEFAULT ROLE ALL;
GRANT CREATE SESSION TO bo_flint_student;
GRANT CREATE PROCEDURE TO bo_flint_student;
GRANT CREATE ANY DIRECTORY TO bo_flint_student;

-- create a profile, assign parameters, and alter user profile
create profile topgun limit PASSWORD_REUSE_TIME unlimited;
alter profile topgun limit PASSWORD_LIFE_TIME  unlimited;
alter user hr profile topgun;


--Check user account status
SELECT username, account_status FROM dba_users
WHERE username = 'tabmgr';
--unlock accounts
alter user hr account unlock;
--Change user account password
alter user hr identified by oracle;

--query the priveleges of a user
select 
*
--username, account_status, EXPIRY_DATE 
from dba_users where username='IAMGR';

--count number of schemas in database
--Count Tables:
SELECT username, 
created, 
count(distinct dba_tables.table_name)tbl_count, 
count(distinct dba_views.view_name)view_count  
FROM all_users au
left join
dba_tables on  au.username = dba_tables.owner
left join
dba_views on au.username = dba_views.owner
where au.username like '%MGR'
group by 
username, 
created
ORDER BY 3 DESC
;
SELECT * FROM dba_tables
--where owner like '%MGR'
--group by owner
--ORDER BY 2 DESC
;
--Count Tables:
SELECT owner, COUNT(*) tbl_count FROM dba_tables
where owner like '%MGR'
group by owner
ORDER BY 2 DESC
;
--Count Sequences
SELECT COUNT(*) FROM user_sequences
;
--Count Views
SELECT owner, COUNT(*) tbl_count FROM dba_views
where owner like '%MGR'
group by owner
ORDER BY 2 DESC
;
--Count Indexes
SELECT COUNT(*) FROM user_indexes
;
grant select on HR.DEPARTMENTS to tabmgr;
grant select on HR.EMPLOYEES to tabmgr;
grant select on HR.JOB_HISTORY to tabmgr;
grant select on HR.JOBS to tabmgr;
grant select on HR.LOCATIONS to tabmgr;
grant select on HR.REGIONS to tabmgr;
grant select on aimsmgr.stvterm_ext to tabmgr;
grant select on aimsmgr.stvlevl_ext to tabmgr;
grant select on aimsmgr.stvstyp_ext to tabmgr;
grant select on aimsmgr.stvterm_ext to iamgr;
grant select on aimsmgr.shbgapp to tabmgr;
grant select on aimsmgr.shrdgmr to tabmgr;
grant select on aimsmgr.TBRACCD to tabmgr;
grant select on aimsmgr.TBBDETC to tabmgr;
grant select on aimsmgr.SGRSATT to tabmgr;
grant select on aimsmgr.STVATTS to tabmgr;
grant select on aimsmgr.stvdegc to IAMGR;
grant select on aimsmgr.stvrsts to tabmgr;
grant select on aimsmgr.stvrsts_ext to tabmgr;
grant select on aimsmgr.sfrstcr_ext to tabmgr;
grant select on aimsmgr.stvsbgi_ext to ugmgr;
grant select on aimsmgr.sobsbgi_ext to ugmgr;
grant select on RCRAPP3_EXT to iamgr;
grant select on RCRAPP4_EXT to iamgr;
grant select on FA_APPLICANT to iamgr;
grant select on FA_AWARD_BY_AIDY to iamgr;
grant select on FA_AWARD_BY_TERM to iamgr;
grant select on sirasgn_ext to iamgr;
grant select on slbrmap_ext to adironmgr;
grant select on aimsmgr.stvmajr_ext to tabmgr;
grant select on aimsmgr.CHECK_ADMISSIONS_APPLICANT to regmgr;
grant select on aimsmgr.CHECK_DEMOGRAPHIC to regmgr;
grant select on aimsmgr.CHECK_STUDENT_DATA to regmgr;
grant select on aimsmgr.stvterm_ext to regmgr;
grant select on um_finaid_by_term to iamgr;


/*******************************************************************************
Datamart Grants for AA Business Objects Schema: bo_flint_student 
*******************************************************************************/
grant select on td_admissions_applicant to bo_flint_student;
grant select on td_catalog_schedule to bo_flint_student;
grant select on td_course_credits to bo_flint_student;
grant select on td_demographic to bo_flint_student;
grant select on td_registration_detail to bo_flint_student;

grant select on td_student_data to bo_flint_student;
grant select on td_term_summary to bo_flint_student;
grant select on td_test_score to bo_flint_student;

grant select on um_admissions_applicant to bo_flint_student;
grant select on um_admissions_daily to bo_flint_student;
grant select on um_catalog to bo_flint_student;
grant select on um_catalog_schedule to bo_flint_student;
grant select on um_current_term to bo_flint_student; --
grant select on um_degree to bo_flint_student;
grant select on um_demographic to bo_flint_student;
revoke select on um_finaid_by_term to bo_flint_student;
grant select on um_reg_course_daily to bo_flint_student;
grant select on um_reg_course_section_daily to bo_flint_student;
grant select on um_reg_student_daily to bo_flint_student;
grant select on um_reg_student_daily_ia to bo_flint_student;
grant select on um_registration_detail to bo_flint_student;
grant select on um_student_data to bo_flint_student;
grant select on um_student_transcript to bo_flint_student;
grant select on um_test_score to bo_flint_student;

grant select on check_admissions_applicant to bo_flint_student;
grant select on check_demographic to bo_flint_student;
grant select on check_student_data to bo_flint_student;

grant select on DM_LOAD to bo_flint_student;
grant select on DM_EXTR to bo_flint_student;
grant select on DM_TRAN to bo_flint_student;
grant select on DM_EXTRACT_TABLE_NAMES to bo_flint_student;
GRANT EXECUTE ON aimsmgr.ZDMCLEN TO bo_flint_student;

/*******************************************************************************
Schema and Datamart Grants: CEPMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
CREATE USER cepmgr IDENTIFIED BY GoBlue070621;
GRANT CREATE TABLE TO cepmgr;
GRANT CREATE VIEW TO cepmgr;
ALTER USER cepmgr QUOTA 100M ON DEVELOPMENT;
GRANT UNLIMITED TABLESPACE TO cepmgr;
ALTER USER cepmgr DEFAULT ROLE ALL;
GRANT CREATE SESSION TO cepmgr;
GRANT CREATE PROCEDURE TO cepmgr;
GRANT CREATE ANY DIRECTORY TO cepmgr;
GRANT CREATE MATERIALIZED VIEW TO cepmgr;
alter user cepmgr profile topgun;
alter user cepmgr profile topgun;
alter user cepmgr profile topgun;

grant select on td_admissions_applicant to cepmgr;
grant select on td_catalog_schedule to cepmgr;
grant select on td_course_credits to cepmgr;
grant select on td_demographic to cepmgr;
grant select on td_registration_detail to cepmgr;
grant select on td_student_transcript to cepmgr;
grant select on td_student_data to cepmgr;
grant select on td_term_summary to cepmgr;
grant select on td_test_score to cepmgr;
grant select on um_admissions_applicant to cepmgr;
grant select on um_admissions_daily to cepmgr;
grant select on um_catalog to cepmgr;
grant select on um_catalog_schedule to cepmgr;
grant select on um_current_term to cepmgr; --
grant select on um_degree to cepmgr;
grant select on um_demographic to cepmgr;
revoke select on um_finaid_by_term to cepmgr;
grant select on um_reg_course_daily to cepmgr;
grant select on um_reg_course_section_daily to cepmgr;
grant select on um_reg_student_daily to cepmgr;
grant select on um_reg_student_daily_ia to cepmgr;
grant select on um_registration_detail to cepmgr;
grant select on um_student_data to cepmgr;
grant select on um_student_transcript to cepmgr;
grant select on um_test_score to cepmgr;


--count number of schemas in database
--Count Tables:
SELECT username, 
created, 
count(distinct dba_tables.table_name)tbl_count, 
count(distinct dba_views.view_name)view_count  
FROM all_users au
left join
dba_tables on  au.username = dba_tables.owner
left join
dba_views on au.username = dba_views.owner
where au.username like 'BO_FLINT_STUDENT'
group by 
username, 
created
ORDER BY 3 DESC
;
SELECT * FROM dba_tables
where owner like 'BO_FLINT_STUDENT'
--group by owner
ORDER BY 2 DESC
;
select * from USER_ROLE_PRIVS where USERNAME='BO_FLINT_STUDENT';
select * from USER_TAB_PRIVS where Grantee = 'BO_FLINT_STUDENT';
select * from USER_SYS_PRIVS where USERNAME = 'BO_FLINT_STUDENT';