select popsel."DEG_COUNT",popsel."PIDM",popsel."GRAD_TERM_CODE",popsel."GRAD_TERM_DESC",popsel."FIRST_NAME",popsel."LAST_NAME",popsel."FULL_NAME",popsel."HONORS_PROG_IND",popsel."GRAD_DATE",popsel."PRIMARY_COLLEGE_CODE",popsel."PRIMARY_COLLEGE_DESC",popsel."DEGREE_CODE",popsel."DEGREE_DESC",popsel."HONOR_ROLE",popsel."HONOR_ROLE_DESC",popsel."GRAD_STATUS",popsel."MULT_DEG_IND",popsel."CLIST_NAME" from(
with dp1 as (
select 
row_number() over(partition by dg.pidm, dg.grad_term_code order by dg.pidm desc) deg_count,
dg.pidm,
dg.grad_term_code,
dg.grad_term_desc,
dm.first_name,
dm.last_name,
case
when dm.middle_initial is null then dm.FIRST_NAME ||' '||dm.LAST_NAME
else dm.FIRST_NAME ||' '||dm.middle_initial||'. '||dm.LAST_NAME
end full_name,
decode (sd.honors_program,'HO','Y','JS','Y','N')Honors_Prog_Ind,
dg.GRAD_DATE,
sd.PRIMARY_COLLEGE_CODE, 
sd.PRIMARY_COLLEGE_DESC,
dg.degree_code,
dg.degree_desc,
case
when sd.report_level_code = 'UG' and sd.primary_college_code in ('HP','HS') and sd.overall_gpa >= 3.50 and (sd.OVERALL_HOURS_ATTEMPTED + sd.TERM_REGISTERED_HOURS) < 45 then '***'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.75 then '**'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.50 and sd.overall_gpa <= 3.74 then '*'
else ''
end Honor_Role,
case
when sd.report_level_code = 'UG' and sd.primary_college_code in ('HP','HS') and sd.overall_gpa >= 3.50 and (sd.OVERALL_HOURS_ATTEMPTED + sd.TERM_REGISTERED_HOURS) < 45 then 'Academic Distinction'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.75 then 'High Honors'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.50 and sd.overall_gpa <= 3.74 then 'Honors'
else ''
end Honor_Role_Desc,
dg.GRAD_STATUS
from UM_DEGREE dg
inner join um_student_data sd on sd.sd_pidm = dg.pidm
                           and sd.sd_term_code = dg.grad_term_code
inner join um_demographic dm on dm.dm_pidm = dg.pidm 
where dg.grad_term_code >= (select LAST_2_TERM from um_current_term)--202020
and dg.grad_status in ('GR','PG')
and (dg.grad_term_code like '%10'
or dg.grad_term_code like '%20'
or dg.grad_term_code like '%40'
)
),dp2 as(
select
'+' mult_deg_ind,
dp1.pidm,
dp1.grad_term_code
from dp1
where deg_count >1
),dp3 as(
select dp1.*, dp2.mult_deg_ind 
from dp1
left join dp2 on dp1.pidm = dp2.pidm
and dp1.grad_term_code = dp2.grad_term_code
)select dp3.*
,dp3.full_name||dp3.honor_role||mult_deg_ind clist_name
from dp3)popsel
;
