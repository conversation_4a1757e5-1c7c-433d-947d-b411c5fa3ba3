--------------------------------------------------------
--  File created - Thursday-May-19-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View DAP_PLANNOTE_DTL
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DWRKSMGR"."DAP_PLANNOTE_DTL" ("DAP_STU_ID", "DW_PIDM", "DW_UMID", "DAP_TERM", "DAP_NOTE_TEXT", "DAP_NOTE_SEQ", "DAP_PLAN_NUM", "UNIQUE_ID", "UNIQUE_KEY") AS 
  select "DAP_STU_ID",
  spriden.spriden_pidm dw_pidm,
  spriden.spriden_id dw_umid,
  "DAP_TERM","DAP_NOTE_TEXT","DAP_NOTE_SEQ","DAP_PLAN_NUM","UNIQUE_ID","UNIQUE_KEY"
from <EMAIL>
inner join aimsmgr.spriden on spriden.spriden_id = cast(trim(dap_stu_id) as varchar2(9))
                           and spriden.spriden_change_ind is null
;
