/*TMCI18N BEGIN HEADER*/
#if !defined( tmBundle_EXISTS )
#define TM_ON_STARTUP tmInitGlobS_shrdegv                                      
#include "tmcilib.h"
static struct TMBundle tmBundle = {"shrdegv.pc",NULL,NULL,NULL,NULL};
#define tmBundle_EXISTS
#endif
/*TMCI18N END HEADER*/

/* AUDIT_TRAIL_MSGKEY_UPDATE
-- PROJECT : MSGKEY
-- MODULE  : SHRDEGV
-- SOURCE  : enUS
-- TARGET  : I18N
-- DATE    : Thu May 19 10:01:25 2016
-- MSGSIGN : #4813a7de69816d1f
END AUDIT_TRAIL_MSGKEY_UPDATE */

/* AUDIT_TRAIL_TM63
-- TranMan 6.3 
-- PROJECT : HEGDE_I18N
-- MODULE  : SHRDEGV
-- SOURCE  : enUS
-- TARGET  : I18N
-- DATE    : Fri Jul 27 07:47:35 2007
END AUDIT_TRAIL_TM63 */
/******************************************************************************/
/* SHRDEGV.PC                                                                 */
/* COPYRIGHT..: Copyright 2001 - 2016 Ellucian Company L.P. and its affiliates.*/
/******************************************************************************/

/**************************************************************************************/
/* SHRDEGV.PC                                                                */
/*                                                                           */
/*  NSC Degree Extract Program with Error Reporting                          */
/* AUDIT TRAIL: 5.1.1                                        INIT    DATE    */
/* _______________________________________________________   ____  ________  */
/* 1. Produces three output files based on user input:        DET  01/30/01  */
/*    only one report can be run at a time.                                  */
/*                                                                           */
/*    a) Error Report - identifies students with missing                     */
/*       data or invalid data. shrdegv.lis                                   */
/*                                                                           */
/*    b) Degree data extract file.  shrdegv.lis                              */
/*                                                                           */
/*    c) Students Selected Listing. shrdegv.lis                              */
/*                                                                           */
/* 2. No provision exists for storing reported data                          */
/*                                                                           */
/* The detail file is created using a "$" as a field delimiter               */
/* columns that have multiple "$" assoicated with them represent fields      */
/* not reported by SCT. The following is a list of the columns not reported  */
/*       Previous last name                                                  */
/*       Previous first name                                                 */
/*       Joint Institution                                                   */
/*       Major course of study 3 and 4                                       */
/*       Minor course of study 3 and 4                                       */
/*       Major option 1 and 2                                                */
/*       Concentration 3                                                     */
/*       Major 3 and 4 cipc codes                                            */
/*       Minor 3 and 4 cipc codes                                            */
/*       Honors Program                                                      */
/*       Other Honors                                                        */
/*       Attendance from date                                                */
/*       Attendance to date                                                  */
/*       School Financial Block                                              */
/*       Institution submitting information (same as header record)          */
/*                                                                           */
/* AUDIT TRAIL: 5.4                                         MW 01/16/02      */
/* 1. Modified functions, read_line_count and seltitle, to clean up compile  */
/*    warning messages.  Changed user_pass to user_password in function,     */
/*    _rptopen1, in the parameter and within the function.                   */
/*    Changed the macros, getdata and process_record, by adding in a DEFAULT */
/*    option at the end ot the switch statement.                             */
/*                                                                           */
/* AUDIT TRAIL: 5.5                                                          */
/* 1. VPD Initiative - eliminate implicit inserts              CLF  06/20/02 */
/*                                                                           */
/* 2. GMW 07/03/2002							                                           */
/*    Defect 78627							                                             */
/*    Problem:  Need the ability to run for all terms.		            	     */
/*    Solution: Modify TERM on gjapdef validation from EQUAL to LIKE.	       */
/*              Modify select statements to be shrdgmr_term_code_grad LIKE   */
/*              not shrdgmr_term_code =.				                             */
/*              Modify printing on control report to print ALL terms when    */
/*              term is %.					                                  	     */
/*              Modify printing of error report to print TERMS when term is  */
/*              %. 							                                             */
/*                                                                           */
/* AUDIT TRAIL: 7.3.1					                                               */
/* 1. Defect 80572                                             CLF  06/27/06 */
/*    Problem: The SSN is NOT a required value. 		                  	     */
/*    Fix: The Clearinghouse actually allows reporting degrees without SSN's */
/*	and wants "NO SSN"   (padded with spaces to nine characters) in the      */
/*	SSN field in that case.					                                         */	
/*    Technical: At the beginning of proc cat_record, add the conditional    */
/*	to check for a value in the SSN field.  If there isn't an SSN, then      */
/*	place the requested wording (NO SSN)padded with spaces in the field      */
/*	prior to printing it.                                                    */
/*      This also means that SSN is no longer a Required field.  Eliminate   */
/*      code in proc process_record.                                         */
/*                                                                           */
/* 2. Defect 98903                                             CLF  06/27/06 */
/*    Problem: SHRDEGV is not reporting certificate programs correctly.      */
/*    Fix: STVACAT_CODE = 22 indicates a certificate program. But the SHRDEGV*/
/*	process repeatedly decodes the stvacat_code of 22 to an 'A', which       */
/*	indicates an Associate's degree.  It should be coded to a 'C'.           */
/*    Technical: Change '22','A', should be                  '22','C'        */
/*                                                                           */
/* AUDIT TRAIL: *******	                                                     */
/* 1. Defect 93057                                             ES   02/15/08 */
/*    Problem: Multiple Institution Honors results in multiple records, but  */
/*           in this condition there should be only one record per student.  */
/*    Solution: Removed the retrieval of the honors values from the main     */
/*           cursors and placed it into a separate cursor for processing.    */
/*           The first goes in honors field, the rest go in others field.    */ 
/*                                                                           */
/* AUDIT TRAIL : 8.0 (I18N)
   Description: Internationalization Unicode Conversion                      */
/* AUDIT TRAIL: 8.1                                                          */
/*  1. Correct length of time_var.  It's 9 char and the result is 12         */
/*   This is related to defect 1-3V7LWU   MAH   8/4/08                       */ 
/* AUDIT TRAIL: 8.2                                                          */
/* 1.  Ran Re-key Utility Tool                               AB 04/09/09     */
/* AUDIT TRAIL: 8.3                                                          */
/* 1. Added new job sub parameter and logic to allow      PL 08/21/2009      */
/*    exclusion of students based on their hold codes.                       */
/* 2. Changed cursors cursor_100, cursor_200 and cursor_300 PL 09/08/2009    */
/*    to retrieve curricula data from the concurrent curricula tables        */
/*    in place of the backfill columns.                                      */
/* 3. Modified the cursor cursor_lfos and cat_record(void)    RG 10/05/2009  */
/*    to select majr3, majr3cipc, majr4, majr4cipc, minr3, minr3cipc,        */
/*    minr4, minr4cipc.                                                       */
/* AUDIT TRAIL: *******                                                      */
/* 1. Defect 1-C11B4S                                    PL 07/28/2010       */
/*    Problem: SSN is Not Printing to File IBM AIX 5.3 only. Additionally    */
/*    formatting errors causing file to be rejected by the clearinghouse.    */
/*    Fix: In cat_record logic replace catstr with tmstrcat for processing   */
/*    ssn.                                                                   */
/* AUDIT TRAIL: 8.5.6                                                        */
/* 1. RPE 1-1ASTODR                                    RG 12/27/2012         */
/*    a. Include the new NSC value of T in the degree level reporting.       */
/*       Modified decode statement "decode(stvdegc_acat_code" to include new */
/*       value '41','T'.                                                     */ 
/*    b. Add a new parameter BANNERID param number 07.If Y then report the   */
/*       institutional/Banner ID for the student. If none exists or if the   */
/*       parameter is NULL, space fill.Created new function readbannerid_flag*/
/*       and variable bannerid_flag to store the value.                      */
/*    c. Fixed related defects 1-CG0DQN,1-BZX2UH,1-E0R6DC,1-18KQKPH.         */
/*                                                                           */
/* AUDIT TRAIL: 8.10.1.1                                                     */
/* 1. CR-000138943                                     RG 04/15/2016         */
/*    The DegreeVerify output file needs to be updated to the new format     */
/*    without $ delimiters.                                                  */
/*   Solution : Removed delimiter $ and also modified following methods to   */
/*              support new format, write_detail_header(),write_detail(),    */
/*              cat_record(),foot(). Modified variables lenghts.             */
/*              Modified process to print .dat file output when run in mode 3*/
/*              and fixed output will be printed in .dat file and .lis file  */
/*              will print report control information.                       */
/*                                                                           */
/* AUDIT TRAIL: 8.10.1.3                                                     */
/* 1.CR-000140066                                      AS 05/19/2016         */
/* SHRDEGV will not route the DAT file to a user job submission directory.   */
/* Solution: Introduced new function find_name_for and called it from        */
/*            _rptopen1.Also modified the main function.                     */
/* AUDIT TRAIL END                                                           */
/* ************************************************************************* */
#include "guarpfe.h"

EXEC SQL INCLUDE guaorac.c;


EXEC SQL BEGIN DECLARE SECTION;

  static TMCHAR  ask_degc_period[16]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   ask_degstat[3]={0}/*TMCI18N CHANGED FROM ""*/;       
  static TMCHAR   ask_rpt_sort[2]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   ask_term[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   ask_run_mode[2]={0}/*TMCI18N CHANGED FROM ""*/; 
  static TMCHAR   ask_hldd_code[3]={0}; 
  static TMCHAR   hldd_code[3]={0};
  static TMCHAR   bannerid_flag[2]={0};
  
  /*NSC 8.3*/
  static TMCHAR   majr_code[5]={0};
  static TMCHAR   minr_code[5]={0};
  static TMCHAR   conc_code[5]={0};
  static TMCHAR   row_num[5]={0};
  static TMCHAR   majr_code_1[5]={0};
  static TMCHAR   majr_code_2[5]={0};
  static TMCHAR   majr_code_3[5]={0};
  static TMCHAR   majr_code_4[5]={0};
  static TMCHAR   minr_code_1[5]={0};
  static TMCHAR   minr_code_2[5]={0};
  static TMCHAR   minr_code_3[5]={0};
  static TMCHAR   minr_code_4[5]={0};
  static TMCHAR   conc_code_1[5]={0};
  static TMCHAR   conc_code_2[5]={0};
  static TMCHAR   out_come[10]={0}/*TMCI18N CHANGED FROM ""*/;

  static TMCHAR  degc_period[81]={0}/*TMCI18N CHANGED FROM ""*/;  /* Degree period free format */
  static TMCHAR   degstat[3]={0}/*TMCI18N CHANGED FROM ""*/;      /* degree status codes to report  */
  static TMCHAR   fice[9]={0}/*TMCI18N CHANGED FROM ""*/;         /* fice */
  static TMCHAR   rptname[8]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   rpt_sort[2]={0}/*TMCI18N CHANGED FROM ""*/;     /* report sort order */
  static TMCHAR  sel_date[12]={0}/*TMCI18N CHANGED FROM ""*/;     /* report date */
  static TMCHAR   term[7]={0}/*TMCI18N CHANGED FROM ""*/;         /*grad term */
  static TMCHAR   run_mode[2]={0}/*TMCI18N CHANGED FROM ""*/;     /* run mode */


/* *** Job submission alteration */
  
  static BANNUMSTR(ask_one_up_no)={0}/*TMCI18N CHANGED FROM ""*/;
  static BANNUMSTR(parm_no)={0}/*TMCI18N CHANGED FROM ""*/;
  static BANNUMSTR(rpt_one_up_no)={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   rpt_optional_ind[2]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   valid_ind[2]={0}/*TMCI18N CHANGED FROM ""*/;

/* *** Gdatevalid variables */
  static TMCHAR  errmsg[31]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   errmsg1[4]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR  errmsg2[13]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR  errmsg3[12]={0}/*TMCI18N CHANGED FROM ""*/;
       
/* ***                */
/* Standard variables */

  static BANNUMSTR(count)={0}/*TMCI18N CHANGED FROM ""*/;
  short  int     lineno;   
  short  int     pageno;   

  static TMCHAR   errflag[2]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR  date[12]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR  institution[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static BANNUMSTR(linelimit)={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR  sessionid[31]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   time_var[12]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR  title_var[31]={0}/*TMCI18N CHANGED FROM ""*/;

/* *************************/
/* Application variables  */

  static TMCHAR    birth_date[9]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   college[51]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   colr_value[30]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   conc1[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   conc2[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   conc3[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr_option_1[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr_option_2[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   degree[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    degc_levl[2]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    dgmr_pidm[9]={0};
  static TMCHAR    dgmr_seq[3]={0};
  static TMCHAR    ferpa[2]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   fname[41]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    grad_date[9]={0}/*TMCI18N CHANGED FROM ""*/;
  static BANNUMSTR(hold_seq)={0};
  static TMCHAR   honr_desc[31]={0};
  static TMCHAR   honors[51]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   htext[3841]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   id[21]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   id_filler[21]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   degc_filler_1[61]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   degc_filler_2[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   filler_1[60]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   lname[41]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr1[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    majr1_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr2[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    majr2_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr1[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    minr1_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr2[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    minr2_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   mname[41]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   name_suffix[6]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   others[151]={0};
  static TMCHAR   ssn[10]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   stud_name[31]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    trans_date[9]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    valid_stat[2]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR    holds_found[2]={0};
  static TMCHAR    ttext[4]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_id[13]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_pname[293]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_degc[280]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_majr[481]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_minr[481]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_conc[521]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_option[321]={0}/*TMCI18N CHANGED FROM ""*/;
  
  static TMCHAR   prev_last_name[41]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   prev_first_name[41]={0}/*TMCI18N CHANGED FROM ""*/;
    
/*  static TMCHAR   file_cipc[78]={0} *TMCI18N CHANGED FROM ""* ;   *******  */
  static TMCHAR   file_cipc[1458]={0};
  static TMCHAR    valid_hldd[2]={0};
  static BANNUMSTR(lcur_seq)={0};
  static BANNUMSTR(pidm)={0};
  /* NSC8.3 */
  static TMCHAR   majr3[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr3_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr4[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr4_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr3[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr3_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr4[81]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr4_cipc[7]={0}/*TMCI18N CHANGED FROM ""*/;
  
  static TMCHAR   majr_filler[161]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   minr_filler[161]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   conc_filler[281]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   majr_option_filler[161]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_cipc_filler_1[21]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   file_cipc_filler_2[121]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   honors_filler_1[197]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   honors_program[51]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   honors_filler_2[101]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   end_filler[707]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   trailer_filler[3828]={0}/*TMCI18N CHANGED FROM ""*/;
  
  static TMCHAR   attendance_from_date[9]={0}/*TMCI18N CHANGED FROM ""*/;
  static TMCHAR   attendance_to_date[9]={0}/*TMCI18N CHANGED FROM ""*/;
  
  short  int     numlines;
  short  int     pagenumlen=4;
  short  int     page_pos;
  short  int     datetimelen=20;
  short  int     inst_len;
  short  int     inst_pos;
  short  int     rptlen=8;
  short  int     rptwid=80;
  short  int     rpt_pos;
  short  int     termlen=6;
  short  int     title_len;
  short  int     title_pos;

/* Shared indicator variables for SELECTs */

         short   Ind_01;
         short   Ind_02;
         short   Ind_03;
         short   Ind_04;
         short   Ind_05;
         short   Ind_06;
         short   Ind_07;
         short   Ind_08;
         short   Ind_09;
         short   Ind_10;
         short   Ind_11;
         short   Ind_12;
         short   Ind_13;
         short   Ind_14;
         short   Ind_15;
         short   Ind_16;
         short   Ind_17;
         short   Ind_18;
         short   Ind_19;
         short   Ind_20;
         short   Ind_21;
         short   Ind_22;
         short   Ind_23;
         short   Ind_24;
         short   Ind_25;
         short   Ind_26;
				 short   Ind_27;
         short   Ind_28;
EXEC SQL END DECLARE SECTION;


/* Local prototypes */
 
  static void cat_record(void);
  static int  honor_data(int mode);
  static int  read_line_count(int mode);
  static int  readterm(int mode);
  static int  readrunmode(int mode);        
  static int  readdegstat(int mode); 
  static int  readhldd(int mode);     
  static int  readsort(int mode);
  static int  readdegprd(int mode);      
  static int  sel_optional_ind (int mode); 
  static int  seldate(int mode);
  static int  selinst(int mode);
  static int  seltitle(int mode);
  static int  sel_fice(int mode);
  static int  validate_one_up_no(int mode);
  static int  valid_deg_stat(int mode); 
  static int  valid_hldd_code(int mode);  
  static int  check_holds(int mode);
   
  static void _rptopen1(TMCHAR *user_pass,int argc,TMCHAR *argv[]);
  static void asmb_honor(void);
  static void delcolr(void);      
  static void del_parms(void);             
  static void err_heading(void);
  static void foot(void);
  static void getdata (void);
  static void getsession(void); 
  static void inscolrdegstat (void);
  static void inscolrhldd (void);
  static void lis_heading(void);
  static void lockcolr(void);
  static void print_err_heading(void);
  static void print_lis_heading(void);
  static void process_record(void);         
  static void readcolr (void);
  static void readcolrhldd (void);
  static void retrieve_parms(void);
  static void write_detail(void);
  static void write_detail_header(void);
  static void write_lis(void);          
  static void write_err(void);
  static void print_foot(void);
  
  static int majr_records(int mode);/*8.3 NSC*/
  static int minr_records(int mode);/*8.3 NSC*/
  static int conc_records(int mode);/*8.3 NSC*/
  static void process_majr_records(void);/*8.3 NSC*/
  static void init_majrminr_parms(void);/*8.3 NSC*/
  static void find_name_for(TMCHAR *fullname);
  
  static int  readbannerid_flag(int mode);

/* Output file. */
  static UFILE *ofile;
  static UFILE *ofile_dat;

  int cnt,i,next_arg;
  /*TMCI18N CHANGED DECLARATION*/ /*TMCHAR *default_lis=_TMC("lis");*/ 
TMCHAR *TMCHARPTR_GLOB_DCL(default_lis,_TMC("lis"));
 /* default extension for output file */
  TMCHAR ofile_name[81];
  TMCHAR ofile_name_dat[81];

/* **************************************************************** */
int main(int argc, TMCHAR *argv[])
{
  extern short sqltrace_flag;                    /* sql_trace option is on */

  _rptopen1(user_pass,argc,argv);
  getxnam(*argv);
  login();                         /* allow user to log into the database */

  if (sqltrace_flag)
    EXEC SQL ALTER SESSION SET SQL_TRACE TRUE;

  tmstrcpy(rptname, _TMC("SHRDEGV"));
  sysdate(time_var,ORA_NLS_TIME_FORMAT);

  getsession();                       /* obtain the current session */
  lockcolr();
  delcolr();    
  EXEC SQL COMMIT;
  POSTORA;

  seldate(FIRST_ROW);   /* Obtain system date */
  sel_fice(FIRST_ROW);   /* Check for institution FICE code */
  if (!*fice )
        {
           tmprintf (&tmBundle, TM_NLS_Get("0000","Required Fice is missing, aborting job\n"));
           exit2os(EXIT_FAILURE);
           return EXIT_FAILURE;
        }

  /* Standard Ask */ 
  input (ask_one_up_no, TM_NLS_Get("0001","RUN SEQUENCE NUMBER: "), 26, NUM);
  if ( *ask_one_up_no ) 
    {
      tmstrcpy(rpt_one_up_no, ask_one_up_no);
      tmstrcpy(valid_ind, _TMC("N"));
      validate_one_up_no(FIRST_ROW);

      if (!tmstrcmp(valid_ind, _TMC("Y")) )
        {
          retrieve_parms();
          EXEC SQL COMMIT;
          POSTORA;
        }

      tmprintf(&tmBundle, TM_NLS_Get("0002","Parameter sequence number is invalid, aborting job\n"));
      exit2os(EXIT_FAILURE);
      return EXIT_FAILURE;
    }


  /* Prompt for term  */
  input (ask_term, TM_NLS_Get("0003","TERM: "),6,  ALPHA);
  if (*ask_term ) 
    {	  
      tmstrcpy(term, str2uc(ask_term));
    }
  else
    {
      tmstrcpy(parm_no, _TMC("01"));
      sel_optional_ind(FIRST_ROW);
      if ( !compare(rpt_optional_ind, _TMC("O"), EQS) ) 
        {
          tmprintf (&tmBundle, TM_NLS_Get("0004","Required parameter is missing, aborting job\n"));
          exit2os(EXIT_FAILURE);
          return EXIT_FAILURE;
        }
    }


  /* Prompt for Degree Status codes to report  */
  while (1)
    {
      input (ask_degstat, TM_NLS_Get("0005","DEGREE STATUS CODE: "),2,ALPHA);
      if ( *ask_degstat )
        {
          tmstrcpy(degstat,ask_degstat);
          tmstrcpy(valid_stat,_TMC("N"));
          valid_deg_stat(FIRST_ROW);
          if ( compare(valid_stat,_TMC("Y"),EQS) ) 
             {     
               inscolrdegstat();
             }
          else
             {
               tmprintf(&tmBundle, TM_NLS_Get("0006","Degree Status code is invalid\n"));
             }
        }
      else
        {
          break;
        }
    }

  EXEC SQL COMMIT;
  POSTORA;


  /* Prompt for Degree Period  -- 15 character free format */

  input (ask_degc_period, TM_NLS_Get("0007","DEGREE PERIOD: "),15,ALPHA);
  if ( *ask_degc_period )
    {
       tmstrcpy(degc_period,ask_degc_period);
    }
  else
    {
      tmstrcpy(parm_no, _TMC("03"));
      sel_optional_ind(FIRST_ROW);
      if ( !compare(rpt_optional_ind, _TMC("O"), EQS) ) 
        {
          tmprintf (&tmBundle, TM_NLS_Get("0008","Required parameter is missing, aborting job\n"));
          exit2os(EXIT_FAILURE);
          return EXIT_FAILURE;
        }
    }
         


  /*  Prompt for run mode */
  input (ask_run_mode,TM_NLS_Get("0009","RUN MODE [1] Missing/Invalid Data, [2] Student Reported, [3] EDI "),1,ALPHA);
  if ( !inlist(ask_run_mode,ALPHA,_TMC("1"),_TMC("2"),_TMC("3")) )
    {
      tmprintf (&tmBundle, TM_NLS_Get("0010","Required parameter is missing, aborting job\n"));
      exit2os(EXIT_FAILURE);
      return EXIT_FAILURE;
    }

  if (*ask_run_mode ) 
    {
      tmstrcpy(run_mode,ask_run_mode);
    }
  else
    {
      tmstrcpy(parm_no,_TMC("04"));
      sel_optional_ind(FIRST_ROW);
      if ( !compare(rpt_optional_ind,_TMC("O"),EQS) )
        {
          tmprintf (&tmBundle, TM_NLS_Get("0011","Required parameter is missing, aborting job\n"));
          exit2os(EXIT_FAILURE);
          return EXIT_FAILURE;
        }
    }


  /* Prompt for report sort order */
  input(ask_rpt_sort,TM_NLS_Get("0012","SORT BY [1] Last name [2] Level Last Name [3] Degree Last Name: "),1,ALPHA);
  if ( (!*ask_rpt_sort) || ( !inlist(ask_rpt_sort,ALPHA,_TMC("1"),_TMC("2"),_TMC("3")) ) )
     {
       tmstrcpy(rpt_sort,_TMC("1"));
     }
   else
      {
       tmstrcpy(rpt_sort,ask_rpt_sort);
      }
      
  /* Prompt for hold codes */
    while (1)
    {
	input (ask_hldd_code, TM_NLS_Get("0013","Hold Codes to be excluded: "),2,ALPHA);
    if (*ask_hldd_code ) 
        {
          tmstrcpy(hldd_code,ask_hldd_code);
          tmstrcpy(valid_hldd,_TMC("N"));
          valid_hldd_code(FIRST_ROW);
          if ( compare(valid_hldd,_TMC("Y"),EQS) ) 
             {     
               inscolrhldd();
             }
          else
             {
               tmprintf(&tmBundle, TM_NLS_Get("0014","Hold code is invalid\n"));
             }
        }
      else
        {
          break;
        }
    }

  EXEC SQL COMMIT;
  POSTORA;
      
   /* Prompt for BANNER ID Flag */
   input(bannerid_flag, TM_NLS_Get("0015","BANNER ID: "), 1, ALPHA);
   if (!*bannerid_flag)
   {
     tmstrcpy(bannerid_flag,_TMC("N"));
   }
   
   /* Prompt for line count */
   input(linelimit, TM_NLS_Get("0016","NUMBER OF LINES PRINTED PER PAGE [55]: "), 26, NUM);
   if (!*linelimit)
     { 
        tmstrcpy(linelimit,_TMC("55"));
     }

    
    if ( compare(run_mode,_TMC("3"),EQS) )
      {
        if (*ofile_name_dat )
        {
           ofile_dat=tmfopen(&tmBundle, ofile_name_dat, _TMC("w"));
        }
        else
        {
          tmstrcpy(ofile_name_dat,_TMC("shrdegv.dat"));
          ofile_dat=tmfopen(&tmBundle, ofile_name_dat, _TMC("w"));
        }
      }
    
    getdata();
        
    delcolr();        /*  delete this session recs from general table */
    EXEC SQL COMMIT;
    POSTORA;

    exit2os(EXIT_SUCCESS);
    return EXIT_SUCCESS;

}  /* main */
 


/* *** Job submission alteration */

/* Check for valid job sequence number */
static int validate_one_up_no(int mode)
{
  EXEC SQL DECLARE cursor_001 CURSOR FOR
        SELECT 'Y'
        FROM GJBPRUN
        WHERE GJBPRUN_JOB = :rptname
        AND   GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_001;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_001;
      POSTORA;
    }

  EXEC SQL FETCH cursor_001 INTO
       :valid_ind:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *valid_ind='\0';
      return FALSE;
    }

  return TRUE;
}



/* Retrieve mandatory/optional indicator for parameter */

static int sel_optional_ind(int mode)
{
  EXEC SQL DECLARE cursor_011 CURSOR FOR
        SELECT GJBPDEF_OPTIONAL_IND
        FROM GJBPDEF
        WHERE GJBPDEF_JOB = :rptname
        AND   GJBPDEF_NUMBER = TO_NUMBER(:parm_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_011;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_011;
      POSTORA;
    }

  EXEC SQL FETCH cursor_011 INTO
       :rpt_optional_ind:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *rpt_optional_ind='\0';
      return FALSE;
    }

  return TRUE;
}


/* Parameter Read macros for job submission */

/* Obtain the number of lines to be printed per page */
static int read_line_count(int mode)
{
   EXEC SQL DECLARE cursor_line_count CURSOR FOR
         SELECT GJBPRUN_VALUE
         FROM  GJBPRUN
         WHERE GJBPRUN_NUMBER = '99'
         AND   GJBPRUN_JOB = UPPER(:rptname)
         AND   GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_line_count;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_line_count;
      POSTORA;
    }

  EXEC SQL FETCH cursor_line_count INTO
       :linelimit:Ind_01;
  POSTORA;
                      
   if (NO_ROWS_FOUND)
     tmstrcpy(linelimit,_TMC("55"));

   return TRUE;
}


/*  Obtain the  degree TERM  */
static int readterm(int mode)
{
  EXEC SQL DECLARE cursor_002 CURSOR FOR
        SELECT GJBPRUN_VALUE
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '01'
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_002;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_002;
      POSTORA;
    }

  EXEC SQL FETCH cursor_002 INTO
       :term:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *term='\0';
      return FALSE;
    }

  return TRUE;
}


/*  Obtain the degree status include code     */
static int readdegstat(int mode)
{
  EXEC SQL DECLARE cursor_003 CURSOR FOR
        SELECT GJBPRUN_VALUE
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '02'
        AND    GJBPRUN_VALUE IS NOT NULL
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_003;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_003;
      POSTORA;
    }

  EXEC SQL FETCH cursor_003 INTO
       :degstat:Ind_02;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *degstat='\0';
      return FALSE;
    }

  return TRUE;
}

/*  Obtain the hold exclude code     */
static int readhldd(int mode)
{
  EXEC SQL DECLARE cursor_053 CURSOR FOR
        SELECT GJBPRUN_VALUE
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '06'
        AND    GJBPRUN_VALUE IS NOT NULL
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_053;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_053;
      POSTORA;
    }

  EXEC SQL FETCH cursor_053 INTO
       :hldd_code:Ind_02;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *hldd_code='\0';
      return FALSE;
    }

  return TRUE;
}

/*  Obtain the Banner ID Flag.     */
static int readbannerid_flag(int mode)
{
  EXEC SQL DECLARE cursor_bannerid_flag CURSOR FOR
        SELECT NVL(GJBPRUN_VALUE,'N')
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '07'        
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_bannerid_flag;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_bannerid_flag;
      POSTORA;
    }

  EXEC SQL FETCH cursor_bannerid_flag INTO
       :bannerid_flag:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      tmstrcpy(bannerid_flag,_TMC("N"));
      return FALSE;
    }

  return TRUE;
}


/*  Obtain the degree period     */
static int readdegprd(int mode)
{
  EXEC SQL DECLARE cursor_004 CURSOR FOR
        SELECT GJBPRUN_VALUE
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '03'
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_004;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_004;
      POSTORA;
    }

  EXEC SQL FETCH cursor_004 INTO
       :degc_period:Ind_03;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *degc_period='\0';
      return FALSE;
    }

  return TRUE;
}

/*  Obtain the run mode indicator     */
static int readrunmode(int mode)
{
  EXEC SQL DECLARE cursor_005 CURSOR FOR
        SELECT GJBPRUN_VALUE
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '04'
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_005;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_005;
      POSTORA;
    }

  EXEC SQL FETCH cursor_005 INTO
       :run_mode:Ind_03;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *run_mode='\0';
      return FALSE;
    }

  return TRUE;
}



/*  Obtain the report sort order     */
static int readsort(int mode)
{
  EXEC SQL DECLARE cursor_007 CURSOR FOR
        SELECT GJBPRUN_VALUE
        FROM   GJBPRUN
        WHERE  GJBPRUN_NUMBER = '05'
        AND    GJBPRUN_JOB = UPPER(:rptname)
        AND GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_007;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_007;
      POSTORA;
    }

  EXEC SQL FETCH cursor_007 INTO
      :rpt_sort:Ind_05;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      tmstrcpy(rpt_sort,_TMC("1"));
      return TRUE;
    }

  return TRUE;
}



/* Read collector records. */

static void readcolr(void)
{
   EXEC SQL DECLARE crs_collector CURSOR FOR
     SELECT SPRCOLR_VALUE_ATYP
     FROM   SPRCOLR
     WHERE  SPRCOLR_SESSIONID = :sessionid
     AND    SPRCOLR_TYPE = 'D'
     ORDER BY SPRCOLR_VALUE_ATYP;
   EXEC SQL OPEN crs_collector;
   POSTORA;
   while (1)
     {
       EXEC SQL FETCH crs_collector INTO :colr_value:Ind_01;
       POSTORA;
       if (NO_ROWS_FOUND)
         {
           *colr_value = '\0';
           break;
         }
         tmfprintf (&tmBundle, ofile,_TMC("{0}  "),colr_value);
     }
  EXEC SQL CLOSE crs_collector;
  POSTORA;
}
/* Read collector records. */

static void readcolrhldd(void)
{
   EXEC SQL DECLARE hldd_collector CURSOR FOR
     SELECT SPRCOLR_VALUE_ATYP
     FROM   SPRCOLR
     WHERE  SPRCOLR_SESSIONID = :sessionid
     AND    SPRCOLR_TYPE = 'H'
     ORDER BY SPRCOLR_VALUE_ATYP;
   EXEC SQL OPEN hldd_collector;
   POSTORA;
   while (1)
     {
       EXEC SQL FETCH hldd_collector INTO :colr_value:Ind_01;
       POSTORA;
       if (NO_ROWS_FOUND)
         {
           *colr_value = '\0';
           break;
         }
         tmfprintf (&tmBundle, ofile,_TMC("{0}  "),colr_value);
     }
  EXEC SQL CLOSE hldd_collector;
  POSTORA;
}

/* Delete parameter table records */
static void del_parms(void)
{

  EXEC SQL
        DELETE FROM GJBPRUN
        WHERE GJBPRUN_JOB = UPPER(:rptname)
        AND   GJBPRUN_ONE_UP_NO = TO_NUMBER(:rpt_one_up_no);
  POSTORA;
}


/* Standard select macros */
/*                        */
/* Get current date       */

static int seldate(int mode)
{
  EXEC SQL DECLARE cursor_008 CURSOR FOR
        SELECT SYSDATE,
               TO_CHAR(SYSDATE,G$_DATE.GET_NLS_DATE_FORMAT),
               TO_CHAR(SYSDATE,'YYYYMMDD')
        FROM   DUAL;

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_008;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_008;
      POSTORA;
    }

  EXEC SQL FETCH cursor_008 INTO
       :date:Ind_01,
       :sel_date:Ind_02,
       :trans_date:Ind_03;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *date='\0';
      *sel_date='\0';
      *trans_date='\0';
      return FALSE;
    }

  return TRUE;
}


/* Get institution name from institution table */

static int selinst(int mode)
{
  EXEC SQL DECLARE cursor_009 CURSOR FOR
        SELECT LTRIM(RTRIM(GUBINST_NAME,' '),' '),
           LENGTH(LTRIM(RTRIM(GUBINST_NAME,' '),' '))
        FROM   GUBINST;

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_009;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_009;
      POSTORA;
    }

  EXEC SQL FETCH cursor_009 INTO
       :institution:Ind_01,
       :inst_len:Ind_02;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *institution='\0';
      return FALSE;
    }

  inst_pos = ((rptwid/2) + (inst_len/2) - datetimelen);
  page_pos = (rptwid - datetimelen - inst_pos - pagenumlen);

  return TRUE;
}

/* Get title of the report */

static int seltitle(int mode)
{
  EXEC SQL DECLARE cursor_title CURSOR FOR
    SELECT LTRIM(RTRIM(GJBJOBS_TITLE,' '),' '),
           LENGTH(LTRIM(RTRIM(GJBJOBS_TITLE,' '),' '))
    FROM  GJBJOBS
    WHERE GJBJOBS_NAME = UPPER(:rptname);

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_title;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_title;
      POSTORA;
    }

  EXEC SQL FETCH cursor_title INTO
       :title_var:Ind_01,
       :title_len:Ind_02;
  POSTORA;

  if (NO_ROWS_FOUND)
    {
      *title_var = '\0';
      return FALSE;
    }

  title_pos = ((rptwid/2) + (title_len/2) - termlen);
  rpt_pos = (rptwid - title_pos - termlen);

  return TRUE;
}



/* Parameter Read Macros */

/* Get session id */

static void getsession(void)
{
  EXEC SQL
        SELECT USERENV('SESSIONID')
	INTO :sessionid
	FROM DUAL;
  POSTORA;
}


/* Delete collector records */
static void delcolr(void)                
{
  EXEC SQL
       DELETE FROM SPRCOLR
        WHERE SPRCOLR_JOB = :rptname
          AND SPRCOLR_SESSIONID = :sessionid;
  POSTORA;
}




/* Procedural macros             */
/* *** Job submission control macro */
/*                               */

static void retrieve_parms(void)
{
  readterm(FIRST_ROW);
        
  readdegstat(FIRST_ROW);     /* Insert degree status included into table */
  if (*degstat)
    {
       tmstrcpy(valid_stat,_TMC("N"));
       valid_deg_stat(FIRST_ROW);
       if ( compare(valid_stat,_TMC("Y"),EQS) ) 
           {     
             inscolrdegstat();
           }
       else
           {
             tmprintf(&tmBundle, TM_NLS_Get("0017","Degree Status code is invalid\n"));
           }
     }
  while (*degstat)
    {
      readdegstat(NEXT_ROW);     /* Insert degree status included into table */
      if (!*degstat)
          {
           break;
          }
      else
          {
            tmstrcpy(valid_stat,_TMC("N"));
            valid_deg_stat(FIRST_ROW);
            if ( compare(valid_stat,_TMC("Y"),EQS) ) 
               {     
                 inscolrdegstat();
               }
            else
               {
                 tmprintf(&tmBundle, TM_NLS_Get("0018","Degree Status code is invalid\n"));
               }
            
          }
    }

  EXEC SQL COMMIT;
  POSTORA;

  readdegprd(FIRST_ROW);

  readrunmode(FIRST_ROW);


  readsort(FIRST_ROW);
  if (!*rpt_sort)
     {
       tmstrcpy(rpt_sort,_TMC("1"));
     }

  readhldd(FIRST_ROW);     /* Insert hold code included into table */
  if (*hldd_code)
    {
       tmstrcpy(valid_hldd,_TMC("N"));
       valid_hldd_code(FIRST_ROW);
       if ( compare(valid_hldd,_TMC("Y"),EQS) ) 
           {     
             inscolrhldd();
           }
       else
           {
             tmprintf(&tmBundle, TM_NLS_Get("0019","Hold code is invalid\n"));
           }
     }
  while (*hldd_code)
    {
      readhldd(NEXT_ROW);     /* Insert degree status included into table */
      if (!*hldd_code)
          {
           break;
          }
      else
          {
            tmstrcpy(valid_hldd,_TMC("N"));
            valid_hldd_code(FIRST_ROW);
            if ( compare(valid_hldd,_TMC("Y"),EQS) ) 
               {     
                 inscolrhldd();
               }
            else
               {
                 tmprintf(&tmBundle, TM_NLS_Get("0020","Hold code is invalid\n"));
               }
            
          }
    }

  EXEC SQL COMMIT;
  POSTORA;
  
  readbannerid_flag(FIRST_ROW);
  read_line_count(FIRST_ROW);
  
  if ( compare(run_mode,_TMC("3"),EQS) )
      {
        if (*ofile_name_dat )
        {
           ofile_dat=tmfopen(&tmBundle, ofile_name_dat, _TMC("w"));
        }
        else
        {
          tmstrcpy(ofile_name_dat,_TMC("shrdegv.dat"));
          ofile_dat=tmfopen(&tmBundle, ofile_name_dat, _TMC("w"));
        }
      }

  getdata();

  del_parms();
  delcolr();    
  EXEC SQL COMMIT;
  POSTORA;
  exit2os(EXIT_SUCCESS);
}


/* Lock collector table */

static void lockcolr(void)
{
  EXEC SQL
       LOCK TABLE SPRCOLR IN SHARE UPDATE MODE;
  POSTORA;
}

/* Retrieve  FICE code */

static int sel_fice(int mode)
{
  EXEC SQL DECLARE cursor_019 CURSOR FOR
        SELECT SHBCGPA_INST_FICE    
        FROM   SHBCGPA;

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_019;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_019;
      POSTORA;
    }

  EXEC SQL FETCH cursor_019 INTO
       :fice:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND ) 
    {
      *fice='\0';
      return FALSE;
    }

  return TRUE;
}

   /* validate the degree status codes entered */
static int valid_deg_stat(int mode)
{
   EXEC SQL DECLARE cursor_020 CURSOR FOR
           SELECT 'Y'
            FROM stvdegs
           WHERE stvdegs_code = :degstat;
   POSTORA;

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_020;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_020;
      POSTORA;
    }

  EXEC SQL FETCH cursor_020 INTO
       :valid_stat:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *valid_stat='\0';
      return FALSE;
    }

  return TRUE;
}
   /* validate the hold codes entered */
static int valid_hldd_code(int mode)
{
   EXEC SQL DECLARE cursor_050 CURSOR FOR
           SELECT 'Y'
            FROM stvhldd
           WHERE stvhldd_code = :hldd_code;
   POSTORA;

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_050;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_050;
      POSTORA;
    }

  EXEC SQL FETCH cursor_050 INTO
       :valid_hldd:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *valid_hldd='\0';
      return FALSE;
    }

  return TRUE;
}
   /* examine student for holds that exclude reporting */
static int check_holds(int mode)
{
   EXEC SQL DECLARE cursor_063 CURSOR FOR
           SELECT 'Y'
            FROM sprhold
           WHERE sprhold_pidm = :dgmr_pidm
           AND	 TRUNC(SYSDATE) >= TRUNC(SPRHOLD_FROM_DATE) 
           AND   TRUNC(SYSDATE) <  TRUNC(SPRHOLD_TO_DATE)
           AND   sprhold_hldd_code IN
           (select substr(sprcolr_value_atyp,1,2)
                          from sprcolr
                          where sprcolr_sessionid = :sessionid
                          and   sprcolr_type = 'H');
           
           
   POSTORA;

  if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_063;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_063;
      POSTORA;
    }

  EXEC SQL FETCH cursor_063 INTO
       :holds_found:Ind_01;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *holds_found='\0';
      return FALSE;
    }

  return TRUE;
}
/* Insert degree status include collector record */
static void inscolrdegstat(void)
{

  EXEC SQL
	INSERT INTO SPRCOLR 
          (sprcolr_sessionid, sprcolr_type, sprcolr_value_date,
           sprcolr_value_atyp, sprcolr_activity_date,
           sprcolr_job, sprcolr_parm_number) 
       VALUES
           (:sessionid,'D',TO_DATE(:sel_date,G$_DATE.GET_NLS_DATE_FORMAT),
            UPPER(:degstat),SYSDATE,'SHRDEGV','');
  POSTORA;
}

/* Insert hold code exclude collector record */
static void inscolrhldd(void)
{

  EXEC SQL
	INSERT INTO SPRCOLR 
          (sprcolr_sessionid, sprcolr_type, sprcolr_value_date,
           sprcolr_value_atyp, sprcolr_activity_date,
           sprcolr_job, sprcolr_parm_number) 
       VALUES
           (:sessionid,'H',TO_DATE(:sel_date,G$_DATE.GET_NLS_DATE_FORMAT),
            UPPER(:hldd_code),SYSDATE,'SHRDEGV','');
  POSTORA;
}

/* Application related functions  */

static void getdata(void)    /* process data */
{
        
        EXEC SQL /*NSC 8.3*/
        				SELECT sb_curriculum_str.f_outcome 
        				INTO :out_come	
        				FROM DUAL;    	
				POSTORA; 
        
        /* Convert the number of lines per page to type int */
        EXEC SQL
                SELECT TO_NUMBER(:linelimit)
                INTO :numlines
                FROM DUAL;
        POSTORA;

        lineno = 60;
        pageno = 0;
        tmstrcpy(count,_TMC("0"));

/* Macros which need to be executed one time per program execution */

    selinst(FIRST_ROW);    /* Select institution name */
    seltitle(FIRST_ROW);   /* Select title of report */

    /* Set the headings for the report*/
   switch (*run_mode)
      {
        case '3':
            write_detail_header();
            break;

        case '1':
            err_heading();
            break;

        case '2':
            lis_heading();
            break;
      default:
        break;
       }    
  /*  Case selection criteria driven from the sort option */		

  switch (*rpt_sort)
    {
      case '1':                                     /* sorted by last name */
            EXEC SQL DECLARE cursor_100 CURSOR FOR
/* 5.5 Defect 78627
                select spbpers_ssn,*/
                select spbpers_ssn,
                       SUBSTR(spriden_first_name,1,40) spriden_first_name,
                       SUBSTR(spriden_mi,1,40)         spriden_mi,
                       SUBSTR(spriden_last_name,1,40)  spriden_last_name,
                       SUBSTR(spbpers_name_suffix,1,5) spbpers_name_suffix,
                       to_char(spbpers_birth_date,'YYYYMMDD'),
                       decode(stvdegc_acat_code,'21','C',
                                                '22','C',
                                                '23','A',
                                                '24','B',
                                                '31','P',
                                                '32','P',
                                                '41','T',
                                                '42','M',
                                                '43','C',
                                                '44','D',
                                                '45','R'),
                       stvdegc_desc,
                       stvcoll_desc,
                       to_char(shrdgmr_grad_date,'YYYYMMDD'),
                       nvl(spbpers_confid_ind,'N'),
                       spriden_id,
                       rpad(substr(spriden_last_name||', '||spriden_first_name||' '||substr(spriden_mi,1,1),1,30),30,' '),
                       shrdgmr_pidm,
                       shrdgmr_seq_no,
                       sovlcur_seqno ,
                       spriden_pidm
                  from stvcoll,
                       stvdegc,
                       sovlcur,
                       spbpers,
                       spriden,
                       shrdgmr
                 where spriden_change_ind is null
                 and   spriden_entity_ind = 'P'
                 and   spriden_pidm = spbpers_pidm(+)
                 and   spriden_pidm = shrdgmr_pidm  
		             and   shrdgmr_term_code_grad LIKE :term
                 and   shrdgmr_degs_code in
                          (select nvl(substr(sprcolr_value_atyp,1,2),'XX')
                           from sprcolr
                           where sprcolr_sessionid = :sessionid
                           and   sprcolr_type = 'D')
                 and   sovlcur_degc_code = stvdegc_code
                 and   sovlcur_coll_code = stvcoll_code
                 and   sovlcur_pidm = shrdgmr_pidm
                 and   sovlcur_key_seqno = shrdgmr_seq_no 
                 and   sovlcur_lmod_code = :out_come
                 and   sovlcur_current_ind = 'Y' and sovlcur_active_ind = 'Y' 
                 and   sovlcur_priority_no = 
                      ( select min(m.sovlcur_priority_no)
                        from sovlcur m
                        where m.sovlcur_pidm = sovlcur.sovlcur_pidm
                        and  m.sovlcur_key_seqno = sovlcur.sovlcur_key_seqno 
                        and m.sovlcur_lmod_code = sovlcur.sovlcur_lmod_code
                        and m.sovlcur_current_ind = 'Y' and m.sovlcur_active_ind = 'Y' )                  
                 order by spriden_last_name, spriden_first_name,spriden_mi;
            
            EXEC SQL OPEN cursor_100;
            POSTORA;
          
            while (1)
                 {
                   EXEC SQL FETCH cursor_100 INTO
                           :ssn:Ind_01,
                           :fname:Ind_02,
                           :mname:Ind_03,
                           :lname:Ind_04,
                           :name_suffix:Ind_05,
                           :birth_date:Ind_06,
                           :degc_levl:Ind_07,
                           :degree:Ind_08,
                           :college:Ind_09,
                           :grad_date:Ind_10,
                           :ferpa:Ind_22,
                           :id:Ind_23,
                           :stud_name:Ind_24,
                           :dgmr_pidm:Ind_25,
                           :dgmr_seq:Ind_26,
                           :lcur_seq:Ind_27,
                           :pidm:Ind_28;
                   POSTORA;
                                       
                    
                   if (NO_ROWS_FOUND)
	             {
	               *ssn='\0';
	               *fname='\0';
                       *mname='\0';
                       *lname='\0';          
	               *name_suffix='\0';          
                       *birth_date='\0'; 
	               *degc_levl='\0';          
	               *degree='\0';          
	               *college='\0';          
                       *grad_date='\0';
                       *ferpa='\0';
                       *id='\0';
                       *stud_name='\0';   
                       *dgmr_pidm='\0';
                       *dgmr_seq='\0';
                       *lcur_seq='\0'; 
                       *pidm='\0';
                       break;           
         	     }
                   
                   
                   process_majr_records();
                   process_record();
                 }         
            break;
      case '2':                         /* sort by degree level indicator and last name */
            EXEC SQL DECLARE cursor_200 CURSOR FOR
/* 5.5 Defect 78627
                select spbpers_ssn,*/
                select spbpers_ssn,
                       SUBSTR(spriden_first_name,1,40)   spriden_first_name,
                       SUBSTR(spriden_mi,1,40)           spriden_mi,
                       SUBSTR(spriden_last_name,1,40)    spriden_last_name,
                       SUBSTR(spbpers_name_suffix,1,5)   spbpers_name_suffix,
                       to_char(spbpers_birth_date,'YYYYMMDD'),
                       decode(stvdegc_acat_code,'21','C',
                                                '22','C',
                                                '23','A',
                                                '24','B',
                                                '31','P',
                                                '32','P',
                                                '41','T',
                                                '42','M',
                                                '43','C',
                                                '44','D',
                                                '45','R'),
                       stvdegc_desc,
                       stvcoll_desc,
                       to_char(shrdgmr_grad_date,'YYYYMMDD'),
                       nvl(spbpers_confid_ind,'N'),
                       spriden_id,
                       rpad(substr(spriden_last_name||', '||spriden_first_name||' '||substr(spriden_mi,1,1),1,30),30,' '),
                       shrdgmr_pidm,
                       shrdgmr_seq_no,
                       sovlcur_seqno ,
                       spriden_pidm
                  from stvcoll,
                       stvdegc,
                       sovlcur,
                       spbpers,
                       spriden,
                       shrdgmr
                 where spriden_change_ind is null
                 and   spriden_entity_ind = 'P'
                 and   spriden_pidm = spbpers_pidm(+)
                 and   spriden_pidm = shrdgmr_pidm  
		             and   shrdgmr_term_code_grad LIKE :term
                 and   shrdgmr_degs_code in
                          (select nvl(substr(sprcolr_value_atyp,1,2),'XX')
                           from sprcolr
                           where sprcolr_sessionid = :sessionid
                           and   sprcolr_type = 'D')
                 and   sovlcur_degc_code = stvdegc_code
                 and   sovlcur_coll_code = stvcoll_code
                 and   sovlcur_pidm = shrdgmr_pidm
                 and   sovlcur_key_seqno = shrdgmr_seq_no 
                 and   sovlcur_lmod_code = :out_come
                 and   sovlcur_current_ind = 'Y' and sovlcur_active_ind = 'Y' 
                 and   sovlcur_priority_no = 
                      ( select min(m.sovlcur_priority_no)
                        from sovlcur m
                        where m.sovlcur_pidm = sovlcur.sovlcur_pidm
                        and  m.sovlcur_key_seqno = sovlcur.sovlcur_key_seqno 
                        and m.sovlcur_lmod_code = sovlcur.sovlcur_lmod_code
                        and m.sovlcur_current_ind = 'Y' and m.sovlcur_active_ind = 'Y' )
                 order by decode(stvdegc_acat_code,'21','C',
                                                   '22','C',
                                                   '23','A',
                                                   '24','B',
                                                   '31','P',
                                                   '32','P',
                                                   '41','T',
                                                   '42','M',
                                                   '43','C',
                                                   '44','D',
                                                   '45','R'),
                           spriden_last_name,
                           spriden_first_name,
                           spriden_mi;

            EXEC SQL OPEN cursor_200;
            POSTORA;
          
            while (1)
                 {
                   EXEC SQL FETCH cursor_200 INTO
                           :ssn:Ind_01,
                           :fname:Ind_02,
                           :mname:Ind_03,
                           :lname:Ind_04,
                           :name_suffix:Ind_05,
                           :birth_date:Ind_06,
                           :degc_levl:Ind_07,
                           :degree:Ind_08,
                           :college:Ind_09,
                           :grad_date:Ind_10,
                           :ferpa:Ind_22,
                           :id:Ind_23,
                           :stud_name:Ind_24,
                           :dgmr_pidm:Ind_25,
                           :dgmr_seq:Ind_26,
                           :lcur_seq:Ind_27,
                           :pidm:Ind_28;
                   POSTORA;
                                       
                    
                   if (NO_ROWS_FOUND)
	             {
	               *ssn='\0';
	               *fname='\0';
                       *mname='\0';
                       *lname='\0';          
	               *name_suffix='\0';          
                       *birth_date='\0';
	               *degc_levl='\0';          
	               *degree='\0';          
	               *college='\0';
                       *grad_date='\0';          
                       *ferpa='\0';
                       *id='\0';
                       *stud_name='\0';
                       *dgmr_pidm='\0';
                       *dgmr_seq='\0';   
                       *dgmr_seq='\0';
                       *lcur_seq='\0'; 
                       *pidm='\0';                   
                       break;           
         	     }
                   
                   process_majr_records();
                   process_record();
                 }
            break;
      case '3':                            /* sort by degree and last name */
            EXEC SQL DECLARE cursor_300 CURSOR FOR
/* 5.5 Defect 78627
                select spbpers_ssn,*/
                select spbpers_ssn,
                       SUBSTR(spriden_first_name,1,40)   spriden_first_name,
                       SUBSTR(spriden_mi,1,40)           spriden_mi,
                       SUBSTR(spriden_last_name,1,40)    spriden_last_name,
                       SUBSTR(spbpers_name_suffix,1,5)   spbpers_name_suffix,
                       to_char(spbpers_birth_date,'YYYYMMDD'),
                       decode(stvdegc_acat_code,'21','C',
                                                '22','C',
                                                '23','A',
                                                '24','B',
                                                '31','P',
                                                '32','P',
                                                '41','T',
                                                '42','M',
                                                '43','C',
                                                '44','D',
                                                '45','R'),
                       stvdegc_desc,
                       stvcoll_desc,
                       to_char(shrdgmr_grad_date,'YYYYMMDD'),
                       nvl(spbpers_confid_ind,'N'),
                       spriden_id,
                       rpad(substr(spriden_last_name||', '||spriden_first_name||' '||substr(spriden_mi,1,1),1,30),30,' '),
                       shrdgmr_pidm,
                       shrdgmr_seq_no,
                       sovlcur_seqno ,
                       spriden_pidm
                  from stvcoll,
                       stvdegc,
                       sovlcur,
                       spbpers,
                       spriden,
                       shrdgmr
                 where spriden_change_ind is null
                 and   spriden_entity_ind = 'P'
                 and   spriden_pidm = spbpers_pidm(+)
                 and   spriden_pidm = shrdgmr_pidm  
		             and   shrdgmr_term_code_grad LIKE :term
                 and   shrdgmr_degs_code in
                          (select nvl(substr(sprcolr_value_atyp,1,2),'XX')
                           from sprcolr
                           where sprcolr_sessionid = :sessionid
                           and   sprcolr_type = 'D')
                 and   sovlcur_degc_code = stvdegc_code
                 and   sovlcur_coll_code = stvcoll_code
                 and   sovlcur_pidm = shrdgmr_pidm
                 and   sovlcur_key_seqno = shrdgmr_seq_no 
                 and   sovlcur_lmod_code = :out_come
                 and   sovlcur_current_ind = 'Y' and sovlcur_active_ind = 'Y' 
                 and   sovlcur_priority_no = 
                      ( select min(m.sovlcur_priority_no)
                        from sovlcur m
                        where m.sovlcur_pidm = sovlcur.sovlcur_pidm
                        and  m.sovlcur_key_seqno = sovlcur.sovlcur_key_seqno 
                        and m.sovlcur_lmod_code = sovlcur.sovlcur_lmod_code
                        and m.sovlcur_current_ind = 'Y' and m.sovlcur_active_ind = 'Y' )
                 order by shrdgmr_degc_code, spriden_last_name,
                          spriden_first_name, spriden_mi;

            EXEC SQL OPEN cursor_300;
            POSTORA;
          
            while (1)
                 {
                   EXEC SQL FETCH cursor_300 INTO
                           :ssn:Ind_01,
                           :fname:Ind_02,
                           :mname:Ind_03,
                           :lname:Ind_04,
                           :name_suffix:Ind_05,
                           :birth_date:Ind_06,
                           :degc_levl:Ind_07,
                           :degree:Ind_08,
                           :college:Ind_09,
                           :grad_date:Ind_10,
                           :ferpa:Ind_22,
                           :id:Ind_23,
                           :stud_name:Ind_24,
                           :dgmr_pidm:Ind_25,
                           :dgmr_seq:Ind_26,
                           :lcur_seq:Ind_27,
                           :pidm:Ind_28;
                   POSTORA;
                                       
                    
                   if (NO_ROWS_FOUND)
	             {
	               *ssn='\0';
	               *fname='\0';
                       *mname='\0';
                       *lname='\0';          
	               *name_suffix='\0';          
                       *birth_date='\0';
	               *degc_levl='\0';          
	               *degree='\0';          
	               *college='\0';
                       *grad_date='\0';          
                       *ferpa='\0';
                       *id='\0';
                       *stud_name='\0';
                       *dgmr_pidm='\0';
                       *dgmr_seq='\0';
                       *lcur_seq='\0'; 
                       *pidm='\0';
                       break;           
         	     }
                  
                   process_majr_records();
                   process_record();
                 }
            break;
      default:
            break;
    }
      foot();
}

static void process_majr_records(void)
{
	init_majrminr_parms();
	
	if ( compare(run_mode,_TMC("3"),EQS) )
	{
		 majr_records(FIRST_ROW);		/*To Fetch 1 Major Record*/
     if (*majr_code)
     {
     	tmstrcpy(majr_code_1,majr_code);
     }
     
     while (*majr_code)				 /*To Fetch 2,3,4 Major Record*/
     {
     		majr_records(NEXT_ROW);
     		if (!*majr_code)
	          {
	           break;
	          }
	      else
	      		{
	      		if ( compare(row_num,_TMC("2"),EQS) ) 
	      				tmstrcpy(majr_code_2,majr_code);
	      		else if ( compare(row_num,_TMC("3"),EQS) ) 
	      				tmstrcpy(majr_code_3,majr_code);
	      		else if ( compare(row_num,_TMC("4"),EQS) ) 
	      				tmstrcpy(majr_code_4,majr_code);
	      		else
	      				break;
	      		}
     }
     
     minr_records(FIRST_ROW);		/*To Fetch 1 Minor Record*/
     if (*minr_code)
     {
     	tmstrcpy(minr_code_1,minr_code);
     }
     
     while (*minr_code)					/*To Fetch 2,3,4 Minor Record*/
     {
     		minr_records(NEXT_ROW);
     		if (!*minr_code)
	          {
	           break;
	          }
	      else
	      		{
	      		if ( compare(row_num,_TMC("2"),EQS) ) 
	      				tmstrcpy(minr_code_2,minr_code);
	      		else if ( compare(row_num,_TMC("3"),EQS) )  
	      				tmstrcpy(minr_code_3,minr_code);
	      		else if ( compare(row_num,_TMC("4"),EQS) ) 
	      				tmstrcpy(minr_code_4,minr_code);
	      		else
	      				break;
	      		}
     }
     
     conc_records(FIRST_ROW);		/*To Fetch 1 Conc Record*/
     if (*conc_code)
     {
     	tmstrcpy(conc_code_1,conc_code);
     }
     
     while (*conc_code)					/*To Fetch 2 Conc Record*/
     {
     		conc_records(NEXT_ROW);
     		if (!*conc_code)
	          {
	           break;
	          }
	      else
	      		{
	      		if ( compare(row_num,_TMC("2"),EQS) ) 
	      				tmstrcpy(conc_code_2,conc_code);	      		
	      		else
	      				break;
	      		}
     }
	}
}

static void init_majrminr_parms(void)/*8.3 NSC*/
{
		*majr_code='\0';
		*minr_code='\0';
		*conc_code='\0';
		*row_num='\0';
		*majr_code_1='\0';
		*majr_code_2='\0';
		*majr_code_3='\0';
		*majr_code_4='\0';
		*minr_code_1='\0';
		*minr_code_2='\0';
		*minr_code_3='\0';
		*minr_code_4='\0';
		*conc_code_1='\0';
		*conc_code_2='\0';
		*majr1='\0';
	  *majr1_cipc='\0';
	  *majr2='\0';
	  *majr2_cipc='\0';
	  *minr1='\0';
	  *minr1_cipc='\0';
	  *minr2='\0';
	  *minr2_cipc='\0';
	  *conc1='\0';
	  *conc2='\0'; 
	  *majr3='\0';
	  *majr3_cipc='\0';
	  *majr4='\0';
	  *majr4_cipc='\0';
	  *minr3='\0';
	  *minr3_cipc='\0';
	  *minr4='\0';
	  *minr4_cipc='\0';
}

static int majr_records(int mode)	/*8.3 NSC*/
{	
        
    EXEC SQL DECLARE cursor_lfos CURSOR FOR
    SELECT lfos.majrcode,rownum
    FROM
			    (
			   	SELECT	sovlfos_majr_code majrcode
			    FROM 	  sovlfos   
			    WHERE 	sovlfos_pidm = :pidm 
			     AND 	  sovlfos_lcur_seqno = :lcur_seq 
			     AND	  sovlfos_current_ind = 'Y' 
			     AND	  sovlfos_active_ind = 'Y'
			     AND		sovlfos_lfst_code=sb_fieldofstudy_str.f_major        
			     ORDER BY DECODE(sovlfos_lfst_code,sb_fieldofstudy_str.f_major,'1',
										sb_fieldofstudy_str.f_minor,'2', 
										sb_fieldofstudy_str.f_concentration,'3','4'),
										sovlfos_priority_no, sovlfos_seqno desc
					 )lfos;
					 		
		 if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_lfos;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_lfos;
      POSTORA;
    }

  EXEC SQL FETCH cursor_lfos INTO
       :majr_code:Ind_01,
       :row_num:Ind_02;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *majr_code='\0';
      *row_num='\0';
      return FALSE;
    }

  return TRUE;    
    
}

static int minr_records(int mode)	/*8.3 NSC*/
{	
        
    EXEC SQL DECLARE cursor_lfos_minr CURSOR FOR
   	SELECT lfos.minrcode,rownum
		FROM
			(	
		   	SELECT	sovlfos_majr_code minrcode
		    FROM 	  sovlfos   
		    WHERE 	sovlfos_pidm = :pidm 
		     AND 	  sovlfos_lcur_seqno = :lcur_seq 
		     AND	  sovlfos_current_ind = 'Y' 
		     AND	  sovlfos_active_ind = 'Y'
		     AND		sovlfos_lfst_code=sb_fieldofstudy_str.f_minor     
		     ORDER BY DECODE(sovlfos_lfst_code,sb_fieldofstudy_str.f_major,'1',
									sb_fieldofstudy_str.f_minor,'2', 
									sb_fieldofstudy_str.f_concentration,'3','4'),
									sovlfos_priority_no, sovlfos_seqno desc
			)lfos;
							
		 if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_lfos_minr;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_lfos_minr;
      POSTORA;
    }

  EXEC SQL FETCH cursor_lfos_minr INTO
       :minr_code:Ind_01,
       :row_num:Ind_02;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *minr_code='\0';
      *row_num='\0';
      return FALSE;
    }

  return TRUE;    
    
}

static int conc_records(int mode)	/*8.3 NSC*/
{	
        
    EXEC SQL DECLARE cursor_lfos_conc CURSOR FOR
   	SELECT lfos.conccode,rownum
		FROM
			(	
			   	SELECT	sovlfos_majr_code conccode
			    FROM 	  sovlfos   
			    WHERE 	sovlfos_pidm = :pidm 
			     AND 	  sovlfos_lcur_seqno = :lcur_seq 
			     AND	  sovlfos_current_ind = 'Y' 
			     AND	  sovlfos_active_ind = 'Y'
			     AND		sovlfos_lfst_code=sb_fieldofstudy_str.f_concentration     
			     ORDER BY DECODE(sovlfos_lfst_code,sb_fieldofstudy_str.f_major,'1',
										sb_fieldofstudy_str.f_minor,'2', 
										sb_fieldofstudy_str.f_concentration,'3','4'),
										sovlfos_priority_no, sovlfos_seqno desc
			)lfos;
							
		 if ( mode==CLOSE_CURSOR )
    {
      EXEC SQL CLOSE cursor_lfos_conc;
      POSTORA;
      return TRUE;
    }

  if ( mode==FIRST_ROW )
    {
      EXEC SQL OPEN cursor_lfos_conc;
      POSTORA;
    }

  EXEC SQL FETCH cursor_lfos_conc INTO
       :conc_code:Ind_01,
       :row_num:Ind_02;
  POSTORA;

  if ( NO_ROWS_FOUND )
    {
      *conc_code='\0';
      *row_num='\0';
      return FALSE;
    }

  return TRUE;    
    
}

static void process_record(void)
{    
		if ( compare(run_mode,_TMC("3"),EQS) )
		{
    EXEC SQL DECLARE cursor_desc CURSOR FOR
   select 
		    shksels.f_stvmajr_value(:majr_code_1,'D')majr1,
		    shksels.f_stvmajr_value(:majr_code_1,'C')majr1cipc,
		    shksels.f_stvmajr_value(:majr_code_2,'D')majr2,
		    shksels.f_stvmajr_value(:majr_code_2,'C')majr2cipc,
		    shksels.f_stvmajr_value(:minr_code_1,'D')minr1,
		    shksels.f_stvmajr_value(:minr_code_1,'C')minr1cipc,
		    shksels.f_stvmajr_value(:minr_code_2,'D')minr2,
		    shksels.f_stvmajr_value(:minr_code_2,'C')minr2cipc,
		    shksels.f_stvmajr_value(:conc_code_1,'D')conc1,
		    shksels.f_stvmajr_value(:conc_code_2,'D')conc2,                         					                        	                        
		    shksels.f_stvmajr_value(:majr_code_3,'D')majr3,
		    shksels.f_stvmajr_value(:majr_code_3,'C')majr3cipc,
		    shksels.f_stvmajr_value(:majr_code_4,'D')majr4,
		    shksels.f_stvmajr_value(:majr_code_4,'C')majr4cipc,
		    shksels.f_stvmajr_value(:minr_code_3,'D')minr3,
		    shksels.f_stvmajr_value(:minr_code_3,'C')minr3cipc,
		    shksels.f_stvmajr_value(:minr_code_4,'D')minr4,	    
		    shksels.f_stvmajr_value(:minr_code_4,'C')minr4cipc
    from dual;
     
     EXEC SQL OPEN cursor_desc;
     POSTORA;
     EXEC SQL FETCH cursor_desc INTO
         :majr1:Ind_01,
         :majr1_cipc:Ind_02,
         :majr2:Ind_03,
         :majr2_cipc:Ind_04,
         :minr1:Ind_05,
         :minr1_cipc:Ind_06,
         :minr2:Ind_07,
         :minr2_cipc:Ind_08,
         :conc1:Ind_09,
         :conc2:Ind_10,         
         :majr3:Ind_11,
         :majr3_cipc:Ind_12,
         :majr4:Ind_13,
         :majr4_cipc:Ind_14,
         :minr3:Ind_15,
         :minr3_cipc:Ind_16,
         :minr4:Ind_17,
         :minr4_cipc:Ind_18;
         
      POSTORA;
                                       
      if (NO_ROWS_FOUND)
	             {   *majr1='\0';
                 *majr1_cipc='\0';
                 *majr2='\0';
                 *majr2_cipc='\0';
                 *minr1='\0';
                 *minr1_cipc='\0';
                 *minr2='\0';
                 *minr2_cipc='\0';
                 *conc1='\0';
                 *conc2='\0'; 
                 *majr3='\0';
                 *majr3_cipc='\0';
                 *majr4='\0';
                 *majr4_cipc='\0';
                 *minr3='\0';
                 *minr3_cipc='\0';
                 *minr4='\0';
                 *minr4_cipc='\0';
         	     }
     EXEC SQL CLOSE cursor_desc;
     POSTORA;
    }
    
    tmstrcpy(errmsg,_TMC(""));
    tmstrcpy(errmsg1,_TMC(""));
    tmstrcpy(errmsg2,_TMC(""));
    tmstrcpy(errmsg3,_TMC(""));
    tmstrcpy(errflag,_TMC("N"));

  /* Verify required values are not missing */
  /** SSN is not required.  Use NO SSN in EDI transmission.
   if ( (!*ssn) || (Ind_01 == -1 ) )
     {
       strcpy(errmsg1,"SSN");
       strcpy(errflag,"Y");
     }
	**/
	
   /* Defect 1-18KQKPH 
   if (!*birth_date)
     {
       if (!*errmsg1)
           tmstrcpy(errmsg2,TM_NLS_Get("0020","Birth Date"));
       else 
           tmstrcpy(errmsg2,TM_NLS_Get("0021",", Birth Date"));
       tmstrcpy(errflag,_TMC("Y"));
    }
   */
   
   if (!*grad_date)
     {
       if ( (!*errmsg1) && (!*errmsg2) )
           tmstrcpy(errmsg3,TM_NLS_Get("0021","Grad Date"));
       else 
           tmstrcpy(errmsg3,TM_NLS_Get("0022",", Grad Date"));
       tmstrcpy(errflag,_TMC("Y"));
     }


		if ( compare(errflag,_TMC("N"),EQS) )
		 {
		     check_holds(FIRST_ROW);
		     if (*holds_found)
		         {
		     		 tmstrcpy(errmsg2,TM_NLS_Get("0023",", Holds"));
		         tmstrcpy(errflag,_TMC("Y"));
		         }
		 }


   switch (*run_mode)
     {
       case '3':
           if ( compare(errflag,_TMC("N"),EQS) )
             {
               *honors='\0';
               *others='\0';
               tmstrcpy(hold_seq,_TMC("0"));
               report(honor_data,asmb_honor,NULL,NULL);
               cat_record();
               write_detail();
             }
           break;
       case '2':
           if ( compare(errflag,_TMC("N"),EQS) )         
             {
               write_lis();
             }
           break;
       case '1':
           if ( compare(errflag,_TMC("Y"),EQS) )
             {
               catstr(errmsg,30,errmsg1,errmsg2,errmsg3,NULL);
               write_err();
             }
           break;
      default:
       break;
      }
}

                  
static void write_lis(void)
{
  tmfprintf(&tmBundle, ofile,_TMC("{0,%14s}    {1} \n"),id, stud_name);
  add(count,count,_TMC("1"));
  lis_heading();
}

static void write_err(void)
{
   tmfprintf(&tmBundle, ofile,_TMC("{0,%14s}     {1}     {2}\n"), id, stud_name, errmsg);
   add(count,count,_TMC("1"));
   err_heading();
}

  
static void err_heading(void)
{
  if (lineno <= (numlines - 2))
    {
      lineno++;
    }
  else
    {
      lineno=6;
      pageno++;
      tmfprintf(&tmBundle, ofile,_TMC("\f"));
      print_err_heading();
    }
}

static void print_err_heading(void)
{
   tmfprintf(&tmBundle, ofile,_TMC("{0} {1,%*s}"), sel_date, inst_pos, institution);
   tmfprintf(&tmBundle, ofile, _TMC("{0,%*s} {1,number,integer}\n"), page_pos, TM_NLS_Get("0024","PAGE"), pageno);
/* 5.5 Defect 78627
   fprintf(ofile, "%s%*s%*s\n\n", term, title_pos, title_var,rpt_pos, rptname);*/
   if(compare(term,_TMC("%"),EQS))
   	tmfprintf(&tmBundle, ofile, TM_NLS_Get("0025","{0}{1,%*s}{2,%*s}\n\n"),TM_NLS_Get("0026","%TERMS"), title_pos, title_var,rpt_pos, rptname);
   else
   	tmfprintf(&tmBundle, ofile, _TMC("{0}{1,%*s}{2,%*s}\n\n"), term, title_pos, title_var,rpt_pos, rptname);
   tmfprintf(&tmBundle, ofile,_TMC("\n"));
   tmfprintf(&tmBundle, ofile,TM_NLS_Get("0027","                               Error Report\n"));
   tmfprintf(&tmBundle, ofile,_TMC("\n"));
   tmfprintf(&tmBundle, ofile,TM_NLS_Get("0028","     ID NUMBER      STUDENT NAME                       MISSING DATA\n"));
}

static void lis_heading(void)
{
  if (lineno <= (numlines - 2))
    {
      lineno++;
    }
  else
    {
      lineno=6;
      pageno++;
      tmfprintf(&tmBundle, ofile,_TMC("\f"));
      print_lis_heading();
    }   
}


static void print_lis_heading(void)
{
   tmfprintf(&tmBundle, ofile,_TMC("{0} {1,%*s}"), sel_date, inst_pos, institution);
   tmfprintf(&tmBundle, ofile, _TMC("{0,%*s} {1,number,integer}\n"), page_pos, TM_NLS_Get("0029","PAGE"), pageno);
   tmfprintf(&tmBundle, ofile, _TMC("{0}{1,%*s}{2,%*s}\n\n"), term, title_pos, title_var,rpt_pos, rptname);
   tmfprintf(&tmBundle, ofile,_TMC("\n"));
   tmfprintf(&tmBundle, ofile,TM_NLS_Get("0030","                              Selected Students\n"));
   tmfprintf(&tmBundle, ofile,_TMC("\n"));
   tmfprintf(&tmBundle, ofile,TM_NLS_Get("0031","     ID NUMBER    STUDENT NAME           \n"));
}


static int honor_data(int mode)
{
     EXEC SQL DECLARE cursor_h CURSOR FOR
            select stvhonr_desc
              from stvhonr,
                   shrdgih
             where shrdgih_pidm = :dgmr_pidm
               and shrdgih_dgmr_seq_no = :dgmr_seq
               and shrdgih_honr_code = stvhonr_code;

   if (mode == CLOSE_CURSOR)
     {
       EXEC SQL CLOSE cursor_h;
       POSTORA;
       return TRUE;
     }

   if (mode == FIRST_ROW)
     {
       EXEC SQL OPEN cursor_h;
       POSTORA;
     }
          
     EXEC SQL FETCH cursor_h INTO
           :honr_desc:Ind_21;

   if (NO_ROWS_FOUND)
     {
       EXEC SQL CLOSE cursor_h;   
       POSTORA;
       *honr_desc='\0';
       return FALSE;
     }

   return TRUE;
}

static void asmb_honor(void)
{
   if ( compare(hold_seq,_TMC("2"),EQ))
       tmstrcat(others,_TMC(","));
   else if ( compare(hold_seq,_TMC("3"),EQ))
       tmstrcat(others,_TMC(","));
   else if ( compare(hold_seq,_TMC("4"),EQ))
       tmstrcat(others,_TMC(","));
   else if ( compare(hold_seq,_TMC("5"),EQ))
      {
       tmstrcat(others,_TMC(","));
       substr(honr_desc,honr_desc,0,26);
      }

   if ( compare(hold_seq,_TMC("0"),EQ))
       tmstrcpy(honors,honr_desc);
   else if ( compare(hold_seq,_TMC("6"),LT))
       tmstrcat(others,honr_desc);

   add(hold_seq,hold_seq,_TMC("1"));
}

/* NSC8.3 */
static void cat_record(void)
{
   if (!*ssn)
	tmstrcpy(ssn,TM_NLS_Get("0032","NO SSN   "));

   EXEC SQL 
       SELECT RPAD(NVL(:ssn,' '),9),RPAD(NVL(:fname,' '),40),RPAD(NVL(:mname,' '),40),RPAD(NVL(:lname,' '),40),RPAD(NVL(:name_suffix,' '),5),
              RPAD(NVL(:prev_last_name,' '),40),RPAD(NVL(:prev_first_name,' '),40),RPAD(NVL(:birth_date,' '),8),RPAD(NVL(:id,' '),20),
              RPAD(NVL(:id_filler,' '),20),RPAD(NVL(:filler_1,' '),59),RPAD(NVL(:degc_levl,' '),1),RPAD(NVL(:degree,' '),80),RPAD(NVL(:college,' '),50),
              RPAD(NVL(:degc_filler_1,' '),60),RPAD(NVL(:grad_date,' '),8),RPAD(NVL(:degc_filler_2,' '),80),
              RPAD(NVL(:majr1,' '),80),RPAD(NVL(:majr2,' '),80),RPAD(NVL(:majr3,' '),80),RPAD(NVL(:majr4,' '),80),RPAD(NVL(:majr_filler,' '),160),
              RPAD(NVL(:minr1,' '),80),RPAD(NVL(:minr2,' '),80),RPAD(NVL(:minr3,' '),80),RPAD(NVL(:minr4,' '),80),RPAD(NVL(:minr_filler,' '),160),
              RPAD(NVL(:majr_option_1,' '),80),RPAD(NVL(:majr_option_2,' '),80),RPAD(NVL(:majr_option_filler,' '),160),
              RPAD(NVL(:conc1,' '),80),RPAD(NVL(:conc2,' '),80),RPAD(NVL(:conc3,' '),80),RPAD(NVL(:conc_filler,' '),280),
              RPAD(NVL(:majr1_cipc,' '),6),RPAD(NVL(:majr2_cipc,' '),6),RPAD(NVL(:majr3_cipc,' '),6),RPAD(NVL(:majr4_cipc,' '),6),
              RPAD(NVL(:file_cipc_filler_1,' '),20),
              RPAD(NVL(:minr1_cipc,' '),6),RPAD(NVL(:minr2_cipc,' '),6),RPAD(NVL(:minr3_cipc,' '),6),RPAD(NVL(:minr4_cipc,' '),6),
              RPAD(NVL(:file_cipc_filler_2,' '),120),
              RPAD(NVL(:honors,' '),50),RPAD(NVL(:honors_filler_1,' '),196),RPAD(NVL(:honors_program,' '),50),
              RPAD(NVL(:honors_filler_2,' '),100),RPAD(NVL(:others,' '),150),
              RPAD(NVL(:attendance_from_date,' '),8),RPAD(NVL(:attendance_to_date,' '),8),RPAD(NVL(:ferpa,' '),1),
              RPAD(NVL(:end_filler,' '),706)
         INTO :ssn,:fname,:mname,:lname,:name_suffix,
              :prev_last_name,:prev_first_name,:birth_date,:id,
              :id_filler,:filler_1,:degc_levl,:degree,:college,
              :degc_filler_1,:grad_date,:degc_filler_2,
              :majr1,:majr2,:majr3,:majr4,:majr_filler,
              :minr1,:minr2,:minr3,:minr4,:minr_filler,
              :majr_option_1,:majr_option_2,:majr_option_filler,
              :conc1,:conc2,:conc3,:conc_filler,
              :majr1_cipc,:majr2_cipc,:majr3_cipc,:majr4_cipc,
              :file_cipc_filler_1,
              :minr1_cipc,:minr2_cipc,:minr3_cipc,:minr4_cipc,
              :file_cipc_filler_2,
              :honors,:honors_filler_1,:honors_program,
              :honors_filler_2,:others,
              :attendance_from_date,:attendance_to_date,:ferpa,
              :end_filler
         FROM DUAL;
   POSTORA;
  
  *file_id='\0';
  
   tmstrcat(file_id,_TMC("DD1"));
   tmstrcat(file_id,ssn);      

   *file_pname='\0';
   
   tmstrcat(file_pname,fname);   
   tmstrcat(file_pname,mname);   
   tmstrcat(file_pname,lname);
   tmstrcat(file_pname,name_suffix);
   tmstrcat(file_pname,prev_last_name);
   tmstrcat(file_pname,prev_first_name);
   tmstrcat(file_pname,birth_date);
      
   if ( compare(bannerid_flag,_TMC("Y"),EQS))
   {
     tmstrcat(file_pname,id);     
   }
   else
   {
     tmstrcat(file_pname,id_filler);
   }
   
   tmstrcat(file_pname,filler_1);
   
   *file_degc='\0';   
   
   tmstrcat(file_degc,degc_levl); 
   tmstrcat(file_degc,degree); 
   tmstrcat(file_degc,college); 
   tmstrcat(file_degc,degc_filler_1); 
   tmstrcat(file_degc,grad_date); 
   tmstrcat(file_degc,degc_filler_2);          
   
   *file_majr='\0';
   
   tmstrcat(file_majr,majr1);
   tmstrcat(file_majr,majr2);
   tmstrcat(file_majr,majr3);
   tmstrcat(file_majr,majr4);   
   tmstrcat(file_majr,majr_filler);   
   
   *file_minr='\0';
   
   tmstrcat(file_minr,minr1);
   tmstrcat(file_minr,minr2);
   tmstrcat(file_minr,minr3);
   tmstrcat(file_minr,minr4);
   tmstrcat(file_minr,minr_filler);   
   
   *file_option='\0';
   
   tmstrcat(file_option,majr_option_1);
   tmstrcat(file_option,majr_option_2);      
   tmstrcat(file_option,majr_option_filler); 
   
   *file_conc='\0';
   
   tmstrcat(file_conc,conc1);
   tmstrcat(file_conc,conc2);      
   tmstrcat(file_conc,conc3); 
   tmstrcat(file_conc,conc_filler);      
   
   *file_cipc='\0';
   
   tmstrcat(file_cipc,majr1_cipc);
   tmstrcat(file_cipc,majr2_cipc);
   tmstrcat(file_cipc,majr3_cipc);
   tmstrcat(file_cipc,majr4_cipc);
   
   tmstrcat(file_cipc,file_cipc_filler_1);
   
   tmstrcat(file_cipc,minr1_cipc);
   tmstrcat(file_cipc,minr2_cipc);
   tmstrcat(file_cipc,minr3_cipc);
   tmstrcat(file_cipc,minr4_cipc);
   
   tmstrcat(file_cipc,file_cipc_filler_2);
   
   tmstrcat(file_cipc,honors);
   
   tmstrcat(file_cipc,honors_filler_1);
   
   tmstrcat(file_cipc,honors_program);  
   
   tmstrcat(file_cipc,honors_filler_2);
    
   tmstrcat(file_cipc,others);
   
   tmstrcat(file_cipc,attendance_from_date);
   tmstrcat(file_cipc,attendance_to_date);
   
   tmstrcat(file_cipc,ferpa);   
   
   tmstrcat(file_cipc,end_filler);   

}


static void write_detail(void)
{

    tmfprintf(&tmBundle, ofile_dat,_TMC("{0}{1}{2}{3}{4}{5}{6}{7}\n"),
              file_id,file_pname,file_degc,file_majr,file_minr,file_option,file_conc,file_cipc);

    add(count,count,_TMC("1"));
}


static void write_detail_header(void)
{
   EXEC SQL 
        SELECT 'DH1'||RPAD(NVL(:fice,' '),6)||'00'||RPAD(NVL(:institution,' '),80)||RPAD(' ',15)||
               'D'||RPAD(NVL(:trans_date,' '),8)||RPAD(NVL(:degc_period,' '),80)||RPAD(' ',3645)
          into :htext
          from dual;
   POSTORA;

   tmfprintf(&tmBundle, ofile_dat,TM_NLS_Get("0033","{0}\n"),
                   htext);
   add(count,count,_TMC("1"));
}


static void foot(void)
{
   if ( compare(run_mode,_TMC("3"),EQS) )
     {
        EXEC SQL
                SELECT 'DT1',RPAD(NVL(:trailer_filler,' '),3827)
                  INTO :ttext,:trailer_filler
                  FROM DUAL;
        POSTORA;

        add(count,count,_TMC("1"));
        
        EXEC SQL
             SELECT LPAD(TRUNC(NVL(:count,0)),10,0)
               INTO :count
               FROM DUAL;
        POSTORA;
        
        tmfprintf(&tmBundle, ofile_dat,TM_NLS_Get("0034","{0}{1}{2}\n"),ttext,count,trailer_filler);
        print_foot();
     }
   else
       {
         print_foot();
       }
}

static void print_foot(void)
{
      /* Report the entered parameters */
   pageno++;
   tmfprintf(&tmBundle, ofile,_TMC("\f"));
   tmfprintf(&tmBundle, ofile,_TMC("{0} {1,%*s}"), sel_date,inst_pos,institution);
   tmfprintf(&tmBundle, ofile, _TMC("{0,%*s} {1,number,integer}\n"), page_pos, TM_NLS_Get("0035","PAGE"), pageno);
   tmfprintf(&tmBundle, ofile, _TMC("{0}{1,%*s}{2,%*s}\n\n"), term, title_pos, title_var, rpt_pos, rptname);
   tmfprintf(&tmBundle, ofile,TM_NLS_Get("0036","\n\n\n\n\t\t       * * * REPORT CONTROL INFORMATION - SHRDEGV - Release 8.10.1.3 * * *\n\n\n"));
/* 5.5 Defect 78627
   fprintf(ofile, "                 TERM: %s\n\n",term);*/
   if(compare(term,_TMC("%"),EQS))
   	tmfprintf(&tmBundle, ofile, TM_NLS_Get("0037","                 TERM: ALL TERMS\n\n"));
   else
   	tmfprintf(&tmBundle, ofile, TM_NLS_Get("0038","                 TERM: {0}\n\n"),term);
   tmfprintf(&tmBundle, ofile, TM_NLS_Get("0039","DEGREE INCLUDE STATUS: "));
   readcolr();
   tmfprintf(&tmBundle, ofile, _TMC("\n\n"));
   tmfprintf(&tmBundle, ofile, TM_NLS_Get("0040","        DEGREE PERIOD: {0}\n\n"),degc_period);
   tmfprintf(&tmBundle, ofile, TM_NLS_Get("0041","             RUN MODE: {0}\n\n"),run_mode);
   tmfprintf(&tmBundle, ofile, TM_NLS_Get("0042","           SORT ORDER: {0}\n\n"),rpt_sort);
   tmfprintf(&tmBundle, ofile, TM_NLS_Get("0043","            BANNER ID: {0}\n\n"),bannerid_flag);
   tmfprintf(&tmBundle, ofile, TM_NLS_Get("0044","HOLD EXCLUDE STATUS: "));
   readcolrhldd();
   tmfprintf(&tmBundle, ofile, _TMC("\n\n"));
   
   EXEC SQL
        SELECT TRUNC(:count) INTO :count FROM DUAL;
   POSTORA;
        
   tmfprintf(&tmBundle, ofile,TM_NLS_Get("0045","                COUNT: {0}\n\n"),count);
}

static void find_name_for(TMCHAR *fullname)
{
static TMCHAR outfile[200]={0}/*TMCI18N CHANGED FROM ""*/;
int  len = 0;

   len = tmstrlen(ofile_name);
   substr(outfile,ofile_name,0,len-3);
   tmstrcat(outfile,_TMC("dat"));
   tmstrcpy(ofile_name_dat,outfile);
} 

/* Parse the job submission control line */
static void _rptopen1(TMCHAR *user_password,int argc,TMCHAR *argv[])
{
  /* parse the command line parameters */

  for ( cnt=1 ; cnt<argc && *argv[cnt] == '-' ; cnt++ )
    if ( ! argv[cnt][1] )
      {
	tmprintf(&tmBundle, TM_NLS_Get("0046","Invalid input parameters.\n"));
	exit2os(EXIT_FAILURE);
      }
    else
      {
      for ( i=1,next_arg=FALSE ; argv[cnt][i] && !next_arg ; i++ )
        switch(argv[cnt][i])
          {
            case 'f' :
              break;
            case 'i' :
              break;
            case 'u' :
              break;
            case 'r' :
              break;
            case 't' :
              break;
            case 'v' :
              break;
            /* -o: specify an output file */
            case 'o' : 
              if ( argv[cnt][i+1] )
	        {
		  tmprintf(&tmBundle, TM_NLS_Get("0047","Invalid input parameters.\n"));
		  exit2os(EXIT_FAILURE);
    	        }
              tmstrcpy(ofile_name,argv[++cnt]);
              tmstrcpy(ofile_name_dat,ofile_name);
              next_arg=TRUE;
              break;
            case 'x' :
              break;
            default  :
	      tmprintf(&tmBundle, TM_NLS_Get("0048","Invalid input parameters.\n"));
	      exit2os(EXIT_FAILURE);
          }
       }
  /* create a valid output file name */
  if (*ofile_name_dat ) 
     find_name_for(ofile_name_dat); 
  if (*ofile_name )     /* user specified output file */
    {
      ofile=tmfopen(&tmBundle, ofile_name, _TMC("w"));
    }
  else
    {
      tmstrcpy(ofile_name,_TMC("shrdegv.lis"));
      ofile=tmfopen(&tmBundle, ofile_name, _TMC("w"));
    }



  /* Capture the password */
  *user_password='\0';
  if ( cnt==argc-1 )
    tmstrcpy(user_password,argv[cnt]);
  else if ( cnt!=argc )
    {
      tmstrcpy(ofile_name,_TMC("shrdegv.lis"));
      ofile=tmfopen(&tmBundle, ofile_name, _TMC("w"));
    }
    
    

} /* rptopen1 */


/*TMCI18N BEGIN GLOBAL STRING INITIALIZATION*/
#ifndef ORA_PROC
void tmInitGlobS_shrdegv(void){
	TMCHARPTR_GLOB_INIT(default_lis);

	#ifdef _DEBUG
	puts("*** Initialisation tmInitGlobS_shrdegv OK ***\n");
	#endif
}
#endif /* not defined ORA_PROC*/
/*TMCI18N END GLOBAL STRING INITIALIZATION*/
