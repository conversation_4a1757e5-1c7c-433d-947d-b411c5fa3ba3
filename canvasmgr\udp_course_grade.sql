--------------------------------------------------------
--  File created - Wednesday-February-21-2024   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View UDP_COURSE_GRADE
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CANVASMGR"."UDP_COURSE_GRADE" ("ACADEMIC_TERM_ID", "COURSE_OFFERING_ID", "LE_CURRENT_COURSE_OFFERING_ID", "LE_ORIGINAL_COURSE_OFFERING_ID", "LEARNING_ENVIRONMENT_ORGANIZATION_ID", "COURSE_SECTION_ID", "ENRL_PERSON_ID", "PIDM", "UNIQNAME", "TERM_NAME", "TERM_CODE", "COURSE_START_DATE", "COURSE_END_DATE", "COURSE_LE_CODE", "COURSE_LE_STATUS", "COURSE_NUM", "COURSE_SUBJ", "COURSE_TITLE", "SEC_CLASS_NUM", "SEC_CREATED_DATE", "SEC_LE_NAME", "SEC_MAX_ERNL", "SEC_NUMBER", "SEC_STATUS", "SEC_TYPE", "ENRL_ROLE", "ENRL_ROLE_STATUS", "ENRL_STATUS", "ENRL_ENTRY_DATE", "ENRL_EXIT_DATE", "ENRL_UDATED_DATE", "SCORE_CREATED_DATE", "LE_CURRENT_SCORE", "LE_FINAL_SCORE") AS 
  SELECT
--Keys
  ucoe.academic_term_id,
  ucoe.course_offering_id,
  ucse.le_current_course_offering_id,
  ucse.le_original_course_offering_id,
  ucoe.learning_environment_organization_id,
  ucse.course_section_id,
  csee.person_id         enrl_person_id,
  uuk.pidm,
  uuk.uniqname,
  stv.stvterm_desc       term_name,
  stv.stvterm_code       term_code,

--Course Data
  ucoe.start_date        course_start_date,
  ucoe.end_date          course_end_date,
  ucoe.le_code           course_le_code,
  ucoe.le_status         course_le_status,
  ucoe.course_num,
  ucoe.course_subj,
  ucoe.title             course_title,

--Course Section Data
  ucse.class_number      sec_class_num,
  ucse.created_date      sec_created_date,
  ucse.le_name           sec_le_name,
  ucse.max_enrollment    sec_max_ernl,
  ucse.section_number    sec_number,
  ucse.status            sec_status,
  ucse.type              sec_type, 

--Course Section Enrollment Data
  csee.role              enrl_role,
  csee.role_status       enrl_role_status,
  csee.enrollment_status enrl_status,
  csee.entry_date        enrl_entry_date,
  csee.exit_date         enrl_exit_date,
  csee.updated_date      enrl_udated_date,

--Course section Grade Data
  ucge.created_date      score_created_date,
  ucge.le_current_score,
  ucge.le_final_score
FROM
       udp_course_offering_ext ucoe
  INNER JOIN udp_course_section_ext            ucse ON ucoe.course_offering_id = ucse.course_offering_id
  INNER JOIN udp_course_section_enrollment_ext csee ON ucse.course_section_id = csee.course_section_id
  INNER JOIN udp_course_grade_ext              ucge ON csee.course_section_id = ucge.course_section_id
                                          AND csee.person_id = ucge.person_id
  INNER JOIN udp_umf_stud_key                  uuk ON ucge.person_id = uuk.person_id
  INNER JOIN aimsmgr.stvterm_ext               stv ON stv.stvterm_code = ( aimsmgr.f_get_term_code_by_date(ucge.created_date) )
WHERE
  le_status IS NOT NULL
  AND academic_term_id IS NOT NULL
  AND le_current_score != le_final_score
--and entry_date is not null
;
