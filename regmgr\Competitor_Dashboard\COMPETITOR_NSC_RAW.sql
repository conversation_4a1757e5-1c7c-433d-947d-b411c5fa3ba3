/*******************************************************************************
Process for SE Datamart Clearinghouse Detail File integration
*******************************************************************************/
/*******************************************************************************
Update NSC Raw Data Table from detail return file to identify students who 
accepted but never registered.
*******************************************************************************/

TRUNCATE TABLE COMPETITOR_NSC_RAW_BAC;
INSERT INTO COMPETITOR_NSC_RAW_BAC
SELECT * FROM COMPETITOR_NSC_RAW;
COMMIT;
SELECT COUNT (*) from COMPETITOR_NSC_RAW;
SELECT COUNT (*) from COMPETITOR_NSC_RAW_BAC;
--Only truncate if you are pulling the entire population of students who were
--admitted but never registered
--TRUNCATE TABLE COMPETITOR_NSC_RAW;

--The uplad to NSC included only the new student applicants who did not register
--import file 002327st_xxxxxx_DETLRPT_SE_xxxxxxxxxxxxx_002327_unregistered_se_mmddyy.csv
