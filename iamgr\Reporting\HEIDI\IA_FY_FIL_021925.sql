desc um_catalog_schedule;
desc ia_td_registration_detail;


WITH dp0 AS (
 --primary instructor
    SELECT
        RD.pidm                       td_pidm,
        RD.td_term_code,
        RD.fy,
        RD.PRIMARY_DEGREE_CODE DEGC,
        RD.PRIMARY_MAJOR_1 MAJR,
        CS.coll_code,
        CS.coll_desc,
        CS.dept_code,
        CS.dept_desc,
        RD.crn_key,
        RD.subj_code,
        RD.crse_number,
        RD.section_number,
        RD.meeting_schd_code_1,
        RD.xlst_group,
        RD.credit_hours               td_credit_hours,
        CS.PRIMARY_INSTRUCTOR_ID            instructor_id,
        CS.primary_instructor_last_name
        || ', '
        || CS.primary_instructor_first_name instructor_name,
        '1'                                                  AS instructor_number
    FROM
        ia_td_registration_detail RD
        inner join um_catalog_schedule CS
        ON CS.term_code_key = RD.td_term_code
        AND CS.crn_key = RD.crn_key
    UNION ALL
      --secondary instructor
    SELECT
        RD.pidm                td_pidm,
        RD.td_term_code,
        RD.fy,         
        RD.PRIMARY_DEGREE_CODE DEGC,         
        RD.PRIMARY_MAJOR_1 MAJR,
        CS.coll_code,
        CS.coll_desc,
        CS.dept_code,
        CS.dept_desc,
        RD.crn_key,
        RD.subj_code,
        RD.crse_number,
        RD.section_number,
        RD.meeting_schd_code_1,
        RD.xlst_group,
        RD.credit_hours        td_credit_hours,
         CS.INSTRUCTOR_ID2            instructor_id,
        CS.instructor_last_name2
        || ', '
        || CS.instructor_first_name2 instructor_name,
        '2'                                           AS instructor_number
    FROM
        ia_td_registration_detail RD
        inner join um_catalog_schedule CS
        ON CS.term_code_key = RD.td_term_code
        AND CS.crn_key = RD.crn_key
    UNION ALL
      --Third instructor
    SELECT
        RD.pidm                td_pidm,
        RD.td_term_code,
        RD.fy,         
        RD.PRIMARY_DEGREE_CODE DEGC,         
        RD.PRIMARY_MAJOR_1 MAJR,
        CS.coll_code,
        CS.coll_desc,
        CS.dept_code,
        CS.dept_desc,
        RD.crn_key,
        RD.subj_code,
        RD.crse_number,
        RD.section_number,
        RD.meeting_schd_code_1,
        RD.xlst_group,
        RD.credit_hours        td_credit_hours,
        CS.INSTRUCTOR_ID3            instructor_id,
        CS.instructor_last_name3
        || ', '
        || CS.instructor_first_name3 instructor_name,
        '3'                                           AS instructor_number
    FROM
        ia_td_registration_detail RD
        inner join um_catalog_schedule CS
        ON CS.term_code_key = RD.td_term_code
        AND CS.crn_key = RD.crn_key
), dp1 AS (
    SELECT
--    DP0.*,
        td_term_code,
        fy,
        crn_key,
        subj_code,
        crse_number,
        section_number,
        meeting_schd_code_1,
        xlst_group,
        instructor_id,
        instructor_name,
        instructor_number,
        dept_desc,
        nces.ia_nces_code,
        nvl(nces.ia_nces_code,'0') ia_nces_code,
        nvl(nces.ia_nces_desc,'Unknown') ia_nces_desc,
        case
            WHEN instructor_name = ', '
                 AND instructor_number = 1 THEN
                'keep'
            WHEN instructor_name = ', '
                 AND instructor_number <> 1 THEN
                'remove'
            ELSE
                'keep'
        END                  ins_clean,
        CASE
            WHEN coll_code = '00'
                 AND ( dept_code = 'HON'
                       OR dept_code = 'UNV' ) THEN
                'AS'
            WHEN coll_code = '00'
                 AND dept_code = 'SEC' THEN
                'EH'
            ELSE
                coll_code
        END                  coll_code,

        CASE
            WHEN crse_number < 300 THEN
                'LL-UG'
            WHEN crse_number < 500
                 AND crse_number >= 300 THEN
                'UL-UG'
            ELSE
                'GR'
        END                  crse_level,
        

        COUNT(td_pidm)       AS td_section_enrollment,
        SUM(td_credit_hours) AS td_credit_hours
    FROM
        dp0
        left join ia_cw_nces_code nces on DP0.DEGC = NCES.DEGREE_CODE
            AND DP0.MAJR = NCES.MAJR_CODE 
    WHERE
        fy = '23-24'                                         --UPDATE THIS
    GROUP BY
        fy,
        td_term_code,
        fy,
        crn_key,
        subj_code,
        crse_number,
        section_number,
        meeting_schd_code_1,
        xlst_group,
        instructor_id,
        instructor_name,
        instructor_number,
        dept_desc,
        nvl(nces.ia_nces_code,'0'),
        nvl(nces.ia_nces_desc,'Unknown'),
        CASE
                WHEN coll_code = '00'
                     AND ( dept_code = 'HON'
                           OR dept_code = 'UNV' ) THEN
                    'AS'
                WHEN coll_code = '00'
                     AND dept_code = 'SEC' THEN
                    'EH'
                ELSE
                    coll_code
        END,
        CASE
                WHEN instructor_name = ', '
                     AND instructor_number = 1 THEN
                    'keep'
                WHEN instructor_name = ', '
                     AND instructor_number <> 1 THEN
                    'remove'
                ELSE
                    'keep'
        END,
        CASE
                WHEN crse_number < 300 THEN
                    'LL-UG'
                WHEN crse_number < 500
                     AND crse_number >= 300 THEN
                    'UL-UG'
                ELSE
                    'GR'
        END  
        
), fil as (
SELECT
dp1.*
FROM
    dp1
WHERE
    ins_clean = 'keep'
ORDER BY
    td_term_code,
    subj_code,
    crse_number,
    section_number,
    instructor_number,
    dept_desc
    )
    
    select 
    sum(TD_CREDIT_HOURS) 
    from fil

    
    
    
/*
if [HEIDI Class Code Dist Ed] = 'GR' 
then ([Credit Hours]/24) 
elseif [HEIDI Class Code Dist Ed] = 'Drs-Prof' 
then ([Credit Hours]/16) 
elseif [HEIDI Class Code Dist Ed] = 'Drs-Acad' 
then ([Credit Hours]/16) 
else ([Credit Hours]/30) end

*/