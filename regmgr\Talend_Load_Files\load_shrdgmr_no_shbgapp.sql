--------------------------------------------------------
--  File created - Friday-April-02-2021   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View LOAD_SHRDGMR_NO_SHBGAPP
--------------------------------------------------------

CREATE OR REPLACE VIEW load_shrdgmr_no_shbgapp AS
SELECT
 shrdgmr.shrdgmr_term_code_grad    grad_term,
 um.last_name
 || ', '
 || um.first_name
 || ' '
 || um.middle_initial              last_first_mi,
 um.umid,
 shrdgmr.shrdgmr_seq_no,
 shrdgmr.shrdgmr_degc_code,
 shrdgmr.shrdgmr_majr_code_1
FROM
      aimsmgr.shrdgmr shrdgmr
 INNER JOIN um_demographic um ON um.dm_pidm = shrdgmr.shrdgmr_pidm
WHERE
  shrdgmr_term_code_grad >= 201910 --(select current_term from um_current_term)--'202020'
 AND NOT EXISTS (
  SELECT
   'x'
  FROM
   aimsmgr.shbgapp
  WHERE
    shbgapp_pidm = shrdgmr_pidm
   AND shbgapp_grad_term_code = shrdgmr_term_code_grad
 )
ORDER BY
 shrdgmr.shrdgmr_term_code_grad DESC,
 um.umid;