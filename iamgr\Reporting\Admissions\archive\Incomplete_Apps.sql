select
*
from sarhead sar
left outer join(
  select
  sar.sarctrl_aidm,
  sar.sarctrl_appl_seqno,
  sar.sarctrl_appl_no_saradap,
  spriden_join.spriden_pidm,
  spriden_join.spriden_id
  from aimsmgr.sarctrl sar
  inner join(
    select 
    spr.spriden_pidm,
    spr.spriden_id,
    spr.spriden_change_ind
    from aimsmgr.spriden spr
  )spriden_join on spriden_join.spriden_pidm = sar.sarctrl_pidm
                and spriden_join.spriden_change_ind is null
)sarctrl_join on sarctrl_join.sarctrl_aidm = sar.sarhead_aidm
              and sarctrl_join.sarctrl_appl_seqno = sar.sarhead_appl_seqno
where (sar.sarhead_wapp_code = 'W4' or sar.sarhead_wapp_code = 'W5')
and sar.sarhead_process_ind != 'P'
and sar.sarhead_term_code_entry >= '201910'
;
