--TRUNCATE TABLE CO_TEN_YR_NSC_TBL_BAC;
--
--CREATE TABLE CO_TEN_YR_NSC_TBL_BAC AS
--INSERT INTO CO_TEN_YR_NSC_TBL_BAC
--SELECT * FROM CO_TEN_YR_NSC_TBL;
--COMMIT;
--
--SELECT COUNT (*) FROM CO_TEN_YR_NSC_TBL_BAC;
--SELECT COUNT (*) FROM CO_TEN_YR_NSC_TBL;

--CREATE TABLE CO_TEN_YR_NSC_TBL
--TRUNCATE TABLE CO_TEN_YR_NSC_TBL;

INSERT INTO CO_TEN_YR_NSC_TBL
(
SELECT DISTINCT
  /*******************************************************************************
  TABLE GRAIN: 1 ROW / STUDENT
  PURPOSE: PULL STUDENT DATA PROJECTED OUT 11 YEARS ON A STUDENT AND CREATE TABLE
  PRIMARY KEYS: IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  FOREIGN KEYS: TD_STUDENT_DATA.SD_PIDM
  *******************************************************************************/
  CO_PIDM,
  CO_TERM_CODE_KEY,
  --FIRST FALL********************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD2.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD2.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 20))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  (SELECT MIN(TWOYEAR_FOURYEAR)
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.CO_PIDM = CO.CO_PIDM
  )TWOFOUR_NSC_ENROLLED,-- ENROLLED AT TWO YEAR OR FOUR YEAR OTHER THAN UMF
  (SELECT TWOYEAR_FOURYEAR
  FROM CO_NSC_DEGREE_TBL NSCD
  WHERE NSCD.CO_PIDM = CO.CO_PIDM
  )TWOFOUR_NSC_DEGREE,-- GRADUATED FROM OTHER COLLEGE
  
  -- FIRST WINTER SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) - 10))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  -- FIRST SPRING SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SPR_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SPR_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = CO.CO_TERM_CODE_KEY
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_SPR_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = CO.CO_TERM_CODE_KEY
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_SPR_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = CO.CO_TERM_CODE_KEY
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_SPR_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SPR_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SPR_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SPR_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = CO.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SPR_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = CO.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SPR_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = CO.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_SPR_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = CO.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_SPR_CH_CMPLTN,--CREDIT HOUR COMPLETION
  -- FIRST SUMMER SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SUM_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SUM_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_SUM_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_SUM_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRST_SUM_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SUM_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SUM_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SUM_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SUM_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SUM_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_SUM_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  )FRST_SUM_CH_CMPLTN,--CREDIT HOUR COMPLETION
  ----------------------------SECOND----------------------------------------------
  -- SECOND FALL SET *************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SCND_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SCND_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SCND_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SCND_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SCND_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 80)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- SECOND WINTER SET ***********************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD1.SD_PIDM            = TD_STUDENT_DATA.SD_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SCND_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SCND_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SCND_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SCND_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SCND_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 90)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  ----------------------------THIRD-----------------------------------------------
  -- THIRD FALL SET **************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )THRD_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )THRD_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )THRD_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )THRD_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )THRD_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 180)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- THIRD WINTER SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )THRD_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )THRD_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )THRD_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )THRD_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )THRD_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 190)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  ----------------------------FOURTH---------------------------------------------------
  -- FOURTH FALL SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FRTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FRTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 280)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- FOURTH WINTER SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FRTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FRTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FRTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 290)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------FIFTH--------------------------------------------------
  -- FIFTH FALL SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FFTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FFTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FFTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FFTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FFTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FFTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FFTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FFTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380)))>0
    THEN 'Y'
    ELSE NULL
  END FFTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 380)))>0
    THEN 'Y'
    ELSE NULL
  END FFTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- FIFTH WINTER SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FFTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FFTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FFTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )FFTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FFTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FFTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )FFTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FFTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )FFTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390)))>0
    THEN 'Y'
    ELSE NULL
  END FFTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 390)))>0
    THEN 'Y'
    ELSE NULL
  END FFTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------SIXTH--------------------------------------------
  -- SIXTH FALL SET **************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SXTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SXTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SXTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SXTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SXTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SXTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SXTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SXTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480)))>0
    THEN 'Y'
    ELSE NULL
  END SXTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 480)))>0
    THEN 'Y'
    ELSE NULL
  END SXTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- SIXTH WINTER SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SXTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SXTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SXTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SXTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SXTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SXTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SXTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SXTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SXTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490)))>0
    THEN 'Y'
    ELSE NULL
  END SXTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 490)))>0
    THEN 'Y'
    ELSE NULL
  END SXTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------SEVENTH-------------------------------------------
  -- SEVENTH FALL SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SVNTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SVNTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SVNTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SVNTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SVNTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SVNTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SVNTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SVNTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580)))>0
    THEN 'Y'
    ELSE NULL
  END SVNTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 580)))>0
    THEN 'Y'
    ELSE NULL
  END SVNTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  --SEVENTH WINTER SET **********************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SVNTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SVNTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SVNTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )SVNTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SVNTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SVNTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )SVNTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SVNTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )SVNTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590)))>0
    THEN 'Y'
    ELSE NULL
  END SVNTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 590)))>0
    THEN 'Y'
    ELSE NULL
  END SVNTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------EIGHTH----------------------------------------------
  -- EIGHTH FALL SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )EIGTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )EIGTH_FALL_TERM_REG_NSC, --REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )EIGTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )EIGTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_FALL_STD_TYPE, --STUDENT TYPE
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )EIGTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )EIGTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_FALL_TERM_GPA, --TERM GPA
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )EIGTH_FALL_CH_ATMPT, --CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )EIGTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680)))>0
    THEN 'Y'
    ELSE NULL
  END EIGTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 680)))>0
    THEN 'Y'
    ELSE NULL
  END EIGTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- EIGHTH WINTER SET **********************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )EIGTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )EIGTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )EIGTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )EIGTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )EIGTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )EIGTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )EIGTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )EIGTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )EIGTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690)))>0
    THEN 'Y'
    ELSE NULL
  END EIGTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 690)))>0
    THEN 'Y'
    ELSE NULL
  END EIGTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------NINTH----------------------------------------------
  -- NINTH FALL SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )NINTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )NINTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )NINTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )NINTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )NINTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )NINTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )NINTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )NINTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780)))>0
    THEN 'Y'
    ELSE NULL
  END NINTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 780)))>0
    THEN 'Y'
    ELSE NULL
  END NINTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- NINTH WINTER SET **********************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )NINTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )NINTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )NINTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )NINTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )NINTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )NINTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )NINTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )NINTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )NINTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790)))>0
    THEN 'Y'
    ELSE NULL
  END NINTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 790)))>0
    THEN 'Y'
    ELSE NULL
  END NINTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------TENTH----------------------------------------------
--   TENTH FALL SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )TENTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )TENTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )TENTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )TENTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )TENTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )TENTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )TENTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED,
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )TENTH_FALL_CH_CMPLTN, --CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880)))>0
    THEN 'Y'
    ELSE NULL
  END TENTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 880)))>0
    THEN 'Y'
    ELSE NULL
  END TENTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- TENTH WINTER SET **********************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )TENTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )TENTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )TENTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )TENTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )TENTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )TENTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )TENTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )TENTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )TENTH_WIN_CH_CMPLTN, --CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890)))>0
    THEN 'Y'
    ELSE NULL
  END TENTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 890)))>0
    THEN 'Y'
    ELSE NULL
  END TENTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -----------------------------ELEVENTH-------------------------------------------
  -- ELEVENTH FALL SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )ELEVENTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )ELEVENTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )ELEVENTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )ELEVENTH_FALL_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_FALL_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )ELEVENTH_FALL_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )ELEVENTH_FALL_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_FALL_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_FALL_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )ELEVENTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED,
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )ELEVENTH_FALL_CH_CMPLTN, --CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980)))>0
    THEN 'Y'
    ELSE NULL
  END ELEVENTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 980)))>0
    THEN 'Y'
    ELSE NULL
  END ELEVENTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  -- ELEVENTH WINTER SET **********************************************************
  (
  SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )ELEVENTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT NSC.REGISTERED_IND
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )ELEVENTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  (SELECT NSC.COLLEGE_NAME
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )ELEVENTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
  (SELECT NSC.ENROLLMENT_MAJOR
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  WHERE NSC.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND NSC.CO_PIDM                      = CO.CO_PIDM
  )ELEVENTH_WIN_MJR_NSC,--NAME OF MAJOR
  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_WIN_STD_TYPE,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )ELEVENTH_WIN_MJR_CODE,
  (SELECT TD1.PRIMARY_COLLEGE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )ELEVENTH_WIN_COL_CODE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_WIN_TERM_GPA,
  (SELECT TD2.MOST_RECENT_ASTD_CODE
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD2.SD_PIDM            = CO.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  )ELEVENTH_WIN_ASTD_CODE,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )ELEVENTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND TD2.SD_PIDM              = CO.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  )ELEVENTH_WIN_CH_CMPLTN, --CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990)))>0
    THEN 'Y'
    ELSE NULL
  END ELEVENTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM CO_NSC_DEGREE_TBL NSCD
      WHERE NSCD.CO_PIDM           = CO.CO_PIDM
      AND NSCD.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990)))>0
    THEN 'Y'
    ELSE NULL
  END ELEVENTH_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
  --Grad Date*********************************************************************
  (
  SELECT MIN(GRAD_DATE)
  FROM um_degree UMD
  WHERE UMD.PIDM         = CO.CO_PIDM
  AND UMD.DEGREE_STATUS  = 'AW'
  AND UMD.LEVEL_CODE     = 'UG'
  AND UMD.DEGREE_CODE    = CO.PRIMARY_DEGREE_CODE
  AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 990))
  AND UMD.GRAD_DATE      > CO.START_DATE
  )UMF_GRAD_DATE
  --COHORT DEMOGRAPHIC TABLE JOINS AND WHERE CLAUSE ******************************
FROM TD_STUDENT_DATA
INNER JOIN CO_POP_UNDUP_TBL CO
ON CO.CO_PIDM           = TD_STUDENT_DATA.SD_PIDM
AND CO.CO_TERM_CODE_KEY = TD_STUDENT_DATA.SD_TERM_CODE
AND CO.CO_TERM_CODE_KEY LIKE '%30'
--WHERE CO.CO_PIDM        = '90434'
);

