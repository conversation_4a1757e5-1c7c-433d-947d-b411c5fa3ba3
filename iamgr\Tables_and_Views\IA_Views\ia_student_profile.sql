--SELECT * FROM IA_STUDENT_PROFILE;

--CREATE OR REPLACE VIEW IA_STUDENT_PROFILE AS
select 
/***********************************************************************************
Student Demographics
************************************************************************************/
--case
--when dm.age > = 25 then 'Non-Traditional Student'
--when dm.age < = 24 then 'Traditional Student'
--else 'No Record'
--end "Traditional Non-Traditional",
--dm.age,
--dm.report_ethnicity "Ethnicity",
--dm.PCOL_DESC_1 "Feeder College", 
--dm.HSCH_DESC "Feeder HS",
--decode (dm.GENDER,'F','Female','Male') "Gender",
--Case
--    when to_number(dm.HSCH_GPA) is null then 'No Record'
--    when to_number(dm.HSCH_GPA) >= 3.5 THEN '3.5 and Up'
--    when to_number(dm.HSCH_GPA) <= 3.499999 AND to_number(dm.HSCH_GPA) >= 3.0 THEN '3.0-3.49'
--    when to_number(dm.HSCH_GPA) <= 2.999999 AND to_number(dm.HSCH_GPA) >= 2.7 THEN '2.7-2.99'
--    when to_number(dm.HSCH_GPA) <= 2.699999 AND to_number(dm.HSCH_GPA) >= 2.5 THEN '2.5-2.69'
--    when to_number(dm.HSCH_GPA) <= 2.499999 AND to_number(dm.HSCH_GPA) >= 2.0 THEN '2.0-2.49'
--    when to_number(dm.HSCH_GPA) < 2.0 THEN 'Less than 2.0'
--    else 'Bad Data'
--end "HS GPA Dist",
--round(to_number(dm.HSCH_GPA),2)"HS GPA",
--Case 
--    when td.report_level_code = 'UG' then 
--        case
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) is null then 'No Record'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 3.5 THEN 'UG 3.5 and Up'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 3.499999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 3.0 THEN 'UG 3.0-3.49'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 2.999999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 2.7 THEN 'UG 2.7-2.99'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 2.699999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 2.5 THEN 'UG 2.5-2.69'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 2.499999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 2.0 THEN 'UG 2.0-2.49'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) < 2.0 THEN 'UG Less than 2.0'
--            else 'Bad Data'
--        end
--    when td.report_level_code = 'GR' then 
--        case
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) is null then 'No Record'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 7 THEN 'GR 7.0 and Up'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 6.999999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 6.0 THEN 'GR 6.0-6.99'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 5.999999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 5.4 THEN 'GR 5.4-5.99'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 5.399999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 5.0 THEN 'GR 5.0-5.39'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) <= 4.999999 AND to_number(dm.PCOL_GPA_TRANSFERRED_1) >= 4.0 THEN 'GR 4.0-4.99'
--            when to_number(dm.PCOL_GPA_TRANSFERRED_1) < 4.0 THEN 'GR Less than 4.0'
--            else 'Bad Data'
--        end
--    else 'No Level Code'
--end "Trans GPA Dist",
--round(to_number(dm.PCOL_GPA_TRANSFERRED_1),2) "Trans GPA",
--dm.hsch_rank "HS Rank",
--decode(dm.veteran_ind,'Y','Yes','No') "Veteran",
--dm.A1_STATE_CODE "A1 State",


/***********************************************************************************
Student Data
************************************************************************************/
--td.SD_TERM_DESC "Term",
--case
--when td.primary_college_code in ('AS','RK') then 'CAS'
--when td.primary_college_code in ('HP','HS') then 'CHS'
--when td.primary_college_code in ('NR') then 'SON'
--when td.primary_college_code in ('MG') then 'SOM'
--when td.primary_college_code in ('EH') then 'SEHS'
--when td.primary_college_code is null then 'No Record'
--else td.primary_college_code
--end "College",
--td.primary_college_code "College Code",
--td.primary_major_1_desc "Major Desc",
--td.primary_major_1 "Major",

--decode(td.full_part_time_ind_umf,'F','Full','P','Part',td.full_part_time_ind_umf) "Full Part Time Ind",
case when td.primary_major_1 in ('AMTH','BIO','BIOC','BIOH','BIOM','BION','BIOW','CAIS'
,'CHM','CIS','CSC','EGR','ENSS','ESP','ESS','GCHM','MEGN','MTH','PHY','REC') then 'STEM'
else 'Non-STEM'
end "STEM Non-STEM"
--td.primary_major_1 "Major",
--to_number(td.total_credit_hours_umf) "Credit Hours",
--td.ia_student_type_code "Student Type",
--td.report_level_code "Report Level",
--td.primary_level_code "Primary Level",






/*******************************************************************************
Test Scores
*******************************************************************************/
/******************************************************************************
ACT
*******************************************************************************/
--case
--when ts.ACT_COMPOSITE >= 30 AND ts.ACT_COMPOSITE <= 36 THEN '30-36'
--when ts.ACT_COMPOSITE >= 24 AND ts.ACT_COMPOSITE <= 29 THEN '24-29'
--when ts.ACT_COMPOSITE >= 18 AND ts.ACT_COMPOSITE <= 23 THEN '18-23'
--when ts.ACT_COMPOSITE >= 12 AND ts.ACT_COMPOSITE <= 17 THEN '12-17'
--when ts.ACT_COMPOSITE >= 6 AND ts.ACT_COMPOSITE <= 11 THEN '6-11'
--when ts.ACT_COMPOSITE <= 5 THEN 'Below 6'
--else 'No Record'
--END "ACT Composite Range",
--to_number(ts.ACT_COMPOSITE) "ACT Composite",
--case
--when ts.ACT_ENGLISH >= 30 AND ts.ACT_ENGLISH <= 36 THEN '30-36'
--when ts.ACT_ENGLISH >= 24 AND ts.ACT_ENGLISH <= 29 THEN '24-29'
--when ts.ACT_ENGLISH >= 18 AND ts.ACT_ENGLISH <= 23 THEN '18-23'
--when ts.ACT_ENGLISH >= 12 AND ts.ACT_ENGLISH <= 17 THEN '12-17'
--when ts.ACT_ENGLISH >= 6 AND ts.ACT_ENGLISH <= 11 THEN '6-11'
--when ts.ACT_ENGLISH <= 5 THEN 'Below 6'
--else 'No Record'
--END "ACT English Range",
--to_number(ts.ACT_ENGLISH) "ACT English",
--case
--when ts.ACT_MATH >= 30 AND ts.ACT_MATH <= 36 THEN '30-36'
--when ts.ACT_MATH >= 24 AND ts.ACT_MATH <= 29 THEN '24-29'
--when ts.ACT_MATH >= 18 AND ts.ACT_MATH <= 23 THEN '18-23'
--when ts.ACT_MATH >= 12 AND ts.ACT_MATH <= 17 THEN '12-17'
--when ts.ACT_MATH >= 6 AND ts.ACT_MATH <= 11 THEN '6-11'
--when ts.ACT_MATH <= 5 THEN 'Below 6'
--else 'No Record'
--END "ACT Math Range",
--to_number(ts.ACT_MATH) "ACT Math",
--case
--when ts.ACT_READING >= 30 AND ts.ACT_READING <= 36 THEN '30-36'
--when ts.ACT_READING >= 24 AND ts.ACT_READING <= 29 THEN '24-29'
--when ts.ACT_READING >= 18 AND ts.ACT_READING <= 23 THEN '18-23'
--when ts.ACT_READING >= 12 AND ts.ACT_READING <= 17 THEN '12-17'
--when ts.ACT_READING >= 6 AND ts.ACT_READING <= 11 THEN '6-11'
--when ts.ACT_READING <= 5 THEN 'Below 6'
--else 'No Record'
--END "ACT Reading Range",
--to_number(ts.ACT_READING) "ACT Reading",
--case
--when ts.ACT_COMBINED_ENG_WRI >= 30 AND ts.ACT_COMBINED_ENG_WRI <= 36 THEN '30-36'
--when ts.ACT_COMBINED_ENG_WRI >= 24 AND ts.ACT_COMBINED_ENG_WRI <= 29 THEN '24-29'
--when ts.ACT_COMBINED_ENG_WRI >= 18 AND ts.ACT_COMBINED_ENG_WRI <= 23 THEN '18-23'
--when ts.ACT_COMBINED_ENG_WRI >= 12 AND ts.ACT_COMBINED_ENG_WRI <= 17 THEN '12-17'
--when ts.ACT_COMBINED_ENG_WRI >= 6 AND ts.ACT_COMBINED_ENG_WRI <= 11 THEN '6-11'
--when ts.ACT_COMBINED_ENG_WRI <= 5 THEN 'Below 6'
--else 'No Record'
--END "ACT Comb Eng Writ Range",
--to_number(ts.ACT_COMBINED_ENG_WRI) "ACT Comb Eng Writ",
/******************************************************************************
SAT
*******************************************************************************/
--case
--when ts.SAT_READ_WRI_S11 >= 700  THEN '700-800'
--when ts.SAT_READ_WRI_S11 >= 600 AND ts.SAT_READ_WRI_S11 <= 699 THEN '600-699'
--when ts.SAT_READ_WRI_S11 >= 500 AND ts.SAT_READ_WRI_S11 <= 599 THEN '500-599'
--when ts.SAT_READ_WRI_S11 >= 400 AND ts.SAT_READ_WRI_S11 <= 499 THEN '400-499'
--when ts.SAT_READ_WRI_S11 >= 300 AND ts.SAT_READ_WRI_S11 <= 399 THEN '300-399'
--when ts.SAT_READ_WRI_S11 >= 200 AND ts.SAT_READ_WRI_S11 <= 299 THEN '200-299'
--else 'No Record'
--END "SAT Read Write Range",
--to_number(ts.SAT_READ_WRI_S11) "SAT Read Write",
--case
--when ts.SAT_MATH_S12 >= 700  THEN '700-800'
--when ts.SAT_MATH_S12 >= 600 AND ts.SAT_MATH_S12 <= 699 THEN '600-699'
--when ts.SAT_MATH_S12 >= 500 AND ts.SAT_MATH_S12 <= 599 THEN '500-599'
--when ts.SAT_MATH_S12 >= 400 AND ts.SAT_MATH_S12 <= 499 THEN '400-499'
--when ts.SAT_MATH_S12 >= 300 AND ts.SAT_MATH_S12 <= 399 THEN '300-399'
--when ts.SAT_MATH_S12 >= 200 AND ts.SAT_MATH_S12 <= 299 THEN '200-299'
--else 'No Record'
--END "SAT Read Write Range",
--to_number(ts.SAT_MATH_S12) "SAT Read Write",
--case
--when ts.SAT_TOTAL_COMBINED_S10 >= 1400  THEN '1400-1600'
--when ts.SAT_TOTAL_COMBINED_S10 >= 1200 AND ts.SAT_TOTAL_COMBINED_S10 <= 1399 THEN '1200-1399'
--when ts.SAT_TOTAL_COMBINED_S10 >= 1000 AND ts.SAT_TOTAL_COMBINED_S10 <= 1199 THEN '1000-1199'
--when ts.SAT_TOTAL_COMBINED_S10 >= 800 AND ts.SAT_TOTAL_COMBINED_S10 <= 999 THEN '800-999'
--when ts.SAT_TOTAL_COMBINED_S10 >= 600 AND ts.SAT_TOTAL_COMBINED_S10 <= 799 THEN '600-799'
--when ts.SAT_TOTAL_COMBINED_S10 >= 400 AND ts.SAT_TOTAL_COMBINED_S10 <= 599 THEN '400-599'
--else 'No Record'
--END "SAT Read Write Range",
--to_number(ts.SAT_TOTAL_COMBINED_S10) "SAT Total Combined",

/******************************************************************************
International Student Teest Scores
*******************************************************************************/
--to_number(ts.MATH_PLACEMENT)  "Math Placement",
--to_number(ts.IELTS_READ)"Ilets Reading Score",
--to_number(ts.IELTS_LISTEN)"Ilets Listening Score",
--to_number(ts.IELTS_OVERALL)"Ilets Overall Score",
--to_number(ts.IELTS_SPEAK)"Ilets Speaking Score",
--to_number(ts.IELTS_WRITE)"Ilets Writing Score",
--to_number(ts.TOEFL)"TOEFL Score",
--to_number(ts.TOEFL_PAPER_TOTAL)"TOEFL Paper Score",
--to_number(ts.TOEFL_CBT_TOTAL)"TOEFL CBT Total Score",
--to_number(ts.TOEFL_IBT_TOTAL)"TOEFL IBT Total Score",
--to_number(ts.TOEFL_IBT_LISTEN)"TOEFL IBT Listen Score",
--to_number(ts.TOEFL_IBT_READ)"TOEFL IBT Read Score",
--to_number(ts.TOEFL_IBT_SPEAK)"TOEFL IBT Speak Score",
--to_number(ts.TOEFL_IBT_WRITE)"TOEFL IBT Writing Score"
/******************************************************************************
Placement Scores
*******************************************************************************/
--case
--when ts.COMP_SC_PLACEMENT <= 59 and ts.COMP_SC_PLACEMENT >= 0 THEN 'Score: 0-59 Fail CSC 151'
--when ts.COMP_SC_PLACEMENT <= 64 AND ts.COMP_SC_PLACEMENT >= 60 THEN 'Score: 60-64 CSC 121 Grade D not waived'
--when ts.COMP_SC_PLACEMENT <= 74 AND ts.COMP_SC_PLACEMENT >= 65 THEN 'Score: 65-74 CSC 121 Grade C not waived'
--when ts.COMP_SC_PLACEMENT <= 84 AND ts.COMP_SC_PLACEMENT >= 75 THEN 'Score: 75-84 CSC 121 Grade B for credit not waived'
--when ts.COMP_SC_PLACEMENT <= 100 AND ts.COMP_SC_PLACEMENT >= 85 THEN 'Score 85-100 CSC 121 Grade A for credit not waived or waived'
--ELSE 'No Record'
--END "Comp Sci Placement Dist",
--to_number(ts.COMP_SC_PLACEMENT) "Comp Sci Placement",
--case 
--when ts.MATH_PLACEMENT <= 10 and ts.MATH_PLACEMENT >= 0 THEN 'Score: 0-10 retake test within 30 days'
--when ts.MATH_PLACEMENT <= 15 AND ts.MATH_PLACEMENT >= 11 THEN 'Score: 11-15 MTH 090 (Intermediate Algebra)'
--when ts.MATH_PLACEMENT <= 17 AND ts.MATH_PLACEMENT >= 16 THEN 'Score: 16-17 MTH 090 (Intermediate Algebra) or MTH 111 (College Algebra)'
--when ts.MATH_PLACEMENT <= 22 AND ts.MATH_PLACEMENT >= 18 THEN 'Score: 18-22 MTH 111 (College Algebra)'
--when ts.MATH_PLACEMENT = 23 THEN 'Score: 23 MTH 111(College Algebra) MTH 118(Calc for MGT) or MTH 120 (Pre-Calc)'
--when ts.MATH_PLACEMENT <= 26 AND ts.MATH_PLACEMENT >= 24 THEN 'Score 24-26 MTH 118 (Calc for MGT) or MTH 120 (Pre-Calc)'
--when ts.MATH_PLACEMENT <= 30 AND ts.MATH_PLACEMENT >= 27 THEN 'Score 27-30 MTH 118 (Calc for MGT) or MTH 120 (Pre-Calc) or MTH 121 (Calc I) '
--ELSE 'Test Not Taken'
--END "Math Placement Dist",
--to_number(ts.MATH_PLACEMENT) "Math Placement"

/*******************************************************************************
aggregates for testing
*******************************************************************************/
--count(*) headcount,
--avg(ts.ACT_COMPOSITE) avg_act,
--avg (SAT_TOTAL_COMBINED_S10) avg_SAT

from td_student_data td 
inner join td_demographic dm
on td.sd_pidm = dm.pidm_key
and td.sd_term_code = dm.td_term_code
left join ia_cw_stem stem
on td.primary_major_1 = stem.majr_code
left join td_test_score ts
on td.sd_pidm = ts.pidm
and td.sd_term_code = ts.td_term_code
--left join um_student_data um
--on td.sd_pidm = um.sd_pidm
--and td.sd_term_code = um.sd_term_code
where td.registered_ind = 'Y'
and td.sd_term_code = '202010'
and td.sd_term_code like '%10'
--and td.ia_student_type_code = 'F'
;