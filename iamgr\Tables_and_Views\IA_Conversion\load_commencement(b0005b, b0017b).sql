/*******************************************************************************************************************************
Data to to build the Tableau Dashboard that will replace the following reports:
Commencement Program List (PROD) b0005b-X
Commencement Program List-Honors Only (PROD) b0017b-X
*******************************************************************************************************************************/
create or replace view load_commencement as
select popsel.* from(
with dp1 as (
select 
row_number() over(partition by dg.pidm, dg.grad_term_code order by dg.pidm desc) deg_count,

dg.pidm,
--dm.umid,
dg.grad_term_code,
dg.grad_term_desc,
dm.first_name,
dm.last_name,
case
when dm.middle_initial is null then dm.FIRST_NAME ||' '||dm.LAST_NAME
else dm.FIRST_NAME ||' '||dm.middle_initial||'. '||dm.LAST_NAME
end full_name,
--case
--when dg.grad_term_code like '%10' then dg.grad_term_desc
--when dg.grad_term_code like '%20' then dg.grad_term_desc
--else null
--end Grad_Term_Parameter,
--case
--when dg.grad_term_code like '%10' then to_char(dg.grad_term_code -70)
--when dg.grad_term_code like '%20' then to_char(dg.grad_term_code +20)
--else dg.grad_term_code
--end more_less_term,
--sd.honors_program,
decode (sd.honors_program,'HO','Y','JS','Y','N')Honors_Prog_Ind,
--sd.report_level_code,
dg.GRAD_DATE,
sd.PRIMARY_COLLEGE_CODE, 
sd.PRIMARY_COLLEGE_DESC,
dg.degree_code,
dg.degree_desc,
--sd.primary_program_desc,
--dg.DEGREE_STATUS_DESC,
--sd.overall_gpa,
case
when sd.report_level_code = 'UG' and sd.primary_college_code in ('HP','HS') and sd.overall_gpa >= 3.50 and (sd.OVERALL_HOURS_ATTEMPTED + sd.TERM_REGISTERED_HOURS) < 45 then '***'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.75 then '**'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.50 and sd.overall_gpa <= 3.74 then '*'
else ''
end Honor_Role,
case
when sd.report_level_code = 'UG' and sd.primary_college_code in ('HP','HS') and sd.overall_gpa >= 3.50 and (sd.OVERALL_HOURS_ATTEMPTED + sd.TERM_REGISTERED_HOURS) < 45 then 'Academic Distinction'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.75 then 'High Honors'
when sd.report_level_code = 'UG' and sd.overall_gpa >= 3.50 and sd.overall_gpa <= 3.74 then 'Honors'
else ''
end Honor_Role_Desc,
dg.GRAD_STATUS
from UM_DEGREE dg
inner join um_student_data sd on sd.sd_pidm = dg.pidm
                           and sd.sd_term_code = dg.grad_term_code
inner join um_demographic dm on dm.dm_pidm = dg.pidm 
where dg.grad_term_code >= (select LAST_2_TERM from um_current_term)--202020
and dg.grad_status in ('GR','PG')
and (dg.grad_term_code like '%10'
or dg.grad_term_code like '%20'
or dg.grad_term_code like '%40'
)
),dp2 as(
select
'+' mult_deg_ind,
dp1.pidm,
dp1.grad_term_code
--dp1.*,
--decode (deg_count,1,'','+')Mult_Deg_ind
from dp1
where deg_count >1
),dp3 as(
select dp1.*, dp2.mult_deg_ind 
from dp1
left join dp2 on dp1.pidm = dp2.pidm
and dp1.grad_term_code = dp2.grad_term_code
)select dp3.*
,dp3.full_name||dp3.honor_role||mult_deg_ind clist_name
from dp3)popsel
;

select 
grad_term_desc, primary_college_desc, degree_desc, clist_name 
from load_commencement 
where grad_term_code in ('202020','202040')
order by grad_term_desc, primary_college_desc, degree_desc, clist_name;
select DISTINCT grad_term_code, grad_term_desc from load_commencement order by grad_term_code;

--select distinct
--primary_college_code,
--primary_college_desc
--from ia_um_student_data
--where sd_term_code = 202020;
--
--select distinct
--honors_program
--from ia_um_student_data
--where sd_term_code = 202020;
--
----GR and PG's for following summer
--select distinct
--grad_status,
--grad_status_desc
--from ia_um_degree;
--
----summer is Aug, fall is Dec, winter is April
--select 
--distinct grad_term_desc, 
--grad_date
--from um_degree
--where grad_term_code >= 202020;
--/*
-- Kim J. Brown  154253
-- Madelyn Vamossy 106824
-- */
-- grant select on um_degree to tabmgr;