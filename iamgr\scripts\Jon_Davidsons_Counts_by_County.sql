--Counts by County
with base_criteria as 
(  select 
  case
    when resd_code = 'R' then
      case 
        when county_code_admit like 'MI%' then replace(replace(county_desc_admit,' (MI)'),'Saint','St.')
        else '~In-State, Unknown'
      end
    when international_ind='Y' then '~International'
    else '~Out-of-State'
  end geographic_segment
  , td_admissions_applicant.*
  from td_admissions_applicant
  left join td_demographic 
    on ad_pidm = dm_pidm  
    and term_code_entry = td_demographic.td_term_code 
  where term_code_entry = '201610'
  and styp_code = 'F' 
  and inst_accepted_app_any_date_ind = 'Y'    --was admitted
  and registered_ind = 'Y'    --enrolled
  ),

geographic_segments as
( select distinct replace(replace(a1_county_desc,' (MI)'),'Saint','St.') as segment
  from td_demographic
  where a1_county_code like 'MI%'
  and a1_county_desc is not null
  union select '~International' from dual
  union select '~Out-of-State' from dual
  union select '~In-State, Unknown' from dual ),

counts as 
( select count(*) count, geographic_segment 
  from base_criteria
  group by geographic_segment )

select count, segment
from geographic_segments
left join counts
  on segment = geographic_segment
order by segment
;
