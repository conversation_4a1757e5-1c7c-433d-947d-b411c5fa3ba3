--------------------------------------------------------
--  DDL for View RC_CONTINUATION
--------------------------------------------------------

CREATE OR REPLACE VIEW RC_CONTINUATION AS 
  
WITH DP1 AS (
SELECT 
SD.UMID,
SD.SD_PIDM,
SD.SD_TERM_CODE,
SD.SD_TERM_DESC,
SD.IA_STUDENT_TYPE_CODE,
NVL(CS.RC_CLUB_SPORTS_IND,'N')RC_CLUB_SPORTS_IND,
NVL(FT.RC_FIT_CLASS_IND,'N')RC_FIT_CLASS_IND,
NVL(IM.RC_IM_SPORTS_IND,'N')RC_IM_SPORTS_IND,
NVL(PE.RC_PE_COURSE_IND,'N')RC_PE_COURSE_IND,
NVL(VI.RC_VISITS,0)RC_VISITS,
  (
  SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(SD.SD_TERM_CODE) + 100))
  AND TD1.SD_PIDM        = SD.SD_PIDM
  AND SD.SD_TERM_CODE LIKE '%10'
  ) NEXT_FALL_CONT_IND,--REGISTERED AT UMF FOR FALL
    CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = SD.SD_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = SD.PRIMARY_LEVEL_CODE
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(SD.SD_TERM_CODE) + 100)))>0
    THEN 'Y'
    ELSE 'N'
  END NEXT_FALL_GRAD_IND
  
FROM TD_STUDENT_DATA SD

LEFT JOIN RC_CLUB_SPORTS CS
ON SD.SD_TERM_CODE = CS.RC_TERM 
AND SD.UMID = CS.RC_UMID 

LEFT JOIN RC_FIT_CLASS FT
ON SD.SD_TERM_CODE = FT.RC_TERM 
AND SD.UMID = FT.RC_UMID 

LEFT JOIN RC_IM_SPORTS IM
ON SD_TERM_CODE = IM.RC_TERM 
AND SD.UMID = IM.RC_UMID 

LEFT JOIN RC_PE_COURSE PE
ON SD.SD_TERM_CODE = PE.RC_TERM 
AND SD.UMID = PE.RC_UMID 

LEFT JOIN RC_VISITS VI
ON SD.SD_TERM_CODE = VI.RC_TERM 
AND SD.UMID = VI.RC_UMID 

WHERE SD.SD_TERM_CODE IN ('201810','201910','202010','202110')
AND SD.REGISTERED_IND = 'Y'
)

SELECT DISTINCT
UMID,
SD_PIDM,
SD_TERM_CODE,
SD_TERM_DESC,
IA_STUDENT_TYPE_CODE,
DECODE(RC_CLUB_SPORTS_IND,'N','No','Yes')RC_CLUB_SPORTS_IND,
DECODE(RC_FIT_CLASS_IND,'N','No','Yes')RC_FIT_CLASS_IND,
DECODE(RC_IM_SPORTS_IND,'N','No','Yes')RC_IM_SPORTS_IND,
DECODE(RC_PE_COURSE_IND,'N','No','Yes')RC_PE_COURSE_IND,
RC_VISITS,
NEXT_FALL_CONT_IND,
NEXT_FALL_GRAD_IND,
  
case
when NEXT_FALL_CONT_IND = 'Y' 
OR  NEXT_FALL_GRAD_IND = 'Y' THEN 'Yes'
ELSE 'No'
END NEXT_FALL_CONT_OR_GRAD_IND,
case
when NEXT_FALL_CONT_IND = 'Y' 
OR  NEXT_FALL_GRAD_IND = 'Y' THEN 1
ELSE 0
END NEXT_FALL_CONT_OR_GRAD_CODE,
case
when RC_VISITS <= 20 Then '0-20 Visits'
when RC_VISITS >= 21 and RC_VISITS <= 40 Then '21-40 Visits'
when RC_VISITS >= 41 and RC_VISITS <= 60 Then '41-60 Visits'
when RC_VISITS >= 61 and RC_VISITS <= 80 Then '61-80 Visits'
when RC_VISITS >= 81 and RC_VISITS <= 100 Then '81-100 Visits'
when RC_VISITS > 101  Then '101 Visits or More'
end
RC_VISITS_GROUP,
case
when RC_VISITS > 0  Then 'Yes'
ELSE 'No'
end
RC_VISIT_IND

FROM DP1
;

