WITH data_pull AS
  (SELECT 
    REPLACE (raw_pidm,'_','') co_pidm,
    TO_CHAR(to_number(SUBSTR(SEARCH_DATE,1,4))+1)
    || '10' co_term_code_key,
    CASE
      WHEN enrollment_status IN ('F','Q','H','L')
      THEN 'Y'
      ELSE 'N'
    END registered_ind,
    to_date(SEARCH_DATE, 'YYYYMMDD') search_date,
    SUBSTR(ENROLLMENT_BEGIN,1,4) ENROLLMENT_BEGIN_year,
    SUBSTR(ENROLLMENT_BEGIN,5,4) ENROLLMENT_BEGIN_Month_day,
    to_date(ENROLLMENT_BEGIN, 'YYYYMMDD') enrollment_begin,
    to_date(ENROLLMENT_END, 'YYYYMMDD') enrollment_end,
    SUBSTR(GRADUATION_DATE,1,4) GRADUATION_DATE_year,
    SUBSTR(GRADUATION_DATE,5,4) GRADUATION_DATE_Month_day,
    to_date(GRADUATION_DATE, 'YYYYMMDD') graduation_date,
    ENROLLMENT_MAJOR_1,
    ENR<PERSON>LMENT_CIP_1,
    TWOYEAR_FOURYEAR,
    PUBLIC_PRIVATE,
    COLLEGE_CODE,
    COLLEGE_NAME,
    CLASS_LEVEL,
    GRADUATED_IND,
    DEGREE_TITLE
  FROM IA_COHORT_CLEARINGHOUSE_RAW
  WHERE (class_level IS NULL
  OR class_level     IN ('F','S','J','R','C','N','B'))
    --and
    --raw_pidm = '99839_'
  ),
  term_code_step1 AS
  (SELECT data_pull.*,
    CASE
      WHEN ENROLLMENT_BEGIN_Month_day >= '0701'
      AND ENROLLMENT_BEGIN_Month_day  <= '0831'
      THEN '40'
      WHEN ENROLLMENT_BEGIN_Month_day >= '0901'
      AND ENROLLMENT_BEGIN_Month_day  <= '1231'
      THEN '10'
      WHEN ENROLLMENT_BEGIN_Month_day >= '0101'
      AND ENROLLMENT_BEGIN_Month_day   <= '0430'
      THEN '20'
      WHEN ENROLLMENT_BEGIN_Month_day >= '0501'
      AND ENROLLMENT_BEGIN_Month_day  <= '0630'
      THEN '30'
    END enroll_begin_semester_code,
    CASE
      WHEN GRADUATION_DATE_Month_day >= '0701'
      AND GRADUATION_DATE_Month_day  <= '0831'
      THEN '40'
      WHEN GRADUATION_DATE_Month_day >= '0901'
      AND GRADUATION_DATE_Month_day  <= '1231'
      THEN '10'
      WHEN GRADUATION_DATE_Month_day >= '0101'
      AND GRADUATION_DATE_Month_day   <= '0430'
      THEN '20'
      WHEN GRADUATION_DATE_Month_day >= '0501'
      AND GRADUATION_DATE_Month_day  <= '0630'
      THEN '30'
    END grad_date_month_day_sem_code
    FROM data_pull
  ),term_code AS
  (SELECT term_code_step1.*,
    CASE
      WHEN enroll_begin_semester_code = '10'
      THEN TO_CHAR(to_number (ENROLLMENT_BEGIN_year)+1)
        || enroll_begin_semester_code
      WHEN enroll_begin_semester_code = '20'
      THEN ENROLLMENT_BEGIN_year
        || enroll_begin_semester_code
      WHEN enroll_begin_semester_code = '30'
      THEN ENROLLMENT_BEGIN_year
        || enroll_begin_semester_code
      WHEN enroll_begin_semester_code = '40'
      THEN ENROLLMENT_BEGIN_year
        || enroll_begin_semester_code
    END term_code_enrollment_begin,
    CASE
      WHEN grad_date_month_day_sem_code = '10'
      THEN TO_CHAR(to_number (GRADUATION_DATE_year)+1)
        || grad_date_month_day_sem_code
      WHEN grad_date_month_day_sem_code = '20'
      THEN GRADUATION_DATE_year
        || grad_date_month_day_sem_code
      WHEN grad_date_month_day_sem_code = '30'
      THEN GRADUATION_DATE_year
        || grad_date_month_day_sem_code
      WHEN grad_date_month_day_sem_code = '40'
      THEN GRADUATION_DATE_year
        || grad_date_month_day_sem_code
    END term_code_graduated
  FROM term_code_step1
  ),concurent_enrolled as(
SELECT 
  row_number() over(partition BY co_pidm, term_code_enrollment_begin order by term_code_enrollment_begin) concurent_enrolled,
  co_pidm,
  co_term_code_key,
  term_code_enrollment_begin,
  registered_ind,
  TWOYEAR_FOURYEAR,
  PUBLIC_PRIVATE,
  COLLEGE_CODE,
  COLLEGE_NAME,
  ENROLLMENT_MAJOR_1 ENROLLMENT_MAJOR,
  ENROLLMENT_CIP_1 ENROLLMENT_CIP
FROM term_code
WHERE GRADUATED_IND = 'N'
AND registered_ind  = 'Y'
and ENROLLMENT_CIP_1 IS NOT NULL
)
select * from concurent_enrolled
--where concurent_enrolled > 1 and COLLEGE_CODE = '002261-00'
;
