create or replace view CEP_REP_EMP_AND_EDEFF AS
WITH DP1 AS (
SELECT
 *
FROM
 umflint_rep_emp_and_edeff
UNION
SELECT
 *
FROM
 umflintaltrte_rep_emp_and_edeff
 )
 SELECT
 DISTINCT PIC,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     DP1 DP2
    WHERE
    DP1.PIC = DP2.PIC
AND DP2.ISMDETEACHER = 'Y'
   ) > 0 THEN
    'Teaching in area endorsed'
   ELSE
    'Not teaching in area endorsed'
  END                                 TEACH_IN_AREA_ENDORSED,
  
    CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     DP1 DP2
    WHERE
    DP1.PIC = DP2.PIC
AND DP2.EDUCATOREFFECTIVENESSLABEL IN ('Effective','Highly Effective')
   ) > 0 THEN
    'Effective or higher'
   ELSE
    'Not effective or higher'
  END                                 EFFECTIVE_OR_HIGHER
   
 FROM DP1;


select * from cep_rep_emp_and_edeff
;
