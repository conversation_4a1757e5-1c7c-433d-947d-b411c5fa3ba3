create or replace PACKAGE BODY ZDMCOUPBT AS

  CURSOR current_term_cur(p_term_code VARCHAR2) IS
  SELECT
    COUNT(*) cntr
  FROM
    co_gr_pop_tbl
  WHERE
    co_gr_term_code_key = get_term(p_term_code);  
  TYPE current_term_array IS
    TABLE OF current_term_cur%rowtype;

  --creating a pointer to a row of the current_term_array
  current_term_rec current_term_array;

--------------------------------------------------------------------------------
-- Helper function to get the term code: returns p_term_code if provided, else current term
--------------------------------------------------------------------------------  
    FUNCTION get_term(p_term_code IN VARCHAR2) RETURN VARCHAR2 as
    v_term_code VARCHAR2(20) := '';
  BEGIN
    IF p_term_code IS NOT NULL THEN
      RETURN p_term_code;
    ELSE
      SELECT current_term INTO v_term_code FROM um_current_term;
      RETURN v_term_code;
    END IF;

  END get_term;

--------------------------------------------------------------------------------
-------------------------------------STEP 0-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 0: Remove term from all cohort tables
*******************************************************************************/
-- Add this before the final END ZDMCOUPBT;
  PROCEDURE p0_remove_term(p_term_code IN VARCHAR2) AS
  BEGIN
    dbms_output.put_line('Removing records for term: ' || p_term_code);

    -- Example: Remove from cohort population tables
    DELETE FROM co_pop_undup_tbl WHERE co_term_code_key = p_term_code;
    DELETE FROM co_gr_pop_tbl    WHERE co_gr_term_code_key = p_term_code;

    -- Example: Remove from financial aid table
    DELETE FROM co_fa_tbl        WHERE co_term_code_key = p_term_code;

    -- Example: Remove from NSC tables
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_nsc_student_data_tbl');
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_nsc_degree_tbl');

    -- Example: Remove from 10 year NSC tables
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_ten_yr_nsc_tbl');
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_gr_ten_yr_nsc_tbl');

    -- Example: Remove from persistence tables
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_persist_nsc_tbl');
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_gr_persist_tbl');

    -- Example: Remove from most recent tables
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_most_recent_tbl');
    EXECUTE IMMEDIATE ('TRUNCATE TABLE co_gr_most_recent_tbl');

    COMMIT;
    dbms_output.put_line('Records removed for term: ' || p_term_code);
  END p0_remove_term;

--------------------------------------------------------------------------------
-------------------------------------STEP 2-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 2A: create procedure to update cohort population FIN aid tables
*******************************************************************************/
  PROCEDURE p1_update_co_pop(p_term_code IN VARCHAR2) AS

 /*******************************************************************************
STEP 2B: Open cursor to build a single cell array with the count of records in 
the cursor built in STEP 1B.
*******************************************************************************/
    v_term_code VARCHAR2(20);
  BEGIN
    v_term_code := get_term(p_term_code);
    dbms_output.enable(NULL);
    dbms_output.put_line('Beginning p_update_co_pop procedure for term: ' || v_term_code);
    dbms_output.put_line('Opening current_term_cur');
    OPEN current_term_cur(v_term_code);
    FETCH current_term_cur BULK COLLECT INTO current_term_rec;
    CLOSE current_term_cur;
    dbms_output.put_line('Closing current_term_cur');


/*******************************************************************************
STEP 2F: insert current term data into cohort population tables
*******************************************************************************/ 
--UG cohort table update
      dbms_output.put_line('Inserting data into co_pop_undup_tbl');
      INSERT INTO co_pop_undup_tbl
        SELECT * FROM co_pop_undup
        WHERE co_term_code_key = v_term_code;

--GR cohort table update    
      dbms_output.put_line('Inserting data into co_gr_pop_tbl');
      INSERT INTO co_gr_pop_tbl
        SELECT * FROM CO_GR_POP
        WHERE co_gr_term_code_key = v_term_code;

      COMMIT;

/*******************************************************************************
STEP 2G: Update Fin Aid Table
*******************************************************************************/
      dbms_output.put_line('Inserting data into CO_FA_TBL');
      p_co_fa_tbl(v_term_code); -- This procedure should be updated to use v_term_code if needed
      COMMIT;


/*******************************************************************************
STEP 2H: End the procedures for step 2
*******************************************************************************/
    dbms_output.put_line('Ending p_update_co_pop procedure');
  END p1_update_co_pop;

--------------------------------------------------------------------------------
-------------------------------------STEP 3-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 3A: create procedure to update NSC student and degree tables
*******************************************************************************/
  PROCEDURE p2_update_co_nsc AS
    v_term_code VARCHAR2(20);
  BEGIN
    
    dbms_output.enable(NULL);
    dbms_output.put_line('Beginning p_update_co_nsc procedure');


/*******************************************************************************
STEP 3D: Truncate NSC student and degree tables
*******************************************************************************/
--UG NSC student and degree tables   
    dbms_output.put_line('Truncating co_nsc_student_data_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_NSC_STUDENT_DATA_TBL' );
    dbms_output.put_line('Truncating co_nsc_degree_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_NSC_DEGREE_TBL' );
    
--GR NSC student and degree tables   
    dbms_output.put_line('Truncating co_gr_nsc_student_data_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_NSC_STUDENT_DATA_TBL' );
    dbms_output.put_line('Truncating co_gr_nsc_degree_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_NSC_DEGREE_TBL' );


/*******************************************************************************
STEP 3E: Insert data into NSC student and degree tables
*******************************************************************************/  
--UG NSC student and degree tables   
    dbms_output.put_line('Inserting data into co_nsc_student_data_tbl');
    p_co_nsc_student_data_tbl; -- Update this procedure to use v_term_code if needed
    dbms_output.put_line('Inserting data into co_nsc_degree_tbl');
    p_co_nsc_degree_tbl; -- Update this procedure to use v_term_code if needed

--Gr NSC student and degree tables   
    dbms_output.put_line('Inserting data into co_gr_nsc_student_data_tbl');
    p_co_gr_nsc_student_data_tbl; -- Update this procedure to use v_term_code if needed
    dbms_output.put_line('Inserting data into co_gr_nsc_degree_tbl');
    p_co_gr_nsc_degree_tbl; -- Update this procedure to use v_term_code if needed

    COMMIT;
    dbms_output.put_line('Ending p_update_co_nsc procedure');
  END p2_update_co_nsc;

--------------------------------------------------------------------------------
-------------------------------------STEP 4-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 4A: create procedure to update 10 year nsc tables
*******************************************************************************/
  PROCEDURE p3_update_co_ten_yr_nsc AS

  BEGIN
     dbms_output.enable(NULL);
    dbms_output.put_line('Truncating co_ten_yr_nsc_tbl');


/*******************************************************************************
STEP 4D: Truncate 10 yr tables for UG and GR
*******************************************************************************/
    dbms_output.put_line('Truncating co_ten_yr_nsc_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_TEN_YR_NSC_TBL' );
    dbms_output.put_line('Truncating co_gr_ten_yr_nsc_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_TEN_YR_NSC_TBL' );

/*******************************************************************************
STEP 4E: Insert data into 10 yr tables for UG and GR
*******************************************************************************/  
--UG
    dbms_output.put_line('Inserting data into fall_co_ten_yr_nsc_tbl');
    p_fall_co_ten_yr_nsc_tbl; -- Update to use v_term_code if needed
    dbms_output.put_line('Inserting data into win_co_ten_yr_nsc_tbl');
    p_win_co_ten_yr_nsc_tbl;  -- Update to use v_term_code if needed


--GR
    dbms_output.put_line('Inserting data into fall_co_gr_ten_yr_nsc_tbl');
    p_fall_co_gr_ten_yr_nsc_tbl; -- Update to use v_term_code if needed
    dbms_output.put_line('Inserting data into win_co_gr_ten_yr_nsc_tbl');
    p_win_co_gr_ten_yr_nsc_tbl;  -- Update to use v_term_code if needed
    COMMIT;
  END p3_update_co_ten_yr_nsc;

--------------------------------------------------------------------------------
-------------------------------------STEP 5-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 5A: create procedure to update persistence tables
*******************************************************************************/
  PROCEDURE p4_update_co_persist AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Truncating co_persist_nsc_tbl_bac table');
  
/*******************************************************************************
STEP 5D: Truncate persist tables for UG and GR
*******************************************************************************/
    dbms_output.put_line('Truncating co_persist_nsc_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_PERSIST_NSC_TBL' );
    dbms_output.put_line('Truncating co_gr_persist_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_PERSIST_TBL' );

/*******************************************************************************
STEP 5E: Insert data into persist tables for UG and GR
*******************************************************************************/  
--UG
    dbms_output.put_line('Inserting data into co_persist_nsc_tbl');
    p_co_persist_nsc_tbl; -- Update to use v_term_code if needed
--GR
    dbms_output.put_line('Inserting data into co_gr_persist_tbl');
    p_co_gr_persist_tbl;  -- Update to use v_term_code if needed
    COMMIT;
  END p4_update_co_persist;

--------------------------------------------------------------------------------  
-------------------------------------STEP 6-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 6A: create procedure to update most recent data table
*******************************************************************************/
  PROCEDURE p5_update_most_recent AS

  BEGIN
    dbms_output.enable(NULL);

/*******************************************************************************
STEP 6D: Truncate most recent backup data table
*******************************************************************************/
--UG
    dbms_output.put_line('Truncating co_most_recent_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_MOST_RECENT_TBL' );
--GR
    dbms_output.put_line('Truncating co_gr_most_recent_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_MOST_RECENT_TBL' );

/*******************************************************************************
STEP 6E: Insert most recent data table
*******************************************************************************/
--UG    
    dbms_output.put_line('Inserting data into co_most_recent_tbl');
    p_most_recent_tbl; -- Update to use v_term_code if needed
--GR    
    dbms_output.put_line('Inserting data into co_gr_most_recent_tbl');
    p_gr_most_recent_tbl; -- Update to use v_term_code if needed

    COMMIT;
  END p5_update_most_recent;

END ZDMCOUPBT;