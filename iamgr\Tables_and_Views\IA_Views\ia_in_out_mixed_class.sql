create or replace view IA_IN_OUT_MIXED_CLASS as
(
select 
distinct sd_pidm,
sd_term_code,
report_level_code,
ia_student_type_code,
'In Class Only' in_out_mixed_class_ind
from ia_td_student_data 
left join
(select distinct pidm,SECTION_NUMBER from td_registration_detail where SECTION_NUMBER like 'W%') popcel
on ia_td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and online_courses_only_ind = 'N' and popcel.SECTION_NUMBER is null
union
--Mixed students
select distinct sd_pidm,
sd_term_code,
report_level_code,
ia_student_type_code,
'Mixed Mode' in_out_mixed_class_ind
from ia_td_student_data 
inner join
(select distinct pidm from td_registration_detail where SECTION_NUMBER like 'W%') popcel
on ia_td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and online_courses_only_ind = 'N' 
union
--Online only students
select distinct sd_pidm,
sd_term_code,
report_level_code,
ia_student_type_code,
'Online_only' in_out_mixed_class_ind
from ia_td_student_data 
where registered_ind = 'Y' and online_courses_only_ind = 'Y' 
);