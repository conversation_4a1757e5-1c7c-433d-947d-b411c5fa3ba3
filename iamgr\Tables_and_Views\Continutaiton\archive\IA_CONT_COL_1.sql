/******************************************************************************* 
This Query is designed to build a view to support a continuation rate dashboard 
it will run on the current term and pull in continuation varaiables for the 
following three semesters with open enrollment.  See query below for availible 
terms
Written by: Dan Getty Institutional Analisys
Date: 040521
*******************************************************************************/  
SELECT
 current_term,
 bridge_term,
 next_bridge_term,
 next_2_bridge_term
FROM
 um_current_term;
/******************************************************************************* 
The main query is below
*******************************************************************************/  

 SELECT
  td.sd_pidm pidm,
  td.sd_term_code cur_term_code,
  td.sd_term_desc cur_term_desc,
--  td.primary_level_code,
  td.primary_college_code cur_college_code,
--  td.primary_major_1,
--  td.primary_degree_code,
    (
   SELECT
    um.sd_term_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    cur_reg_term_code, --registered AT UMF for winter
      (
   SELECT
    um.sd_term_desc
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    cur_reg_term_desc, --registered AT UMF for winter
  (
   SELECT
    um.registered_ind
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    cur_reg_term_enrolled_ind, --registered AT UMF for winter
--      (
--   SELECT
--    um.primary_level_code
--   FROM
--    um_student_data um
--   WHERE
--     um.sd_term_code = (
--      SELECT
--       bridge_term
--      FROM
--       um_current_term
--     )
--    AND um.sd_pidm = td.sd_pidm
--    AND um.registered_ind = 'Y'
--  )    cur_reg_term_level_code, --registered AT UMF for winter
 
--   CASE
--   WHEN (
--    SELECT
--     um.primary_level_code
--    FROM
--     um_student_data um
--    WHERE
--      um.sd_term_code = (
--       SELECT
--        bridge_term
--       FROM
--        um_current_term
--      )
--     AND um.sd_pidm = td.sd_pidm
--     AND um.registered_ind = 'Y'
--   ) = td.primary_level_code THEN
--    'Y'
--   ELSE
--    'N'
--  END  cur_reg_term_level_cont,
  (
   SELECT
    um.primary_college_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    cur_reg_term_college_code, --registered AT UMF for winter
 
   CASE
   WHEN (
    SELECT
     um.primary_college_code
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        bridge_term
       FROM
        um_current_term
      )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
   ) = td.primary_college_code THEN
    'Y'
   ELSE
    'N'
  END  cur_reg_term_college_code_cont,
--  (
--   SELECT
--    um.primary_major_1
--   FROM
--    um_student_data um
--   WHERE
--     um.sd_term_code = (
--      SELECT
--       bridge_term
--      FROM
--       um_current_term
--     )
--    AND um.sd_pidm = td.sd_pidm
--    AND um.registered_ind = 'Y'
--  )    cur_reg_term_major_1, --registered AT UMF for winter
-- 
--   CASE
--   WHEN (
--    SELECT
--     um.primary_major_1
--    FROM
--     um_student_data um
--    WHERE
--      um.sd_term_code = (
--       SELECT
--        bridge_term
--       FROM
--        um_current_term
--      )
--     AND um.sd_pidm = td.sd_pidm
--     AND um.registered_ind = 'Y'
--   ) = td.primary_major_1 THEN
--    'Y'
--   ELSE
--    'N'
--  END  cur_reg_term_major_1_cont,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = td.sd_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = td.primary_degree_code
     AND umd.level_code = td.primary_level_code
     AND umd.grad_term_code < (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END  cur_reg_term_grad_ind
 FROM
  td_student_data  td
  LEFT OUTER JOIN td_demographic   dm ON dm.td_term_code = td.sd_term_code
                                       AND dm.dm_pidm = td.sd_pidm
 WHERE
   sd_term_code = (
    SELECT
     current_term
    FROM
     um_current_term
   )
   AND td.registered_ind = 'Y'
;
--Terms to build out
