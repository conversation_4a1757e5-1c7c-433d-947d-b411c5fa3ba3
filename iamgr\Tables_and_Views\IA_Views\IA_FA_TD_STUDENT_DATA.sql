CREATE OR REPLACE VIEW ia_fa_td_student_data AS
 SELECT DISTINCT
  fy,
  fafy,
  sd.sd_term_code,
  sd.sd_term_desc,
  sd.sd_pidm,
  sd.primary_degree_code,
  sd.ia_student_type_code,
  sd.report_level_code,
  sd.report_ethnicity,
  sd.primary_major_1,
  sd.primary_major_1_desc,
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      famgr.fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type = 'LOAN'
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') loan_ind,
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      famgr.fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type IN('GRNT', 'SCHL')
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') grant_schlshp_ind,
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      famgr.fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type = 'WORK'
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') work_study_ind,
  nvl((
   SELECT
    'Y'
   FROM
    dual
   WHERE
    EXISTS(
     SELECT
      fb.pidm
     FROM
      famgr.fa_award_by_term fb
     WHERE
       fb.pidm = sd.sd_pidm
      AND fb.term_code = sd.sd_term_code
      AND fb.fund_type IN('LOAN', 'GRNT', 'SCHL', 'WORK')
      AND fb.paid_amt > 0
      AND fb.paid_amt IS NOT NULL
    )
  ), 'N') any_aid_ind
 FROM
  ia_td_student_data sd
 WHERE
  sd.sd_term_code >= ( to_char(to_number((
   SELECT
    current_term
   FROM
    um_current_term
  ) - 200)) );