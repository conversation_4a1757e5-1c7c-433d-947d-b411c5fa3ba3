--CREATE table ia_kcp_three_yr_tbl AS 
 
  /*******************************************************************************
  TABLE GRAIN: 1 ROW / STUDENT
  PURPOSE: PULL STUDENT DATA PROJECTED FOR KCP REPORT
  *******************************************************************************/
WITH DP1 AS (
SELECT
  CO.CO_PIDM PIDM,
  CO.CO_TERM_CODE_KEY KCP_ENTRY_TERM_CODE,
  CO.FIRST_NAME
  ||' '||CO.LAST_NAME STUDENT_NAME,
  CO.BIRTHDATE,
CO.CO_GENDER,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT FB.FA_PIDM
    FROM IA_FA_FUND_BASE FB
    WHERE FB.FA_PIDM    = CO.co_pidm
--    AND FB.FA_TERM_CODE = CO.CO_TERM_CODE_KEY
    AND FB.FA_FUND_CODE = '1PELL'
    AND FB.FA_OFFER_AMT  >0
    )
  ), 'No') pell_grant_ind,
  CO.DISADVANTAGED_IND,
  CO.CITIZEN_IND,
  DECODE(CO.RESIDENCY_CODE,'R','Yes','No')MI_RES,
  CO.CO_ETHNICITY,
  CO.START_DATE,
  SD.PRIMARY_ADMIT_TERM,
  SD.PRIMARY_ADMIT_CODE,
--First Fall Set
  (SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT TD2.OVERALL_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_OVERALL_GPA,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_MAJOR_CODE,
  (SELECT TD1.CLASS_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_CLASS_CODE,
--Second Fall Set
  (SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT TD2.OVERALL_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD2.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_OVERALL_GPA,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_MAJOR_CODE,
  (SELECT TD1.CLASS_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_CLASS_CODE,
--Third Fall Set
  (SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT TD2.OVERALL_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD2.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_OVERALL_GPA,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_MAJOR_CODE,
  (SELECT TD1.CLASS_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_CLASS_CODE,
--Fourth Fall Set
  (SELECT TD1.sd_term_code
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT TD2.OVERALL_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD2.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_OVERALL_GPA,
  (SELECT TD1.PRIMARY_MAJOR_1
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_MAJOR_CODE,
  (SELECT TD1.CLASS_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_CLASS_CODE, 
  
  CASE
  WHEN (SELECT COUNT(*)
  FROM um_degree UMD
  WHERE UMD.PIDM         = CO.CO_PIDM
  AND UMD.DEGREE_STATUS  = 'AW'
  AND UMD.LEVEL_CODE     = 'UG'
  AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300)))>0
  THEN 'Y'
  ELSE NULL
  END GRAD_IND,
  (
  SELECT MIN(GRAD_DATE)
  FROM um_degree UMD
  WHERE UMD.PIDM         = CO.CO_PIDM
  AND UMD.DEGREE_STATUS  = 'AW'
  AND UMD.LEVEL_CODE     = 'UG'
  AND UMD.DEGREE_CODE    = CO.PRIMARY_DEGREE_CODE
  AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND UMD.GRAD_DATE      > CO.START_DATE
  )GRAD_DATE,
  (SELECT PRIMARY_MAJOR_1
  FROM um_degree UMD
  WHERE UMD.PIDM         = CO.CO_PIDM
  AND UMD.DEGREE_STATUS  = 'AW'
  AND UMD.LEVEL_CODE     = 'UG'
  AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  )GRAD_MAJOR
  
FROM TD_STUDENT_DATA SD
INNER JOIN ia_kcp_pop CO
ON CO.CO_PIDM           = SD.SD_PIDM
AND CO.CO_TERM_CODE_KEY = SD.SD_TERM_CODE

), MAX_TERM AS(
SELECT DP1.PIDM, 
MAX(SD.SD_TERM_CODE) MAX_TERM
--,
--SUM(TOTAL_CREDIT_HOURS_UMF)TOTAL_CREDIT_HOURS
FROM DP1
INNER JOIN TD_STUDENT_DATA SD ON SD.SD_PIDM = DP1.PIDM
WHERE
REGISTERED_IND = 'Y'
AND REPORT_LEVEL_CODE = 'UG'
GROUP BY DP1.PIDM
)
SELECT 
DP1.STUDENT_NAME,
DP1.BIRTHDATE,
DP1.CO_GENDER GENDER,
--MOST_RECENT.PRIMARY_MAJOR_1_DESC ACADEMIC_MAJOR,
MOST_RECENT.TERM_REGISTERED_HOURS CURRENT_LOAD,
MOST_RECENT.OVERALL_HOURS_EARNED,
MOST_RECENT.OVERALL_GPA,
MOST_RECENT.MOST_RECENT_ASTD_CODE,

--IAMGR_IA_UM_STUDENT_DATA.TERM_REGISTERED_HOURS, --
--IAMGR_IA_UM_STUDENT_DATA.OVERALL_HOURS_ATTEMPTED,
--IAMGR_IA_UM_STUDENT_DATA.OVERALL_HOURS_EARNED, --
--IAMGR_IA_UM_STUDENT_DATA.OVERALL_GPA_HOURS, 
--IAMGR_IA_UM_STUDENT_DATA.OVERALL_GPA, --
--IAMGR_IA_UM_STUDENT_DATA.PRIMARY_LEVEL_CODE 

DP1.PELL_GRANT_IND ECONOMIC_DISADVANTAGE,
DP1.DISADVANTAGED_IND ACADEMIC_DISADVANTAGE,
DP1.CITIZEN_IND,
DP1.MI_RES,
DP1.CO_ETHNICITY,

DP1.PIDM,
DP1.KCP_ENTRY_TERM_CODE,
--MAX_TERM.MAX_TERM,
DP1.START_DATE,
DP1.PRIMARY_ADMIT_CODE,
DP1.PRIMARY_ADMIT_TERM,

DP1.FRST_FALL_TERM_CODE,
DP1.FRST_FALL_TERM_REG_IND,
DP1.FRST_FALL_CLASS_CODE,
DP1.FRST_FALL_MAJOR_CODE,
DP1.FRST_FALL_OVERALL_GPA,

SCND_FALL_TERM_CODE,
SCND_FALL_TERM_REG_IND,
SCND_FALL_CLASS_CODE,
SCND_FALL_MAJOR_CODE,
SCND_FALL_OVERALL_GPA,

THRD_FALL_TERM_CODE,
THRD_FALL_TERM_REG_IND,
THRD_FALL_CLASS_CODE,
THRD_FALL_MAJOR_CODE,
THRD_FALL_OVERALL_GPA,

DP1.FRTH_FALL_TERM_CODE,
DP1.FRTH_FALL_TERM_REG_IND,
DP1.FRTH_FALL_CLASS_CODE,
DP1.FRTH_FALL_MAJOR_CODE,
DP1.FRTH_FALL_OVERALL_GPA,

DP1.GRAD_IND,
DP1.GRAD_DATE,
DP1.GRAD_MAJOR

FROM MAX_TERM
LEFT JOIN UM_STUDENT_DATA MOST_RECENT
ON MAX_TERM.PIDM = MOST_RECENT.SD_PIDM
AND MAX_TERM.MAX_TERM = MOST_RECENT.SD_TERM_CODE
LEFT JOIN DP1
ON MAX_TERM.PIDM = DP1.PIDM


;
