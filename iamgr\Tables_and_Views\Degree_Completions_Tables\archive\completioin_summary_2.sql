with 
/*******************************************************************************
primary degree from primary_major_1 where primary_major_2 IS NULL
*******************************************************************************/
pop_sel as( 
  select 
   case
    when ia_cw_hp.degree_code is not null then 'Health'
    when ia_cw_stem.majr_code is not null then 'STEM'
    else 'Standard'
  end hp_stem_order_desc,
  case
    when ia_cw_hp.degree_code is not null then 1 
    when ia_cw_stem.majr_code is not null then 2
    else 3
  end hp_stem_order_num, -- first priority for degree
  ia_um_degree.primary_major_1 degree_major_code,  
  'primary degree' degree_source_desc,
  1 degree_source,
  ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
--  where 
--  ia_um_degree.primary_major_2 is null
--  and pidm = 187157

--  union all
  
/*******************************************************************************
primary degree from primary_major_1 where primary_major_2 IS NOT NULL
*******************************************************************************/
 
--  select
--  case
--    when ia_cw_hp.degree_code is not null then 'Health'
--    when ia_cw_stem.majr_code is not null then 'STEM'
--    else 'Standard'
--  end hp_stem_order_desc,
--  case
--    when ia_cw_hp.degree_code is not null then 1
--    when ia_cw_stem.majr_code is not null then 2
--    else 3
--  end hp_stem_order_num,
--  ia_um_degree.primary_major_1 degree_major_code,  
--  'primary degree' degree_source_desc,
--  2 degree_source,
--  ia_um_degree.*
--  from ia_um_degree
--  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
--                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
--  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
--  where 
--  ia_um_degree.primary_major_2 is not null
  
    union all
/*******************************************************************************
second degree from primary_major_2
*******************************************************************************/
  select 
 case
    when ia_cw_hp.degree_code is not null then 'Health'
    when ia_cw_stem.majr_code is not null then 'STEM'
    else 'Standard'
  end hp_stem_order_desc,
  3 hp_stem_order_num,
  ia_um_degree.primary_major_2 degree_major_code,
  'second major' degree_source_desc,
  3 degree_source,
  ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
  where 
  ia_um_degree.primary_major_2 is not null

)
select 
pop_sel.pidm,
pop_sel.grad_term_code,
pop_sel.DEGREE_CODE,
pop_sel.degree_major_code,
pop_sel.hp_stem_order_desc,
pop_sel.hp_stem_order_num,
pop_sel.degree_source_desc,
pop_sel.degree_source,


--pop_sel.degree_college_code,

--pop_sel.college_order_num,
--pop_sel.hp_stem_order_num,
--pop_sel.degree_seqno
row_number() over(partition by pop_sel.pidm order by pop_sel.degree_source, pop_sel.hp_stem_order_num, pop_sel.degree_seqno )degree_pririty
from 
pop_sel
where 
pop_sel.grad_term_code in ('201440', '201510', '201520', '201530') and
pop_sel.degree_status = 'AW'

order by 
pop_sel.pidm --pop_sel.hp_stem_order_num, pop_sel.degree_seqno
