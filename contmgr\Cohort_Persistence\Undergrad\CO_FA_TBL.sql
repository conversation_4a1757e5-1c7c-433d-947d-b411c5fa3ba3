/**********************************************************************************
This script builds the cohort FA data based on a snapshot of the finaid status
of the studnent at tenth day.  

INSTERT DATA IN FALL FOR NEW COHORT
**********************************************************************************/

TRUNCATE TABLE CO_FA_TBL_BAC;

INSERT INTO CO_FA_TBL_BAC
SELECT * FROM CO_FA_TBL;

SELECT COUNT (*) FROM CO_FA_TBL_BAC;
SELECT COUNT (*) FROM CO_FA_TBL;
commit;

DROP TABLE CO_FA_TBL;
TRUNCATE TABLE CO_FA_TBL;
--CREATE TABLE CO_FA_TBL AS
INSERT INTO CO_FA_TBL

SELECT 
DISTINCT
CO.co_pidm,
  CO.co_term_code_key,
  FY.FY_CO_TERM_CODE,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.co_pidm
    AND FB.TERM_CODE  = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE in ('SCHL','GRNT')
    AND FB.FUND_SOURCE in ('INST','OTHR')
    AND FB.FUND_CODE NOT LIKE ('PRIV%')
    AND FB.PAID_AMT   >0
    AND PAID_AMT                  IS NOT NULL
    )
  ), 'N') ins_grant_ind,--includes grants and scholorships
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.co_pidm
    AND FB.TERM_CODE  = FY.FY_CO_TERM_CODE
    AND (FB.FUND_CODE IN ('UMFSAW','2TRUBL')
    OR FB.FUND_CODE LIKE ('2FMS%') 
    OR FB.FUND_CODE LIKE ('2FT%'))
    AND FB.PAID_AMT   >0
    AND PAID_AMT                  IS NOT NULL
    )
  ), 'N') freshman_merit_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.co_pidm
    AND FB.TERM_CODE  = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE in ('SCHL','GRNT')
    AND FB.FUND_SOURCE IN ('FDRL','STAT')
    OR (FB.FUND_TYPE = 'SCHL' 
    AND FB.FUND_SOURCE = 'OTHR' 
    AND FB.FUND_CODE LIKE ('PRIV%'))
    AND FB.PAID_AMT   >0
    AND PAID_AMT                  IS NOT NULL
    )
  ), 'N') outside_grant_ind,--includes grants and scholorships
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM    = CO.co_pidm
    AND FB.TERM_CODE = FY.FY_CO_TERM_CODE
    AND FB.FUND_CODE = '1PELL'
    AND FB.PAID_AMT  >0
    AND PAID_AMT                 IS NOT NULL
    )
  ), 'N') pell_grant_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM    = CO.co_pidm
    AND FB.TERM_CODE = FY.FY_CO_TERM_CODE
    AND FB.FUND_CODE = '1SUBLN'
    AND FB.PAID_AMT  >0
    AND PAID_AMT                 IS NOT NULL
    )
  ), 'N') fed_subsidized_loan_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'LOAN'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') loan_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'WORK'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') work_study_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'GRNT'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') grant_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'SCHL'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') scholorship_ind
FROM CO_POP_UNDUP_TBL CO
INNER JOIN IAMGR.IA_CW_FY_TERM FY ON CO.CO_TERM_CODE_KEY = FY.FY_TERM_CODE
--WHERE CO.CO_TERM_CODE_KEY = 
--              (select current_term from um_current_term) --UPDTE THIS IN FALL
--ORDER BY 1,2
;

