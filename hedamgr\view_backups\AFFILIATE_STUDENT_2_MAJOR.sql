--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View AFFILIATE_STUDENT_2_MAJOR
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "HEDAMGR"."AFFILIATE_STUDENT_2_MAJOR" ("ID", "HED__ACCOUNT__C", "HED__CONTACT__C", "HED__PRIMARY__C", "HED__ROLE__C") AS 
  with student_sel as(
  select 
  sgbstdn.sgbstdn_pidm ,
  'Student' banner_rec_type,
  sgbstdn.sgbstdn_levl_code,
  sgbstdn.sgbstdn_styp_code,
  sgbstdn.sgbstdn_coll_code_1,
  sgbstdn.sgbstdn_program_1,
  sgbstdn.sgbstdn_majr_code_1,
  sgbstdn.sgbstdn_majr_code_conc_1,
  sgbstdn.sgbstdn_term_code_admit,
  sgbstdn.sgbstdn_stst_code
  from aimsmgr.sgbstdn
  where sgbstdn.sgbstdn_term_code_eff = (select max(s1.sgbstdn_term_code_eff)
                                         from aimsmgr.sgbstdn s1
                                         where s1.sgbstdn_pidm = sgbstdn.sgbstdn_pidm)
  and sgbstdn.sgbstdn_stst_code = 'AS'
  and not exists (select 'is a dup'
                  from aimsmgr.sprhold sprhold
                  where sprhold.sprhold_pidm = sgbstdn.sgbstdn_pidm
                  and sprhold.sprhold_hldd_code = 'DP')
)
select 
aff.id,
account.id hed__account__c,
contact.id hed__contact__c,
'true' hed__primary__c,
'Student' hed__role__c

from student_sel
inner join contact on contact.pidm__c = student_sel.sgbstdn_pidm
inner join account on account.um_external_key__c = student_sel.sgbstdn_majr_code_1 || ' - ' || student_sel.sgbstdn_program_1 || ' (Major)'
left outer join hed__affiliation__c aff on aff.hed__contact__c = contact.id
                                        and aff.hed__account__c = account.id
where student_sel.sgbstdn_pidm = 132931
;
