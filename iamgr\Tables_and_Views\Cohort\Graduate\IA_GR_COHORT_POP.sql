/*******************************************************************************
This script pulls the Graduate Student Cohorts going back to Fall 10
with anamoulous student records removed
pidm    term code
90306   201310
*******************************************************************************/
CREATE OR REPLACE VIEW IA_GR_COHORT_POP
AS
  (SELECT ia_td_student_data.sd_pidm AS CO_GR_PIDM,
    ia_td_student_data.UMID,
    ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
    to_date('01-SEP-'
    ||(to_number(SUBSTR(ia_td_student_data.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
    ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
    ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
    ia_td_student_data.report_ethnicity AS CO_GR_ETHNICITY,
    ia_td_student_data.gender           AS CO_GR_GENDER,
    ia_td_student_data.report_level_code,
    ia_td_student_data.primary_level_code,
    CASE
      WHEN (ia_td_student_data.PRIMARY_DEGREE_CODE = 'MLS'
      AND ia_td_student_data.sd_term_code         IN ('201310'))
      THEN 'MA'
      ELSE ia_td_student_data.PRIMARY_DEGREE_CODE
    END PRIMARY_DEGREE_CODE,
    ia_td_student_data.primary_major_1_CIPC_CODE,
    ia_td_student_data.CITIZENSHIP_CODE,
    ia_td_student_data.CITIZENSHIP_DESC,
    ia_td_student_data.PCOL_GPA_TRANSFERRED_1 trans_gpa,
    ia_td_student_data.PCOL_DESC_1,
    ia_td_student_data.PRIMARY_MAJOR_1,
    ia_td_student_data.PRIMARY_PROGRAM,
    ia_td_student_data.PRIMARY_CONC_1
  FROM ia_td_student_data
  WHERE registered_ind      = 'Y'
  AND ia_student_type_code IN ('N')
  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '201110'
  /* Students record anomalys removed*/
  MINUS
  SELECT ia_td_student_data.sd_pidm AS CO_GR_PIDM,
    ia_td_student_data.UMID,
    ia_td_student_data.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
    to_date('01-SEP-'
    ||(to_number(SUBSTR(ia_td_student_data.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
    ia_td_student_data.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
    ia_td_student_data.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
    ia_td_student_data.report_ethnicity AS CO_GR_ETHNICITY,
    ia_td_student_data.gender           AS CO_GR_GENDER,
    ia_td_student_data.report_level_code,
    ia_td_student_data.primary_level_code,
    CASE
      WHEN (ia_td_student_data.PRIMARY_DEGREE_CODE = 'MLS'
      AND ia_td_student_data.sd_term_code         IN ('201310'))
      THEN 'MA'
      ELSE ia_td_student_data.PRIMARY_DEGREE_CODE
    END PRIMARY_DEGREE_CODE,
    ia_td_student_data.primary_major_1_CIPC_CODE,
    ia_td_student_data.CITIZENSHIP_CODE,
    ia_td_student_data.CITIZENSHIP_DESC,
    ia_td_student_data.PCOL_GPA_TRANSFERRED_1 trans_gap,
    ia_td_student_data.PCOL_DESC_1,
    ia_td_student_data.PRIMARY_MAJOR_1,
    ia_td_student_data.PRIMARY_PROGRAM,
    ia_td_student_data.PRIMARY_CONC_1
  FROM ia_td_student_data
  WHERE registered_ind      = 'Y'
  AND ia_student_type_code IN ('N')
  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '201110'
    --/******************************************************************************
    --Anamalous student records removed Start
    --******************************************************************************/
  AND (ia_td_student_data.sd_pidm     = 90306
  AND ia_td_student_data.sd_term_code = '201110')
    --
  );
