CREATE OR REPLACE PROCEDURE p_dm_dba_new_schema(
	pi_username IN VARCHAR2,
	pi_password IN VARCHAR2) IS
	
	user_name VARCHAR2(20)  	:= pi_username;
	pwd VARCHAR2(20) 		      := pi_password;
-- li_count INTEGER	         := 0;

 
 type grnt_stmt_array is VARRAY(7) OF VARCHAR2(128);
 grnt_stmt grnt_stmt_array;
 
 
BEGIN
 grnt_stmt :=  grnt_stmt_array(
             'CREATE MATERIALIZED VIEW',
             'CREATE SYNONYM',
	       		   'CREATE TABLE',
	       		   'CREATE VIEW',
	       		   'CREATE SESSION',
             'CREATE PROCEDURE',
	       		   'UNLIMITED TABLESPACE',
             'CREATE JOB',
             'MANAGE SCHEDULER'
             ) 
         -- ****** Object: Create User ******
--	EXECUTE IMMEDIATE 'CREATE USER ' || user_name || ' IDENTIFIED BY ' || pwd;

DBMS_OUTPUT.put_line(lv_stmt);     

        -- ****** Object: System privileges for user ******
	

    
             
             || user_name;

--        EXECUTE IMMEDIATE ( lv_stmt );
       	DBMS_OUTPUT.put_line(lv_stmt);
        
	COMMIT;
END p_dm_dba_new_schema;

--begin
--	p_dm_dba_new_schema ('foo','bar');
--end;
--
--CREATE USER foo IDENTIFIED BY bar DEFAULT TABLESPACE DEVELOPMENT;