SELECT DISTINCT
  pidm, 
umid,
last_name,
first_name,
 CASE
 WHEN
 intl_ind = 'Y' THEN
   'IC'
  ELSE
   decode (LEVEL_CODE,'GR','GR','G2','GR','G3','GR','DR','GR','D2','GR','D3','GR','UG')
 END                                                                          
 dept_sort,
 message
FROM
      AIMSMGR.CHECK_APPLICANT;
      
select
count (*)
from 
check_applicant
;
select
to_char(sysdate, 'MM/DD/YYYY HH:MI') run_date,
aimsmgr.zdmclen.f_fill_check_tables() return_message
from dual;

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Projected Student Type does not match Actual Student Type (excluding Readmit projected types)' || ' ' || check_student_data.projected_student_type || ' ' || check_student_data.student_type_code || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code
WHERE (
check_student_data.projected_student_type != check_student_data.student_type_code
OR check_student_data.student_status_code IS NULL
) 
AND check_student_data.projected_student_type != 'R'
AND SYSDATE >= stvterm.stvterm_start_date

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                            dept_sort,
 CAST('Missing Gender' AS VARCHAR2(256))        message
FROM
      aimsmgr.check_demographic
 INNER JOIN aimsmgr.check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.gender IS NULL

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                               dept_sort,
 CAST('Missing Birthdate' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.birthdate IS NULL

UNION

SELECT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                            dept_sort,
 CAST('Missing County' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.a1_county_code IS NULL
 AND ( check_demographic.a1_nation_code IS NULL
       OR check_demographic.a1_nation_code = '157' )

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                                          dept_sort,
 CAST('Suffix Non Conforming : ' || check_demographic.name_suffix AS VARCHAR2(256))           message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.name_suffix NOT IN ( 'I', 'II', 'III', 'IV', 'V','Jr', 'Sr', 
 'Esq','CPA', 'DDS','MD','PhD','DVM','JD','RN','DO','EdD','EdS','OD','RNC',
 'LLD','DD','DC','DMD')

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                                           dept_sort,
 CAST('International Student Missing Country of Citizenship(GOAINTL)' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.nation_citizen_code IS NULL
 AND 
 check_demographic.intl_ind = 'Y'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                                          dept_sort,
 CAST('International Ind is Y but SPBPERS_CITZ_CODE is not NC or NO' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.citizenship_code NOT IN ( 'NO', 'NC' )
 AND check_demographic.intl_ind = 'Y'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                         dept_sort,
 CAST('Citizenship Code is Missing' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.citizenship_code IS NULL

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                  dept_sort,
 CAST('No Active A1 Address' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.a1_street_line1 IS NULL

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                 dept_sort,
 CAST('Not Michigan County' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
  check_demographic.a1_state_code = 'MI'
 AND ( check_demographic.a1_county_code IS NULL
       OR substr(check_demographic.a1_county_code, 1, 2) != 'MI' )

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                        dept_sort,
 CAST('US State but not US Nation' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
  nvl(check_demographic.a1_nation_code, '157') != '157'
 AND check_demographic.a1_state_code IN ( 'AK', 'AL', 'AR', 'AZ', 'CA','CO', 
 'CT', 'DC','DE','FL','GA','HI','IA','ID','IL','IN','KS','KY','LA','MA','MD',
 'ME','MI','MN','MO','MS','MT','NC','ND','NE','NH','NJ','NM','NV','NY','OH',
 'OK','OR','PA','RI','SC','SD','TN','TX','UT','VA','VT','WA','WI','WV','WY' )

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                            dept_sort,
 CAST('Phone contains special characters: '
      || check_demographic.a1_area_code
      || check_demographic.a1_phone_number AS VARCHAR2(256))         message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 REGEXP_LIKE ( check_demographic.a1_phone_number,
               '^(-|_|!|@|#|$|%|^|&|*|(|)|+|=|||{|}|[|]|:|~|`|<|>|,|.|?|/|\|)$' )
 OR REGEXP_LIKE ( check_demographic.a1_area_code,
                  '^(-|_|!|@|#|$|%|^|&|*|(|)|+|=|||{|}|[|]|:|~|`|<|>|,|.|?|/|\|)$' )
UNION
SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                                                    dept_sort,
 CAST('High School GPA may be out of Range: ' || check_demographic.hsch_gpa AS VARCHAR2(256))           message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 ( check_demographic.hsch_gpa < 0
   OR check_demographic.hsch_gpa > 5 )
 OR REGEXP_LIKE ( check_demographic.hsch_gpa,
                  '^(|''_|!|@|#|$|%|^|&|*|(|)|+|=||||{||}|[|]|:|~|`|<|>|?|\|G|g|..||/)$' )
UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                                    dept_sort,
 CAST('AreaCode not 3 or Phone not 7, Nation is US or blank' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 ( nvl(length(check_demographic.a1_area_code), 0) != 3
   OR nvl(length(check_demographic.a1_phone_number), 0) != 7 )
 AND nvl(check_demographic.a1_nation_code, '157') = '157'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                               dept_sort,
 CAST('High School GPA missing for FTIAC' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.hsch_gpa IS NULL
 AND student_type_code = 'F'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                                      dept_sort,
 CAST('International Student (NC Citizenship) Missing Visa type' AS VARCHAR2(256))        message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
WHERE
 check_demographic.visa_type_code IS NULL
 AND check_demographic.citizenship_code = 'NC'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('NV Hold' AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE
check_demographic.reg_with_nv_ind = 'Y'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Invalid Transfer Grade(s)' AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE
check_demographic.invalid_transfer_grade_ind = 'Y'
AND
check_student_data.report_level_code = 'UG'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Missing Rate Code ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE
check_student_data.rate_code IS NULL
AND
check_student_data.primary_level_code NOT IN ('DR', 'D2', 'D3', 'GR', 'G2', 'G3', 'PT', 'NA')

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('UG Rate Code '|| check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE
check_student_data.rate_code = 'UG'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('RNBSN NonRes without RBSN2 Rate Code ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE (
check_student_data.rate_code != 'RBSN2' 
OR check_student_data.rate_code IS NULL
)
AND check_student_data.residency_code = 'N'
AND (
check_student_data.primary_major_1 = 'NURR'
OR check_student_data.primary_major_1 = 'NURN'
)

UNION
SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('UG Rate Code - not UG Level ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.rate_code IN ('UG', 'UG2', 'NUR2', 'NURN2', 'RBSN2')
AND check_student_data.primary_level_code NOT IN ('U3', 'U2', 'UG')

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('UG2 Rate Code - NURG major ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.rate_code = 'UG2'
AND check_student_data.primary_major_1 = 'NURG'

UNION
SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('NUR2 or NURN2 Rate Code - not NURG major ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.rate_code IN ('NUR2', 'NURN2')
AND check_student_data.primary_major_1 != 'NURG'


UNION
SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Missing Residency Code ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.residency_code IS NULL
AND check_student_data.primary_level_code != 'NA'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('FTIAC Primary level not UG ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.primary_level_code != 'UG'
AND check_student_data.student_type_code = 'F'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('U2 or U3 Level with < 90 Hours ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.primary_level_code IN ('U3', 'U2')
AND (
check_student_data.overall_hours_earned IS NULL
OR check_student_data.overall_hours_earned < 90
)

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Registered but not Active Student Status' || ' ' || check_student_data.sd_term_code || ' ' || check_student_data.student_status_code || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE (
check_student_data.student_status_code != 'AS'
OR check_student_data.student_status_code IS NULL
)

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Missing Primary Major ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.primary_major_1 IS NULL

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Projected Student Type does not match Actual Student Type (excluding Readmit projected types)' || ' ' || check_student_data.projected_student_type || ' ' || check_student_data.student_type_code || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code
WHERE (
check_student_data.projected_student_type != check_student_data.student_type_code
OR check_student_data.student_status_code IS NULL
) 
AND check_student_data.projected_student_type != 'R'
AND SYSDATE >= stvterm.stvterm_start_date

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('NCFD Student with Incorrect Degree Code:' || ' ' || check_student_data.primary_degree_code  || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.student_type_code = 'S'
AND (
check_student_data.primary_degree_code NOT IN ('000000', 'TC')
)

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Undeclared College or Student Type:' || ' ' || check_student_data.primary_college_code || ' ' || check_student_data.student_type_code || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.primary_college_code IN ('00', '0')

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('New with Continuing Projected_Student_Type:' || ' ' || check_student_data.new_cont_ind || ' ' || check_student_data.projected_student_type || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.new_cont_ind = 'N'
AND check_student_data.projected_student_type = 'C'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Admitted this term on SGASTDN but no Application for this term:' || ' ' || check_student_data.primary_admit_term || ' ' || check_student_data.admitted_this_term_ind || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.sd_term_code = check_student_data.primary_admit_term
AND check_student_data.admitted_this_term_ind IS NULL

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Continuing with New Projected_Student_Type:' || ' ' || check_student_data.new_cont_ind || ' ' || check_student_data.projected_student_type || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.new_cont_ind = 'C'
AND check_student_data.projected_student_type IN ('N','F','G','R','T')

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('New Student Type, No Admitted Application ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code

WHERE check_student_data.student_type_code IN ('T','N','D','G','F')
AND check_student_data.new_cont_ind = 'C'
AND SYSDATE >= stvterm.stvterm_start_date

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('In ELP course with incorrect RSTS code:' || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.elp_wrong_reg_status_ind = 'Y'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('In ELP course but no ELP level:' || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.elp_wrong_stype_ind = 'Y'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, 
 check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('U2/U3/G2/G3/D2 level with UG/GR/DR course level:' || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.reg_level_mismatch_ind = 'Y'

UNION

SELECT DISTINCT
 check_demographic.dm_pidm pidm, check_demographic.umid,
 check_demographic.last_name,
 check_demographic.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Withdrawn Enr Status with Registered Course Status' || ' ' || check_student_data.sd_term_code AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm

WHERE check_student_data.withdrawn_with_reg_ind = 'Y'

----------------------------Admissions Part-------------------------------------
UNION
SELECT DISTINCT
 check_admissions_applicant.ad_pidm pimd, 
 check_admissions_applicant.umid,
 check_admissions_applicant.last_name,
 check_admissions_applicant.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Readmit Admit Type but Not Contuining Student Type (SAAADMS) ' || check_admissions_applicant.term_code_entry AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN check_admissions_applicant ON check_admissions_applicant.ad_pidm = check_demographic.dm_pidm
-- INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code

WHERE check_admissions_applicant.admt_code = 'RE'
AND ( 
check_admissions_applicant.styp_code != 'C' 
OR check_admissions_applicant.styp_code IS NULL
)

UNION

SELECT DISTINCT
 check_admissions_applicant.ad_pidm pimd, 
 check_admissions_applicant.umid,
 check_admissions_applicant.last_name,
 check_admissions_applicant.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('App Student Type does not match Stu Student Type: ' || check_admissions_applicant.styp_code || ' ' || check_admissions_applicant.styp_code_stu || ' ' || check_admissions_applicant.term_code_entry AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN check_admissions_applicant ON check_admissions_applicant.ad_pidm = check_demographic.dm_pidm
-- INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code

WHERE (
check_admissions_applicant.styp_code != check_admissions_applicant.styp_code_stu
OR check_admissions_applicant.styp_code_stu IS NULL
)

UNION

SELECT DISTINCT
 check_admissions_applicant.ad_pidm pimd, 
 check_admissions_applicant.umid,
 check_admissions_applicant.last_name,
 check_admissions_applicant.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('App Admit Code does not match Stu Admit Code:' || ' ' || check_admissions_applicant.admt_code || ' ' || check_admissions_applicant.admt_code_stu AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN check_admissions_applicant ON check_admissions_applicant.ad_pidm = check_demographic.dm_pidm
-- INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code

WHERE ( 
check_admissions_applicant.admt_code != check_admissions_applicant.admt_code_stu
OR check_admissions_applicant.admt_code_stu IS NULL
)
AND check_admissions_applicant.admt_code != 'CO'

UNION

SELECT DISTINCT
 check_admissions_applicant.ad_pidm pimd, 
 check_admissions_applicant.umid,
 check_admissions_applicant.last_name,
 check_admissions_applicant.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('App Residency does not match Stu Residency:' || ' ' || check_admissions_applicant.resd_code || ' ' || check_admissions_applicant.resd_code_stu || ' ' || check_admissions_applicant.term_code_entry AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN check_admissions_applicant ON check_admissions_applicant.ad_pidm = check_demographic.dm_pidm
-- INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code

WHERE check_admissions_applicant.resd_code != check_admissions_applicant.resd_code_stu

UNION

SELECT DISTINCT
 check_admissions_applicant.ad_pidm pimd, check_admissions_applicant.umid,
 check_admissions_applicant.last_name,
 check_admissions_applicant.first_name,
 CASE
  WHEN check_demographic.intl_ind = 'Y' THEN
   'IC'
  ELSE
   check_student_data.report_level_code
 END                                                                          
 dept_sort,
 CAST('Residency is not R or not N' || ' ' || check_admissions_applicant.resd_code || ' ' || check_admissions_applicant.resd_code_stu || ' ' || check_admissions_applicant.term_code_entry AS VARCHAR2(256))      message
FROM
      check_demographic
 INNER JOIN check_student_data ON check_student_data.sd_pidm = check_demographic.dm_pidm
 INNER JOIN check_admissions_applicant ON check_admissions_applicant.ad_pidm = check_demographic.dm_pidm
-- INNER JOIN aimsmgr.stvterm_ext stvterm ON stvterm.stvterm_code = check_student_data.sd_term_code

WHERE ( 
check_admissions_applicant.resd_code NOT IN ('R', 'N')
OR check_admissions_applicant.resd_code_stu NOT IN ('R', 'N')
)

ORDER BY
 6, 5;