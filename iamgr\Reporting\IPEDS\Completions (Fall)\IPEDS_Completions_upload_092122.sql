/*
This query is designed to create the IPEDS degreee Completions Upload. The table
being queried (IA_DEGREE_COMPLETIONS)will requre to be updated after census day
of the spring semester.  

Export data to comma dilimeted file and edit the file to remove trailing commas.
Upload to IPEDS as Key Value Pair.
Section D must be manually entered.  Use the Tableau Completions Dashboard
*/
WITH ps0 AS (
  SELECT
    pidm,
    age,
    degree_code,
    completion_type_code,
    cip_code,
    nces_code,
    primary_level_code,
    decode(primary_level_code, 'UG', 'UG', 'U2', 'UG',
           'U3', 'UG', 'GR')        report_level_code,
    decode(nces_code, '1b', '9', '5', '4',
           '7', '5', '17', '6', '18',
           '6', '6', '7', '8', '7') completer_level,
    ipeds_race_code,
    report_ethnicity,
    ipeds_gender_code,
    gender,
    CASE
      WHEN age < 18  THEN
        'AGE1'
      WHEN age >= 18
           AND age <= 24 THEN
        'AGE2'
      WHEN age >= 25
           AND age <= 39 THEN
        'AGE3'
      WHEN age >= 40 THEN
        'AGE4'
      ELSE
        'AGE5'
    END                             partd_age
  FROM
    ia_degree_completions
  WHERE
    fiscal_year = '22-23'                --  Update before running
), ps1 AS (
  SELECT
    degree_code,
    completion_type_code,
    cip_code,
    nces_code,
    ipeds_race_code,
    ipeds_gender_code,
    COUNT(pidm) AS deg_count
  FROM
    ps0
  GROUP BY
    degree_code,
    completion_type_code,
    cip_code,
    nces_code,
    ipeds_race_code,
    ipeds_gender_code
), ps2 AS (
  SELECT
    'UNITID=171146'                         a,
    'SURVSECT=COM'                          b,
    'PART=A'                                c,
    'MAJORNUM=' || ps1.completion_type_code d,
    'CIPCODE='
    || substr(ps1.cip_code, 1, 2)
    || '.'
    || substr(ps1.cip_code, 3, 6)           e,
    'AWLEVEL=' || ps1.nces_code             f,
    'RACE=' || ps1.ipeds_race_code          g,
    'SEX=' || ps1.ipeds_gender_code         h,
    'COUNT=' || ps1.deg_count               i
  FROM
    ps1
), ps3 AS (
  SELECT
    ps2.*,
    CASE
      WHEN 
/*******************************************************************************
            Update Online Bachelor's Degrees check OEL website
*******************************************************************************/ 
          ( e = 'CIPCODE=52.0301'
             AND f = 'AWLEVEL=5' )--UG Accounting 
           OR ( e = 'CIPCODE=13.1210'
                AND f = 'AWLEVEL=5' )--UG Early Childhood Studies 
           OR ( e = 'CIPCODE=52.0701'
                AND f = 'AWLEVEL=5' )--UG Entrepreneurship & Innovation Management 
           OR ( e = 'CIPCODE=52.0801'
                AND f = 'AWLEVEL=5' )--UG Finance 
           OR ( e = 'CIPCODE=52.0201'
                AND f = 'AWLEVEL=5' ) --UG General Business: BBA BUS 
           OR ( e = 'CIPCODE=38.0101'
                AND f = 'AWLEVEL=5' )--UG General Philosophy 
           OR ( e = 'CIPCODE=51.0701'
                AND f = 'AWLEVEL=5' )--UG Health Care Administration 
           OR ( e = 'CIPCODE=54.0101'
                AND f = 'AWLEVEL=5' )--UG History 
           OR ( e = 'CIPCODE=30.0000'
                AND f = 'AWLEVEL=5' )--UG Interdiciplinary Studies 
           OR ( e = 'CIPCODE=52.1101'
                AND f = 'AWLEVEL=5' )--UG International Business 
           OR ( e = 'CIPCODE=52.1401'
                AND f = 'AWLEVEL=5' )--UG Marketing 
           OR (E = 'CIPCODE=' 
                AND F = 'AWLEVEL=5')--UG Operations & Supply Chain Management
           OR ( e = 'CIPCODE=52.1001'
                AND f = 'AWLEVEL=5' )--UG Organizational Behavior & Human Resources Management 
           OR ( e = 'CIPCODE=42.0101'
                AND f = 'AWLEVEL=5' )--UG Psychology 
           OR ( e = 'CIPCODE=44.0701'
                AND f = 'AWLEVEL=5' ) --UG Social Work: BSW SWK,SUTI  
           OR ( e = 'CIPCODE=30.9999'
                AND f = 'AWLEVEL=5' ) --Applied Science:UG BAS APLS 
           OR ( e = 'CIPCODE=51.3801'
                AND f = 'AWLEVEL=5' ) --UG Nursing RN to BSN: BSN NURR, NURG - 
           OR ( e = 'CIPCODE=51.0908'
                AND f = 'AWLEVEL=5' ) --UG Respiratory Therapy: BS BSRT 
           OR ( e = 'CIPCODE=51.0701'
                AND f = 'AWLEVEL=5' ) --UG Respiratory Therapy: BS RSP 
           OR ( e = 'CIPCODE=51.0908'
                AND f = 'AWLEVEL=5' ) --UG Respiratory Therapy: BS RSP 
/*******************************************************************************
            Update Online Master's Degrees check OEL website
*******************************************************************************/
           OR ( e = 'CIPCODE=52.0301'
                AND f = 'AWLEVEL=7' ) --GR Accounting: MSACC
           OR ( e = 'CIPCODE=501001'
                AND f = 'AWLEVEL=7' )--Arts Administration: MA
           OR ( e = 'CIPCODE=52.0201'
                AND f = 'AWLEVEL=7' )--GR Business Administration: MBA
           OR ( e = 'CIPCODE=11.0101'
                AND f = 'AWLEVEL=7' ) --GR Computer Science and Information Systems: MS CAIS
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=7')--Educational Administration: MA
           OR ( e = 'CIPCODE=51.0701'
                AND f = 'AWLEVEL=7' )--GR Health Care Management: MS
           OR ( e = 'CIPCODE=24.0199'
                AND f = 'AWLEVEL=7' ) --GR Liberal Studies: MA LBS
           OR ( e = 'CIPCODE=51.3801'
                AND f = 'AWLEVEL=7' ) --GR Nursing: MSN Family Nurse Practitioner MSN NURN 
           OR ( e = 'CIPCODE=44.0401'
                AND f = 'AWLEVEL=7' )--GR Public Administration: MPA
           OR ( e = 'CIPCODE=51.2201'
                AND f = 'AWLEVEL=7' )--Public Health (MPH)
/*******************************************************************************
             Update Online Specialist Programs
*******************************************************************************/
           OR ( e = 'CIPCODE=13.0101'
                AND f = 'AWLEVEL=7' )--Education Specialist: EdS
/*******************************************************************************
             Update Online Doctoral Degrees check OEL website
*******************************************************************************/
           OR ( e = 'CIPCODE=13.0101'
                AND f = 'AWLEVEL=17' )--Education: EdD
           OR ( e = 'CIPCODE=51.2308'
                AND f = 'AWLEVEL=18' ) --DR Physical Therapy: Transitional DPT 
           OR ( e = 'CIPCODE=51.3801'
                AND f = 'AWLEVEL=18' ) --DR Nursing:DNP
/*******************************************************************************
             Update Online Undergrad Certs check OEL website
*******************************************************************************/                           
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Africana Studies
           OR ( e = 'CIPCODE=52.0213'
                AND f = 'AWLEVEL=1b' )--UG Business Leadership
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Early Childhood Trauma & Education
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG International Business Development
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG International Market Analysis
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Nursing & Healthcare InformaticsNursing Case Management
           OR ( e = 'CIPCODE=51.3815'
                AND f = 'AWLEVEL=1b' )--UG Nursing Case Management
           OR ( e = 'CIPCODE=51.3802'
                AND f = 'AWLEVEL=1b' )--UG Nursing Leadership & Management
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Specialist in Aging
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Teacher’s Certificate Minor in Psychology
           OR ( e = 'CIPCODE=13.1401'
                AND f = 'AWLEVEL=1b' )--UG Teaching English to Speakers of Other Languages (TESOL)

/*******************************************************************************
             Update Online Graduate Certs check OEL website
*******************************************************************************/
           OR ( e = 'CIPCODE=52.0301'
                AND f = 'AWLEVEL=8' )--GR Accounting
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Adult Gerontology Acute Care Nurse Practitioner
           OR ( e = 'CIPCODE=52.0201'
                AND f = 'AWLEVEL=8' )--GR Business
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Data Science
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Finance
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Healthcare Administration
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Healthcare Analytics
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Healthcare Simulation
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Information Security
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Intelligent Systems
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR International Business
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Long Term Care Administration
           OR ( e = 'CIPCODE=52.1401'
                AND f = 'AWLEVEL=8' )--GR Marketing
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=8')--GR Nurse Educator
           OR ( e = 'CIPCODE=52.0213'
                AND f = 'AWLEVEL=8' )--GR Organizational Leadership
           OR ( e = 'CIPCODE=51.3810'
                AND f = 'AWLEVEL=8' )--GR Psychiatric Mental Health Nurse Practitioner
                 THEN
        '1'
      ELSE
        '2'
    END dis_ed
  FROM
    ps2
), ps4 AS (
  SELECT DISTINCT
    pidm,
    ipeds_gender_code,
    ipeds_race_code,
    completer_level,
    MAX(age) age
  FROM
    ps0
  GROUP BY
    pidm,
    ipeds_gender_code,
    ipeds_race_code,
    completer_level
), part_c_a AS (
  SELECT DISTINCT
    ipeds_race_code,
    ipeds_gender_code,
    pidm
  FROM
    ps0
  WHERE
    completion_type_code = 1
), part_c AS (
  SELECT
    ipeds_race_code,
    ipeds_gender_code,
    COUNT(*) demo_count
  FROM
    part_c_a
  GROUP BY
    ipeds_race_code,
    ipeds_gender_code
)
SELECT
  a,
  b,
  c,
  d,
  e,
  f,
  g,
  h,
  i,
  CAST(NULL AS VARCHAR2(16)) j,
  CAST(NULL AS VARCHAR2(16)) k,
  CAST(NULL AS VARCHAR2(16)) l,
  CAST(NULL AS VARCHAR2(16)) m,
  CAST(NULL AS VARCHAR2(16)) n,
  CAST(NULL AS VARCHAR2(16)) o,
  CAST(NULL AS VARCHAR2(16)) p,
  CAST(NULL AS VARCHAR2(16)) q,
  CAST(NULL AS VARCHAR2(16)) r,
  CAST(NULL AS VARCHAR2(16)) s,
  CAST(NULL AS VARCHAR2(16)) t
FROM
  ps3
UNION ALL
SELECT DISTINCT
  a,
  b,
  'PART=B'                   c,
  d,
  e,
  f,
  'DistanceED=' || dis_ed    g,
  CAST(NULL AS VARCHAR2(16)) h,
  CAST(NULL AS VARCHAR2(16)) i,
  CAST(NULL AS VARCHAR2(16)) j,
  CAST(NULL AS VARCHAR2(16)) k,
  CAST(NULL AS VARCHAR2(16)) l,
  CAST(NULL AS VARCHAR2(16)) m,
  CAST(NULL AS VARCHAR2(16)) n,
  CAST(NULL AS VARCHAR2(16)) o,
  CAST(NULL AS VARCHAR2(16)) p,
  CAST(NULL AS VARCHAR2(16)) q,
  CAST(NULL AS VARCHAR2(16)) r,
  CAST(NULL AS VARCHAR2(16)) s,
  CAST(NULL AS VARCHAR2(16)) t
FROM
  ps3
UNION ALL
SELECT
  'UNITID=171146'                    a,
  'SURVSECT=COM'                     b,
  'PART=C'                           c,
  'RACE=' || part_c.ipeds_race_code  d,
  'SEX=' || part_c.ipeds_gender_code e,
  'COUNT=' || part_c.demo_count      f,
  CAST(NULL AS VARCHAR2(16))         g,
  CAST(NULL AS VARCHAR2(16))         h,
  CAST(NULL AS VARCHAR2(16))         i,
  CAST(NULL AS VARCHAR2(16))         j,
  CAST(NULL AS VARCHAR2(16))         k,
  CAST(NULL AS VARCHAR2(16))         l,
  CAST(NULL AS VARCHAR2(16))         m,
  CAST(NULL AS VARCHAR2(16))         n,
  CAST(NULL AS VARCHAR2(16))         o,
  CAST(NULL AS VARCHAR2(16))         p,
  CAST(NULL AS VARCHAR2(16))         q,
  CAST(NULL AS VARCHAR2(16))         r,
  CAST(NULL AS VARCHAR2(16))         s,
  CAST(NULL AS VARCHAR2(16))         t
FROM
  part_c
UNION ALL
SELECT
  'UNITID=171146'            a,
  'SURVSECT=COM'             b,
  'PART=E'                   c,
  'CGU01=1'                  d,
  'CGU011='
  || (
    SELECT
      COUNT(*)
    FROM
      ps0
    WHERE
        report_level_code = 'UG'
      AND gender = 'N'
  )                          e,
  'CGU012=0'                 f,
  'CGU02=1'                  g,
  'CGU021='
  || (
    SELECT
      COUNT(*)
    FROM
      ps0
    WHERE
        report_level_code = 'GR'
      AND gender = 'N'
  )                          h,
  'CGU022=0'                 i,
  CAST(NULL AS VARCHAR2(16)) j,
  CAST(NULL AS VARCHAR2(16)) k,
  CAST(NULL AS VARCHAR2(16)) l,
  CAST(NULL AS VARCHAR2(16)) m,
  CAST(NULL AS VARCHAR2(16)) n,
  CAST(NULL AS VARCHAR2(16)) o,
  CAST(NULL AS VARCHAR2(16)) p,
  CAST(NULL AS VARCHAR2(16)) q,
  CAST(NULL AS VARCHAR2(16)) r,
  CAST(NULL AS VARCHAR2(16)) s,
  CAST(NULL AS VARCHAR2(16)) t
FROM
  dual
UNION ALL
SELECT
  'UNITID=171146'                       a,
  'SURVSECT=COM'                        b,
  'PART=D'                              c,
  'CTLEVEL=' || completer_level         d,
  'CRACE15='
  || decode(ipeds_gender_code, 1, 1, 0) e,
  'CRACE16='
  || decode(ipeds_gender_code, 2, 1, 0) f,
  'CRACE17='
  || decode(ipeds_race_code, 1, 1, 0)   g,
  'CRACE41='
  || decode(ipeds_race_code, 2, 1, 0)   h,
  'CRACE42='
  || decode(ipeds_race_code, 3, 1, 0)   i,
  'CRACE43='
  || decode(ipeds_race_code, 4, 1, 0)   j,
  'CRACE44='
  || decode(ipeds_race_code, 5, 1, 0)   k,
  'CRACE45='
  || decode(ipeds_race_code, 6, 1, 0)   l,
  'CRACE46='
  || decode(ipeds_race_code, 7, 1, 0)   m,
  'CRACE47='
  || decode(ipeds_race_code, 8, 1, 0)   n,
  'CRACE23='
  || decode(ipeds_race_code, 9, 1, 0)   o,
  'AGE1='
  ||
  CASE
    WHEN age < 18 THEN
        1
    ELSE
      0
  END
  p,
  'AGE2='
  ||
  CASE
    WHEN age >= 18
         AND age <= 24 THEN
        1
    ELSE
      0
  END
  q,
  'AGE3='
  ||
  CASE
    WHEN age >= 25
         AND age <= 39 THEN
        1
    ELSE
      0
  END
  r,
  'AGE4='
  ||
  CASE
    WHEN age >= 40 THEN
        1
    ELSE
      0
  END
  s,
  'AGE5='
  ||
  CASE
    WHEN age IS NULL THEN
        1
    ELSE
      0
  END
  t
FROM
  ps4;