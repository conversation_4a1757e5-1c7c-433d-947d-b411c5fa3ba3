select
rpad('D1', 2, ' ') as D1,
rpad(' ', 9, ' ') as SSN,
rpad(substr(um_demographic.first_name, 1, 20), 20, ' ')as "First",
rpad(substr(um_demographic.middle_initial, 1, 1), 1, ' ') as "Middle",
rpad(substr(um_demographic.last_name, 1, 20), 20, ' ') as "Last",
rpad(nvl(substr(um_demographic.name_suffix, 1, 5), ' '), 5, ' ') as "Suffix",
substr(to_char(um_demographic.birthdate, 'YYYYMMDD'), 1, 8) as "Birth Date",
substr(to_char((select stvterm.stvterm_start_date from stvterm_ext stvterm where stvterm.stvterm_code = '201110'), 'YYYYMMDD'), 1, 8) as "Begin Search Date",
substr(' ', 1, 1) as "Blank",
substr('002327', 1, 6) as "School Code",
substr('00', 1, 2) as "School Branch",
rpad(sem_farnum_hold.umid, 50, ' ') as "UMID"

from sem_farnum_hold
inner join um_demographic on um_demographic.dm_pidm = sem_farnum_hold.pidm

where substr(sem_farnum_hold.last_term, 1, 4) = '2011'

order by um_demographic.last_name, um_demographic.first_name, um_demographic.umid