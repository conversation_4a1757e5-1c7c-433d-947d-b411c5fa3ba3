/*
eligible for in-state tuition
eligible to apply for financial aid
family income of <=$65k
AND assets <$50k
pursuing first bachelor's degree

Tuition and fees
less Gift Aid (Grants and Scholarships)*/

--pull 1718 fafsa info for UG with residency
SELECT DISTINCT spriden_id, spriden_last_name, spriden_first_name, RCRAPP4_FISAP_INC, SZVCSTU_RESD_CODE, SZVCSTU_LEVL_CODE, 
        
        RCRAPP1_CASH_AMT, RCRAPP1_HME_VAL, RCRAPP1_HME_DBT, RCRAPP4_INV_NET_WORTH, RCRAPP4_PAR_INV_NET_WORTH,
        RCRAPP4_BUS_NET_WORTH, RCRAPP4_PAR_BUS_NET_WORTH, RCRAPP4_FARM_NET_WORTH, RCRAPP4_PAR_FARM_NET_WORTH
from spriden, rcrapp1, rcrapp4, szvcstu
where spriden_pidm = rcrapp1_pidm
and spriden_change_ind IS NULL
and rcrapp1_aidy_code = '1718'
and rcrapp1_curr_rec_ind = 'Y'
and rcrapp1_pidm = rcrapp4_pidm
and rcrapp4_infc_code = rcrapp1_infc_code
and rcrapp4_aidy_code = rcrapp1_aidy_code
and rcrapp4_seq_no = rcrapp1_seq_no 
and spriden_pidm = szvcstu_pidm
and SZVCSTU_TERM_CODE LIKE '2018%'
and SZVCSTU_LEVL_CODE = 'UG'

ORDER BY spriden_id
;


--pull prior degrees and graduation date
sELECT DISTINCT spriden_id, spriden_last_name, spriden_first_name, SFRSTCR_TERM_CODE--,
        --SORDEGR_DEGC_CODE, SORDEGR_DEGC_DATE, SORDEGR_DEGC_YEAR
from spriden,  SFRSTCR--, sordegr
WHERE spriden_change_ind IS NULL
and spriden_pidm = SFRSTCR_PIDM
and SFRSTCR_TERM_CODE LIKE '2018%'
--and Sordegr_PIDM = SFRSTCR_PIDM
ORDER BY spriden_id


--pull tuition and fees
SELECT spriden_id, spriden_last_name, spriden_first_name,
                  TBRACCD_DETAIL_CODE, tbbdetc_desc, TBRACCD_TERM_CODE, TBRACCD_AMOUNT
from spriden,  tbraccd, tbbdetc
WHERE spriden_change_ind IS NULL
and spriden_pidm = tbraccd_PIDM
and tbraccd_term_code like '2018%'
and tbbdetc_detail_code = tbraccd_detail_code
and tbbdetc_dcat_code in ('FEE','TUI')

ORDER BY spriden_id


--pull all gift aid paid 1718
SELECT DISTINCT SPRIDEN_ID, SPRIDEN_LAST_NAME, SPRIDEN_FIRST_NAME, RPRAWRD_FUND_CODE, RFRBASE_FUND_TITLE
                    RPRAWRD_AWST_CODE, RFRBASE_FTYP_CODE, RPRAWRD_PAID_AMT
FROM SPRIDEN, RPRAWRD, RFRBASE
    WHERE SPRIDEN_PIDM = RPRAWRD_PIDM
    AND SPRIDEN_CHANGE_IND IS NULL
    AND RPRAWRD_AIDY_CODE = '1718'
    AND RPRAWRD_AWST_CODE <> 'CNCL'
    AND RPRAWRD_PAID_AMT IS NOT NULL
    AND RPRAWRD_FUND_CODE = RFRBASE_FUND_CODE
    AND RFRBASE_FTYP_CODE in ('GRNT', 'SCHL')
    
    
--just pull residency for all undegrads
sELECT  DISTINCT spriden_id, spriden_last_name, spriden_first_name, SZVCSTU_RESD_CODE, SZVCSTU_LEVL_CODE
FROM spriden, szvcstu
WHERE spriden_change_ind IS NULL
and spriden_pidm = szvcstu_pidm
and SZVCSTU_TERM_CODE LIKE '2018%'
and SZVCSTU_LEVL_CODE = 'UG'
ORDER BY spriden_id






