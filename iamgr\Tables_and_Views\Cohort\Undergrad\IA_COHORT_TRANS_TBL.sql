TRUNCATE TABLE IA_COHORT_TRANS_TBL_BAC;

INSERT INTO IA_COHORT_TRANS_TBL_BAC AS
SELECT * FROM IA_COHORT_TRANS_TBL;
COMMIT;

SELECT COUNT (*) FROM IA_COHORT_TRANS_TBL_BAC;
SELECT COUNT (*) FROM IA_COHORT_TRANS_TBL;

TRUNCATE TABLE IA_COHORT_TRANS_TBL;

INSERT INTO IA_COHORT_TRANS_TBL;


--create table IA_COHORT_TRANS_TBL as
/******************************************************************************
The purpose of this query is to create a view that shows the six year projected
outcome of cohort enrolment in courses that have been designated in high fail-
ure rates referred to as Gate Keeper Courses.  The query will also track 1st year
experience courses and civic engagement courses.  The data will be used for 
correlational research of student success.
******************************************************************************/

SELECT distinct CO.CO_PIDM,
/*******************************************************************************
Gate Keeper Courses
*******************************************************************************/
--Bio 111, 113, 167

/* BIO 111*/
( select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '111') BIO_111_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '111') BIO_111_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '111') BIO_111_ATTMPTS
,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '111') BIO_111_Grade
,

/* BIO 113*/
(select 
td1.I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '113') BIO_113_IND
,
  
(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '113') BIO_113_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '113') BIO_113_ATTMPTS
,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'BIO' and course = '113') BIO_113_Grade
,
--
--/*BIO 167*/
--(select 
--case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
--from  IA_COHORT_TRANS_POP_TBL TD1
--where td1.CO_PIDM = CO.CO_PIDM
--AND subject = 'BIO' and course = '167') BIO_167_IND
--, 

--(select 
--case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
--then  COMPLETED_IND else NULL end CFS_IND
--from  IA_COHORT_TRANS_POP_TBL TD1
--where td1.CO_PIDM = CO.CO_PIDM
--AND subject = 'BIO' and course = '167') BIO_167_CFIRST2SEM_IND
--,

--(select 
--NUMOFTRIALS
--from  IA_COHORT_TRANS_POP_TBL TD1
--where td1.CO_PIDM = CO.CO_PIDM
--AND subject = 'BIO' and course = '167') BIO_167_ATTMPTS
--,

--(select 
--case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
--then  GRADE else NULL end crs_grade
--from  IA_COHORT_TRANS_POP_TBL TD1
--where td1.CO_PIDM = CO.CO_PIDM
--AND subject = 'BIO' and course = '167') BIO_167_Grade
--,

--Chm 140, 150, 260

/*CHM 140*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '140') CHM_140_IND
, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '140') CHM_140_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '140') CHM_140_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '140') CHM_140_Grade,

/* CHM 150*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '150') CHM_150_IND, 
--
(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '150') CHM_150_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '150') CHM_150_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '150') CHM_150_Grade
,

/* CHM 260*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '260') CHM_260_IND, 

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '260') CHM_260_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '260') CHM_260_CFIRST2SEM_IND,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'CHM' and course = '260') CHM_260_Grade,

--MTH 090, 111, 118, 120
/* MTH 090 */
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '090') MTH_090_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '090') MTH_090_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '090') MTH_090_ATTMPTS
,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '090') MTH_090_Grade,

/* MTH 111 */
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '111') MTH_111_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '111') MTH_111_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '111') MTH_111_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '111') MTH_111_Grade
,

/*MTH 118*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '118') MTH_118_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '118') MTH_118_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '118') MTH_118_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '118') MTH_118_Grade,

/*MTH 120*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '120') MTH_120_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '120') MTH_120_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '120') MTH_120_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL td1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'MTH' and course = '120') MTH_120_Grade
,
   
----Phy 143, 145, 243, 245
--/*PHY 143*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '143') PHY_143_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '143') PHY_143_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '143') PHY_143_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '143') PHY_143_Grade,

/*PHY 145*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '145') PHY_145_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '145') PHY_145_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '145') PHY_145_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '145') PHY_145_Grade
,
--
--/*PHY 243 */
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '243') PHY_243_IND, 

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '243') PHY_243_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '243') PHY_243_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '243') PHY_243_Grade
,

/*PHY 245*/
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else 'N' end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '245') PHY_245_IND,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '245') PHY_245_CFIRST2SEM_IND,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '245') PHY_245_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'PHY' and course = '245') PHY_245_Grade
,

/*******************************************************************************
First Year Experience
*******************************************************************************/     
(select 
case when td1.I_TYPE is not null then  td1.I_TYPE else NULL end I_TYPE
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'UNV' and course = '100') UNV_100_IND,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  COMPLETED_IND else NULL end CFS_IND
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'UNV' and course = '100') UNV_100_CFIRST2SEM_IND
,

(select 
NUMOFTRIALS
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'UNV' and course = '100') UNV_100_ATTMPTS,

(select 
case when  (COMPLETED_IND = 'Y' AND (MAXTERM - CO_TERM_CODE_KEY <= 10)) 
then  GRADE else NULL end crs_grade
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND subject = 'UNV' and course = '100') UNV_100_Grade
,

/*******************************************************************************
Civic Engagement Courses
*******************************************************************************/
(
SELECT SUM(NUMOFTRIALS) 
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND   CIVIC_IND ='CE' AND ((COURSE LIKE '1%')  or (COURSE LIKE '2%')  
OR (COURSE LIKE '3%') OR (COURSE LIKE '4%'))AND I_TYPE <> 'W'
GROUP BY td1.CO_PIDM
) CE_ATTMPTS, 

(
SELECT SUM(NUMOFTRIALS) 
from  IA_COHORT_TRANS_POP_TBL TD1
where td1.CO_PIDM = CO.CO_PIDM
AND   CIVIC_IND ='CE' AND ((COURSE LIKE '1%')  or (COURSE LIKE '2%')  
OR (COURSE LIKE '3%') OR (COURSE LIKE '4%'))AND I_TYPE <> 'W' AND COMPLETED_IND ='Y'
GROUP BY td1.CO_PIDM
) CE_CMPLTD 

FROM
IA_COHORT_TRANS_POP_TBL CO;


