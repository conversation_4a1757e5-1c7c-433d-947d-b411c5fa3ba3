SELECT
  M_GLDW1.BUDGET_JRNL_LN.LEDGER_GROUP,
  M_GLDW1.BUDGET_JRNL_LN.ACCOUNT,
  M_GLDW1.BUDGET_JRNL_LN.DEPTID,
  BUD_JRNL_LN_CURR_FN_DEPT_VW.DEPT_DESCR,
  M_GLDW1.BUDGET_JRNL_LN.PROGRAM_CODE,
  BUD_JRNL_LN_CURR_PROGRAM_VW.PROGRAM_DESCR,
  sum(M_GLDW1.BUDGET_JRNL_LN.BUD_JRNL_LN_MONETARY_AMOUNT),
  M_GLDW1.BUDGET_JRNL_LN.BUD_JRNL_LN_DESCR,
  M_GLDW1.BUDGET_JRNL_HDR.BUDG_TRANS_TYPE_DESCR,
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_ID,
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_DATE,
  M_GLDW1.BUDGET_JRNL_LN.BUDGET_PERIOD
FROM
  M_GLDW1.BUDGET_JRNL_LN,
  M_GLDW1.CURR_FN_DEPT_VW  BUD_JRNL_LN_CURR_FN_DEPT_VW,
  M_GLDW1.CURR_PROGRAM_VW  BUD_JRNL_LN_CURR_PROGRAM_VW,
  M_GLDW1.BUDGET_JRNL_HDR
WHERE
  ( M_GLDW1.BUDGET_JRNL_HDR.BUD_JOURNAL_ID=M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_ID AND M_GLDW1.BUDGET_JRNL_HDR.BUD_JOURNAL_DATE = M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_DATE  )
  AND  ( BUD_JRNL_LN_CURR_FN_DEPT_VW.DEPTID=M_GLDW1.BUDGET_JRNL_LN.DEPTID  )
  AND  ( BUD_JRNL_LN_CURR_PROGRAM_VW.PROGRAM_CODE=M_GLDW1.BUDGET_JRNL_LN.PROGRAM_CODE  )
  AND  
  (
   M_GLDW1.BUDGET_JRNL_LN.FUND_CODE  =  '10000'
   AND
   M_GLDW1.BUDGET_JRNL_LN.DEPTID  BETWEEN  '950100'  AND  '964200'
   AND
   M_GLDW1.BUDGET_JRNL_LN.BUDGET_PERIOD  IN  ('2015')
   AND
   M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_DATE  BETWEEN  '01-03-2015 00:00:00'  AND  '31-03-2015 00:00:00'
   AND
   M_GLDW1.BUDGET_JRNL_HDR.BUDG_TRANS_TYPE  IN  ( '2','3','1'  )
  )
GROUP BY
  M_GLDW1.BUDGET_JRNL_LN.LEDGER_GROUP, 
  M_GLDW1.BUDGET_JRNL_LN.ACCOUNT, 
  M_GLDW1.BUDGET_JRNL_LN.DEPTID, 
  BUD_JRNL_LN_CURR_FN_DEPT_VW.DEPT_DESCR, 
  M_GLDW1.BUDGET_JRNL_LN.PROGRAM_CODE, 
  BUD_JRNL_LN_CURR_PROGRAM_VW.PROGRAM_DESCR, 
  M_GLDW1.BUDGET_JRNL_LN.BUD_JRNL_LN_DESCR, 
  M_GLDW1.BUDGET_JRNL_HDR.BUDG_TRANS_TYPE_DESCR, 
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_ID, 
  M_GLDW1.BUDGET_JRNL_LN.BUD_JOURNAL_DATE, 
  M_GLDW1.BUDGET_JRNL_LN.BUDGET_PERIOD
HAVING
  sum(M_GLDW1.BUDGET_JRNL_LN.BUD_JRNL_LN_MONETARY_AMOUNT)  <>  0


