TRUNCATE TABLE IA_FY_COHORT_TEN_YR_TBL_BAC;

INSERT INTO IA_FY_COHORT_TEN_YR_TBL_BAC
SELECT * FROM IA_FY_COHORT_TEN_YR_TBL;
COMMIT;

select count (*) from IA_FY_COHORT_POP_UNDUP_TBL;
SELECT COUNT (*) FROM IA_FY_COHORT_TEN_YR_TBL;
SELECT COUNT (*) FROM IA_FY_COHORT_TEN_YR_TBL_BAC;


TRUNCATE TABLE IA_FY_COHORT_TEN_YR_TBL;

INSERT INTO IA_FY_COHORT_TEN_YR_TBL
--CREATE TABLE IA_FY_COHORT_TEN_YR_TBL AS
( SELECT DISTINCT
/*******************************************************************************
TABLE GRAIN: 1 ROW / STUDENT
PURPOSE: PULL STUDENT DATA PROJECTED OUT 6 YEARS ON A STUDENT AND CREATE VIEW
PRIMARY KEYS: CO.CO_PIDM
FOREIGN KEYS: TD_STUDENT_DATA.SD_PIDM
*******************************************************************************/
CO_PIDM,
FY,
FY_TERM_CODE,
FY_CO_TERM_CODE,
-- SECOND FALL SET *************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 100))
AND TD1.SD_PIDM        = CO.CO_PIDM
)SCND_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 100))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)SCND_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 100)))>0
THEN 'Y'
ELSE NULL
END SCND_FALL_GRAD_IND,
----------------------------THIRD-----------------------------------------------
-- THIRD FALL SET **************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 200))
AND TD1.SD_PIDM        = CO.CO_PIDM
)THRD_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 200))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)THRD_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 200)))>0
THEN 'Y'
ELSE NULL
END THRD_FALL_GRAD_IND,

----------------------------FOURTH---------------------------------------------------
-- FOURTH FALL SET ******************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 300))
AND TD1.SD_PIDM        = CO.CO_PIDM
)FRTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 300))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)FRTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 300)))>0
THEN 'Y'
ELSE NULL
END FRTH_FALL_GRAD_IND,
-----------------------------FIFTH--------------------------------------------------
-- FIFTH FALL SET ******************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 400))
AND TD1.SD_PIDM        = CO.CO_PIDM
)FFTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 400))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)FFTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 400)))>0
THEN 'Y'
ELSE NULL
END FFTH_FALL_GRAD_IND,
-----------------------------SIXTH--------------------------------------------
-- SIXTH FALL SET **************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 500))
AND TD1.SD_PIDM        = CO.CO_PIDM
)SXTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 500))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)SXTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 500)))>0
THEN 'Y'
ELSE NULL
END SXTH_FALL_GRAD_IND,
-----------------------------SEVENTH-------------------------------------------
-- SEVENTH FALL SET ************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 600))
AND TD1.SD_PIDM        = CO.CO_PIDM
)SVNTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 600))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)SVNTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 600)))>0
THEN 'Y'
ELSE NULL
END SVNTH_FALL_GRAD_IND,

-----------------------------EIGHTH----------------------------------------------
-- EIGHTH FALL SET ************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 700))
AND TD1.SD_PIDM        = CO.CO_PIDM
)EIGTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 700))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)EIGTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 700)))>0
THEN 'Y'
ELSE NULL
END EIGTH_FALL_GRAD_IND,
-----------------------------NINTH----------------------------------------------
-- NINTH FALL SET ************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 800))
AND TD1.SD_PIDM        = CO.CO_PIDM
)NINTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 800))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)NINTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 800)))>0
THEN 'Y'
ELSE NULL
END NINTH_FALL_GRAD_IND,
-----------------------------TENTH----------------------------------------------
-- TENTH FALL SET ************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 900))
AND TD1.SD_PIDM        = CO.CO_PIDM
)TENTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 900))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)TENTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 900)))>0
THEN 'Y'
ELSE NULL
END TENTH_FALL_GRAD_IND,
-----------------------------ELEVENTH-------------------------------------------
-- ELEVENTH FALL SET ************************************************************
(SELECT TD1.sd_term_code
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 1000))
AND TD1.SD_PIDM        = CO.CO_PIDM
)ELEVENTH_FALL_TERM_CODE,
(SELECT TD1.REGISTERED_IND
FROM TD_STUDENT_DATA TD1
WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 1000))
AND TD1.SD_PIDM            = CO.CO_PIDM
AND TD1.PRIMARY_LEVEL_CODE = 'UG'
AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
)ELEVENTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
CASE
WHEN (SELECT COUNT(*)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 1000)))>0
THEN 'Y'
ELSE NULL
END ELEVENTH_FALL_GRAD_IND,

--Grad Date*********************************************************************
(SELECT min(GRAD_DATE)
FROM um_degree
WHERE UM_DEGREE.PIDM         = CO.CO_PIDM
AND UM_DEGREE.DEGREE_STATUS  = 'AW'
AND UM_DEGREE.LEVEL_CODE     = 'UG'
AND UM_DEGREE.DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.FY_CO_TERM_CODE) + 1010))
AND UM_DEGREE.GRAD_DATE > CO.START_DATE
)UMF_GRAD_DATE
--COHORT DEMOGRAPHIC TABLE JOINS AND WHERE CLAUSE ******************************
FROM TD_STUDENT_DATA
INNER JOIN IA_FY_COHORT_POP_UNDUP_TBL CO
ON CO.CO_PIDM = TD_STUDENT_DATA.SD_PIDM
AND CO.FY_TERM_CODE = TD_STUDENT_DATA.SD_TERM_CODE
--    WHERE CO.CO_PIDM = '106835'
);
