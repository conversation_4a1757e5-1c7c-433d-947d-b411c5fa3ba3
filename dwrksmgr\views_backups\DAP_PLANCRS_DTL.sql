--------------------------------------------------------
--  File created - Thursday-May-19-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View DAP_PLANCRS_DTL
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DWRKSMGR"."DAP_PLANCRS_DTL" ("DAP_STU_ID", "DW_PIDM", "DW_UMID", "DAP_SCHOOL", "DAP_DEGREE", "DAP_ACTIVE_FLAG", "DAP_TERM", "DAP_DISCIPLINE", "DAP_COURSE_NUM", "DAP_GRID_ROW", "DAP_GRID_COL", "DAP_CRS_SEQ", "DAP_PLAN_NUM", "UNIQUE_ID", "UNIQUE_KEY", "DAP_CREDITS") AS 
  select "DAP_STU_ID",
  spriden.spriden_pidm dw_pidm,
  spriden.spriden_id dw_umid,
  "DAP_SCHOOL","DAP_DEGREE","DAP_ACTIVE_FLAG","DAP_TERM","DAP_DISCIPLINE","DAP_COURSE_NUM","DAP_GRID_ROW","DAP_GRID_COL","DAP_CRS_SEQ","DAP_PLAN_NUM","UNIQUE_ID","UNIQUE_KEY","DAP_CREDITS"
from <EMAIL>
inner join aimsmgr.spriden on spriden.spriden_id = cast(trim(dap_stu_id) as varchar2(9))
                           and spriden.spriden_change_ind is null
;
