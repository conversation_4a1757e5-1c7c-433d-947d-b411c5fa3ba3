CREATE OR REPLACE VIEW ia_co_ft_ftiac_ret_by_col AS
WITH dp1 AS (
    SELECT
        ty.*,
        hlc.co_term_code_key,
        hlc.co_full_part_ind_umf,
        hlc.co_ia_student_type_code,
        hlc.co_term_desc,
        hlc.primary_college_code
    FROM
             ia_cohort_ten_yr_tbl_nsc ty
        INNER JOIN ia_hlc_study_tbl hlc ON hlc.co_pidm = ty.co_pidm
--where
    ), total_headcount AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) total_headcount
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
), frst_win_reg AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) frst_win_reg
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
        AND dp1.frst_win_term_reg_ind = 'Y'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
), scnd_fall_reg AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) scnd_fall_reg
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
        AND dp1.scnd_fall_term_reg_ind = 'Y'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
), thrd_fall_reg AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) thrd_fall_reg
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
        AND dp1.thrd_fall_term_reg_ind = 'Y'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
), ffth_fall_grad AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) ffth_fall_grad
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
        AND dp1.ffth_fall_grad_ind = 'Y'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
), svnth_fall_grad AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) svnth_fall_grad
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
        AND dp1.svnth_fall_grad_ind = 'Y'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
), ninth_fall_grad AS (
    SELECT
        dp1.co_term_desc,
        dp1.primary_college_code,
        COUNT(*) ninth_fall_grad
    FROM
        dp1
    WHERE
            dp1.co_full_part_ind_umf = 'F'
        AND dp1.co_ia_student_type_code = 'F'
        AND dp1.ninth_fall_grad_ind = 'Y'
    GROUP BY
        dp1.co_term_desc,
        dp1.primary_college_code
    ORDER BY
        1,
        2
)
SELECT
    th.co_term_desc            term,
    th.primary_college_code    college,
    th.total_headcount,
    fw.frst_win_reg,
    sf.scnd_fall_reg,
    tf.thrd_fall_reg,
    ff.ffth_fall_grad,
    svf.svnth_fall_grad,
    nf.ninth_fall_grad
FROM
    total_headcount  th
    LEFT JOIN frst_win_reg     fw ON th.co_term_desc = fw.co_term_desc
                                 AND th.primary_college_code = fw.primary_college_code
    LEFT JOIN scnd_fall_reg    sf ON th.co_term_desc = sf.co_term_desc
                                  AND th.primary_college_code = sf.primary_college_code
    LEFT JOIN thrd_fall_reg    tf ON th.co_term_desc = tf.co_term_desc
                                  AND th.primary_college_code = tf.primary_college_code
    LEFT JOIN ffth_fall_grad   ff ON th.co_term_desc = ff.co_term_desc
                                   AND th.primary_college_code = ff.primary_college_code
    LEFT JOIN svnth_fall_grad  svf ON th.co_term_desc = svf.co_term_desc
                                     AND th.primary_college_code = svf.primary_college_code
    LEFT JOIN ninth_fall_grad  nf ON th.co_term_desc = nf.co_term_desc
                                    AND th.primary_college_code = nf.primary_college_code    

--select * from frst_win_reg
--select * from scnd_fall_reg
--select * from thrd_fall_reg