CREATE OR R<PERSON>LACE VIEW ia_td_test_score_isc AS
  (SELECT TD_TEST_SCORE.PIDM,
    TD_TEST_SCORE.TD_TERM_CODE,
    TD_TEST_SCORE.act_composite,
    TD_TEST_SCORE.act_math,
    TD_TEST_SCORE.act_english,
    TD_TEST_SCORE.TOEFL_IBT_TOTAL,
    TD_TEST_SCORE.IELTS_OVERALL,
    CASE
      WHEN TD_TEST_SCORE.TOEFL_IBT_TOTAL IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL    IS NULL
      THEN NULL
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 32
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 34
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '4.0')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 32
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 34
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '4.0')
      THEN 'I1'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 35
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 45
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '4.5')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 35
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 45
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '4.5')
      THEN 'I2'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 46
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 59
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 46
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 59
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      THEN 'I3'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 60
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 78
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 60
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 78
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      THEN 'I4'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 79
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 93
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 79
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 93
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      THEN 'A1'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 94
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 101
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.5')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 94
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 101
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.5')
      THEN 'A2'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 102
      AND TD_TEST_SCORE.IELTS_OVERALL                in ('7.0','7.5'))
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 102
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                in ('7.0','7.5'))
      THEN 'A3'
      ELSE 'Outlier'
    END ILSC_LEVEL,
    CASE
      WHEN TD_TEST_SCORE.TOEFL_IBT_TOTAL IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL    IS NULL
      THEN NULL
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) < 60
      AND TD_TEST_SCORE.IELTS_OVERALL                = '4.5')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   < 60
      AND TD_TEST_SCORE.IELTS_OVERALL               IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)  IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                = '4.5')
      THEN 'Pre-Intermediate'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 60
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 69
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 60
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 69
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.0')
      THEN 'Intermediate'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 70
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 79
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 70
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 79
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '5.5')
      THEN 'Upper Intermediate'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 80
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 89
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 80
      AND to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   <= 89
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.0')
      THEN 'Lower Advanced'
      WHEN (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL) >= 90
      AND TD_TEST_SCORE.IELTS_OVERALL                 = '6.5')
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   >= 90
      AND TD_TEST_SCORE.IELTS_OVERALL                IS NULL)
      OR (to_number(TD_TEST_SCORE.TOEFL_IBT_TOTAL)   IS NULL
      AND TD_TEST_SCORE.IELTS_OVERALL                IN ('6.5','7.0','7.5'))
      THEN 'Upper Advanced'
      ELSE 'Outlier'
    END VGC_LEVEL
  FROM TD_TEST_SCORE
  );