--------------------------------------------------------
--  DDL for View STUDENT_ACCOUNT_DETAIL
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "BO_FLINT_STUDENT"."STUDENT_ACCOUNT_DETAIL" ("PIDM", "Amount", "Type", "First Name", "Last Name", "Middle Initial", "UMID", "SSN", "Tbraccd Term Code", "Detail Code", "Description", "Balance", "User", "TBBDETC_DCAT_CODE", "TBRACCD_ENTRY_DATE", "Effective Date", "Activity Date", "Address", "City", "State", "Zip", "A1 Area Code", "A1 Phone Number", "Ca Email", "Residency", "Registered Ind", "Registered Hours", "Billing Hours", "Subject", "Course", "Section", "Class", "Primary College Code", "Primary College Desc", "TTVDCAT_DESC") AS 
  SELECT
TBRACCD.TBRACCD_PIDM AS "PIDM",
TBRACCD.TBRACCD_AMOUNT AS "Amount",
TBBDETC_TYPE_IND AS "Type",
UM_DEMOGRAPHIC.FIRST_NAME AS "First Name",
UM_DEMOGRAPHIC.LAST_NAME AS "Last Name",
UM_DEMOGRAPHIC.MIDDLE_INITIAL AS "Middle Initial",
UM_DEMOGRAPHIC.UMID AS "UMID",
UM_DEMOGRAPHIC.SSN AS "SSN",
TBRACCD.TBRACCD_TERM_CODE AS "Tbraccd Term Code",
TBRACCD.TBRACCD_DETAIL_CODE AS "Detail Code",
TBRACCD.TBRACCD_DESC AS "Description",
TBRACCD.TBRACCD_BALANCE AS "Balance",
TBRACCD.TBRACCD_USER AS "User",
TBBDETC.TBBDETC_DCAT_CODE,
TBRACCD.TBRACCD_ENTRY_DATE,
TBRACCD.TBRACCD_EFFECTIVE_DATE AS "Effective Date",
TBRACCD.TBRACCD_ACTIVITY_DATE AS "Activity Date",
UM_DEMOGRAPHIC.A1_STREET_LINE1 AS "Address",
UM_DEMOGRAPHIC.A1_CITY AS "City",
UM_DEMOGRAPHIC.A1_STATE_CODE AS "State",
UM_DEMOGRAPHIC.A1_ZIP AS "Zip",
UM_DEMOGRAPHIC.A1_AREA_CODE AS "A1 Area Code",
UM_DEMOGRAPHIC.A1_PHONE_NUMBER AS "A1 Phone Number",
UM_DEMOGRAPHIC.CA_EMAIL AS "Ca Email",
UM_STUDENT_DATA.RESIDENCY_CODE AS "Residency",
UM_STUDENT_DATA.REGISTERED_IND AS "Registered Ind",
UM_STUDENT_DATA.TERM_REGISTERED_HOURS AS "Registered Hours",
UM_STUDENT_DATA.TERM_BILLING_HOURS AS "Billing Hours",
UM_CATALOG_SCHEDULE.SUBJ_CODE AS "Subject",
UM_CATALOG_SCHEDULE.CRSE_NUMBER AS "Course",
UM_CATALOG_SCHEDULE.SEQ_NUMBER_KEY AS "Section",
UM_STUDENT_DATA.CLASS_DESC AS "Class",
UM_STUDENT_DATA.PRIMARY_COLLEGE_CODE AS "Primary College Code",
UM_STUDENT_DATA.PRIMARY_COLLEGE_DESC AS "Primary College Desc",
ttvdcat_desc

FROM
TBRACCD

INNER JOIN aimsmgr.TBBDETC
ON TBRACCD.TBRACCD_DETAIL_CODE = TBBDETC.TBBDETC_DETAIL_CODE

LEFT OUTER JOIN UM_DEMOGRAPHIC
ON TBRACCD.TBRACCD_PIDM = UM_DEMOGRAPHIC.DM_PIDM

LEFT OUTER JOIN UM_STUDENT_DATA
ON TBRACCD.TBRACCD_PIDM = UM_STUDENT_DATA.SD_PIDM
AND TBRACCD.TBRACCD_TERM_CODE = UM_STUDENT_DATA.SD_TERM_CODE

INNER JOIN aimsmgr.STVTERM
ON TBRACCD.TBRACCD_TERM_CODE = STVTERM.STVTERM_CODE

LEFT OUTER JOIN UM_CATALOG_SCHEDULE
ON TBRACCD.TBRACCD_TERM_CODE = UM_CATALOG_SCHEDULE.TERM_CODE_KEY
AND TBRACCD.TBRACCD_CRN = UM_CATALOG_SCHEDULE.CRN_KEY

LEFT OUTER JOIN aimsmgr.TBBACCT
ON TBRACCD.TBRACCD_PIDM = TBBACCT.TBBACCT_PIDM

left outer join
ttvdcat
on ttvdcat_code = tbbdetc.tbbdetc_dcat_code
;
