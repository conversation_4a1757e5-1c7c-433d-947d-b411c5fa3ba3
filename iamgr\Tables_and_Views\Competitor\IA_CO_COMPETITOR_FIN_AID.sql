CREATE OR REPLACE VIEW IA_CO_COMPETITOR_FIN_AID AS(
SELECT 
CO.CO_PIDM,
CO.CO_TERM_CODE_KEY,
SUM(INST_GIFT_AMT) INST_AID_OFFER_AMT,
--SUM(STATE_GIFT_AMT) STATE_AID_OFFER_AMT,
SUM(FED_GIFT_AMT) FED_AID_OFFER_AMT,
SUM(OUT_GIFT_AMT) SCHLRSHP_OFFER_AMT,
SUM(EFC) EFC
FROM IA_CO_COMPETITOR_STUDENT_DATA CO
LEFT OUTER JOIN IA_EFC_ABILITY_MATRIX AM
ON AM.PIDM_KEY = CO.CO_PIDM
AND AM.TERM_CODE_KEY = CO.CO_TERM_CODE_KEY
GROUP BY
CO.CO_PIDM,
CO.CO_TERM_CODE_KEY
)
;

select co_pidm, 
count (co_pidm) Headcount
from IA_CO_COMPETITOR_STUDENT_DATA
group by co_pidm;

--
--SELECT 
--CO_PIDM,
--CO_TERM_CODE_KEY,
--SUM(FA_OFFER_AMT) INST_AID_OFFER_AMT
--FROM IA_COMPETITOR_STUDENT_DATA
--LEFT OUTER JOIN IA_FA_FUND_BASE 
--ON IA_FA_FUND_BASE.FA_PIDM = IA_COMPETITOR_STUDENT_DATA.CO_PIDM
--AND IA_FA_FUND_BASE.FA_TERM_CODE = IA_COMPETITOR_STUDENT_DATA.CO_TERM_CODE_KEY
--AND IA_FA_FUND_BASE.FA_FUND_SOURCE = 'INST'
----AND IA_FA_FUND_BASE.FA_FUND_CODE IN ('2FMS','2CRITD','2MMTG','2OCHG','2RARB','2UG','2UG2','2UGGRD')
--GROUP BY
--CO_PIDM,
--CO_TERM_CODE_KEY
----)
--;
--
--SELECT 
--CO_PIDM,
--CO_TERM_CODE_KEY,
--SUM(FA_OFFER_AMT) INST_AID_OFFER_AMT
--FROM IA_COHORT_POP_UNDUP_TBL
--LEFT JOIN IA_FA_FUND_BASE 
--ON IA_FA_FUND_BASE.FA_PIDM = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
--AND IA_FA_FUND_BASE.FA_TERM_CODE = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
--AND IA_FA_FUND_BASE.FA_FUND_CODE IN ('2CRITD','2MMTG','2OCHG','2RARB','2UG','2UG2','2UGGRD')
--GROUP BY
--CO_PIDM,
--CO_TERM_CODE_KEY
--;