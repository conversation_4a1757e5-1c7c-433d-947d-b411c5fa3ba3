CREATE OR REPLACE VIEW IA_COMPETITOR_STUDENT_DATA AS
(
SELECT popsel.*
FROM
  ( WITH data_pull AS
  (SELECT DISTINCT  -- remove exact duplicates in the raw clearinghouse file
    PIDM co_pidm , -- clean pidm
MAPPED_TERM_CODE co_term_code_key, --create cohort term
    CASE                      --F=Full Time, Q=3/4 time, H=Half Time, L=Less than Half-Time
      WHEN enrollment_status IN ('F','Q','H','L')
      THEN 'Y'
      ELSE 'N'
    END registered_ind, --Indicates students enrolled (Less than HT to FT)
    CASE
      WHEN COLLEGE_CODE = '002327-00'
      THEN '2'
      ELSE '1'
    END COLLEGE_PRIORITY, --creates ind idenityihng umf with 1 all others get 2
    --to_date(SEARCH_DATE, 'YYYYMMDD') search_date,   --transforms date filed to match banner date format
    SEARCH_DATE,
    ENROLLMENT_BEGIN,
   SUBSTR(TO_CHAR(ENROLLMENT_BEGIN, 'YYYYMMDD'),1,4) ENROLLMENT_BEGIN_year, --strips out the year of enrollment
    SUBSTR(TO_CHAR(ENROLLMENT_BEGIN,'YYYYMMDD'),5,2) ENROLLMENT_BEGIN_Month,--strips out the month of enrollment
    ENROLLMENT_MAJOR_1,
    ENROLLMENT_CIP_1,
    TWOYEAR_FOURYEAR,
    PUBLIC_PRIVATE,
    COLLEGE_CODE,
    COLLEGE_NAME
--  FROM IA_COMPETITOR_NSC_RAW
FROM NSCMGR.NSC_META
  WHERE (class_level IS NULL                           -- some students have no class level but need to stay in set
  OR class_level     IN ('F','S','J','R','C','N','B')) -- keeps UG's
  AND GRADUATED_IND   = 'N'                            -- removes degree records
  AND ENROLLMENT_CIP_1 IS NOT NULL -- removes records with no major data, does however keep undeclared majors

  ),
  term_code_step1 AS
  (SELECT data_pull.*,
    /*
    The enrollment months are chosen based on the enrollment schedule used at UMF
    at the bottom of the query there are records removed that have months that do
    not mach UMF enrollment months.  This will allow for the ability to better identify
    concurrent enrollment and when a student enrolls elsewhere during a UMF semester.
    */
     CASE
      WHEN ENROLLMENT_BEGIN_Month IN ('06','07')
      THEN '40'
      WHEN ENROLLMENT_BEGIN_Month IN ('08','09')
      THEN '10'
      WHEN ENROLLMENT_BEGIN_Month IN ('12','01')
      THEN '20'
      WHEN ENROLLMENT_BEGIN_Month IN ('04','05')
      THEN '30'
    END enroll_begin_semester_code -- creates the Banner semester code based on enrollment begin month
  FROM data_pull
  WHERE registered_ind  = 'Y'      -- Keep only students enrolled (Less than HT to FT)
  --and ENROLLMENT_BEGIN_Month NOT IN ('11','03', '02', '10') -- removes records where enrollment was in a month that does not match UMF Enrollment cycles
  ),
  term_code AS
  (SELECT term_code_step1.*,
    CASE
      WHEN enroll_begin_semester_code = '10'
      THEN TO_CHAR(to_number (ENROLLMENT_BEGIN_year)+1) -- this identifies the fall term and adds 1 to the year to match Banner fall term codes
        || enroll_begin_semester_code
      WHEN enroll_begin_semester_code = '20'
      THEN ENROLLMENT_BEGIN_year
        || enroll_begin_semester_code
      WHEN enroll_begin_semester_code = '30'
      THEN ENROLLMENT_BEGIN_year
        || enroll_begin_semester_code
      WHEN enroll_begin_semester_code = '40'
      THEN ENROLLMENT_BEGIN_year
        || enroll_begin_semester_code
    END term_code_enrollment_begin -- Creates the Enrollment Term Code by concatinating the year and the semester code
  FROM term_code_step1
  ),
  concurent_enrolled AS
  (SELECT 
    row_number() over(partition BY co_pidm, term_code_enrollment_begin, COLLEGE_CODE order by term_code_enrollment_begin) dup_record,  -- identifies the duplicate records with the same enrollment begin term at the same college
    row_number() over(partition BY co_pidm, term_code_enrollment_begin order by co_pidm, term_code_enrollment_begin, COLLEGE_PRIORITY ) concurent_enrolled, -- identifies the concurrent records of students attending another college while attending UMF
    row_number() over(partition By CO_PIDM, COLLEGE_CODE
    order by TERM_CODE_ENROLLMENT_BEGIN) college_attend_order, -- identifies colleges attended by student in a time series order
    co_pidm,
    co_term_code_key,
    term_code_enrollment_begin,
    registered_ind,
    TWOYEAR_FOURYEAR,
    PUBLIC_PRIVATE,
    COLLEGE_CODE,
    COLLEGE_NAME,
    ENROLLMENT_MAJOR_1 ENROLLMENT_MAJOR,
    ENROLLMENT_CIP_1 ENROLLMENT_CIP
  FROM term_code
   )
SELECT 
 -- row_number() over(partition BY co_pidm, term_code_enrollment_begin order by co_pidm, term_code_enrollment_begin) final_dup_check, -- final check to identify duplicate records with the same pidm and enrollment term code
 -- college_attend_order,
  to_number(co_pidm)co_pidm,
  co_term_code_key,
  registered_ind,
  TWOYEAR_FOURYEAR,
  PUBLIC_PRIVATE,
  COLLEGE_CODE,
  COLLEGE_NAME,
  ENROLLMENT_MAJOR,
  ENROLLMENT_CIP,
  min (term_code_enrollment_begin) term_code_enrollment_begin
FROM concurent_enrolled
WHERE 
dup_record       = 1   -- removes the duplicate records with the same enrollment begin term at the same college
AND 
CONCURENT_ENROLLED = 1 -- removes concurrent records of students attending another college while attending UMF
AND 
COLLEGE_CODE NOT IN '002327-00' --removes UMF student records because we only want the student enrollment records that enrolled at another institution
AND
co_term_code_key = term_code_enrollment_begin --keep only the students who enrolled elesewhere in the term for which they were accepted and did not register
group by
  to_number(co_pidm),
  co_term_code_key,
  registered_ind,
  TWOYEAR_FOURYEAR,
  PUBLIC_PRIVATE,
  COLLEGE_CODE,
  COLLEGE_NAME,
  ENROLLMENT_MAJOR,
  ENROLLMENT_CIP
 
  )popsel
) ;
