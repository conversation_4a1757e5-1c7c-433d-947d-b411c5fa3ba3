--people who have a shrdgmr but dont have a grad app for the same term    
--located in tabmgr schema
create or replace view load_shrdgmr_no_shbgapp as
select 
shrdgmr.shrdgmr_term_code_grad Grad_Term,
um.last_name ||', '|| um.first_name ||' '|| um.middle_initial last_first_mi,
um.umid,
shrdgmr.shrdgmr_seq_no,
shrdgmr.shrdgmr_degc_code,
shrdgmr.shrdgmr_majr_code_1
from aimsmgr.shrdgmr shrdgmr
inner join um_demographic um on um.dm_pidm = shrdgmr.shrdgmr_pidm
where 
shrdgmr_term_code_grad >= 201910 --(select current_term from um_current_term)--'202020'
and 
not exists (
                select 'x' from aimsmgr.shbgapp
                where shbgapp_pidm = shrdgmr_pidm
                and shbgapp_grad_term_code = shrdgmr_term_code_grad
               )
order by 
shrdgmr.shrdgmr_term_code_grad  desc,
um.umid
;