--create or replace view IA_GR_COHORT_SIX_YR as
/*******************************************************************************
Table grain: 1 row / student
Purpose: pull student data projected out 6 years on a student and create view
Primary keys: IA_GR_COHORT_POP.CO_GR_PIDM
Foreign keys: TD_STUDENT_DATA.SD_PIDM
*******************************************************************************/
select DISTINCT CO_GR_PIDM,
 
--First Fall********************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM  ) frst_fall_term_code,

( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM  ) frst_fall_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM ) frst_fall_Std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM ) frst_fall_term_gpa,
 
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0 ) frst_fall_CH_atmpt,--Credit Hour Attempted
 
( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0 ) frst_fall_CH_cmpltn,--Credit Hour Completion 
   
----------------------------Second----------------------------------------------
-- Second Fall Set *************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) Scnd_fall_term_code,
  
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) Scnd_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) Scnd_fall_std_type,
  
( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) scnd_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C')) scnd_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) scnd_fall_CH_cmpltn,--Credit Hour Completion 

  (select distinct 'Y'    
    from  um_degree 
      where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM and 
      --IA_GR_COHORT_POP.co_full_part_ind_umf = 'F' and
      --IA_GR_COHORT_POP.co_ia_student_type_code = 'F' and
      um_degree.degree_status = 'AW' and
      um_degree.level_code = IA_GR_COHORT_POP.primary_level_code and
      um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 100))
                                            )scnd_fall_grad_ind,-- Second Year Graduation Indicator  

----------------------------Third-----------------------------------------------
-- Third Fall Set **************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) thrd_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) thrd_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) thrd_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) thrd_fall_term_gpa,
    
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C')) thrd_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) thrd_fall_CH_cmpltn,--Credit Hour Completion 
  
( select distinct 'Y'    
  from  um_degree 
  where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM 
  and um_degree.degree_status = 'AW' 
  and um_degree.level_code = IA_GR_COHORT_POP.primary_level_code
  and   um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 200))
                                            )thrd_fall_grad_ind,-- Third Year Graduation Indicator  
    

----------------------------fourth---------------------------------------------------
-- Fourth Fall Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) frth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) frth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) frth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) frth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) frth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) frth_fall_CH_cmpltn,--Credit Hour Completion 
  
(select distinct 'Y'    
    from  um_degree 
      where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM and 
      --IA_GR_COHORT_POP.co_full_part_ind_umf = 'F' and
      --IA_GR_COHORT_POP.co_ia_student_type_code = 'F' and
      um_degree.degree_status = 'AW' and
      um_degree.level_code = IA_GR_COHORT_POP.primary_level_code and
      um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 300))
                                            )frth_fall_grad_ind,-- Fourth Year Graduation Indicator
  
-----------------------------Fifth--------------------------------------------------
-- Fifth Fall Set ******************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) ffth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) ffth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C')) ffth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM 
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) ffth_fall_term_gpa,

( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) ffth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0 
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) ffth_fall_CH_cmpltn,--Credit Hour Completion 

(select distinct 'Y'    
    from  um_degree 
      where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM and 
      --IA_GR_COHORT_POP.co_full_part_ind_umf = 'F' and
      --IA_GR_COHORT_POP.co_ia_student_type_code = 'F' and
      um_degree.degree_status = 'AW' and
      um_degree.level_code = IA_GR_COHORT_POP.primary_level_code and
      um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 400))
                                              )ffth_fall_grad_ind,-- Five Year Graduation Indicator
-----------------------------Sixth--------------------------------------------
-- Sixth Fall Set **************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) sxth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) sxth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) sxth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) sxth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) sxth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C')) sxth_fall_CH_cmpltn,--Credit Hour Completion 

(select distinct 'Y'    
    from  um_degree 
      where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM and 
      --IA_GR_COHORT_POP.co_full_part_ind_umf = 'F' and
      --IA_GR_COHORT_POP.co_ia_student_type_code = 'F' and
      um_degree.degree_status = 'AW' and
      um_degree.level_code = IA_GR_COHORT_POP.primary_level_code and
      um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 500))
                                            )sxth_fall_grad_ind,-- sixth Year Graduation Indicator   
-----------------------------Seventh-------------------------------------------
-- Seventh Fall Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) svnth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) svnth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) svnth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) svnth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) svnth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) svnth_fall_CH_cmpltn,--Credit Hour Completion

(select distinct 'Y'    
    from  um_degree 
      where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM and 
      --IA_GR_COHORT_POP.co_full_part_ind_umf = 'F' and
      --IA_GR_COHORT_POP.co_ia_student_type_code = 'F' and
      um_degree.degree_status = 'AW' and
      um_degree.level_code = IA_GR_COHORT_POP.primary_level_code and
      um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 600))
                                            )svnth_fall_grad_ind,-- Seventh Year Graduation Indicator

-----------------------------Eigth----------------------------------------------
-- Eight Fall Set ************************************************************
( select 
  td1.sd_term_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) eigth_fall_code,
 
( select 
  td1.registered_ind
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) eigth_fall_term_reg_ind,

( select 
  td1.student_type_code
  from td_student_data td1
  where td1.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700)) 
  and td1.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td1.report_level_code = 'GR'
  and td1.student_type_code in ('C') ) eigth_fall_std_type,

( select 
  td2.term_gpa
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700)) 
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) eigth_fall_term_gpa,
  
( select
  td2.term_hours_attempted
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) eigth_fall_CH_atmpt,--Credit Hour Attempted

( select
  round ((td2.term_hours_passed/td2.term_hours_attempted),2)
  from um_student_data td2
  where td2.sd_term_code = to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
  and td2.sd_pidm = IA_GR_COHORT_POP.CO_GR_PIDM
  and td2.term_hours_attempted >0
  and td2.report_level_code = 'GR'
  and td2.student_type_code in ('C') ) eigth_fall_CH_cmpltn,--Credit Hour Completion
  
(select distinct 'Y'    
    from  um_degree 
      where um_degree.pidm = IA_GR_COHORT_POP.CO_GR_PIDM and 
      um_degree.degree_status = 'AW' and
      um_degree.level_code = IA_GR_COHORT_POP.primary_level_code and
      um_degree.grad_term_code < to_char((to_number(IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY) + 700))
                                            )eigth_fall_grad_ind-- eighth Year Graduation Indicator  

-- Cohort Demographic Table joins and Where Clause *****************************
  from TD_student_data
  inner join IA_GR_COHORT_POP on IA_GR_COHORT_POP.CO_GR_PIDM = TD_student_data.sd_pidm;

  
