/*

*/
CREATE TABLE IA_CW_NCES_CODE_BAC AS (SELECT * FROM IA_CW_NCES_CODE);

insert into IA_CW_NCES_CODE 


select
popsel.*,
''IA_NCES_CODE,
''IA_NCES_DESC,
''IA_ACAT_CODE,
''IA_ACAT_DESC
from
(
select
distinct
um_degree.degree_code, 
um_degree.primary_major_1 majr_code,
um_degree.PRIMARY_MAJOR_1_DESC MAJR_DESC
--,
--''IA_NCES_CODE,
--''IA_NCES_DESC
from um_degree
--left join ia_cw_nces_code on um_degree.degree_code = ia_cw_nces_code.DEGREE_CODE
--and um_degree.primary_major_1 = ia_cw_nces_code.MAJR_CODE
where grad_term_code >= 201910 --update to prior year fall
--and IA_CW_NCES_CODE.IA_NCES_CODE is null
and um_degree.DEGREE_STATUS = 'AW'
minus
select
distinct
degree_code, 
majr_code,
MAJR_DESC
--,
--IA_NCES_CODE,
--IA_NCES_DESC
from IA_CW_NCES_CODE
--left join ia_cw_nces_code on um_degree.degree_code = ia_cw_nces_code.DEGREE_CODE
--and um_degree.primary_major_1 = ia_cw_nces_code.MAJR_CODE
--where grad_term_code >= 201110 --update to prior year fall
--and IA_CW_NCES_CODE.IA_NCES_CODE is null
--and um_degree.DEGREE_STATUS = 'AW'

)popsel
;
SELECT
DISTINCT
PRIMARY_MAJOR_1,
PRIMARY_MAJOR_1_DESC,
PRIMARY_MAJOR_1_CIPC_CODE,
PRIMARY_DEGREE_CODE
FROM IA_TD_STUDENT_DATA
--WHERE PRIMARY_MAJOR_1_DESC LIKE '%Sub%'
--WHERE PRIMARY_MAJOR_1 IN ('RSP','SAT')
--WHERE PRIMARY_MAJOR_1 IN ('INED')
WHERE PRIMARY_DEGREE_CODE IN ('CERG')
;
CREATE TABLE IA_CW_NCES_CODE AS 
SELECT * FROM(
WITH DP1 AS (
SELECT IA_CW_NCES_CODE_BAC.*,
DECODE(IA_NCES_CODE,'1', '21', '2', '22', '3', '23', '4', '25','5', '24', '6', 
'41', '7', '42', '8', '43','17', '44', '18', '31', '8','32','18','45',' 19',
'46','0') IA_ACAT_CODE
FROM IA_CW_NCES_CODE_BAC
)SELECT DP1.*,
AC.STVACAT_DESC IA_ACAT_DESC
FROM DP1
LEFT JOIN IA_CW_ACAT_CODE AC on DP1.IA_ACAT_CODE = AC.STVACAT_CODE)POPSEL
;