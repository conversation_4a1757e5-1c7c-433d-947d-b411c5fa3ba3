-- Data Set Form A: Countries Represented
SELECT DISTINCT NATION_CITIZEN_DESC
FROM IA_TD_STUDENT_DATA
WHERE registered_ind      = 'Y'
AND sd_term_code          = 202110
AND report_level_code     = 'UG'
AND ia_student_type_code IN ('R','F','T','C')
AND intl_ind = 'Y'
ORDER BY 1 ;
-- Data Set Form A: Top 10 foreign Countries
SELECT DISTINCT NATION_CITIZEN_DESC,
  COUNT (*)
FROM IA_TD_STUDENT_DATA
WHERE registered_ind      = 'Y'
AND sd_term_code          = 202110
AND report_level_code     = 'UG'
AND ia_student_type_code IN ('R','F','T','C')
AND intl_ind = 'Y'
GROUP BY NATION_CITIZEN_DESC
ORDER BY 2 DESC ;
-- Data Set Form B: Percent FTIAC from public schools
SELECT 
hsch_code,
hsch_desc,
  COUNT (*) headcount
FROM IA_TD_STUDENT_DATA
WHERE registered_ind      = 'Y'
AND sd_term_code          = 202110
AND ia_student_type_code IN ('F')
GROUP BY hsch_code, hsch_desc
ORDER BY 2 DESC ;
-- Data Set Form B: Applications from Vets, Active duty, and Reserves
select 
INST_ACCEPTED_APP_ANY_DATE_IND,
count (*)
from IA_TD_ADMISSIONS_APPLICANT
where TERM_CODE_ENTRY = '202110'
and VETERAN_IND = 'Y'
group by
INST_ACCEPTED_APP_ANY_DATE_IND;
-- Data Set Form B: Percent FTIAC from public schools
select 
COLLEGE_NAME,
count (*)
from IA_COMPETITOR_STUDENT_DATA
where CO_TERM_CODE_KEY = '202110'
group by
COLLEGE_NAME
order by
2 desc;
-- Data Set Form B: COUNT OF ELECTRONIC APPLICATIONS
SELECT COUNT (*)
FROM SATURN.SARADAP SD
JOIN SATURN.SARCTRL SC
ON SD.SARADAP_PIDM             = SC.SARCTRL_PIDM
AND SD.SARADAP_APPL_NO         = SC.SARCTRL_APPL_NO_SARADAP
WHERE SD.SARADAP_STYP_CODE     = 'F'
AND SD.SARADAP_TERM_CODE_ENTRY = '202110' ;
-- Data Set Form D: Majors by CIP
SELECT DISTINCT PRIMARY_MAJOR_1_CIPC_CODE,
  PRIMARY_MAJOR_1_DESC,
  COUNT (*)
FROM IA_TD_STUDENT_DATA
WHERE registered_ind      = 'Y'
AND sd_term_code          = 202110
AND report_level_code     = 'UG'
AND ia_student_type_code IN ('R','F','T','C')
GROUP BY PRIMARY_MAJOR_1_CIPC_CODE,
  PRIMARY_MAJOR_1_DESC
ORDER BY 1 ;
-- Data Set Form D: Majors and level counts
SELECT FA.CIPC_FAM_DESC,
  CLASS_CODE,
  COUNT (*)
FROM IA_TD_STUDENT_DATA sd
LEFT JOIN IA_CW_CIPC_FAM FA
ON sd.IA_CIP_FAMILY       = FA.CIPC_FAM_CODE
WHERE registered_ind      = 'Y'
AND sd_term_code          = 202110
AND report_level_code     = 'UG'
AND ia_student_type_code IN ('R','F','T','C')
AND IA_CIP_FAMILY        IN ('26','52','13','14','38','42')
GROUP BY FA.CIPC_FAM_DESC,
  CLASS_CODE
ORDER BY 1 ;

--STUDENTS RECIEVING SCHL_GRNT
WITH DP1 AS
  (SELECT sd_pidm,
    sd_term_code,
    co.IA_STUDENT_TYPE_CODE,
    CO.FULL_PART_TIME_IND_UMF,
     NVL(
    (SELECT 'Y'
    FROM DUAL
    WHERE EXISTS
      (SELECT FB.PIDM
      FROM IA_FA_AWARD_BY_AIDY FB
      WHERE FB.PIDM         = CO.SD_PIDM
      AND FB.AID_YEAR   = '1920' --update
      AND FB.FUND_TYPE IN ('SCHL','GRNT')
      AND FB.PAID_AMT        >0
      AND PAID_AMT          IS NOT NULL
      )
    ), 'N') FRESHMAN_SCHL_GRNT_IND,
    NVL(
    (SELECT SUM(OFFER_AMT)
    FROM IA_FA_AWARD_BY_AIDY FB
    WHERE FB.PIDM         = CO.SD_PIDM
    AND FB.AID_YEAR   = '1920' --update
    AND FB.FUND_TYPE IN ('SCHL','GRNT')
    ),0) FRESHMAN_SCHL_GRNT_AMT,
    NVL(
    (SELECT 'Y'
    FROM dual
    WHERE EXISTS
      (SELECT FB.PIDM
      FROM IA_FA_AWARD_BY_AIDY fb
      WHERE fb.PIDM        = co.sd_pidm
      AND FB.AID_YEAR  = '1920' --update
      AND FB.FUND_TYPE = 'LOAN'
      AND fb.PAID_AMT       >0
      AND fb.PAID_AMT      IS NOT NULL
      )
    ), 'N') LOAN_IND,
    NVL(
    (SELECT SUM(PAID_AMT)
    FROM IA_FA_AWARD_BY_AIDY fb
    WHERE fb.PIDM        = co.sd_pidm
    AND FB.AID_YEAR  = '1920' --update
    AND FB.FUND_TYPE = 'LOAN'
    ),0) LOAN_AMT,
    NVL(
    (SELECT 'Y'
    FROM dual
    WHERE EXISTS
      (SELECT FB.PIDM
      FROM IA_FA_AWARD_BY_AIDY fb
      WHERE fb.PIDM       = co.sd_pidm
      AND FB.AID_YEAR = '1920' --update
      AND fb.PAID_AMT      >0
      AND fb.PAID_AMT     IS NOT NULL
      )
    ), 'N') ANY_AID_IND,
    NVL(
    (SELECT SUM(PAID_AMT)
    FROM IA_FA_AWARD_BY_AIDY fb
    WHERE fb.PIDM       = co.sd_pidm
    AND FB.AID_YEAR = '1920' --update
    ),0) ANY_AID_AMT,
    NVL(
    (SELECT 'Y'
    FROM dual
    WHERE EXISTS
      (SELECT FB.PIDM
      FROM IA_FA_AWARD_BY_AIDY FB
      WHERE FB.PIDM       = co.sd_pidm
      AND FB.AID_YEAR = '1920' --update
      AND FB.FUND_CODE    IN ('2FMS','2FMS3','2FMS5','2FMS7','UMFSAW')
      AND FB.PAID_AMT      >0
      AND PAID_AMT        IS NOT NULL
      )
    ), 'N') MERIT_IND,
    NVL(
    (SELECT SUM(OFFER_AMT)
    FROM IA_FA_AWARD_BY_AIDY FB
    WHERE FB.PIDM       = CO.SD_PIDM
    AND FB.AID_YEAR = '1920' --update
    AND FB.FUND_CODE    IN ('2FMS','2FMS3','2FMS5','2FMS7','UMFSAW')
    ),0) MERIT_AMT,
    
        NVL(
    (SELECT 'Y'
    FROM dual
    WHERE EXISTS
      (SELECT FB.PIDM
      FROM IA_FA_AWARD_BY_AIDY FB
      WHERE FB.PIDM       = co.sd_pidm
      AND FB.AID_YEAR = '1920' --update
      AND FB.FUND_CODE     = '1PELL'
      AND FB.PAID_AMT      >0
      AND PAID_AMT        IS NOT NULL
      )
    ), 'N') PELL_IND,
    NVL(
    (SELECT SUM(OFFER_AMT)
    FROM IA_FA_AWARD_BY_AIDY FB
    WHERE FB.PIDM       = CO.SD_PIDM
    AND FB.AID_YEAR = '1920' --update
    AND FB.FUND_CODE    = '1PELL'
    ),0) PELL_AMT
    
  FROM ia_td_student_data co
  WHERE REGISTERED_IND     = 'Y'
  AND CO.REPORT_LEVEL_CODE = 'UG'
  AND CO.SD_TERM_CODE     IN ('202010') --update
  ),
  DP2 AS
  (SELECT
    (SELECT COUNT (*)
    FROM DP1
    WHERE ANY_AID_IND        = 'Y'
    AND ia_student_type_code = 'F'
    )ANY_AID_IND_FTIAC_HEADCOUNT,
    (SELECT COUNT (*) FROM DP1 WHERE IA_STUDENT_TYPE_CODE = 'F'
    )FTIAC_HEADCOUNT,
    (SELECT COUNT (*) FROM DP1 WHERE ANY_AID_IND = 'Y'
    )ANY_AID_IND_UG_HEADCOUNT,
    (SELECT COUNT (*) FROM DP1
    )UG_HEADCOUNT,
    (SELECT ROUND (AVG(FRESHMAN_SCHL_GRNT_AMT),0)
    FROM DP1
    WHERE FRESHMAN_SCHL_GRNT_IND = 'Y'
    AND ia_student_type_code     = 'F'
    )AVG_FTIAC_SCHL_GRNT,
    (SELECT ROUND (AVG(LOAN_AMT),0)
    FROM DP1
    WHERE LOAN_IND           = 'Y'
    AND ia_student_type_code = 'F'
    )AVG_FTIAC_LOAN_AMT,
    (SELECT SUM (MERIT_AMT)
    FROM DP1
    )TOTAL_MERIT_UG,
    (SELECT ROUND(AVG(MERIT_AMT),0)
    FROM DP1
    WHERE 
    IA_STUDENT_TYPE_CODE IN ('F')
    AND 
    MERIT_IND = 'Y'
    )AVG_FTIAC_MERIT,
    (SELECT COUNT (*) FROM DP1
    WHERE PELL_IND = 'Y'
    )HEADCOUNT_PELL_UG,
    (SELECT ROUND(AVG(PELL_AMT),0)
    FROM DP1
    WHERE 
    PELL_IND = 'Y'
    )AVG_PELL_UG
  FROM DUAL
  )
SELECT
  --ANY_AID_IND_FTIAC_HEADCOUNT,
  --  FTIAC_HEADCOUNT,
  --  ANY_AID_IND_UG_HEADCOUNT,
  --  UG_HEADCOUNT,
  AVG_FTIAC_SCHL_GRNT,
  AVG_FTIAC_LOAN_AMT,
  ROUND((ANY_AID_IND_FTIAC_HEADCOUNT/FTIAC_HEADCOUNT)*100,0) ANY_AID_FTIAC,
  ROUND((ANY_AID_IND_UG_HEADCOUNT   /UG_HEADCOUNT)*100,0) ANY_AID_UG,
  TOTAL_MERIT_UG,
  AVG_FTIAC_MERIT,
  HEADCOUNT_PELL_UG,
  AVG_PELL_UG
FROM DP2 ; 