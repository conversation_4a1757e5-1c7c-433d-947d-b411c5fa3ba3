JOIN
INNER UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM
AND UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE IN UM_STUDENT_DATA TO MULTIPLE
UM_STUDENT_TRANSCRIPT.UM_STUDENT_TRANSCRIPT.PIDM
AND UM_STUDENT_TRANSCRIPT.UM_STUDENT_TRANSCRIPT.TERM IN UM_STUDENT_TRANSCRIPT
TAG J0 AS J0
END
TABLE FILE UM_STUDENT_DATA
PRINT 
     UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE
     UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE
BY  LOWEST UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM
WHERE (( UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE EQ '&SD_TERM_CODE.Registered for Term: .' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.REGISTERED_IND EQ 'Y' ));
WHERE ( UM_STUDENT_DATA.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE EQ 'U2' OR 'U3' OR 'UG' ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_GPA GE 3.0 ) 
AND ( UM_STUDENT_DATA.UM_STUDENT_DATA.OVERALL_HOURS_EARNED GE 10 ) 
AND ( J0.UM_STUDENT_TRANSCRIPT.COURSE GE '200' );
ON TABLE SET PAGE-NUM NOLEAD 
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDUG FORMAT ALPHA
END
JOIN
 INNER HOLDUG.HOLDUG.SD_PIDM AND HOLDUG.HOLDUG.PRIMARY_LEVEL_CODE IN HOLDUG
 TO MULTIPLE UM_STUDENT_TRANSCRIPT.UM_STUDENT_TRANSCRIPT.PIDM
 AND UM_STUDENT_TRANSCRIPT.UM_STUDENT_TRANSCRIPT.LEVL IN UM_STUDENT_TRANSCRIPT
 TAG J1 AS J1
 END
JOIN
 INNER J1.UM_STUDENT_TRANSCRIPT.GRADE AND J1.UM_STUDENT_TRANSCRIPT.LEVL IN
HOLDUG TO UNIQUE SHRGRDE.SHRGRDE.SHRGRDE_CODE
 AND SHRGRDE.SHRGRDE.SHRGRDE_LEVL_CODE IN SHRGRDE TAG J2 AS J2
 END
DEFINE FILE HOLDUG
POL_GPA_Hours/D12.3=IF J2.SHRGRDE.SHRGRDE_GPA_IND EQ 'Y' THEN J1.UM_STUDENT_TRANSCRIPT.HOURS ELSE 0;
POL_Quality_Points/D12.3=IF J2.SHRGRDE.SHRGRDE_GPA_IND EQ 'Y' THEN ( J1.UM_STUDENT_TRANSCRIPT.HOURS * J2.SHRGRDE.SHRGRDE_QUALITY_POINTS ) ELSE 0;
END
TABLE FILE HOLDUG
SUM
     HOLDUG.HOLDUG.SD_TERM_CODE
     J1.UM_STUDENT_TRANSCRIPT.HOURS AS 'POL_Credit_Hours'
     J2.SHRGRDE.POL_GPA_Hours
     J2.SHRGRDE.POL_Quality_Points
BY  HOLDUG.HOLDUG.SD_PIDM
WHERE ( J1.UM_STUDENT_TRANSCRIPT.TYPE EQ 'I' ) AND ( J1.UM_STUDENT_TRANSCRIPT.SUBJECT EQ 'POL' );
WHERE ( J1.UM_STUDENT_TRANSCRIPT.GRADE NE 'YW' OR 'Y' OR '*' OR 'I' OR 'W' OR 'IW' );
WHERE ( J1.UM_STUDENT_TRANSCRIPT.REPEAT_IND EQ MISSING ) OR ( J1.UM_STUDENT_TRANSCRIPT.REPEAT_IND EQ 'I' OR 'A' );
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDUG2 FORMAT FOCUS INDEX 'HOLDUG.HOLDUG.SD_PIDM' 'HOLDUG.HOLDUG.SD_TERM_CODE'
END
DEFINE FILE HOLDUG2
-*HIS_GPA/D12.3=IF HIS_GPA_Hours NE 0 THEN ( HIS_Quality_Points / HIS_GPA_Hours ) ELSE 0;
POL_GPA/P12.3=IF POL_GPA_Hours NE 0 THEN ( POL_Quality_Points / POL_GPA_Hours ) ELSE 0;
END
TABLE FILE HOLDUG2
PRINT
     HOLDUG2.SEG01.SD_PIDM
     HOLDUG2.SEG01.SD_TERM_CODE
     HOLDUG2.SEG01.POL_CREDIT_HOURS
     HOLDUG2.SEG01.POL_GPA_Hours
     HOLDUG2.SEG01.POL_Quality_Points
     HOLDUG2.SEG01.POL_GPA
WHERE HOLDUG2.SEG01.POL_CREDIT_HOURS GE 10;
WHERE HOLDUG2.SEG01.POL_GPA GE 3.0;
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE HOLD AS HOLDUGGPA FORMAT FOCUS INDEX 'HOLDUG2.SEG01.SD_PIDM' 'HOLDUG2.SEG01.SD_TERM_CODE'
END
SET EMPTYREPORT = ON
SET NODATA = ''
JOIN
 HOLDUGGPA.SEG01.SD_PIDM AND HOLDUGGPA.SEG01.SD_TERM_CODE IN HOLDUGGPA TO UNIQUE
 UM_STUDENT_DATA.UM_STUDENT_DATA.SD_PIDM
 AND UM_STUDENT_DATA.UM_STUDENT_DATA.SD_TERM_CODE IN UM_STUDENT_DATA TAG J5
 AS J5
 END
JOIN
 J5.UM_STUDENT_DATA.SD_PIDM IN HOLDUGGPA TO UNIQUE
 UM_DEMOGRAPHIC.UM_DEMOGRAPHIC.DM_PIDM IN UM_DEMOGRAPHIC TAG J6 AS J6
 END
TABLE FILE HOLDUGGPA
PRINT
     J6.UM_DEMOGRAPHIC.NAME_SUFFIX AS 'Suffix'
     J6.UM_DEMOGRAPHIC.UMID
     J6.UM_DEMOGRAPHIC.A1_STREETADDRESS
     J6.UM_DEMOGRAPHIC.A1_CITY AS 'City'
     J6.UM_DEMOGRAPHIC.A1_STATE_CODE AS 'State'
     J6.UM_DEMOGRAPHIC.A1_ZIP AS 'Zip'
     J6.UM_DEMOGRAPHIC.A1_AREAPHONE
     J6.UM_DEMOGRAPHIC.CA_EMAIL AS 'Campus,Email'
     J5.UM_STUDENT_DATA.PRIMARY_LEVEL_CODE AS 'Primary,Level'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_1 AS 'Primary,Major1'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_1_DESC AS 'Primary,Major1 Desc'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_2 AS 'Primary,Major2'
     J5.UM_STUDENT_DATA.PRIMARY_MAJOR_2_DESC AS 'Primary,Major2 Desc'
     J5.UM_STUDENT_DATA.OVERALL_HOURS_EARNED AS 'Overall Hours,Earned'
     J5.UM_STUDENT_DATA.OVERALL_GPA AS 'Overall,GPA'
     HOLDUGGPA.SEG01.POL_CREDIT_HOURS AS 'POL,Cr Hrs'
     HOLDUGGPA.SEG01.POL_GPA_Hours AS 'POL,GPA Hrs'
     HOLDUGGPA.SEG01.POL_GPA AS 'POL GPA'
BY  LOWEST J6.UM_DEMOGRAPHIC.LAST_NAME AS 'Last Name'
BY  LOWEST J6.UM_DEMOGRAPHIC.FIRST_NAME AS 'First Name'
BY  LOWEST J6.UM_DEMOGRAPHIC.MIDDLE_INITIAL AS 'MI'
ON TABLE SUBHEAD
"Political Science Honor Society Eligibility List"
"Pi Sigma Alpha"
"<J5.UM_STUDENT_DATA.SD_TERM_DESC "
ON TABLE SUBFOOT
"s0066s00 Political Science Honor Society Eligibility List  Run on: <+0>&DATEMDYY <+0> "
"(Criteria:  Undergraduate level only, Registered for term selected, Overall GPA 3.0 or higher, at least 10 credits in POL completed at UM-Flint, POL subject GPA of 3.0 or higher)"
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE PCHOLD FORMAT &WFFMT.(<HTML,HTML>,<PDF,PDF>,<Excel 2000,EXL2K>,<Excel Formula,EXL2K FORMULA>,<HTML Active Report,AHTML>,<Active Report for Adobe Flash Player,FLEX>,<Active Report for PDF,APDF>,<PowerPoint,PPT>).Select type of display output.
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
-MRNOEDIT INCLUDE=endeflt, $
-*     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,$
TYPE=REPORT,
     GRAPHCOLOR='GREEN',
$
TYPE=DATA,
     COLUMN=N1,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N2,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N3,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N4,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N5,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N6,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N7,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N8,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N9,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N10,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N11,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N12,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N13,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N14,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N15,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N16,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N17,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N18,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N19,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N20,
     BACKCOLOR='NONE',
$
TYPE=DATA,
     COLUMN=N21,
     BACKCOLOR='NONE',
$
TYPE=TABHEADING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABHEADING,
     LINE=2,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABHEADING,
     LINE=3,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABHEADING,
     LINE=3,
     OBJECT=FIELD,
     ITEM=1,
     SIZE=12,
     STYLE=BOLD,
$
TYPE=TABFOOTING,
     LINE=1,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=2,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=1,
     OBJECT=TEXT,
     ITEM=3,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=TABFOOTING,
     LINE=2,
     JUSTIFY=LEFT,
$
TYPE=TABFOOTING,
     LINE=2,
     OBJECT=TEXT,
     ITEM=1,
     SIZE=8,
     STYLE=ITALIC,
$
TYPE=REPORT,
     OBJECT=MENU,
     COLOR='WHITE',
     HOVER-COLOR=RGB(66 70 73),
     BACKCOLOR=RGB(102 102 102),
     HOVER-BACKCOLOR=RGB(218 225 232),
     BORDER-COLOR='WHITE',
$
TYPE=REPORT,
     OBJECT=STATUS-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
TYPE=REPORT,
     OBJECT=CURRENT-ROW,
     HOVER-BACKCOLOR=RGB(218 225 232),
     BACKCOLOR=RGB(200 200 200),
$
TYPE=REPORT,
     OBJECT=CALC-AREA,
     COLOR='WHITE',
     BACKCOLOR=RGB(102 102 102),
$
ENDSTYLE
END
 