--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View CONTACTS_TERM_UPDATE_DSERV
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "SALESFORCEMGR"."CONTACTS_TERM_UPDATE_DSERV" ("ID", "REGISTERED_IND__C", "PRIMARY_LEVEL_CODE__C", "STUDENT_TYPE_CODE__C", "STUDENT_TYPE_DESC__C", "ACTIVE_INDICATOR__C", "STUDENT_STATUS_CODE__C", "STUDENT_STATUS_DESC__C", "CLASS_CODE__C", "CLASS_DESC__C", "HOUSING_INDICATOR__C") AS 
  with fac_staff_sel as(
  select
  distinct ps.sibinst_pidm,
  'Facutly/Staff' banner_rec_type 
  from(
    select 
    sibinst.sibinst_pidm,
    sibinst.sibinst_fcst_code,
    sirdpcl.sirdpcl_term_code_eff
    from aimsmgr.sibinst
    inner join aimsmgr.sirdpcl on sirdpcl.sirdpcl_pidm = sibinst.sibinst_pidm
                               and sirdpcl.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
                                                                    from aimsmgr.sirdpcl s1
                                                                    where s1.sirdpcl_pidm = sirdpcl.sirdpcl_pidm)
    where sibinst.sibinst_term_code_eff = (select max(s1.sibinst_term_code_eff)
                                           from aimsmgr.sibinst s1
                                           where s1.sibinst_pidm = sibinst.sibinst_pidm)
    and not exists (select 'is a dup'
                    from aimsmgr.sprhold sprhold
                    where sprhold.sprhold_pidm = sibinst.sibinst_pidm
                    and sprhold.sprhold_hldd_code = 'DP')
  )ps
  where ps.sibinst_fcst_code = 'AC'
  order by 1
),
student_sel as(
  select 
  ps.sgbstdn_pidm,
  'Student' banner_rec_type
  from(
    select 
    sgbstdn.sgbstdn_pidm,
    sgbstdn.sgbstdn_stst_code,
    row_number() over(partition by sgbstdn.sgbstdn_pidm order by sgbstdn.sgbstdn_term_code_eff desc) order_num
    from aimsmgr.sgbstdn
    where not exists (select 'is a dup'
                      from aimsmgr.sprhold sprhold
                      where sprhold.sprhold_pidm = sgbstdn.sgbstdn_pidm
                      and sprhold.sprhold_hldd_code = 'DP')
  )ps
  where ps.sgbstdn_stst_code = 'AS'
  and ps.order_num = 1
)
,
pop_sel as(
  select
  pr.pop_sel_pidm,
  pr.banner_rec_type,
  row_number() over(partition by pr.pop_sel_pidm order by pr.banner_rec_type desc) order_num
  from(
    select
    fac_staff_sel.sibinst_pidm pop_sel_pidm,
    banner_rec_type
    from fac_staff_sel
    union all
    select
    student_sel.sgbstdn_pidm,
    banner_rec_type
    from student_sel
  )pr
)
select
--contact.pidm__c,
--pop_sel.pop_sel_pidm,
--pop_sel.banner_rec_type,
--
contact.id,
case
  when pop_sel.banner_rec_type = 'Student' and nvl(um_student_data.reg_ind_with_nonacademic, 'X') = 'Y' then 'true'
  else 'false'
end registered_ind__c, 
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.primary_level_code
  else null
end primary_level_code__c,
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.student_type_code
  else null
end student_type_code__c,
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.student_type_desc
  else null
end student_type_desc__c,
case
  when um_student_data.student_status_code = 'AS' then 'true'
  else 'false'
end active_indicator__c,
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.student_status_code
  else null
end student_status_code__c,
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.student_status_desc
  else null
end student_status_desc__c,
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.class_code
  else null
end class_code__c,
case
  when nvl(pop_sel.banner_rec_type, 'Student') = 'Student' then um_student_data.class_desc
  else null
end class_desc__c,
case
  when nvl(um_student_data.housing_ind, 'X') = 'Y' then 'true'
  else 'false'
end housing_indicator__c

from all_contacts_dserv contact
inner join um_current_term on um_current_term.current_term != 'XX'
left outer join pop_sel on pop_sel.pop_sel_pidm = contact.pidm__c
                        and pop_sel.order_num = 1
left outer join um_student_data on um_student_data.sd_pidm = contact.pidm__c
                                and um_student_data.sd_term_code = um_current_term.current_term
                                
--where contact.id = '003g000001XTcbiAAD'
order by to_number(contact.pidm__c)
;
