/*******************************************************************************
-- Author:		<PERSON>, University of Michigan-Flint
-- Create date: 9/9/21
-- Description:	Querie to gather Banner data for upload to National Student 
   Clearinhouse to identify students That have gone inactive.
-- Notes:       EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
-- Known issue(s):	
*******************************************************************************/

--set basic selection criteria here
WITH dp1 AS (
 SELECT
  sd.sd_pidm,
  MIN(sd.sd_term_code) AS min_term
 FROM
  ia_td_student_data sd
 WHERE
   sd_term_code >= 201110
  AND sd_term_code < (
   SELECT
    current_term
   FROM
    um_current_term
  )
    AND student_status_code = 'IS'
 GROUP BY
  sd.sd_pidm
), base_sel AS (
 SELECT DISTINCT
  sd.first_name,
  sd.middle_initial    AS middle_name,
  sd.last_name,
  sd.name_suffix,
  sd.birthdate,
  dp1.min_term,
  sd.sd_pidm           AS dm_pidm
 FROM
       dp1
  INNER JOIN ia_td_student_data sd ON sd.sd_pidm = dp1.sd_pidm
                                      AND sd.sd_term_code = dp1.min_term
),
--End basic selection criteria here
 pop_sel AS (
  --This section creates a header file.
   SELECT
  1                                 order_num,
  'H1'                              "A",
  '002327'                          "B",
  '00'                              "C",
  'UNIVERSITY OF MICHIGAN FLINT'    "D",
  to_char(sysdate, 'YYYYMMDD')      "E",
  'SE'                              "F",--report type SE, CO, DA
  'I'                               "G",
  NULL                              "H",
  NULL                              "I",
  NULL                              "J",
  NULL                              "K",
  NULL                              "L"
 FROM
  dual
 UNION
  --This section pulls the student records for the payload.
   SELECT
  2                                                                                   order_num,
  'D1'                                                                                "A",
  NULL                                                                                "B",
  CASE
   WHEN ( first_name IS NULL
          OR first_name = '.'
          OR length(first_name) < 3 ) THEN
    'Jon'
   ELSE
    substr(first_name, 1, 20)
  END                                                                                 "C",
  substr(regexp_replace(TRIM(middle_name), '[[:punct:] ]', NULL), 1, 1)                "D",
  CASE
   WHEN ( last_name IS NULL
          OR last_name = '.'
          OR length(first_name) < 3 ) THEN
    'Doe'
   ELSE
    substr(last_name, 1, 20)
  END                                                                                 "E",
  substr(regexp_replace(TRIM(name_suffix), '[[:punct:] ]', NULL), 1, 5)                  "F",
  to_char(birthdate, 'YYYYMMDD')                                                      "G",
  CASE
   WHEN substr(min_term, 5, 6) = 10         THEN
    to_char(to_number(substr(min_term, 1, 4)) - 1
            || '1222')
   WHEN substr(min_term, 5, 6) = 20         THEN
    to_char(to_number(substr(min_term, 1, 4))
            || '0501')
   WHEN substr(min_term, 5, 6) = 30         THEN
    to_char(to_number(substr(min_term, 1, 4))
            || '0711')
   WHEN substr(min_term, 5, 6) = 40         THEN
    to_char(to_number(substr(min_term, 1, 4))
            || '0901')
  END                                                                                 "H",
  NULL                                                                                "I",
  '002327'                                                                            "J",
  '00'                                                                                "K",
  to_char(dm_pidm)                                                                    "L"
 FROM
  base_sel
 UNION
    --This is to count the number of records and append a trailer record
   SELECT
  3                                             order_num,
  'T1'                                          "A",
  to_char(COUNT(base_sel.dm_pidm) + 2)            "B",
  NULL                                          "C",
  NULL                                          "D",
  NULL                                          "E",
  NULL                                          "F",
  NULL                                          "G",
  NULL                                          "H",
  NULL                                          "I",
  NULL                                          "J",
  NULL                                          "K",
  NULL                                          "L"
 FROM
  base_sel
 ORDER BY
  order_num
)
SELECT
 pop_sel.a,
 pop_sel.b,
 pop_sel.c,
 pop_sel.d,
 pop_sel.e,
 pop_sel.f,
 pop_sel.g,
 pop_sel.h,
 pop_sel.i,
 pop_sel.j,
 pop_sel.k,
 pop_sel.l
FROM
 pop_sel;