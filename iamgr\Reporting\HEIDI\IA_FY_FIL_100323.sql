desc um_catalog_schedule;

WITH dp0 AS (
 --primary instructor
    SELECT
        ia_td_registration_detail.pidm                       td_pidm,
        ia_td_registration_detail.td_term_code,
        ia_td_registration_detail.fy,
        um_catalog_schedule.coll_code,
        um_catalog_schedule.coll_desc,
        um_catalog_schedule.dept_code,
        um_catalog_schedule.dept_desc,
        ia_td_registration_detail.crn_key,
        ia_td_registration_detail.subj_code,
        ia_td_registration_detail.crse_number,
        ia_td_registration_detail.section_number,
        ia_td_registration_detail.meeting_schd_code_1,
        ia_td_registration_detail.xlst_group,
        ia_td_registration_detail.credit_hours               td_credit_hours,
        um_catalog_schedule.PRIMARY_INSTRUCTOR_ID            instructor_id,
        um_catalog_schedule.primary_instructor_last_name
        || ', '
        || um_catalog_schedule.primary_instructor_first_name instructor_name,
        '1'                                                  AS instructor_number
    FROM
        ia_td_registration_detail
        FULL OUTER JOIN um_catalog_schedule 
        ON um_catalog_schedule.term_code_key = ia_td_registration_detail.td_term_code
        AND um_catalog_schedule.crn_key = ia_td_registration_detail.crn_key
    UNION ALL
      --secondary instructor
    SELECT
        ia_td_registration_detail.pidm                td_pidm,
        ia_td_registration_detail.td_term_code,
        ia_td_registration_detail.fy,
        um_catalog_schedule.coll_code,
        um_catalog_schedule.coll_desc,
        um_catalog_schedule.dept_code,
        um_catalog_schedule.dept_desc,
        ia_td_registration_detail.crn_key,
        ia_td_registration_detail.subj_code,
        ia_td_registration_detail.crse_number,
        ia_td_registration_detail.section_number,
        ia_td_registration_detail.meeting_schd_code_1,
        ia_td_registration_detail.xlst_group,
        ia_td_registration_detail.credit_hours        td_credit_hours,
         um_catalog_schedule.INSTRUCTOR_ID2            instructor_id,
        um_catalog_schedule.instructor_last_name2
        || ', '
        || um_catalog_schedule.instructor_first_name2 instructor_name,
        '2'                                           AS instructor_number
    FROM
        ia_td_registration_detail
        FULL OUTER JOIN um_catalog_schedule 
        ON um_catalog_schedule.term_code_key = ia_td_registration_detail.td_term_code
        AND um_catalog_schedule.crn_key = ia_td_registration_detail.crn_key
    UNION ALL
      --Third instructor
    SELECT
        ia_td_registration_detail.pidm                td_pidm,
        ia_td_registration_detail.td_term_code,
        ia_td_registration_detail.fy,
        um_catalog_schedule.coll_code,
        um_catalog_schedule.coll_desc,
        um_catalog_schedule.dept_code,
        um_catalog_schedule.dept_desc,
        ia_td_registration_detail.crn_key,
        ia_td_registration_detail.subj_code,
        ia_td_registration_detail.crse_number,
        ia_td_registration_detail.section_number,
        ia_td_registration_detail.meeting_schd_code_1,
        ia_td_registration_detail.xlst_group,
        ia_td_registration_detail.credit_hours        td_credit_hours,
        um_catalog_schedule.INSTRUCTOR_ID3            instructor_id,
        um_catalog_schedule.instructor_last_name3
        || ', '
        || um_catalog_schedule.instructor_first_name3 instructor_name,
        '3'                                           AS instructor_number
    FROM
        ia_td_registration_detail
        FULL OUTER JOIN um_catalog_schedule 
        ON um_catalog_schedule.term_code_key = ia_td_registration_detail.td_term_code
        AND um_catalog_schedule.crn_key = ia_td_registration_detail.crn_key
), dp1 AS (
    SELECT
        td_term_code,
        fy,
        CASE
            WHEN instructor_name = ', '
                 AND instructor_number = 1 THEN
                'keep'
            WHEN instructor_name = ', '
                 AND instructor_number <> 1 THEN
                'remove'
            ELSE
                'keep'
        END                  ins_clean,
        CASE
            WHEN coll_code = '00'
                 AND ( dept_code = 'HON'
                       OR dept_code = 'UNV' ) THEN
                'AS'
            WHEN coll_code = '00'
                 AND dept_code = 'SEC' THEN
                'EH'
            ELSE
                coll_code
        END                  coll_code,
        crn_key,
        subj_code,
        crse_number,
        section_number,
        meeting_schd_code_1,
        xlst_group,
        instructor_id,
        instructor_name,
        instructor_number,
        CASE
            WHEN crse_number < 300 THEN
                'LL-UG'
            WHEN crse_number < 500
                 AND crse_number >= 300 THEN
                'UL-UG'
            ELSE
                'GR'
        END                  crse_level,
        COUNT(td_pidm)       AS td_section_enrollment,
        SUM(td_credit_hours) AS td_credit_hours
    FROM
        dp0
    WHERE
        fy = '22-23'                                         --UPDATE THIS
    GROUP BY
        fy,
        td_term_code,
        fy,
        CASE
                WHEN coll_code = '00'
                     AND ( dept_code = 'HON'
                           OR dept_code = 'UNV' ) THEN
                    'AS'
                WHEN coll_code = '00'
                     AND dept_code = 'SEC' THEN
                    'EH'
                ELSE
                    coll_code
        END,
        CASE
                WHEN instructor_name = ', '
                     AND instructor_number = 1 THEN
                    'keep'
                WHEN instructor_name = ', '
                     AND instructor_number <> 1 THEN
                    'remove'
                ELSE
                    'keep'
        END,
        crn_key,
        subj_code,
        crse_number,
        section_number,
        meeting_schd_code_1,
        xlst_group,
        instructor_id,
        instructor_name,
        instructor_number,
        CASE
                WHEN crse_number < 300 THEN
                    'LL-UG'
                WHEN crse_number < 500
                     AND crse_number >= 300 THEN
                    'UL-UG'
                ELSE
                    'GR'
        END
)
SELECT
    fy,
    td_term_code,
    coll_code,
    crn_key,
    subj_code,
    crse_number,
    section_number,
    meeting_schd_code_1,
    xlst_group,
    instructor_id,
    instructor_name,
    instructor_number,
    crse_level,
    td_section_enrollment,
    td_credit_hours
FROM
    dp1
WHERE
    ins_clean = 'keep'
ORDER BY
    td_term_code,
    subj_code,
    crse_number,
    section_number,
    instructor_number