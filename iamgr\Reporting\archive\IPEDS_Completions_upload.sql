/*
This query is designed to create the IPEDS degreee Completions Upload. The table
being queried (IA_DEGREE_COMPLETIONS)will requre to be updated after census day
of the spring semester.
*/


SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=A' C,
'MAJORNUM='||POPSEL.COMPLETION_TYPE_CODE D,
'CIPCODE='||SUBSTR(POPSEL.CIP_CODE, 1, 2) ||'.'||SUBSTR(POPSEL.CIP_CODE, 3, 6) E,
'AWLEVEL='||POPSEL.NCES_CODE F,
'RACE='||POPSEL.IPEDS_RACE_CODE  G,
'SEX='||POPSEL.IPEDS_GENDER_CODE H,
'COUNT='||POPSEL.DEG_COUNT I
FROM(
select 
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE,
COUNT(IA_DEGREE_COMPLETIONS.PIDM) AS DEG_COUNT
FROM
IA_DEGREE_COMPLETIONS
where FISCAL_YEAR = '14-15'                --  Update before running
GROUP BY
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE
)POPSEL

ORDER BY
E,F,G,H
