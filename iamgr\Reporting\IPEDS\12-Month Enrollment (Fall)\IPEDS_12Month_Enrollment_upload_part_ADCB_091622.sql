/*******************************************************************************
IPEDS 
Fall Collection: 12-Month Enrollment for July 1 � June 30 of FY
Applies to: 4-year Institutions
File Type: Key Value Pair (*.TXT)
Part A: Unduplicated Count
Part C: Distance Education Status
Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
Be sure to update the FY before running
Export as comma dilimeted .TXT file
Remove trailing commas from upload file
Do not use enclosurs
*******************************************************************************/

/*******************************************************************************
--Part A: Unduplicated Count
*******************************************************************************/

select * from (
with 
DP1 as(
select
row_number() OVER(PARTITION BY SD_PIDM ORDER BY SD_TERM_CODE ) MIN_TERM,
sd_pidm,
sd_term_code,
full_part_time_ind_umf,
ia_student_type_code,
ia_gender as gender,
IPEDS_GENDER_CODE,
REPORT_ETHNICITY,
IPEDS_RACE_CODE,
REPORT_LEVEL_CODE,
case 
when report_level_code = 'UG' and full_part_time_ind_umf = 'F' and ia_student_type_code = 'F' then '1'
when report_level_code = 'UG' and full_part_time_ind_umf = 'F' and ia_student_type_code = 'T' then '2'
when report_level_code = 'UG' and full_part_time_ind_umf = 'F' and ia_student_type_code in ('C','R') then '3'
when report_level_code = 'UG' and full_part_time_ind_umf = 'F' and ia_student_type_code in ('D','E','G','S','X') then '7'
when report_level_code = 'GR' and full_part_time_ind_umf = 'F' and ia_student_type_code in ('C','R','N','G','S','X') then '11'
when report_level_code = 'UG' and full_part_time_ind_umf = 'P' and ia_student_type_code = 'F' then '15'
when report_level_code = 'UG' and full_part_time_ind_umf = 'P' and ia_student_type_code = 'T' then '16'
when report_level_code = 'UG' and full_part_time_ind_umf = 'P' and ia_student_type_code in ('C','R') then '17'
when report_level_code = 'UG' and full_part_time_ind_umf = 'P' and ia_student_type_code in ('D','E','G','S','X') then '21'
when report_level_code = 'GR' and full_part_time_ind_umf = 'P' and ia_student_type_code in ('C','R','N','G','S','X') then '25'
else report_level_code||'-'||full_part_time_ind_umf||'-'||ia_student_type_code
end line_a
from
IA_TD_STUDENT_DATA
where
registered_ind = 'Y' and 
fy = '21-22'                                                                              --Update This
              
)

SELECT
'UNITID=171146' A,
'SURVSECT=E1D' B,
'PART=A' C,
'LINE='|| DP1.line_a D,
'RACE='||DP1.IPEDS_RACE_CODE E,
'SEX='||DP1.IPEDS_GENDER_CODE F,
'COUNT='||count(DP1.sd_pidm) G,
cast(null as varchar2(16)) H,
cast(null as varchar2(16)) I
from DP1 
WHERE MIN_TERM = 1
group by
'UNITID=171146',
'SURVSECT=E1D',
'PART=A',
'LINE='|| DP1.line_a,
'RACE='||DP1.IPEDS_RACE_CODE,
'SEX='||DP1.IPEDS_GENDER_CODE)Popsel 

union all

/*******************************************************************************
--Part D: Gender Unknown
*******************************************************************************/

select * from (
with 
DP1 as(
select
row_number() OVER(PARTITION BY SD_PIDM ORDER BY SD_TERM_CODE ) MIN_TERM,
sd_pidm,
sd_term_code,
GENDER,
REPORT_LEVEL_CODE

from
IA_TD_STUDENT_DATA
where
registered_ind = 'Y' and 
fy = '21-22'                                                                              --Update This
              
)
SELECT 
--*
'UNITID=171146' A,
'SURVSECT=E1D' B,
'PART=D' C,
'FYGU01=1'D,
'FYGU011='||(SELECT COUNT (*) FROM DP1 WHERE MIN_TERM = 1 AND REPORT_LEVEL_CODE = 'UG' AND GENDER = 'N') E,
'FYGU012=0' F,
'FYGU02=1'G,
'FYGU021='||(SELECT COUNT (*) FROM DP1 WHERE MIN_TERM = 1 AND REPORT_LEVEL_CODE = 'GR' AND GENDER = 'N') H,
'FYGU022=0' I

from 
DUAL

)Popsel 

UNION ALL
/*******************************************************************************
--Part C: Distance Education Status
*******************************************************************************/

select * from (

WITH DP1 AS(
SELECT
sd_pidm,
sd.sd_term_code,
sd.online_courses_only_ind,
case 
when online_courses_only_ind = 'Y' then 1
else 0
end online_courses_only_code,
case 
    when online_courses_only_ind = 'N' and 
     (select count(*)
      from td_registration_detail 
      where td_registration_detail.pidm = sd.sd_pidm
      and td_registration_detail.td_term_code = sd.sd_term_code
      and td_registration_detail.section_number like 'W%') > 0 then 'Y'
    else 'N'
end some_online_ind,
case 
    when online_courses_only_ind = 'N' and 
     (select count(*)
      from td_registration_detail 
      where td_registration_detail.pidm = sd.sd_pidm
      and td_registration_detail.td_term_code = sd.sd_term_code
      and td_registration_detail.section_number like 'W%') > 0 then '1'
    else '0'
end some_online_code,
case 
when sd.report_level_code = 'UG'  and sd.ia_student_type_code in ('F','T','C','R') then '1'
when sd.report_level_code = 'UG'  and sd.ia_student_type_code in ('D','E','G','S','X') then '2'
when sd.report_level_code = 'GR'  then '3'
else sd.report_level_code||'-'|| sd.ia_student_type_code
end D

from ia_td_student_data sd
where sd.registered_ind = 'Y'
and sd.fy = '21-22'                                                --UPDATE THIS
),cnt1 as (
select 
dp1.sd_pidm,
sum (dp1.online_courses_only_code) online_only_count,
sum (dp1.some_online_code) some_online_count,
count (dp1.sd_term_code) term_count
from dp1
group by
dp1.sd_pidm
),fy_course_type as (
select 
cnt1.*,
case 
when cnt1.online_only_count = cnt1.term_count then 'fy_online_only'
when cnt1.online_only_count = 0 and cnt1.some_online_count = 0 then 'fy_in_class_only'
else 'fy_some_online'
end fy_course_type_ind
from cnt1
),min_term as(
select
dp1.sd_pidm,
min (dp1.sd_term_code) min_term
from dp1
group by dp1.sd_pidm
)
--select * from min_term
,EE as (
select 
D,
count(distinct dp1.sd_pidm ) E
from dp1 
inner join min_term on dp1.sd_pidm = min_term.sd_pidm 
and dp1.sd_term_code = min_term.min_term
inner join fy_course_type on fy_course_type.sd_pidm = min_term.sd_pidm
where fy_course_type_ind = 'fy_online_only'
group by
D
)
--select * from EE
,ES as (
select 
D ,
count(distinct dp1.sd_pidm) F
from dp1 
inner join min_term on dp1.sd_pidm = min_term.sd_pidm 
and dp1.sd_term_code = min_term.min_term
inner join fy_course_type on fy_course_type.sd_pidm = min_term.sd_pidm
where fy_course_type_ind = 'fy_some_online'
group by
D
)
--select * from ES

select
'UNITID=171146' A,
'SURVSECT=E1D' B,
'PART=C' C,
'LINE='||EE.D D, 
'ENROLL_EXCLUSIVE='|| EE.E E,
'ENROLL_SOME='|| ES.F F,
cast(null as varchar2(16)) G,
cast(null as varchar2(16)) H,
cast(null as varchar2(16)) I
from EE 
left outer join ES on EE.D = ES.D

)popsel2

union all

/*******************************************************************************
--Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
*******************************************************************************/

SELECT
'UNITID=171146' A,
'SURVSECT=E1D' B,
'PART=B' C,
--CREDHRSU Credit hour instructional activity at the undergraduate level	
'CREDHRSU='||(select sum (TOTAL_CREDIT_HOURS_UMF)
from ia_td_student_data where registered_ind = 'Y' and fy = '21-22' and                 --UPDATE THIS
ipeds_class_code = 'UG') D,
--CONTHRS Contact hour instructional activity (undergraduate level only)	              
'CONTHRS='  E,          
--CREDHRSG Credit hour instructional activity at the graduate level	               
'CREDHRSG='||(select sum (TOTAL_CREDIT_HOURS_UMF)
from ia_td_student_data where registered_ind = 'Y' and
fy = '21-22' and                                                                        --UPDATE THIS
(ipeds_class_code = 'GR' or ipeds_class_code = 'Drs-Acad')) F,
--RDOCFTE Reported Doctor's degree-professional practice student FTE	             
'RDOCFTE='|| (select round(sum(TOTAL_CREDIT_HOURS_UMF/16),0) FYES
from ia_td_student_data where registered_ind = 'Y' and
fy = '21-22' and                                                                        --UPDATE THIS
ipeds_class_code = 'Drs-Prof') G,
cast(null as varchar2(16)) H,
cast(null as varchar2(16)) I

FROM
Dual

;
--,