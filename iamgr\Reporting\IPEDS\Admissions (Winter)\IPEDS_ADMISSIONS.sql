select 
styp_desc  student_type,
inst_accepted_app_any_date_ind admitted,
registered_ind registered,
report_ethnicity,
td_demographic.gender,
count (*) 
  from td_admissions_applicant
  left join td_demographic 
    on ad_pidm = dm_pidm  
    and term_code_entry = td_demographic.td_term_code 
  where term_code_entry = '201610' --change this each term
  and styp_code not in ('N','X')--remove new grads and non-academic
group by 
styp_desc, 
inst_accepted_app_any_date_ind,
registered_ind,
report_ethnicity,
td_demographic.gender
;

--select distinct STYP_CODE, STYP_DESC
--from td_admissions_applicant
--where term_code_entry = '201610'


