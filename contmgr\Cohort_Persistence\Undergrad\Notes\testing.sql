SELECT
co_term_code_key,
count(*)
from CO_POP_UNDUP_TBL
group BY co_term_code_key
order by 1 desc
;

SELECT
co_term_code_key,
count(*)
from CO_FA_TBL
group BY co_term_code_key
order by 1 desc
;

SELECT
-- co_term_code_key,
*
-- count(*)
from CO_TEN_YR_NSC_TBL
-- group BY co_term_code_key
-- order by 1 desc
;

SELECT
-- co_term_code_key,
-- count(*)
*
from CO_PERSIST_NSC_TBL
-- group BY co_term_code_key
-- order by 1 desc
;

SELECT
co_gr_term_code_key,
count(*)
from CO_GR_POP_TBL
group BY co_gr_term_code_key
order by 1 desc
;

SELECT
co_gr_term_code_key,
count(*)
from CO_GR_TEN_YR_NSC_TBL
group BY co_gr_term_code_key
order by 1 desc
;

SELECT
co_gr_term_code_key,
count(*)
from CO_GR_PERSIST_TBL
group BY co_gr_term_code_key
order by 1 desc
;

create table co_gr_nsc_degree_tbl_bac as
select * from co_nsc_degree_tbl
;

create table CO_GR_NSC_STUDENT_DATA_TBL_BAC as
select * from CO_GR_NSC_STUDENT_DATA_TBL
;
insert into CO_FA_TBL
SELECT 
  DISTINCT
  CO.co_pidm,
  CO.co_term_code_key,
  FY.FY_CO_TERM_CODE,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.co_pidm
    AND FB.TERM_CODE  = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE in ('SCHL','GRNT')
    AND FB.FUND_SOURCE in ('INST','OTHR')
    AND FB.FUND_CODE NOT LIKE ('PRIV%')
    AND FB.PAID_AMT   >0
    AND PAID_AMT                  IS NOT NULL
    )
  ), 'N') ins_grant_ind,--includes grants and scholorships
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.co_pidm
    AND FB.TERM_CODE  = FY.FY_CO_TERM_CODE
    AND (FB.FUND_CODE IN ('UMFSAW','2TRUBL')
    OR FB.FUND_CODE LIKE ('2FMS%') 
    OR FB.FUND_CODE LIKE ('2FT%'))
    AND FB.PAID_AMT   >0
    AND PAID_AMT                  IS NOT NULL
    )
  ), 'N') freshman_merit_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.co_pidm
    AND FB.TERM_CODE  = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE in ('SCHL','GRNT')
    AND FB.FUND_SOURCE IN ('FDRL','STAT')
    OR (FB.FUND_TYPE = 'SCHL' 
    AND FB.FUND_SOURCE = 'OTHR' 
    AND FB.FUND_CODE LIKE ('PRIV%'))
    AND FB.PAID_AMT   >0
    AND PAID_AMT                  IS NOT NULL
    )
  ), 'N') outside_grant_ind,--includes grants and scholorships
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM    = CO.co_pidm
    AND FB.TERM_CODE = FY.FY_CO_TERM_CODE
    AND FB.FUND_CODE = '1PELL'
    AND FB.PAID_AMT  >0
    AND PAID_AMT                 IS NOT NULL
    )
  ), 'N') pell_grant_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM    = CO.co_pidm
    AND FB.TERM_CODE = FY.FY_CO_TERM_CODE
    AND FB.FUND_CODE = '1SUBLN'
    AND FB.PAID_AMT  >0
    AND PAID_AMT                 IS NOT NULL
    )
  ), 'N') fed_subsidized_loan_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'LOAN'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') loan_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'WORK'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') work_study_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'GRNT'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') grant_ind,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.co_pidm
    AND FB.TERM_CODE      = FY.FY_CO_TERM_CODE
    AND FB.FUND_TYPE = 'SCHL'
    AND FB.PAID_AMT       >0
    AND PAID_AMT                      IS NOT NULL
    )
  ), 'N') scholorship_ind
FROM CO_POP_UNDUP_TBL CO
INNER JOIN IAMGR.IA_CW_FY_TERM FY ON CO.CO_TERM_CODE_KEY = FY.FY_TERM_CODE
WHERE NOT EXISTS (
  SELECT 1
  FROM CO_FA_TBL FA
  WHERE FA.co_term_code_key = CO.co_term_code_key
    AND FA.co_pidm = CO.co_pidm
)
--ORDER BY 1,2
;

INSERT INTO CO_GR_PERSIST_NSC_TBL

    (SELECT CO_GR_TEN_YR_NSC_TBL.*,
      ----------------------------SECOND FALL PERSISTENCE-----------------------
      CASE
        WHEN SCND_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC    IS NULL
        AND SCND_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND SCND_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SCND_FALL_PERSIST,
      ----------------------------THIRD FALL PERSISTENCE------------------------
      CASE
        WHEN THRD_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC    IS NULL
        AND THRD_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND THRD_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END THRD_FALL_PERSIST,
      ----------------------------FOURTH FALL PERSISTENCE-----------------------
      CASE
        WHEN FRTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC    IS NULL
        AND FRTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRTH_FALL_PERSIST,
      ----------------------------FIFTH FALL PERSISTENCE------------------------
      CASE
        WHEN FFTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FFTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC    IS NULL
        AND FFTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_NSC         = 'Y'
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC     = 'Y'
        AND FFTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC     = 'Y'
        AND FFTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FFTH_FALL_PERSIST,
      ----------------------------SIXTH FALL PERSISTENCE------------------------
      CASE
        WHEN SXTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SXTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC    IS NULL
        AND SXTH_FALL_GRAD_NSC        IS NULL )
        THEN 'LOST'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_NSC         = 'Y'
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC     = 'Y'
        AND SXTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC     = 'Y'
        AND SXTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SXTH_FALL_PERSIST,
      ----------------------------SEVENTH FALL PERSISTENCE----------------------
      CASE
        WHEN SVNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SVNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC    IS NULL
        AND SVNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_NSC         = 'Y'
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND SVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND SVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SVNTH_FALL_PERSIST,
      ----------------------------EIGTH FALL PERSISTENCE------------------------
      CASE
        WHEN EIGTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN EIGTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC    IS NULL
        AND EIGTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_NSC         = 'Y'
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC     = 'Y'
        AND EIGTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC     = 'Y'
        AND EIGTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END EIGTH_FALL_PERSIST,
      ----------------------------NINTH FALL PERSISTENCE------------------------
      CASE
        WHEN NNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN NNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_TERM_REG_NSC    IS NULL
        AND NNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_NSC         = 'Y'
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_TERM_REG_NSC     = 'Y'
        AND NNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_TERM_REG_NSC     = 'Y'
        AND NNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END NNTH_FALL_PERSIST,
      ----------------------------TENTH FALL PERSISTENCE------------------------
      CASE
        WHEN TNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN TNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_TERM_REG_NSC    IS NULL
        AND TNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_NSC         = 'Y'
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_TERM_REG_NSC     = 'Y'
        AND TNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_TERM_REG_NSC     = 'Y'
        AND TNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END TNTH_FALL_PERSIST,
      ----------------------------ELEVENTH FALL PERSISTENCE---------------------
      CASE
        WHEN ELVNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN ELVNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_TERM_REG_NSC    IS NULL
        AND ELVNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_NSC         = 'Y'
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND ELVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND ELVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END ELVNTH_FALL_PERSIST
  FROM CO_GR_TEN_YR_NSC_TBL)

select distinct co_term_code_key, co_pidm from CO_POP_UNDUP_TBL
minus
select distinct co_term_code_key, co_pidm from co_fa_tbl


;

BEGIN
    -- ZDMCODM_REMOVE_TERM.REMOVE_TERM('202430');
    ZDMCODM_REMOVE_TERM.REMOVE_TERM('202510');
END;
/

BEGIN
    -- ZDMCODM2.P0_remove_term('202510');
    -- ZDMCODM2.P1_UPDATE_CO_POP('202510');
    -- ZDMCODM2.P2_UPDATE_CO_NSC;
    ZDMCODM2.P3_UPDATE_CO_TEN_YR_NSC;
    ZDMCODM2.P4_UPDATE_CO_PERSIST;
    -- ZDMCODM2.P5_UPDATE_MOST_RECENT;
END;
/
TRUNCATE TABLE CO_GR_TEN_YR_NSC_TBL;
TRUNCATE table CO_GR_PERSIST_NSC_TBL;
TRUNCATE table CO_GR_MOST_RECENT_TBL;


BEGIN
    -- p_co_gr_nsc_student_data_tbl;
    -- P_CO_FA_TBL('202520');
    -- p_fall_co_ten_yr_nsc_tbl;
    -- P_WIN_CO_TEN_YR_NSC_TBL;
    -- P_SPR_CO_TEN_YR_NSC_TBL;
    -- P_CO_PERSIST_NSC;
    -- P_MOST_RECENT_TBL;
    P_FALL_CO_GR_TEN_YR_NSC_TBL;
    P_WIN_CO_GR_TEN_YR_NSC_TBL;
    P_CO_GR_PERSIST_NSC_TBL;
END;
/
select distinct co_term_code_key from CO_POP_UNDUP_TBL;
DELETE FROM CO_POP_UNDUP_TBL WHERE co_term_code_key LIKE '%30';
DELETE FROM CO_fa_TBL WHERE co_term_code_key LIKE '%30';
COMMIT;

truncate table co_fa_tbl;

select distinct co_gr_term_code_key from CO_GR_POP_TBL;
DELETE FROM CO_GR_POP_TBL WHERE co_gr_term_code_key LIKE '%40';
select count(*) from CO_GR_POP_TBL;
select count(*) from co_gr_nsc_student_data_tbl;
select count(*) from co_gr_nsc_student_data_tbl_bac;

insert into CO_FA_TBL
select * from CO_FA_TBL_BAC;

DESC CO_GR_TEN_YR_NSC_TBL;

select 'CO_POP_UNDUP_TBL' TBL, COUNT(*) REC_COUNT from CO_POP_UNDUP_TBL
UNION ALL
select 'CO_FA_TBL' TBL, COUNT(*) REC_COUNT from CO_FA_TBL
UNION ALL
SELECT 'CO_TEN_YR_NSC_TBL' TBL, COUNT(*) REC_COUNT FROM CO_TEN_YR_NSC_TBL
UNION ALL
SELECT 'CO_PERSIST_NSC_TBL' TBL, COUNT(*) REC_COUNT FROM CO_PERSIST_NSC_TBL
UNION ALL
SELECT 'CO_MOST_RECENT_TBL' TBL, COUNT(*) REC_COUNT FROM CO_MOST_RECENT_TBL
;

select 'CO_GR_POP_TBL' TBL, COUNT(*) REC_COUNT from CO_GR_POP_TBL
UNION ALL
SELECT 'CO_GR_TEN_YR_NSC_TBL' TBL, COUNT(*) REC_COUNT FROM CO_GR_TEN_YR_NSC_TBL
UNION ALL
SELECT 'CO_GR_PERSIST_NSC_TBL' TBL, COUNT(*) REC_COUNT FROM CO_GR_PERSIST_NSC_TBL
UNION ALL
SELECT 'CO_GR_MOST_RECENT_TBL' TBL, COUNT(*) REC_COUNT FROM CO_GR_MOST_RECENT_TBL
;

