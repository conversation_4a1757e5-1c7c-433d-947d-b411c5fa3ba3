CREATE OR REPLACE VIEW IA_DWPROD_GL_JRNL_LN
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_RECON_EXCPTN
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CNVCHARTFD
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CNVCHARTFD
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_JRNL_HEADER
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CAL_DETP
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_ACCOUNT
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_FUND
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_FN_DEPT
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_PERSONAL_DATA
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_PROGRAM
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_CLASS_CF
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_PROJ_GRNT
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_FUND
AS
  (SELECT * FROM <EMAIL>
  );
CREATE OR REPLACE VIEW IA_DWPROD_GL_CURR_FN_DEPT
AS
  (SELECT * FROM <EMAIL>
  ); 