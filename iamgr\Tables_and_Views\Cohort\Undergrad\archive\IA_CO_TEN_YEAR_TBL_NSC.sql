CREATE TABLE IA_COHORT_TEN_YR_TBL_NLC AS
(
SELECT DISTINCT
  /*******************************************************************************
  TABLE GRAIN: 1 ROW / STUDENT
  PURPOSE: PULL STUDENT DATA PROJECTED OUT 6 YEARS ON A STUDENT AND CREATE VIEW
  PRIMARY KEYS: IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  FOREIGN KEYS: TD_STUDENT_DATA.SD_PIDM
  *******************************************************************************/
  CO_PIDM,CO_TERM_CODE_KEY,
  --FIRST FALL********************************************************************

/*   (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
(SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM        = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_FALL_TERM_GPA, 
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  
  (SELECT MIN(TWOYEAR_FOURYEAR)
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.CO_PIDM  = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
    )TWOFOUR_NSC_ENROLLED,-- ENROLLED AT TWO YEAR OR FOUR YEAR OTHER THAN UMF   
  
  (SELECT TWOYEAR_FOURYEAR
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM  = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
    )TWOFOUR_NSC_DEGREE,-- GRADUATED FROM OTHER COLLEGE 
  -- FIRST WINTER SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -- FIRST SPRING SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_SPR_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_SPR_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_SPR_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_SPR_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_SPR_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_SPR_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  )FRST_SPR_GRAD_IND,-- GRADUATED FROM UMF
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 20))
  )FRST_SPR_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- FIRST SUMMER SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_SUM_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRST_SUM_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_SUM_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRST_SUM_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_SUM_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 30))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  AND TD2.TERM_HOURS_ATTEMPTED >0
  ) FRST_SUM_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  ----------------------------SECOND----------------------------------------------
  -- SECOND FALL SET *************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SCND_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) SCND_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SCND_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SCND_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SCND_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SCND_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  )SCND_FALL_GRAD_IND,-- GRADUATED FROM UMF
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 100))
  )SCND_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- SECOND WINTER SET ***********************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  AND TD1.SD_PIDM            = TD_STUDENT_DATA.SD_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SCND_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) SCND_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SCND_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SCND_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SCND_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 110))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SCND_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  ----------------------------THIRD-----------------------------------------------
  -- THIRD FALL SET **************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) THRD_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) THRD_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) THRD_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) THRD_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) THRD_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) THRD_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  )THRD_FALL_GRAD_IND,-- THIRD YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 200))
  )THRD_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- THIRD WINTER SET ******************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) THRD_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) THRD_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) THRD_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) THRD_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) THRD_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 210))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) THRD_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  ----------------------------FOURTH---------------------------------------------------
  -- FOURTH FALL SET ******************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRTH_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRTH_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FRTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FRTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  )FRTH_FALL_GRAD_IND,-- FOURTH YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 300))
  )FRTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- FOURTH WINTER SET ******************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FRTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FRTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FRTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 310))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FRTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -----------------------------FIFTH--------------------------------------------------
  -- FIFTH FALL SET ******************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FFTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FFTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  /* (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FFTH_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FFTH_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FFTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FFTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  )FFTH_FALL_GRAD_IND,-- FIVE YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 400))
  )FFTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- FIFTH WINTER SET ******************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FFTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) FFTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FFTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) FFTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FFTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 410))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) FFTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -----------------------------SIXTH--------------------------------------------
  -- SIXTH FALL SET **************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SXTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) SXTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SXTH_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SXTH_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SXTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SXTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  (SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  )SXTH_FALL_GRAD_IND,-- SIXTH YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 500))
  )SXTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- SIXTH WINTER SET ******************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SXTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) SXTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SXTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SXTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SXTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 510))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SXTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -----------------------------SEVENTH-------------------------------------------
  -- SEVENTH FALL SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SVNTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) SVNTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SVNTH_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SVNTH_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SVNTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SVNTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  )SVNTH_FALL_GRAD_IND,-- SEVENTH YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 600))
  )SVNTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- SEVENTH WINTER SET **********************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SVNTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) SVNTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SVNTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) SVNTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SVNTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 610))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) SVNTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -----------------------------EIGHTH----------------------------------------------
  -- EIGHTH FALL SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) EIGTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) EIGTH_FALL_TERM_REG_NSC, --REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) EIGTH_FALL_STD_TYPE, --STUDENT TYPE
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) EIGTH_FALL_TERM_GPA, --TERM GPA
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) EIGTH_FALL_CH_ATMPT, --CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) EIGTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  )EIGTH_FALL_GRAD_IND,-- EIGHTH YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 700))
  )EIGTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- EIGHTH WINTER SET **********************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) EIGTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) EIGTH_TERM_REG_NSC,
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) EIGTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) EIGTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) EIGTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 710))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) EIGTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -----------------------------NINTH----------------------------------------------
  -- NINTH FALL SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) NINTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) NINTH_FALL_TERM_REG_NSC,
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) NINTH_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) NINTH_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) NINTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) NINTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  )NINTH_FALL_GRAD_IND,-- NINTH YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 800))
  )NINTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- NINTH WINTER SET **********************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) NINTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) NINTH_WIN_TERM_REG_NSC,
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) NINTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) NINTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) NINTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 810))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) NINTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION */

  -----------------------------TENTH----------------------------------------------
  -- TENTH FALL SET ************************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) TENTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) TENTH_FALL_TERM_REG_NSC,
/*   (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) TENTH_FALL_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) TENTH_FALL_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) TENTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED,
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) TENTH_FALL_CH_CMPLTN, --CREDIT HOUR COMPLETION */
  ( SELECT DISTINCT 'Y'
  FROM UM_DEGREE
  WHERE UM_DEGREE.PIDM         = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND UM_DEGREE.DEGREE_STATUS  = 'AW'
  AND UM_DEGREE.LEVEL_CODE     = 'UG'
  AND UM_DEGREE.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  )TENTH_FALL_GRAD_IND, -- TENTH YEAR GRADUATION INDICATOR
  ( SELECT DISTINCT 'Y'
  FROM IA_NSC_DEGREES
  WHERE IA_NSC_DEGREES.CO_PIDM           = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND IA_NSC_DEGREES.TERM_CODE_GRADUATED < TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 900))
  )TENTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE

  -- TENTH WINTER SET **********************************************************

  (SELECT TD1.REGISTERED_IND
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) TENTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT IA_NSC_STUDENT_DATA.REGISTERED_IND
  FROM IA_NSC_STUDENT_DATA
  WHERE IA_NSC_STUDENT_DATA.TERM_CODE_ENROLLMENT_BEGIN = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  AND IA_NSC_STUDENT_DATA.CO_PIDM                      = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  ) TENTH_WIN_TERM_REG_NSC--,--REGISTERED AT ANOTHER COLLEGE
 /*  (SELECT TD1.STUDENT_TYPE_CODE
  FROM TD_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  AND TD1.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  ) TENTH_WIN_STD_TYPE,
  (SELECT TD2.TERM_GPA
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  AND TD2.SD_PIDM            = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
  ) TENTH_WIN_TERM_GPA,
  (SELECT TD2.TERM_HOURS_ATTEMPTED
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) TENTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
  FROM UM_STUDENT_DATA TD2
  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 910))
  AND TD2.SD_PIDM              = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  AND TD2.TERM_HOURS_ATTEMPTED >0
  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
  ) TENTH_WIN_CH_CMPLTN--, --CREDIT HOUR COMPLETION */

  -- COHORT DEMOGRAPHIC TABLE JOINS AND WHERE CLAUSE ********************************
FROM TD_STUDENT_DATA
INNER JOIN IA_COHORT_POP_UNDUP_TBL
ON IA_COHORT_POP_UNDUP_TBL.CO_PIDM    = TD_STUDENT_DATA.SD_PIDM
--WHERE IA_COHORT_POP_UNDUP_TBL.CO_PIDM = '103257'
);
