--------------------------------------------------------
--  DDL for Procedure P_DM_DBA_REVOKE_SELECT_R_VIEW
--------------------------------------------------------
set define off;

  CREATE OR REPLACE EDITIONABLE PROCEDURE "AIMSMGR"."P_DM_DBA_REVOKE_SELECT_R_VIEW" (id_in varchar2) as

 obj_name   VARCHAR2(4000);
 CURSOR table_cur IS
 SELECT
view_name
FROM
 all_views
WHERE
owner LIKE 'AIMSMGR'
and (view_name like 'RB%'
OR view_name like 'RC%'
OR view_name like 'RF%'
OR view_name like 'RN%'
OR view_name like 'RO%'
OR view_name like 'RP%'
OR view_name like 'RR%'
OR view_name like 'RT%')
;
 table_rec  table_cur%rowtype;
BEGIN
 dbms_output.enable(NULL);
 OPEN table_cur;
 LOOP
  FETCH table_cur INTO table_rec;
  EXIT WHEN table_cur%notfound;
  obj_name := 'REVOKE SELECT ON aimsmgr.'
              || table_rec.view_name
              || ' FROM '|| id_in;
  dbms_output.put_line(obj_name);
  EXECUTE IMMEDIATE obj_name;
 END LOOP;

 CLOSE table_cur;
END p_dm_dba_revoke_select_r_view;

/
