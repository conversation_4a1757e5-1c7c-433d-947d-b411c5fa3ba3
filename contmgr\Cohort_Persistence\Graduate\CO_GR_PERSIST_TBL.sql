--DROP TABLE CO_GR_PERSIST_TBL;
TRUNCATE TABLE CO_GR_PERSIST_TBL;
--CREATE TABLE CO_GR_PERSIST_TBL AS
INSERT INTO CO_GR_PERSIST_TBL

  (SELECT CO_GR_TEN_YR_NSC_TBL.*,
--    CO_GR_TEN_YR_NSC_TBL.GRAD_DATE,
    ----------------------------Second Fall Persistence-----------------------------
    CASE
      WHEN SCND_FALL_GRAD_IND = 'Y'
      THEN 'Grad'
      WHEN (SCND_FALL_TERM_REG_IND = 'Y'
      AND SCND_FALL_GRAD_IND      IS NULL)
      THEN 'Retain'
      ELSE 'Lost'
    END SCND_FALL_PERSIST,
    ----------------------------Third Fall Persistence------------------------------
    CASE
      WHEN THRD_FALL_GRAD_IND = 'Y'
      THEN 'Grad'
      WHEN (THRD_FALL_TERM_REG_IND = 'Y'
      AND THRD_FALL_GRAD_IND      IS NULL)
      THEN 'Retain'
      ELSE 'Lost'
    END THRD_FALL_PERSIST,
    ----------------------------Fourth Fall Persistence-----------------------------
    CASE
      WHEN FRTH_FALL_GRAD_IND = 'Y'
      THEN 'Grad'
      WHEN (FRTH_FALL_TERM_REG_IND = 'Y'
      AND FRTH_FALL_GRAD_IND      IS NULL)
      THEN 'Retain'
      ELSE 'Lost'
    END FRTH_FALL_PERSIST,
    ----------------------------Fifth Fall Persistence------------------------------
    CASE
      WHEN FFTH_FALL_GRAD_IND = 'Y'
      THEN 'Grad'
      WHEN (FFTH_FALL_TERM_REG_IND = 'Y'
      AND FFTH_FALL_GRAD_IND      IS NULL)
      THEN 'Retain'
      ELSE 'Lost'
    END FFTH_FALL_PERSIST,
    ----------------------------Sixth Fall Persistence------------------------------
    CASE
      WHEN SXTH_FALL_GRAD_IND = 'Y'
      THEN 'Grad'
      WHEN (SXTH_FALL_TERM_REG_IND = 'Y'
      AND SXTH_FALL_GRAD_IND      IS NULL)
      THEN 'Retain'
      ELSE 'Lost'
    END SXTH_FALL_PERSIST,
    ----------------------------Seventh Fall Persistence----------------------------
    CASE
      WHEN SVNTH_FALL_GRAD_IND = 'Y'
      THEN 'Grad'
      WHEN (SVNTH_FALL_TERM_REG_IND = 'Y'
      AND SVNTH_FALL_GRAD_IND      IS NULL)
      THEN 'Retain'
      ELSE 'Lost'
    END SVNTH_FALL_PERSIST,
        ----------------------------Eighth Fall Persistence----------------------------
        CASE
          WHEN EIGTH_FALL_GRAD_IND = 'Y'
          THEN 'Grad'
          WHEN (EIGTH_FALL_TERM_REG_IND = 'Y'
          AND EIGTH_FALL_GRAD_IND      IS NULL)
          THEN 'Retain'
          ELSE 'Lost'
        END EIGTH_FALL_PERSIST,
    ----------------------------Ninth Fall Persistence----------------------------
    CASE
       WHEN  NNTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
       WHEN (NNTH_FALL_TERM_REG_IND = 'Y' AND NNTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
       ELSE 'Lost'
       END NNTH_FALL_PERSIST,
    
    ----------------------------Tenth Fall Persistence----------------------------
    CASE
       WHEN  TNTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
       WHEN (TNTH_FALL_TERM_REG_IND = 'Y' AND TNTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
       ELSE 'Lost'
       END TNTH_FALL_PERSIST,
       
           ----------------------------Eleventh Fall Persistence----------------------------
    CASE
       WHEN  ELVNTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
       WHEN (ELVNTH_FALL_TERM_REG_IND = 'Y' AND ELVNTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
       ELSE 'Lost'
       END ELVNTH_FALL_PERSIST
  FROM CO_GR_TEN_YR_NSC_TBL

  );
