with dp1 as (
select
distinct 
COURSE,
SECTION_NUMBER,
MEETING_SCHD_CODE_1,
case
when IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER >= 500 then 'GR'
when IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER < 500 then 'UG'
else IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER
end level_code,
count (IA_TD_REGISTRATION_DETAIL.pidm) course_enrollment
from
IA_TD_REGISTRATION_DETAIL
WHERE
IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE in (select current_term from um_current_term)           --UPDATE HERE
and IA_TD_REGISTRATION_DETAIL.section_number 
    not in ('W1','W2','W3','W4','W5','W6','W7','IS','90','91','92','93','94',
            '95','96','97','98','99') 
and MEETING_SCHD_CODE_1 in ('L/L','L/D','MM','LAB')
group by 
COURSE,
SECTION_NUMBER,
MEETING_SCHD_CODE_1,
case
when IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER >= 500 then 'GR'
when IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER < 500 then 'UG'
else IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER
end
)
select 
--level_code,
round ((sum (course_enrollment) / count (course)),2) avg_count
from dp1
--group by
--level_code
;
