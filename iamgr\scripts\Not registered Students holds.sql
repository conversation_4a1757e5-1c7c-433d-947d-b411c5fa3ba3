select 
sd_pidm, 
um_student_data.umid,first_name, 
Last_name,
CA_EMAIL,
A1_AREA_CODE,
A1_PHONE_NUMBER,
TERM_REGISTERED_HOURS,
REASON,
MOST_RECENT_HOURS_EARNED,
overall_gpa,
CLASS_DESC,
HLDD_CODE,
STUDENT_TYPE_Desc
from um_student_data  
left join holdstemp on um_student_data.sd_pidm = Holdstemp.Pidm
inner join um_demographic on um_student_data.sd_pidm = Um_Demographic.Dm_Pidm
where 
STUDENT_TYPE_CODE in ('F', 'T', 'R', 'C') and STUDENT_STATUS_Code = 'AS' and sd_term_code = 201610  and Report_Level_Code = 'UG' and registered_ind = 'Y' and 
sd_pidm not in (select sd_pidm from um_student_data where registered_ind = 'Y' and sd_term_code = 201620) and pidm is not null;
