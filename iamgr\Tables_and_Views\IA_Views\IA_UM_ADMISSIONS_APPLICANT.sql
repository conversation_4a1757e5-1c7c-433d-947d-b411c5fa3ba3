create or replace view IA_UM_ADMISSIONS_APPLICANT as (

select
UM_ADMISSIONS_APPLICANT.AD_PIDM,
UM_DEMOGRAPHIC.UMID,
UM_ADMISSIONS_APPLICANT.TERM_CODE_ENTRY,
UM_ADMISSIONS_APPLICANT.ENROLLED_IND,
UM_ADMISSIONS_APPLICANT.APPL_DATE,
UM_ADMISSIONS_APPLICANT.APST_CODE,
UM_ADMISSIONS_APPLICANT.APST_DESC,
UM_ADMISSIONS_APPLICANT.APST_DATE,
UM_ADMISSIONS_APPLICANT.ADMT_CODE,
UM_ADMISSIONS_APPLICANT.ADMT_DESC,
UM_ADMISSIONS_APPLICANT.STYP_CODE,
UM_ADMISSIONS_APPLICANT.STYP_DESC,
UM_ADMISSIONS_APPLICANT.CAMP_CODE,
UM_ADMISSIONS_APPLICANT.RESD_CODE, 
UM_ADMISSIONS_APPLICANT.RESD_DESC,

UM_DEMOGRAPHIC.FIRST_NAME,
UM_DEMOGRAPHIC.MIDDLE_INITIAL,
UM_DEMOGRAPHIC.LAST_NAME,
UM_DEMOGRAPHIC.NAME_SUFFIX,
UM_DEMOGRAPHIC.A1_STREET_LINE1,
UM_DEMOGRAPHIC.A1_STREET_LINE2,
UM_DEMOGRAPHIC.A1_STREET_LINE3,
UM_DEMOGRAPHIC.A1_CITY,
UM_DEMOGRAPHIC.A1_STATE_CODE,
UM_DEMOGRAPHIC.A1_STATE_DESC,
UM_DEMOGRAPHIC.A1_ZIP,
UM_DEMOGRAPHIC.A1_COUNTY_CODE,
UM_DEMOGRAPHIC.A1_COUNTY_DESC,
UM_DEMOGRAPHIC.A1_NATION_CODE,
UM_DEMOGRAPHIC.A1_NATION_DESC,
UM_DEMOGRAPHIC.A1_AREA_CODE,
UM_DEMOGRAPHIC.A1_PHONE_NUMBER,
case
when UM_ADMISSIONS_APPLICANT.COUNTY_CODE_ADMIT in 
('MI049','MI087','MI093','MI125','MI145','MI155','MI157')then 'Y'
else 'N'
end commutible_ind,
--UM_DEMOGRAPHIC.NR_STREET_LINE1,
--UM_DEMOGRAPHIC.NR_STREET_LINE2,
--UM_DEMOGRAPHIC.NR_STREET_LINE3,
--UM_DEMOGRAPHIC.NR_CITY,
--UM_DEMOGRAPHIC.NR_STATE_CODE,
--UM_DEMOGRAPHIC.NR_STATE_DESC,
--UM_DEMOGRAPHIC.NR_ZIP,
--UM_DEMOGRAPHIC.NR_COUNTY_CODE,
--UM_DEMOGRAPHIC.NR_COUNTY_DESC,
--UM_DEMOGRAPHIC.NR_NATION_CODE,
--UM_DEMOGRAPHIC.NR_NATION_DESC,
--UM_DEMOGRAPHIC.NR_AREA_CODE,
--UM_DEMOGRAPHIC.NR_PHONE_NUMBER,
UM_DEMOGRAPHIC.CA_EMAIL,
UM_DEMOGRAPHIC.HM_EMAIL_1,
UM_DEMOGRAPHIC.BIRTHDATE,
UM_DEMOGRAPHIC.AGE,
UM_DEMOGRAPHIC.GENDER,
UM_DEMOGRAPHIC.GENDER_DESC,
DECODE(UM_DEMOGRAPHIC.gender,'F',2,1)IPEDS_GENDER_CODE,
UM_DEMOGRAPHIC.CITIZENSHIP_CODE,
UM_DEMOGRAPHIC.CITIZENSHIP_DESC,
UM_DEMOGRAPHIC.VETERAN_IND,
UM_DEMOGRAPHIC.INTL_IND,
UM_DEMOGRAPHIC.VETERAN_BENIFITS_ELIGIBILE,
UM_DEMOGRAPHIC.VISA_TYPE_CODE,
UM_DEMOGRAPHIC.VISA_TYPE_DESC,
UM_DEMOGRAPHIC.NATION_CITIZEN_CODE,
UM_DEMOGRAPHIC.NATION_CITIZEN_DESC,
UM_DEMOGRAPHIC.REPORT_ETHNICITY,
DECODE(UM_DEMOGRAPHIC.REPORT_ETHNICITY,
      'Nonresident Alien',1,
      'Hispanic or Latino',2,
      'American Indian or Alaska Native',3,
      'Asian',4,
      'Black or African American',5,
      'Native Hawaiian and Other Pacific Islander',6,
      'White',7,
      'Two or more races',8,9)IPEDS_RACE_CODE,
UM_DEMOGRAPHIC.HSCH_CODE,
UM_DEMOGRAPHIC.HSCH_DESC,
UM_DEMOGRAPHIC.HSCH_GRAD_DATE,
UM_DEMOGRAPHIC.HSCH_RANK,
UM_DEMOGRAPHIC.HSCH_SIZE,
UM_DEMOGRAPHIC.HSCH_GPA,
UM_DEMOGRAPHIC.HSCH_STREET_LINE_1,
UM_DEMOGRAPHIC.HSCH_STREET_LINE_2,
UM_DEMOGRAPHIC.HSCH_CITY,
UM_DEMOGRAPHIC.HSCH_STATE,
UM_DEMOGRAPHIC.HSCH_ZIP,
UM_DEMOGRAPHIC.PCOL_CODE_1,
UM_DEMOGRAPHIC.PCOL_DESC_1,
UM_DEMOGRAPHIC.PCOL_TRANS_RECV_DATE_1,
UM_DEMOGRAPHIC.PCOL_DEGC_CODE_1,
UM_DEMOGRAPHIC.PCOL_DEGC_DATE_1,
UM_DEMOGRAPHIC.PCOL_HOURS_TRANSFERRED_1,
UM_DEMOGRAPHIC.PCOL_GPA_TRANSFERRED_1,
UM_DEMOGRAPHIC.VISA_NUMBER,
UM_DEMOGRAPHIC.NATION_BIRTH_CODE,
UM_DEMOGRAPHIC.NATION_BIRTH_DESC
from 
UM_ADMISSIONS_APPLICANT
inner join UM_DEMOGRAPHIC on UM_DEMOGRAPHIC.DM_PIDM = UM_ADMISSIONS_APPLICANT.AD_PIDM
);