TRUNCATE TABLE IA_CO_TEN_YR_TBL_NSC_PROD_BAC;

INSERT INTO IA_CO_TEN_YR_TBL_NSC_PROD_BAC
SELECT * FROM IA_CO_TEN_YR_TBL_NSC_PROD;
COMMIT;

SELECT COUNT (*) FROM IA_CO_TEN_YR_TBL_NSC_PROD_BAC;
SELECT COUNT (*) FROM IA_CO_TEN_YR_TBL_NSC_PROD;

select * from IA_CO_TEN_YR_TBL_NSC_PROD;

TRUNCATE TABLE IA_CO_TEN_YR_TBL_NSC_PROD;

CREATE TABLE IA_CO_TEN_YR_TBL_NSC_PROD AS

--INSERT INTO IA_CO_TEN_YR_TBL_NSC_PROD
(
SELECT DISTINCT
/*******************************************************************************
  TABLE GRAIN: 1 ROW / STUDENT
  PURPOSE: PULL STUDENT DATA PROJECTED OUT 11 YEARS ON A STUDENT AND CREATE TABLE
  PRIMARY KEYS: IA_COHORT_POP_UNDUP_TBL.CO_PIDM
  FOREIGN KEYS: UM_STUDENT_DATA.SD_PIDM
*******************************************************************************/
  CO_PIDM,
  CO_TERM_CODE_KEY,
--FIRST FALL********************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_FALL_TERM_REG_IND,--REGISTERED AT UMF
 
   (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = CO.CO_TERM_CODE_KEY
  AND NSC.PIDM                      = CO.CO_PIDM
  )FRST_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
  
--  (SELECT DISTINCT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_FALL_COL_NSC--,--NAME OF COLLEGE OR UNIVERSITY

--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_FALL_MJR_NSC,--NAME OF MAJOR

--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_FALL_STD_TYPE,

--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_FALL_MJR_CODE,

--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_FALL_COL_CODE,

--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND TD2.SD_PIDM        = CO.CO_PIDM
--  )FRST_FALL_TERM_GPA,

--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
--  AND TD2.SD_PIDM        = CO.CO_PIDM
--  )FRST_FALL_ASTD_CODE,

--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = CO.CO_TERM_CODE_KEY
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = CO.CO_TERM_CODE_KEY
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION

  (SELECT MIN(TWOYEAR_FOURYEAR)
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.PIDM = CO.CO_PIDM
  AND MIN_TERM_IND = 1
  )TWOFOUR_NSC_ENROLLED,-- ENROLLED AT TWO YEAR OR FOUR YEAR OTHER THAN UMF

  (SELECT DISTINCT TWOYEAR_FOURYEAR
  FROM NSC_DEGREE NSCD
  WHERE NSCD.PIDM = CO.CO_PIDM
AND MIN_TERM_IND = 1
  )TWOFOUR_NSC_DEGREE,-- GRADUATED FROM OTHER COLLEGE
  
--  -- FIRST WINTER SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
  AND NSC.PIDM                      = CO.CO_PIDM
  )FRST_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_WIN_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_WIN_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_WIN_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_WIN_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_WIN_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_WIN_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 10))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
--  -- FIRST SPRING SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SPR_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SPR_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
  AND NSC.PIDM                      = CO.CO_PIDM
  )FRST_SPR_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_SPR_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_SPR_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_SPR_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_SPR_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_SPR_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_SPR_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_SPR_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_SPR_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 20))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_SPR_CH_CMPLTN,--CREDIT HOUR COMPLETION
--  -- FIRST SUMMER SET ************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRST_SUM_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRST_SUM_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
  AND NSC.PIDM                      = CO.CO_PIDM
  )FRST_SUM_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_SUM_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRST_SUM_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_SUM_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_SUM_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRST_SUM_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_SUM_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRST_SUM_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_SUM_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 30))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  )FRST_SUM_CH_CMPLTN,--CREDIT HOUR COMPLETION
--  ----------------------------SECOND----------------------------------------------
--  -- SECOND FALL SET *************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
  AND NSC.PIDM                      = CO.CO_PIDM
  )SCND_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )SCND_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )SCND_FALL_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )SCND_FALL_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )SCND_FALL_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )SCND_FALL_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )SCND_FALL_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )SCND_FALL_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )SCND_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )SCND_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM NSC_DEGREE NSCD
      WHERE NSCD.PIDM           = CO.CO_PIDM
      AND NSCD.MAPPED_GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 100)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
--  -- SECOND WINTER SET ***********************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )SCND_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )SCND_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
  AND NSC.PIDM                      = CO.CO_PIDM
  )SCND_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )SCND_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )SCND_WIN_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )SCND_WIN_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )SCND_WIN_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )SCND_WIN_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )SCND_WIN_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )SCND_WIN_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )SCND_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )SCND_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM NSC_DEGREE NSCD
      WHERE NSCD.PIDM           = CO.CO_PIDM
      AND NSCD.MAPPED_GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 110)))>0
    THEN 'Y'
    ELSE NULL
  END SCND_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
--  ----------------------------THIRD-----------------------------------------------
--  -- THIRD FALL SET **************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
  AND NSC.PIDM                      = CO.CO_PIDM
  )THRD_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )THRD_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )THRD_FALL_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )THRD_FALL_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )THRD_FALL_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )THRD_FALL_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )THRD_FALL_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )THRD_FALL_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )THRD_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )THRD_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM NSC_DEGREE NSCD
      WHERE NSCD.PIDM           = CO.CO_PIDM
      AND NSCD.MAPPED_GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 200)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
--  -- THIRD WINTER SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )THRD_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )THRD_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
  AND NSC.PIDM                      = CO.CO_PIDM
  )THRD_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )THRD_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )THRD_WIN_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )THRD_WIN_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )THRD_WIN_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )THRD_WIN_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )THRD_WIN_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )THRD_WIN_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )THRD_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )THRD_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM NSC_DEGREE NSCD
      WHERE NSCD.PIDM           = CO.CO_PIDM
      AND NSCD.MAPPED_GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 210)))>0
    THEN 'Y'
    ELSE NULL
  END THRD_WIN_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
--  ----------------------------FOURTH---------------------------------------------------
--  -- FOURTH FALL SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_FALL_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_FALL_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
  AND NSC.PIDM                      = CO.CO_PIDM
  )FRTH_FALL_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRTH_FALL_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRTH_FALL_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRTH_FALL_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRTH_FALL_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRTH_FALL_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRTH_FALL_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRTH_FALL_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )FRTH_FALL_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )FRTH_FALL_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_FALL_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM NSC_DEGREE NSCD
      WHERE NSCD.PIDM           = CO.CO_PIDM
      AND NSCD.MAPPED_GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 300)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_FALL_GRAD_NSC,-- GRADUATED FROM OTHER COLLEGE
--  -- FOURTH WINTER SET ******************************************************************
  (
  SELECT TD1.sd_term_code
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
  AND TD1.SD_PIDM        = CO.CO_PIDM
  )FRTH_WIN_TERM_CODE,
  (SELECT TD1.REGISTERED_IND
  FROM UM_STUDENT_DATA TD1
  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
  AND TD1.SD_PIDM            = CO.CO_PIDM
  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
  )FRTH_WIN_TERM_REG_IND,--REGISTERED AT UMF
  (SELECT DISTINCT NSC.REGISTERED_IND
  FROM NSC_ENROLLMENT NSC
  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
  AND NSC.PIDM                      = CO.CO_PIDM
  )FRTH_WIN_TERM_REG_NSC,--REGISTERED AT ANOTHER COLLEGE
--  (SELECT NSC.COLLEGE_NAME
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRTH_WIN_COL_NSC,--NAME OF COLLEGE OR UNIVERSITY
--  (SELECT NSC.ENROLLMENT_MAJOR
--  FROM NSC_ENROLLMENT NSC
--  WHERE NSC.MAPPED_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND NSC.PIDM                      = CO.CO_PIDM
--  )FRTH_WIN_MJR_NSC,--NAME OF MAJOR
--  (SELECT TD1.STUDENT_TYPE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD1.SD_PIDM            = CO.CO_PIDM
--  AND TD1.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD1.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRTH_WIN_STD_TYPE,
--  (SELECT TD1.PRIMARY_MAJOR_1
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRTH_WIN_MJR_CODE,
--  (SELECT TD1.PRIMARY_COLLEGE_CODE
--  FROM UM_STUDENT_DATA TD1
--  WHERE TD1.SD_TERM_CODE = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD1.SD_PIDM        = CO.CO_PIDM
--  )FRTH_WIN_COL_CODE,
--  (SELECT TD2.TERM_GPA
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRTH_WIN_TERM_GPA,
--  (SELECT TD2.MOST_RECENT_ASTD_CODE
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE     = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD2.SD_PIDM            = CO.CO_PIDM
--  AND TD2.PRIMARY_LEVEL_CODE = 'UG'
--  AND TD2.STUDENT_TYPE_CODE IN ('C','T','R')
--  )FRTH_WIN_ASTD_CODE,
--  (SELECT TD2.TERM_HOURS_ATTEMPTED
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )FRTH_WIN_CH_ATMPT,--CREDIT HOUR ATTEMPTED
--  (SELECT ROUND ((TD2.TERM_HOURS_PASSED/TD2.TERM_HOURS_ATTEMPTED),2)
--  FROM UM_STUDENT_DATA TD2
--  WHERE TD2.SD_TERM_CODE       = TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310))
--  AND TD2.SD_PIDM              = CO.CO_PIDM
--  AND TD2.TERM_HOURS_ATTEMPTED >0
--  AND TD2.PRIMARY_LEVEL_CODE   = 'UG'
--  AND TD2.STUDENT_TYPE_CODE   IN ('C','T','R')
--  )FRTH_WIN_CH_CMPLTN,--CREDIT HOUR COMPLETION
  CASE
    WHEN (SELECT COUNT(*)
      FROM um_degree UMD
      WHERE UMD.PIDM         = CO.CO_PIDM
      AND UMD.DEGREE_STATUS  = 'AW'
      AND UMD.LEVEL_CODE     = 'UG'
      AND UMD.GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_WIN_GRAD_IND,
  CASE
    WHEN (SELECT COUNT(*)
      FROM NSC_DEGREE NSCD
      WHERE NSCD.PIDM           = CO.CO_PIDM
      AND NSCD.MAPPED_GRAD_TERM_CODE < TO_CHAR((TO_NUMBER(CO.CO_TERM_CODE_KEY) + 310)))>0
    THEN 'Y'
    ELSE NULL
  END FRTH_WIN_GRAD_NSC--,-- GRADUATED FROM OTHER COLLEGE

  --COHORT DEMOGRAPHIC TABLE JOINS AND WHERE CLAUSE ******************************
FROM UM_STUDENT_DATA  SD
INNER JOIN IA_COHORT_POP_UNDUP_TBL CO
ON CO.CO_PIDM           = SD.SD_PIDM
AND CO.CO_TERM_CODE_KEY = SD.SD_TERM_CODE
--WHERE CO.CO_PIDM        = '90434'
);
