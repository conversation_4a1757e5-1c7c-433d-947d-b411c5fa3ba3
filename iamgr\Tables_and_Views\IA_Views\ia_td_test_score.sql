create or replace view ia_td_test_score as (

select 
TD_STUDENT_DATA.sd_pidm,
TD_STUDENT_DATA.sd_term_code,
TD_STUDENT_DATA.registered_ind,
TD_STUDENT_DATA.PRIMARY_MAJOR_1 AS PRIMARY_MAJOR,
TD_STUDENT_DATA.PRIMARY_COLLEGE_CODE AS COLLEGE_CODE,
TD_STUDENT_DATA.ia_student_type_code,
CASE
WHEN TD_STUDENT_DATA.PRIMARY_COLLEGE_CODE IN ('AS','RK') THEN 'CAS'
WHEN TD_STUDENT_DATA.PRIMARY_COLLEGE_CODE = 'HP' THEN 'SHPS'
WHEN TD_STUDENT_DATA.PRIMARY_COLLEGE_CODE = 'EH' THEN 'SEHS'
WHEN TD_STUDENT_DATA.PRIMARY_COLLEGE_CODE = 'MG' THEN 'SOM'
WHEN TD_STUDENT_DATA.PRIMARY_COLLEGE_CODE IN ('NA','00') THEN 'NON-ACAD'
END COLLEGE_CODE_DESC,
TD_DEMOGRAPHIC.UMID,
TD_DEMOGRAPHIC.FIRST_NAME,
TD_DEMOGRAPHIC.MIDDLE_INITIAL,
TD_DEMOGRAPHIC.LAST_NAME,
TD_DEMOGRAPHIC.A1_STREET_LINE1 AS A1_STREET,
TD_DEMOGRAPHIC.A1_CITY,
TD_DEMOGRAPHIC.A1_STATE_CODE,
TD_DEMOGRAPHIC.A1_STATE_DESC,
TD_DEMOGRAPHIC.A1_ZIP,
TD_DEMOGRAPHIC.A1_COUNTY_CODE,
TD_DEMOGRAPHIC.A1_COUNTY_DESC,
TD_DEMOGRAPHIC.A1_NATION_CODE,
TD_DEMOGRAPHIC.A1_NATION_DESC,
TD_DEMOGRAPHIC.A1_AREA_CODE,
TD_DEMOGRAPHIC.A1_PHONE_NUMBER,
TD_DEMOGRAPHIC.UNIQNAME,
TD_DEMOGRAPHIC.CA_EMAIL,
TD_DEMOGRAPHIC.HM_EMAIL_1 AS HM_EMAIL,
TD_DEMOGRAPHIC.BIRTHDATE,
TD_DEMOGRAPHIC.AGE,
TD_DEMOGRAPHIC.GENDER,
TD_DEMOGRAPHIC.DECEASED_IND,
TD_DEMOGRAPHIC.DECEASED_DATE,
TD_DEMOGRAPHIC.CITIZENSHIP_CODE,
TD_DEMOGRAPHIC.CITIZENSHIP_DESC,
TD_DEMOGRAPHIC.VETERAN_IND,
TD_DEMOGRAPHIC.INTL_IND,
TD_DEMOGRAPHIC.VETERAN_BENIFITS_ELIGIBILE,
TD_DEMOGRAPHIC.VISA_TYPE_CODE,
TD_DEMOGRAPHIC.VISA_TYPE_DESC,
TD_DEMOGRAPHIC.NATION_CITIZEN_CODE,
TD_DEMOGRAPHIC.NATION_CITIZEN_DESC,
TD_DEMOGRAPHIC.REPORT_ETHNICITY,
TD_DEMOGRAPHIC.A1_ZIP5,
TD_DEMOGRAPHIC.HSCH_CODE,
TD_DEMOGRAPHIC.HSCH_DESC,
TD_DEMOGRAPHIC.HSCH_GRAD_DATE,
TD_DEMOGRAPHIC.HSCH_RANK,
TD_DEMOGRAPHIC.HSCH_PERCENTILE,
ROUND(TO_NUMBER(TD_DEMOGRAPHIC.hsch_gpa),2)HSCH_GPA,
TD_DEMOGRAPHIC.HSCH_CITY,
TD_DEMOGRAPHIC.HSCH_STATE,
TD_DEMOGRAPHIC.HSCH_ZIP,
TD_DEMOGRAPHIC.PCOL_CODE_1 AS PCOL_CODE,
TD_DEMOGRAPHIC.PCOL_DESC_1 AS PCOL_DESC,
TD_DEMOGRAPHIC.PCOL_DEGC_CODE_1 AS PCOL_DEGC_CODE,
TD_DEMOGRAPHIC.PCOL_DEGC_DATE_1 AS PCOL_DEGC_DATE,
TD_DEMOGRAPHIC.PCOL_HOURS_TRANSFERRED_1 PCOL_HOURS,
round(TD_DEMOGRAPHIC.PCOL_GPA_TRANSFERRED_1,2) PCOL_GPA,
CASE
WHEN round(TD_DEMOGRAPHIC.PCOL_GPA_TRANSFERRED_1,2) <= 4.00 THEN round(TD_DEMOGRAPHIC.PCOL_GPA_TRANSFERRED_1,2)
WHEN round(TD_DEMOGRAPHIC.PCOL_GPA_TRANSFERRED_1,2) > 4.00 THEN NULL
END PCOL_GPA_CLEAN,
TD_TEST_SCORE.ACT_ENGLISH,
TD_TEST_SCORE.ACT_MATH,
TD_TEST_SCORE.TD_TEST_SCORE.ACT_READING,
TD_TEST_SCORE.ACT_SCIENCE_REASONING,
TD_TEST_SCORE.ACT_COMPOSITE,
TD_TEST_SCORE.ACT_SUM_OF_STANDARD,
TD_TEST_SCORE.ACT_COMBINED_ENG_WRI,
TD_TEST_SCORE.SAT_VERBAL,
TD_TEST_SCORE.SAT_MATHEMATICS,
TD_TEST_SCORE.SAT_READING,
TD_TEST_SCORE.SAT_VOCABULARY,
TD_TEST_SCORE.SAT_WRITING,
TD_TEST_SCORE.SAT_ESSAY,
TD_TEST_SCORE.SAT_MC,
TD_TEST_SCORE.SAT_TOTAL_COMBINED_S10,
TD_TEST_SCORE.SAT_READ_WRI_S11,
TD_TEST_SCORE.SAT_MATH_S12,
TD_TEST_SCORE.GMAT_VERBAL_PERCENTILE,
TD_TEST_SCORE.GMAT_QUANTITATIVE_PERCENTILE,
TD_TEST_SCORE.GMAT_TOTAL_SCORE,
TD_TEST_SCORE.GMAT_WRITING_PERCENTILE,
TD_TEST_SCORE.GMAT_TOTAL_PERCENTILE,
TD_TEST_SCORE.GMAT_VERBAL_SCORE,
TD_TEST_SCORE.GMAT_QUANTITATIVE_SCORE,
TD_TEST_SCORE.GMAT_WRITING_SCORE,
TD_TEST_SCORE.GRE_VERBAL,
TD_TEST_SCORE.GRE_QUANTITATIVE,
TD_TEST_SCORE.GRE_ANALYTICAL_WRITING,
TD_TEST_SCORE.GRE_REV_GEN_QUANTITATIVE,
TD_TEST_SCORE.GRE_REV_GEN_VERBAL,
TD_TEST_SCORE.GRE_REV_GEN_WRITING,
TD_TEST_SCORE.GRE_QUANT_ESTIMATED_CUR,
TD_TEST_SCORE.GRE_VERBAL_ESTIMATED_CUR,
TD_TEST_SCORE.TOEFL,
TD_TEST_SCORE.TOEFL_PAPER_TOTAL,
TD_TEST_SCORE.TOEFL_CBT_TOTAL,
TD_TEST_SCORE.TOEFL_IBT_TOTAL,
TD_TEST_SCORE.TOEFL_IBT_LISTEN,
TD_TEST_SCORE.TOEFL_IBT_READ,
TD_TEST_SCORE.TOEFL_IBT_SPEAK,
TD_TEST_SCORE.TOEFL_IBT_WRITE,
TD_TEST_SCORE.IELTS_OVERALL,
TD_TEST_SCORE.IELTS_LISTEN,
TD_TEST_SCORE.IELTS_READ,
TD_TEST_SCORE.IELTS_WRITE,
TD_TEST_SCORE.IELTS_SPEAK,
TD_TEST_SCORE.MELAB_FINAL,
TD_TEST_SCORE.MELAB_COMP,
TD_TEST_SCORE.MELAB_GCVR,
TD_TEST_SCORE.MELAB_LISTEN,
TD_TEST_SCORE.MELAB_SPEAK,
TD_TEST_SCORE.MATH_PLACEMENT,
TD_TEST_SCORE.ENG_READ_PLACEMENT,
TD_TEST_SCORE.ENG_WRIT_PLACEMENT,
TD_TEST_SCORE.COMP_SC_PLACEMENT,
TD_TEST_SCORE.FL_ARABIC,
TD_TEST_SCORE.FL_FRENCH,
TD_TEST_SCORE.FL_GERMAN,
TD_TEST_SCORE.FL_SPANISH,
TD_TEST_SCORE.EDU_PROGRAM_ADMIT,
TD_TEST_SCORE.PLMTH_IND,
TD_TEST_SCORE.PLWRT_IND,
TD_TEST_SCORE.ITEG_GRAMMAR,
TD_TEST_SCORE.ITEL_LISTEN,
TD_TEST_SCORE.ITEP_OVERALL,
TD_TEST_SCORE.ITER_READ,
TD_TEST_SCORE.ITES_SPEAK,
TD_TEST_SCORE.ITEW_WRITE

from AIMSMGR.TD_STUDENT_DATA
left join TD_TEST_SCORE 
  on TD_TEST_SCORE.pidm = td_student_data.sd_pidm
  and TD_TEST_SCORE.TD_TERM_CODE = td_student_data.SD_TERM_CODE
inner join td_demographic
  on td_demographic.dm_pidm = td_student_data.sd_pidm
  and td_demographic.td_term_code = td_student_data.sd_term_code
where 
REGISTERED_IND = 'Y'
--and sd_term_code = '201610'
)
;