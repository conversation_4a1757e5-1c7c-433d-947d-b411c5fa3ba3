create or replace view IA_COHORT_FA as

/*******************************************************************************
Cohort table count 13688 students as of Fall 15
*******************************************************************************/
SELECT IA_COHORT_POP_UNDUP.co_pidm,
  IA_COHORT_POP_UNDUP.co_term_code_key,
  NVL(
  (SELECT 'Y'
  FROM dual
  WHERE EXISTS
    (SELECT IA_FA_FUND_BASE.FA_PIDM
    FROM IA_FA_FUND_BASE
    WHERE IA_FA_FUND_BASE.FA_PIDM     = IA_COHORT_POP_UNDUP.co_pidm
    AND IA_FA_FUND_BASE.FA_TERM_CODE  = IA_COHORT_POP_UNDUP.CO_TERM_CODE_KEY
    AND IA_FA_FUND_BASE.FA_FUND_CODE IN ('2CRITD','2MMTG','2OCHG','2RARB','2UG','2UG2','2UGGRD')
    AND IA_FA_FUND_BASE.FA_PAID_AMT   >0
    AND FA_PAID_AMT                  IS NOT NULL
    )
  ), 'N') ins_grant_ind
FROM IA_COHORT_POP_UNDUP
ORDER BY 1,2;