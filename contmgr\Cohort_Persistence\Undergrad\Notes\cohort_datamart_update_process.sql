/*******************************************************************************
Cohort Data Mart Update Process
Author: Dan Getty
Date: 10-08-20
updated 06-07-23
********************************************************************************
-----------Update Cohort Population Table in CONTMGR SCHEMA--------------------
*******************************************************************************/
--Drop or Trunk Backup
--DROP TABLE CO_POP_UNDUP_TBL_BAC;
TRUNCATE TABLE CO_POP_UNDUP_TBL_BAC;

--Create or Insert into Backup
--CREATE TABLE CO_POP_UNDUP_TBL_BAC AS
INSERT INTO CO_POP_UNDUP_TBL_BAC
SELECT * FROM CO_POP_UNDUP_TBL;

--Insert rows for new Students in Cohort 
--DROP TABLE CO_POP_UNDUP_TBL;
--TRUNCATE TABLE CO_POP_UNDUP_TBL;
--CREATE TABLE CO_POP_UNDUP_TBL AS
  INSERT INTO co_pop_undup_tbl
    SELECT
      *
    FROM
      co_pop_undup
    WHERE
      co_term_code_key = (
        SELECT
          current_term
        FROM
          um_current_term
      )
commit:

--Count rows to varify backup 
SELECT COUNT (*) FROM CO_POP_UNDUP;
SELECT COUNT (*) FROM CO_POP_UNDUP_TBL; --29956
SELECT COUNT (*) FROM CO_POP_UNDUP_TBL_BAC;

********************************************************************************
----------Update GR Cohort Population Table in CONTMGR SCHEMA-------------------
*******************************************************************************/
--Drop or Trunk Backup
--DROP TABLE CO_GR_POP_TBL_BAC;
TRUNCATE TABLE CO_GR_POP_TBL_BAC;

--Create or Insert into Backup
--CREATE TABLE CO_GR_POP_TBL_BAC AS
INSERT INTO CO_GR_POP_TBL_BAC
SELECT * FROM CO_GR_POP_TBL;

--Insert rows for new Students in Cohort 
--DROP TABLE CO_GR_POP_TBL;
TRUNCATE TABLE CO_GR_POP_TBL;
--CREATE TABLE CO_GR_POP_TBL AS
INSERT INTO CO_GR_POP_TBL 
SELECT * FROM CO_GR_POP
--WHERE CO_TERM_CODE_KEY = (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
commit:

--Count rows to varify backup 
SELECT
  COUNT(*)
FROM
  co_gr_pop;

SELECT
  COUNT(*)
FROM
  co_gr_pop_tbl; --8135
SELECT
  COUNT(*)
FROM
  co_gr_pop_tbl_bac;

/*******************************************************************************
--------------Analyze Cohort Population Table in CONTMGR SCHEMA-----------------
*******************************************************************************/
--Identify student records removed from cohorts (misscoded transfer and FTIAC) 
SELECT
  *
FROM
  co_pop_removed;

--Count of misscoded transfer and FTIAC by term
SELECT
  co_term_code_key,
  COUNT(*)
FROM
  co_pop_removed
GROUP BY
  co_term_code_key
ORDER BY
  1;

--Identify students removed from cohorts 
SELECT
  sd.sd_term_code,
  sd.ia_student_type_code,
  sd.full_part_time_ind_umf,
  COUNT(*)
FROM
  td_student_data sd
WHERE
    sd.registered_ind = 'Y'
  AND sd.sd_term_code = (
      SELECT
        current_term
      FROM
        um_current_term
    )
      AND sd.ia_student_type_code IN ( 'F', 'T' )
          AND NOT EXISTS (
    SELECT
      co.co_pidm
    FROM
      cohort_pop_undup co
    WHERE
        sd.sd_pidm = co.co_pidm
      AND co.co_term_code_key = (
        SELECT
          current_term
        FROM
          um_current_term
      )
  )
GROUP BY
  sd.sd_term_code,
  sd.ia_student_type_code,
  sd.full_part_time_ind_umf;

--Count rows to varify backup 
WITH dp1 AS (
  SELECT
    *
  FROM
    co_pop_undup_tbl
  MINUS
  SELECT
    *
  FROM
    co_pop_undup_tbl_bac
)
SELECT
  COUNT(*) incomming_added
FROM
  dp1;

--Current term FTIAC and Transfers 
WITH dp1 AS (
  SELECT
    sd.sd_pidm,
    sd.sd_term_code,
    sd.primary_level_code,
    sd.ia_student_type_code
  FROM
    td_student_data sd
  WHERE
      sd.registered_ind = 'Y'
    AND sd.sd_term_code = (
        SELECT
          current_term
        FROM
          um_current_term
      )
        AND sd.ia_student_type_code IN ( 'F', 'T' )
)
SELECT
  COUNT(*) total_incoming
FROM
  dp1;

--Count rows by season
WITH dp1 AS (
  SELECT
    co.*,
    substr(co.co_term_code_key, 5, 6) season
  FROM
    co_pop_undup_tbl co
)
SELECT
  season,
  COUNT(*)
FROM
  dp1
GROUP BY
  season;

--Count rows by student
SELECT
  co_pidm,
  COUNT(*) row_count
FROM
  co_pop_undup_tbl
GROUP BY
  co_pidm
ORDER BY
  2 DESC;

/*******************************************************************************
------------Analyze GR Cohort Population Table in CONTMGR SCHEMA----------------
*******************************************************************************/
--Count of students in populaton 8135
  SELECT
    count (*)
  FROM
    CO_GR_POP_tbl;

--Count of students in populaton 8135
  SELECT
    count (*)
  FROM
    CO_GR_TEN_YR_NSC_TBL;
    
    

--Count rows to varify backup 
WITH dp1 AS (
  SELECT
    *
  FROM
    CO_GR_POP_tbl
  MINUS
  SELECT
    *
  FROM
    CO_GR_POP_tbl_bac
)
SELECT
  COUNT(*) incomming_added
FROM
  dp1;

--Current term New Grads 
WITH dp1 AS (
  SELECT
    sd.sd_pidm,
    sd.sd_term_code,
    sd.primary_level_code,
    sd.ia_student_type_code
  FROM
    td_student_data sd
  WHERE
      sd.registered_ind = 'Y'
    AND sd.sd_term_code = (
        SELECT
          current_term
        FROM
          um_current_term
      )
        AND sd.ia_student_type_code IN ( 'N' )
)
SELECT
  COUNT(*) total_incoming
FROM
  dp1;

/*
20	2214
30	507
10	5190
40	224
*/

--Count rows by season from pop table this will have summer starts
WITH dp1 AS (
  SELECT
    co.*,
    substr(co.co_gr_term_code_key, 5, 6) season
  FROM
    CO_GR_POP_tbl co
)
SELECT
  season,
  COUNT(*)
FROM
  dp1
GROUP BY
  season;
  
  --Count rows by season from ten year table summer starts will now be part of fall
WITH dp1 AS (
  SELECT
    co.*,
    substr(co.co_gr_term_code_key, 5, 6) season
  FROM
    co_gr_ten_yr_nsc_tbl co
)
SELECT
  season,
  COUNT(*)
FROM
  dp1
GROUP BY
  season;

--Count rows by student
SELECT
  co_gr_pidm,
  COUNT(*) row_count
FROM
  CO_GR_POP_tbl
GROUP BY
  co_gr_pidm
ORDER BY
  2 DESC;

/*******************************************************************************
-------Process for SE Datamart Clearinghouse Detail File integration-----------

*******************************************************************************
Update NSC Raw Data Table from detail return file to identify students who
registered and are in the Cohort Datamart but were lost in the future.
This is done in the NSCMGR SHEMA

Export and upload a tab delimeted text file with no header from query below
to the NSCH FTP site https://ftps.nslc.org/
use the file naming structure 002327_unregistered_se_<mmddyy>.txt where 
*******************************************************************************/
SELECT
  *
FROM
  nsc_student_selection;
/*******************************************************************************
Download detail report return file from NSCH
002327st_xxxxxx_DETLRPT_SE_xxxxxxxxxxxxx_002327_unregistered_se_mmddyy.csv
make backup of NSC_RAW (ie. NSC_RAW_202230) appending the current term code
*******************************************************************************/
CREATE TABLE nsc_raw_20xxxx
  AS
    SELECT
      *
    FROM
      nsc_raw;

TRUNCATE TABLE nsc_raw;
/*******************************************************************************
Import the detail report file from NSC into the NSC_RAW table remember
to import with UTF-8 Encoding, as a comma dilimeted file, un-check header and 
skip rows 1.
********************************************************************************
Build the NSC_META table using the follwing PL/SQL command
*******************************************************************************/
BEGIN
  p_update_nsc;
END;

/*******************************************************************************
Update NSC Student Data Table in CONTMGR SCHEMA
*******************************************************************************/
--DROP TABLE CO_NSC_STUDENT_DATA_TBL_BAC;
TRUNCATE TABLE co_nsc_student_data_tbl_bac;
--CREATE TABLE CO_NSC_STUDENT_DATA_TBL_BAC AS
INSERT INTO co_nsc_student_data_tbl_bac
  SELECT
    *
  FROM
    co_nsc_student_data_tbl;

COMMIT;

SELECT
  COUNT(*)
FROM
  co_nsc_student_data_tbl;

SELECT
  COUNT(*)
FROM
  co_nsc_student_data_tbl_bac;

TRUNCATE TABLE co_nsc_student_data_tbl;
--run CO_NSC_STUDENT_DATA_TBL.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\contmgr\Cohort_Persistence\Undergrad

/*******************************************************************************
Update NSC Degrees Table in CONTMGR SCHEMA
*******************************************************************************/
--DROP TABLE CO_NSC_DEGREE_TBL_BAC;
TRUNCATE TABLE co_nsc_degree_tbl_bac;
--CREATE TABLE CO_NSC_DEGREE_TBL_BAC AS
INSERT INTO co_nsc_degree_tbl_bac
  SELECT
    *
  FROM
    co_nsc_degree_tbl;

COMMIT;

SELECT
  COUNT(*)
FROM
  co_nsc_degree_tbl;

SELECT
  COUNT(*)
FROM
  co_nsc_degree_tbl_bac;

TRUNCATE TABLE co_nsc_degree_tbl;
--run CO_NSC_DEGREE_TBL.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\contmgr\Cohort_Persistence\Undergrad

/*******************************************************************************
Update Ten year registered or graduated table.  This is a process using three 
queries one for each of the semesters Fall, Spring, and Winter.  These queries 
are quite extensive and take quite a while to run.
*******************************************************************************/
TRUNCATE TABLE co_ten_yr_nsc_tbl_bac;
--CREATE TABLE CO_TEN_YR_NSC_TBL_BAC AS
INSERT INTO co_ten_yr_nsc_tbl_bac
  SELECT
    *
  FROM
    co_ten_yr_nsc_tbl;

COMMIT;

SELECT
  COUNT(*)
FROM
  co_ten_yr_nsc_tbl;

SELECT
  COUNT(*)
FROM
  co_ten_yr_nsc_tbl_bac;

TRUNCATE TABLE co_ten_yr_nsc_tbl;

WITH dp1 AS (
  SELECT
    co.*,
    substr(co.co_term_code_key, 5, 6) season
  FROM
    co_ten_yr_nsc_tbl co
)
SELECT
  season,
  COUNT(*)
FROM
  dp1
GROUP BY
  season;

--SELECT COUNT (*) from CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%10';
--DELETE FROM CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%10';
--run FALL_CO_TEN_YR_NSC_TBL.sql 

--SELECT COUNT (*) from CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%20';
--DELETE FROM CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%20';
--run WIN_CO_TEN_YR_NSC_TBL.sql 

--SELECT COUNT (*) from CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%30';
--DELETE FROM CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%30';
--run SPR_CO_TEN_YR_NSC_TBL.sql 

--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update Graduate Ten year registered or graduated table.  This is a process using 
three queries one for each of the semesters Fall, Spring, and Winter.  These 
queries are quite extensive and take quite a while to run.
*******************************************************************************/
TRUNCATE TABLE co_gr_ten_yr_nsc_tbl_bac;
--CREATE TABLE CO_TEN_YR_NSC_TBL_BAC AS
INSERT INTO co_gr_ten_yr_nsc_tbl_bac
  SELECT
    *
  FROM
    co_gr_ten_yr_nsc_tbl;

COMMIT;

SELECT
  COUNT(*)
FROM
  co_gr_ten_yr_nsc_tbl;

SELECT
  COUNT(*)
FROM
  co_gr_ten_yr_nsc_tbl_bac;

TRUNCATE TABLE co_gr_ten_yr_nsc_tbl;

WITH dp1 AS (
  SELECT
    co.*,
    substr(co.co_gr_term_code_key, 5, 6) season
  FROM
    co_gr_ten_yr_nsc_tbl co
)
SELECT
  season,
  COUNT(*)
FROM
  dp1
GROUP BY
  season;

--SELECT COUNT (*) from CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%10';
--DELETE FROM CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%10';
--run FALL_CO_TEN_YR_NSC_TBL.sql 

--SELECT COUNT (*) from CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%20';
--DELETE FROM CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%20';
--run WIN_CO_TEN_YR_NSC_TBL.sql 

--SELECT COUNT (*) from CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%30';
--DELETE FROM CO_TEN_YR_NSC_TBL WHERE CO_TERM_CODE_KEY LIKE '%30';
--run SPR_CO_TEN_YR_NSC_TBL.sql 

--G:\Shared drives\UMF-IA\DAN\SQL\contmgr\Cohort_Persistence\Graduate

/*******************************************************************************
Update cohort persistence table in CONTMGR SCHEMA
*******************************************************************************/

TRUNCATE TABLE co_persist_nsc_tbl_bac;
--CREATE TABLE CO_PERSIST_NSC_TBL_BAC AS
INSERT INTO co_persist_nsc_tbl_bac
  SELECT
    *
  FROM
    co_persist_nsc_tbl;

COMMIT;

SELECT
  COUNT(*)
FROM
  co_persist_nsc_tbl;

SELECT
  COUNT(*)
FROM
  co_persist_nsc_tbl_bac;

TRUNCATE TABLE co_persist_nsc_tbl;
--run CO_PERSIST_NSC_TBL.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update Most Recent Table in CONTMGR SCHEMA
This provides filter data for analysis of persistence data.
*******************************************************************************/

TRUNCATE TABLE co_most_recent_tbl_bac;
--CREATE TABLE CO_MOST_RECENT_TBL_BAC AS
INSERT INTO co_most_recent_tbl_bac
  SELECT
    *
  FROM
    co_most_recent_tbl;

COMMIT;

SELECT
  COUNT(*)
FROM
  co_most_recent_tbl;

SELECT
  COUNT(*)
FROM
  co_most_recent_tbl_bac;

TRUNCATE TABLE co_most_recent_tbl;
--run CO_MOST_RECENT_TBL.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad

/*******************************************************************************
Update Cohort Financial Aid Table in CONTMGR SCHEMA
This provides filter data for analysis of persistence data.
*******************************************************************************/

TRUNCATE TABLE co_fa_tbl_bac;
--CREATE TABLE CO_FA_TBL_BAC AS
INSERT INTO co_fa_tbl_bac
  SELECT
    *
  FROM
    co_fa_tbl;

COMMIT;

TRUNCATE TABLE co_fa_tbl;
--CREATE TABLE CO_FA_TBL_BAC AS
INSERT INTO co_fa_tbl
  SELECT
    *
  FROM
    co_fa_tbl_bac;

SELECT
  COUNT(*)
FROM
  co_fa_tbl;

SELECT
  COUNT(*)
FROM
  co_fa_tbl_bac;
--run CO_FA_TBL.sql located in 
--G:\Shared drives\UMF-IA\DAN\SQL\iamgr\Tables_and_Views\Cohort\Undergrad
