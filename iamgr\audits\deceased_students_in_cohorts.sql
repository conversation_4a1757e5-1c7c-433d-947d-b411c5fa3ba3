select 
IA_COHORT_POP_UNDUP_TBL.CO_PIDM,
IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY,
popcel.deceased_date,
IA_COHORT_POP_UNDUP_TBL.CO_FULL_PART_IND_UMF,
IA_COHORT_POP_UNDUP_TBL.CO_IA_STUDENT_TYPE_CODE,
IA_COHORT_POP_UNDUP_TBL.CO_GENDER,
IA_COHORT_PERSIST_TBL_NSC.SCND_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.THRD_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.FRTH_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.FFTH_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.SXTH_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.SVNTH_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.EIGTH_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.NINTH_FALL_PERSIST,
IA_COHORT_PERSIST_TBL_NSC.TENTH_FALL_PERSIST
from IA_COHORT_POP_UNDUP_TBL
inner join 
(select DM_PIDM, deceased_date
from um_demographic 
where deceased_ind = 'Y')popcel on 
popcel.dm_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
inner join
IA_COHORT_PERSIST_TBL_NSC on IA_COHORT_PERSIST_TBL_NSC.co_pidm = IA_COHORT_POP_UNDUP_TBL.CO_PIDM
order by CO_TERM_CODE_KEY
;

