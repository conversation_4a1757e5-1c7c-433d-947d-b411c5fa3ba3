select
spriden.spriden_id as "SchoolAssignedID",

case
  when (current_sgbstdn_join.sgbstdn_levl_code = 'GR' or
        current_sgbstdn_join.sgbstdn_levl_code = 'PT') then 
    case
      when current_sgbstdn_join.sgbstdn_degc_code_1 = '000000' then 'GraduateNonDegree'
      when current_sgbstdn_join.sgbstdn_degc_code_1 like 'D%' then 'Doctoral'
      else 'Masters'
    end
  else
    case
    when (current_sgbstdn_join.sgbstdn_styp_code = 'S' or
          current_sgbstdn_join.sgbstdn_styp_code = 'X') then 'NonDegree'
    else
      case 
      when (current_sgbstdn_join.class_standing = 'FR' and
            current_sgbstdn_join.sgbstdn_styp_code = 'F') then 'CollegeFirstYear'
      when current_sgbstdn_join.class_standing = 'FR' then 'CollegeFirstYearAttendedBefore'
      when current_sgbstdn_join.class_standing = 'SO' then 'CollegeSophomore'
      when current_sgbstdn_join.class_standing = 'JR' then 'CollegeJunior'
      when current_sgbstdn_join.class_standing = 'SR' then 'CollegeSenior'
      end
    end
end as "StudentLevel",

(select to_char(term2.stvterm_start_date, 'YYYY-MM') 
 from stvterm term2
 where term2.stvterm_code = (select min(shrttrm.shrttrm_term_code)
                             from shrttrm
                             where shrttrm.shrttrm_pidm = spriden.spriden_pidm)
) as "EntryDate",

case
when substr(shrtgpa_join.shrtgpa_term_code, 5, 2) = '10' then substr(to_char(to_number(substr(shrtgpa_join.shrtgpa_term_code, 1, 4)) - 1), 1, 4)
else substr(shrtgpa_join.shrtgpa_term_code, 1, 4) 
end as "SessionDesignator",

case substr(shrtgpa_join.shrtgpa_term_code, 5, 2)
  when '10' then 'Fall'
  when '20' then 'Winter'
  when '30' then 'Spring'
  when '40' then 'Summer'
  else 'Other'
end as "SessionName",

'Semester' as "SessionType",

trunc(shrtgpa_join.gradepointaverage, 2) as "GradePointAverage",

shrtgpa_join.programcipcode as "ProgramCIPCode",

shrtgpa_join.academicprogramtype as "AcademicProgamType",

shrtgpa_join.academicprogramname as "AcademicProgramName"

from spriden
-- start term of our student popsel
inner join(
  select 
  max(stvterm.STVTERM_CODE) as start_term_code
  from stvterm
  where stvterm.STVTERM_CODE = '201130'
) start_term on start_term.start_term_code <> '999999'
-- end term of our student pop sel
inner join(
  select max(stvterm.STVTERM_CODE) as end_term_code
  from stvterm
  where stvterm.STVTERM_CODE = '201220'
) end_term on end_term.end_term_code <> '999999'
-- our begining of time (first banner term)
inner join(
  select 
  stvterm.stvterm_code as bobt_term_code
  from stvterm
  where stvterm.stvterm_code = '199910'
) begining_of_banner_term on begining_of_banner_term.bobt_term_code <> '999999'
-- their current term sgbstdn
inner join(
  select
  sgbstdn.sgbstdn_pidm,
  sgbstdn.sgbstdn_term_code_eff,
  sgbstdn.sgbstdn_levl_code,
  sgbstdn.sgbstdn_styp_code,
  sgbstdn.sgbstdn_degc_code_1,
  f_class_calc_fnc(sgbstdn.sgbstdn_pidm, sgbstdn.sgbstdn_levl_code, sgbstdn.sgbstdn_term_code_eff) as class_standing
  from 
  sgbstdn
) current_sgbstdn_join on current_sgbstdn_join.sgbstdn_pidm = spriden.spriden_pidm
                       and current_sgbstdn_join.sgbstdn_term_code_eff = (select max(s2.sgbstdn_term_code_eff)
                                                                         from sgbstdn s2
                                                                         where s2.sgbstdn_pidm = current_sgbstdn_join.sgbstdn_pidm
                                                                         and s2.sgbstdn_term_code_eff <= end_term.end_term_code)
inner join(
  select
  distinct inst_trans_join.term_code shrtgpa_term_code,
  inst_trans_join.pidm shrtgpa_pidm,
  inst_trans_join.academicprogramtype,
  inst_trans_join.academicprogramname,
  inst_trans_join.programcipcode,
  sum(inst_trans_join.gradepointaverage) gradepointaverage
  from (
    select
    shrtgpa.shrtgpa_pidm pidm,
    shrtgpa.shrtgpa_term_code term_code,
    shrtgpa.shrtgpa_gpa gradepointaverage,
  --  sgbstdn_join.sgbstdn_levl_code,
  --  sgbstdn_join.sgbstdn_degc_code_1,
  --  sgbstdn_join.sgbstdn_styp_code,
    sgbstdn_join.stvmajr_cipc_code programcipcode,
  --  sgbstdn_join.stvmajr_desc,
    sgbstdn_join.academicprogramtype academicprogramtype,
    sgbstdn_join.stvmajr_desc academicprogramname
    from shrtgpa
    inner join(
      select
      sgbstdn.sgbstdn_pidm,
      sgbstdn.sgbstdn_term_code_eff,
      sgbstdn.sgbstdn_levl_code,
      sgbstdn.sgbstdn_degc_code_1,
      sgbstdn.sgbstdn_styp_code,
      stvmajr_join.stvmajr_cipc_code,
      stvmajr_join.stvmajr_desc,
      'Major' as academicprogramtype
      from sgbstdn
      left outer join(
        select 
        stvmajr.stvmajr_code,
        stvmajr.stvmajr_cipc_code,
        stvmajr.stvmajr_desc
        from stvmajr
      ) stvmajr_join on stvmajr_join.stvmajr_code = sgbstdn.sgbstdn_majr_code_1
    ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                   and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                             from sgbstdn sgbstdn_1
                                                             where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                             and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                   and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
    where shrtgpa.shrtgpa_gpa_type_ind = 'I'
    union all
    select
    shrtgpa.shrtgpa_pidm pidm,
    shrtgpa.shrtgpa_term_code,
    0, --shrtgpa.shrtgpa_gpa gradepointaverage,
  --  sgbstdn_join.sgbstdn_levl_code,
  --  sgbstdn_join.sgbstdn_degc_code_1,
  --  sgbstdn_join.sgbstdn_styp_code,
    sgbstdn_join.stvmajr_cipc_code programcipcode,
  --  sgbstdn_join.stvmajr_desc,
    sgbstdn_join.academicprogramtype academicprogramtype,
    sgbstdn_join.stvmajr_desc academicprogramname
    from shrtgpa
    inner join(
      select
      sgbstdn.sgbstdn_pidm,
      sgbstdn.sgbstdn_term_code_eff,
      sgbstdn.sgbstdn_levl_code,
      sgbstdn.sgbstdn_degc_code_1,
      sgbstdn.sgbstdn_styp_code,
      stvmajr_join.stvmajr_cipc_code,
      stvmajr_join.stvmajr_desc,
      'Major' as academicprogramtype
      from sgbstdn
      left outer join(
        select 
        stvmajr.stvmajr_code,
        stvmajr.stvmajr_cipc_code,
        stvmajr.stvmajr_desc
        from stvmajr
      ) stvmajr_join on stvmajr_join.stvmajr_code = sgbstdn.sgbstdn_majr_code_1
    ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                   and sgbstdn_join.sgbstdn_term_code_eff = (select min(sgbstdn_1.sgbstdn_term_code_eff)
                                                             from sgbstdn sgbstdn_1
                                                             where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                             and sgbstdn_1.sgbstdn_term_code_eff >= shrtgpa.shrtgpa_term_code)
                   and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
    where shrtgpa.shrtgpa_gpa_type_ind = 'T'
    and exists (select
                'has a shrtrce rec'
                from shrtrce
                where shrtrce.shrtrce_pidm = shrtgpa.shrtgpa_pidm
                and shrtrce.shrtrce_term_code_eff = shrtgpa.shrtgpa_term_code
                and shrtrce.shrtrce_levl_code = sgbstdn_join.sgbstdn_levl_code
                and shrtrce.shrtrce_trit_seq_no = shrtgpa.shrtgpa_trit_seq_no
                and shrtrce.shrtrce_tram_seq_no = shrtgpa.shrtgpa_tram_seq_no)
    ) inst_trans_join
    group by inst_trans_join.term_code, inst_trans_join.pidm, inst_trans_join.academicprogramtype, inst_trans_join.academicprogramname, inst_trans_join.programcipcode
)shrtgpa_join on shrtgpa_join.shrtgpa_pidm = spriden.spriden_pidm
              and shrtgpa_join.shrtgpa_term_code >= begining_of_banner_term.bobt_term_code
              and shrtgpa_join.shrtgpa_term_code <= end_term.end_term_code
-- has to have a UID
inner join(
  select 
  goradid.GORADID_PIDM,
  goradid.GORADID_ADDITIONAL_ID
  from goradid
  where goradid.GORADID_ADID_CODE = 'UIC'
  and goradid.GORADID_ADDITIONAL_ID is not null
) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
-- spbpers on not dead
inner join(
  select
  spbpers.spbpers_pidm
  from spbpers
  where spbpers.SPBPERS_DEAD_IND is null
)spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
where spriden.spriden_change_ind is null
and exists (select
            'has course work in their primary level in these terms'
            from shrtgpa
            inner join(
              select
              sgbstdn.sgbstdn_pidm,
              sgbstdn.sgbstdn_term_code_eff,
              sgbstdn.sgbstdn_levl_code
              from sgbstdn
            ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                           and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                     from sgbstdn sgbstdn_1
                                                                     where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                     and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                           and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
            where shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
            and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
            and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
--and spriden.SPRIDEN_PIDM in (102494)


