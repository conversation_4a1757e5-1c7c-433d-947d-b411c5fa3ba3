desc um_demographic;
desc um_registration_detail;
desc um_student_transcript;
desc td_student_data;

--create or replace view Grade_Report as
with dp1 as (
select 
sd.sd_pidm,
sd.sd_term_code,
sd.sd_term_desc,
sd.primary_admit_term,
dm.umid,
dm.first_name,
dm.last_name,
sd.primary_major_1,
sd.PRIMARY_MAJOR_1_DESC
from td_student_data sd
inner join td_demographic dm on sd.sd_pidm = dm.dm_pidm
and sd.sd_term_code = dm.td_term_code
where sd.registered_ind = 'Y'
--and sd.sd_term_code = '202220'
--and sd.primary_major_1 = 'PTP'
), dp2 as (
select 
dp1.*,
second_major_1,
primary_program,
um.term_gpa,
um.overall_gpa,
um.term_hours_earned,
um.overall_hours_earned
from dp1
left join um_student_data um on dp1.sd_pidm = um.sd_pidm
 and dp1.sd_term_code = um.sd_term_code
 )
 select 
 dp2.*,
 tr.subject,
 tr.course,
 tr.title,
 tr.hours,
 tr.grade
 from dp2
 left join um_student_transcript tr on dp2.sd_pidm = tr.pidm
 and dp2.sd_term_code = tr.term 
 