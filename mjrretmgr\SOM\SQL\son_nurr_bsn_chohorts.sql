    DROP TABLE SOM_BBA_COHORTS;
    
    TRUNCATE TABLE SOM_BBA_COHORTS;
    
    insert into SOM_BBA_COHORTS
    select
    sd.sd_pidm,
    dm.umid umid,
    dm.FIRST_NAME,
    dm.MIDDLE_INITIAL,
    dm.LAST_NAME,
    dm.name_suffix name_suffix,
    dm.birthdate birthdate,
    MIN(sd.sd_term_code) SD_TERM_CODE
    FROM TD_STUDENT_DATA SD
    inner join td_demographic dm on dm.dm_pidm = sd.sd_pidm
                               and dm.td_term_code = sd.sd_term_code
                           and dm.last_name not in ('.', ',')
                           and upper(dm.last_name) != 'NFN'
                           and upper(dm.first_name) != 'NFN'
                           and dm.first_name not in ('.', ',')
--    INNER JOIN contmgr.CO_POP_UNDUP_TBL CO ON SD.SD_PIDM = CO.CO_PIDM
--                                    AND SD.SD_TERM_CODE = CO.CO_TERM_CODE_KEY
    WHERE SD.SD_TERM_CODE >= 200010 --'" +  ((String)globalMap.get("row1.TERM")) + "'
    AND SD.SD_TERM_CODE < (SELECT CURRENT_TERM FROM UM_CURRENT_TERM)
--    AND sd.primary_major_1 = 'NURR'
    AND sd.primary_degree_code = 'BBA'
    AND sd.registered_ind = 'Y'
    GROUP BY
    sd.sd_pidm,
    dm.umid,
    dm.FIRST_NAME,
    dm.MIDDLE_INITIAL,
    dm.LAST_NAME,
    dm.name_suffix,
    dm.birthdate 
    ;
    
    
    select * from SOM_BBA_COHORTS;