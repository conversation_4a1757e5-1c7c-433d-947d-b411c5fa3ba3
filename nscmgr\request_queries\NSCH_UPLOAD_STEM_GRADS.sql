/*******************************************************************************
-- Author:  <PERSON>, University of Michigan-Flint
-- Create date: 3/23/17
-- Description: Querie to gather Banner data for upload to National Student
Clearinhouse to identify students in that graduated with STEM degrees.
-- Notes:       EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
-- Known issue(s):
*******************************************************************************/
--set basic selection criteria here
WITH Data_pull AS
  (SELECT distinct pidm sd_pidm,
    MAX(grad_term_code)last_term
  FROM ia_um_degree
  WHERE GRAD_TERM_CODE >= 201540 -- FY Start term
  AND GRAD_TERM_CODE   <= 201630 -- FY End term
  AND DEGREE_STATUS     = 'AW'
  AND STEM_MJR_IND_1    = 'Y'
  AND REPORT_LEVEL_CODE = 'UG'
  GROUP BY pidm
  ),
  base_sel AS
  ( SELECT DISTINCT IA_UM_DEGREE.first_name,
    IA_UM_DEGREE.MIDDLE_INITIAL AS middle_name,
    IA_UM_DEGREE.last_name,
    IA_UM_DEGREE.name_suffix,
    IA_UM_DEGREE.birthdate,
    IA_UM_DEGREE.GRAD_term_code AS term_code_entry,
    IA_UM_DEGREE.pidm           AS dm_pidm
  FROM data_pull
  INNER JOIN IA_UM_DEGREE
  ON IA_UM_DEGREE.pidm            = data_pull.sd_pidm
  AND IA_UM_DEGREE.GRAD_term_code = data_pull.last_term
  ),
  --End basic selection criteria here
  pop_sel AS
  (
  --This section creates a header file.
  SELECT 1 order_num,
    'H1' "A",
    '002327' "B",
    '00' "C",
    'UNIVERSITY OF MICHIGAN FLINT' "D",
    TO_CHAR(sysdate, 'YYYYMMDD') "E",
    'SE' "F",
    'I' "G",
    NULL "H",
    NULL "I",
    NULL "J",
    NULL "K",
    NULL "L"
  FROM dual
  UNION
  --This section pulls the student records for the payload.
  SELECT 2 order_num,
    'D1' "A",
    NULL "B",
    SUBSTR(first_name,1,20) "C",
    SUBSTR(middle_name, 1 ,1) "D",
    SUBSTR(last_name,1,20) "E",
    SUBSTR(name_suffix,1,5) "F",
    TO_CHAR(birthdate, 'YYYYMMDD') "G",
    TO_CHAR(to_number(SUBSTR(term_code_entry,1,4))-1
    ||'0901') "H",
    NULL "I",
    '002327' "J",
    '00' "K",
    TO_CHAR(dm_pidm) "L"
  FROM base_sel
  UNION
  --This is to count the number of records and append a trailer record
  SELECT 3 order_num,
    'T1' "A",
    TO_CHAR(COUNT(base_sel.dm_pidm)+2) "B",
    NULL "C",
    NULL "D",
    NULL "E",
    NULL "F",
    NULL "G",
    NULL "H",
    NULL "I",
    NULL "J",
    NULL "K",
    NULL "L"
  FROM base_sel
  ORDER BY order_num
  )
SELECT pop_sel.A,
  pop_sel.B,
  pop_sel.C,
  pop_sel.D,
  pop_sel.E,
  pop_sel.F,
  pop_sel.G,
  pop_sel.H,
  pop_sel.I,
  pop_sel.J,
  pop_sel.K,
  pop_sel.L
FROM pop_sel ;
