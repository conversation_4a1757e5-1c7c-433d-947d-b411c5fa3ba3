--------------------------------------------------------
--  DDL for View anticipated_grad_honors_report
--------------------------------------------------------
--set define off;
  CREATE OR REPLACE FORCE EDITIONABLE VIEW anticipated_grad_honors_report AS 
  WITH dp1 AS (
  SELECT
    dg.pidm,
    dg.grad_term_desc,
    case
    when dg.college_code in ('AS','RK') then 'AS'
    else dg.college_code
    end college_code,
    case
    when dg.college_code in ('AS','RK') then 'College of Arts & Sciences'
    else dg.college_desc
    end college_desc,
    dm.last_name,
    dm.first_name,
    dm.middle_initial,
    dm.name_suffix,
    dg.umid,
    dm.A1_STREET_LINE1,
    dm.A1_STREET_LINE2,
    dm.a1_city,
    dm.a1_state_code,
    dm.a1_zip,
    dm.ca_email,
    sd.primary_level_code,
    dg.degree_code,
    sd.primary_major_1,
    sd.primary_major_1_desc,
    sd.second_major_1,
    sd.second_major_1_desc,
    sd.primary_major_2,
    sd.primary_major_2_desc,
    sd.second_major_2,
    sd.second_major_2_desc,
    sd.overall_gpa,
    -- honor calculated field
    -- honors_ind calculated field
    dg.grad_term_code,
    dm.CONFIDENTIALITY_IND,
    

    --Calculated
        ROW_NUMBER()
    OVER(PARTITION BY dg.pidm, dg.grad_term_code
         ORDER BY
           dg.pidm DESC
    )                               deg_count,
    substr(dg.grad_term_code, 5, 6) season_code,
    CASE
      WHEN substr(dg.grad_term_code, 5, 6) = 20 THEN
        to_number(dg.grad_term_code) + 20
      WHEN substr(dg.grad_term_code, 5, 6) = 40 THEN
        to_number(dg.grad_term_code)
      WHEN substr(dg.grad_term_code, 5, 6) = 10 THEN
        to_number(dg.grad_term_code) - 70
    END                             sum_term_join,
    CASE
      WHEN sd.report_level_code = 'UG'
           AND sd.primary_college_code IN ( 'HP', 'HS' )
           AND sd.overall_gpa >= 3.50
           AND ( sd.overall_hours_attempted + sd.term_registered_hours ) < 45 THEN
        '***'
      WHEN sd.report_level_code = 'UG'
           AND sd.overall_gpa >= 3.75 THEN
        '**'
      WHEN sd.report_level_code = 'UG'
           AND sd.overall_gpa >= 3.50
           AND sd.overall_gpa <= 3.74 THEN
        '*'
      ELSE
        ''
    END                             honor_role,
    CASE
      WHEN sd.report_level_code = 'UG'
           AND sd.primary_college_code IN ( 'HP', 'HS' )
           AND sd.overall_gpa >= 3.50
           AND ( sd.overall_hours_attempted + sd.term_registered_hours ) < 45 THEN
        'AD'
      WHEN sd.report_level_code = 'UG'
           AND sd.overall_gpa >= 3.75 THEN
        'HH'
      WHEN sd.report_level_code = 'UG'
           AND sd.overall_gpa >= 3.50
           AND sd.overall_gpa <= 3.74 THEN
        'HR'
      ELSE
        ''
    END                             honor_role_desc,
    dg.grad_status
  FROM
    um_degree       dg
    LEFT JOIN um_student_data sd ON dg.pidm = sd.sd_pidm
                                    AND dg.grad_term_code = sd.sd_term_code
    INNER JOIN um_demographic  dm ON dg.pidm = dm.dm_pidm
  WHERE 
      dg.grad_term_code >= (
        SELECT
          last_2_term
        FROM
          um_current_term
      )
    AND dg.grad_term_code <= (
      SELECT
        next_4_term
      FROM
        um_current_term
    )
    AND dg.grad_status IN ( 'GR', 'PG' )
), w1 AS (
  SELECT DISTINCT
    'Anticipated Graduation Honors ' || dp1.grad_term_desc com_select,
    dp1.*
  FROM
    dp1
  WHERE
    season_code = '20'
), w2 AS (
  SELECT DISTINCT
    'Anticipated Graduation Honors ' || w1.grad_term_desc com_select,
    dp1.*
  FROM
         dp1
    INNER JOIN w1 ON dp1.grad_term_code = w1.sum_term_join
  WHERE
    dp1.grad_status = 'PG'
), s1 AS (
  SELECT DISTINCT
    'Anticipated Graduation Honors ' || dp1.grad_term_desc com_select,
    dp1.*
  FROM
    dp1
  WHERE
    season_code = '40'
), f1 AS (
  SELECT DISTINCT
    'Anticipated Graduation Honors ' || grad_term_desc com_select,
    dp1.*
  FROM
    dp1
  WHERE
    season_code = '10'
), f2 AS (
  SELECT DISTINCT
    'Anticipated Graduation Honors ' || f1.grad_term_desc com_select,
    dp1.*
  FROM
         dp1
    INNER JOIN f1 ON dp1.grad_term_code = f1.sum_term_join
  WHERE
    dp1.grad_status = 'GR'
), com1 AS (
  SELECT
    *
  FROM
    w1
  UNION ALL
  SELECT
    *
  FROM
    w2
  UNION ALL
  SELECT
    *
  FROM
    s1
  UNION ALL
  SELECT
    *
  FROM
    f1
  UNION ALL
  SELECT
    *
  FROM
    f2
)
SELECT DISTINCT

COM_SELECT, 
--PIDM, 
GRAD_TERM_CODE,
GRAD_TERM_DESC, 
COLLEGE_CODE      "College", 
COLLEGE_DESC "College Desc",
LAST_NAME         "Last Name",
FIRST_NAME        "First Name", 
MIDDLE_INITIAL    "MI", 
NAME_SUFFIX       "Suffix", 
UMID, 
A1_STREET_LINE1   "Street1", 
A1_STREET_LINE2   "Street2", 
A1_CITY           "City", 
A1_STATE_CODE "State", 
A1_ZIP "Zip", 
CA_EMAIL "Campus Email", 
PRIMARY_LEVEL_CODE "Level", 
DEGREE_CODE "Degree", 
PRIMARY_MAJOR_1 "Major1", 
PRIMARY_MAJOR_1_DESC "Major1 Desc", 
SECOND_MAJOR_1 "Major1_2", 
SECOND_MAJOR_1_DESC "Major1_2 Desc", 
PRIMARY_MAJOR_2 "Major2", 
PRIMARY_MAJOR_2_DESC "Major2 Desc", 
SECOND_MAJOR_2 "Major2_2", 
SECOND_MAJOR_2_DESC "Major2_2 Desc", 
OVERALL_GPA "GPA", 
HONOR_ROLE "Honor", 
HONOR_ROLE_DESC "Honor Ind", 
CONFIDENTIALITY_IND "Confidentiality Ind"
--, 
--DEG_COUNT, 
--SEASON_CODE, 
--SUM_TERM_JOIN, 
--GRAD_STATUS
FROM
  com1
;

