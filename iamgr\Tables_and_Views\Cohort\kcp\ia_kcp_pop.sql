create or replace view ia_kcp_pop as (

SELECT 
FIRST_NAME,
LAST_NAME,
BIRTHDATE,

sd_pidm co_pidm,
  sd_term_code co_term_code_key,
  gender co_gender,
  ia_student_type_code co_ia_student_type_code,
  report_ethnicity co_ethnicity,
  PRIMARY_DEGREE_CODE,
  PRIMARY_MAJOR_1_DESC,
  CASE
    WHEN hsch_gpa < 2.7
    THEN 'Yes'
    WHEN hsch_gpa >= 2.7
    THEN 'No'
    WHEN hsch_gpa IS NULL
    THEN 'No HSCH GPA'
  END disadvantaged_ind,
  CASE
    WHEN report_ethnicity = 'Nonresident Alien'
    THEN 'No'
    ELSE 'Yes'
  END citizen_ind,
  --hsch_gpa case for disadavantaged gpa <2.7
  --citizen yes no based on report_ethicity
  RESIDENCY_CODE,
to_date('01-SEP-'||(to_number(SUBSTR(sd_term_code,3,2))-1),'DD-MON-YY') start_date
FROM ia_td_student_data
WHERE registered_ind     = 'Y'
AND sd_term_code         >= 201910
and sd_term_code like '%10'
AND PRIMARY_ADMIT_CODE   = 'CH'
AND IA_STUDENT_TYPE_CODE = 'F' 

union all

select popsel.* from(
WITH dp1 AS
  (SELECT 
  sd.FIRST_NAME,
sd.LAST_NAME,
sd.BIRTHDATE,
  sd.sd_pidm co_pidm,
    sd.sd_term_code co_term_code_key,
    sd.gender co_gender,
    sd.ia_student_type_code co_ia_student_type_code,
    sd.report_ethnicity co_ethnicity,
    sd.PRIMARY_DEGREE_CODE,
    sd.PRIMARY_MAJOR_1_DESC,
    CASE
      WHEN sd.hsch_gpa < 2.7
      THEN 'Yes'
      WHEN sd.hsch_gpa >= 2.7
      THEN 'No'
      WHEN sd.hsch_gpa IS NULL
      THEN 'No HSCH GPA'
    END disadvantaged_ind,
    CASE
      WHEN sd.report_ethnicity = 'Nonresident Alien'
      THEN 'No'
      ELSE 'Yes'
    END citizen_ind,
    sd.RESIDENCY_CODE,
--    ,    (select
--    min (sd2.sd_term_code)
--    from ia_td_student_data sd2
--where sd2.sd_pidm = sd.sd_pidm
--group by sd2.sd_pidm) start_term

sd.primary_admit_term start_term

 
  FROM ia_td_student_data sd
  WHERE sd.registered_ind = 'Y'
  AND sd.sd_term_code    IN ('201710','201810')
  AND sd.sd_pidm         IN ( '9464', '66059', '71437', '74938', '75909', '77251', 
    '77310', '77662', '77777', '81377', '81828', '82068', '85876', '87567', 
    '90037', '90995', '91032', '91286', '91380', '91390', '91481', '91579', 
    '92232', '92534', '92804', '94077', '95318', '96872', '101281', '101356', 
    '101615', '101658', '101738', '102941', '104564', '105029', '106513', 
    '108761', '113032', '115160', '117393', '118491', '118679', '119039', 
    '119410', '119476', '119479', '119509', '120050', '120651', '120680', 
    '121214', '121339', '121428', '121429', '121491', '121648', '121947', 
    '121967', '121972', '121987', '122151', '122191', '122208', '122818', 
    '122819', '122862', '123137', '123601', '124063', '124126', '124228', 
    '124322', '124497', '124523', '124574', '124754', '124841', '124990', 
    '126497', '126770', '128597', '128610', '129164', '130468', '130514', 
    '130661', '130682', '130913', '131513', '132105', '132341', '132359', 
    '132539', '132576', '132619', '132684', '133148', '133298', '133618',
    '133739', '133798', '133812', '133815', '135372', '136276', '136285', 
    '136289', '137328', '137337', '137798', '137833', '138552', '139668', 
    '139710', '140066', '140151', '140360', '140402', '140591', '140668', 
    '140715', '140792', '141043', '141010', '141318', '141395', '141398', 
    '141460', '141552', '141573', '141557', '141958', '141973', '142145', 
    '142281', '142307', '142534', '142551', '142624', '142662', '143173', 
    '143194', '143453', '143467', '143813', '143859', '144075', '144082', 
    '144103', '144110', '144248', '144260', '144291', '144435', '144447', 
    '144463', '144542', '144626', '144632', '144915', '145050', '145080', 
    '146082', '146092', '146114', '146223', '146234', '146254', '146528', 
    '146731', '146767', '146846', '147904', '146965', '147340', '148014', 
    '148074', '148293', '148312', '148524', '148978', '152202', '152592', 
    '152625', '152795', '153864', '154834', '154969', '155239', '155291', 
    '155559', '155578', '155629', '155674', '155692', '156286', '156326', 
    '156465', '156484', '156536', '156614', '156639', '156667', '156757', 
    '157130', '157231', '157746', '158268', '158333', '158395', '158442', 
    '158932', '160484', '160673', '160966', '161926', '162849', '164093', 
    '165142', '166275', '167236', '167486', '167676', '167758', '168118', 
    '168450', '168824', '169151', '169429', '169460', '169481', '170159', 
    '170278', '170333', '170418', '170545', '171132', '171479', '171500', 
    '171897', '172053', '172169', '172279', '172582', '172598', '172729', 
    '172787', '173060', '173172', '174346', '174397', '174424', '175019', 
    '175153', '175677', '177857', '178669', '179077', '179136', '179379', 
    '179828', '179966', '180597', '205474', '207762', '207925')
  )
,min_term as (
select
co_pidm,
min(co_term_code_key)co_term_code_key
from dp1
group by
co_pidm
)
select  
dp1.FIRST_NAME,
dp1.LAST_NAME,
dp1.BIRTHDATE,
mt.co_pidm,
mt.co_term_code_key,
dp1.co_gender,
dp1.co_ia_student_type_code,
dp1.co_ethnicity,
dp1.PRIMARY_DEGREE_CODE,
dp1.PRIMARY_MAJOR_1_DESC,
dp1.disadvantaged_ind,
dp1.citizen_ind,
dp1.RESIDENCY_CODE,
case
when start_term like '%10'
then to_date('01-SEP-'||(to_number(SUBSTR(dp1.start_term,3,2))-1),'DD-MON-YY') 
when start_term like '%20'
then to_date('01-JAN-'||(to_number(SUBSTR(dp1.start_term,3,2))),'DD-MON-YY')
when start_term like '%30'
then to_date('01-MAY-'||(to_number(SUBSTR(dp1.start_term,3,2))),'DD-MON-YY')
when start_term like '%40'
then to_date('25-JUN-'||(to_number(SUBSTR(dp1.start_term,3,2))),'DD-MON-YY')
end start_date

from min_term mt
left join dp1 on mt.co_pidm = dp1.co_pidm
and mt.co_term_code_key = dp1.co_term_code_key
)popsel
)
;