--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View CONTACT_ACCOUNT_UPSERT
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "HEDAMGR"."CONTACT_ACCOUNT_UPSERT" ("PIDM__C", "ID", "UMID__C", "LASTNAME", "FIRSTNAME", "MAILINGSTREET", "MAILINGCITY", "MAILINGSTATE", "MAILINGPOSTALCODE", "MOBILEPHONE", "HOMEPHONE", "BIRTHDATE", "HED__DECEASED__C", "HED__ETHNICITY__C", "HED__GENDER__C", "HED__MILITARY_SERVICE__C", "HED__ALTERNATEEMAIL__C", "HED__PREFERREDPHONE__C", "HED__PREFERRED_EMAIL__C", "HED__PRIMARY_ADDRESS_TYPE__C", "HED__UNIVERSITYEMAIL__C", "INTERNATIONAL_INDICATOR__C", "PREFERRED_FIRST_NAME__C", "UIC_CODE__C", "CONFIDENTIALITY_IND__C", "ACCOUNTID") AS 
  with fac_staff_all_sel as(
  select 
  sibinst.sibinst_pidm,
  sibinst.sibinst_fcst_code,
  sirdpcl.sirdpcl_term_code_eff
  from aimsmgr.sibinst
  inner join aimsmgr.sirdpcl on sirdpcl.sirdpcl_pidm = sibinst.sibinst_pidm
                             and sirdpcl.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
                                                                  from aimsmgr.sirdpcl s1
                                                                  where s1.sirdpcl_pidm = sirdpcl.sirdpcl_pidm)
  where sibinst.sibinst_term_code_eff = (select max(s1.sibinst_term_code_eff)
                                         from aimsmgr.sibinst s1
                                         where s1.sibinst_pidm = sibinst.sibinst_pidm)
  and not exists (select 'is a dup'
                  from aimsmgr.sprhold sprhold
                  where sprhold.sprhold_pidm = sibinst.sibinst_pidm
                  and sprhold.sprhold_hldd_code = 'DP')
  order by 1
),
fac_staff_sel as(
  select
  fac_staff_all_sel.sibinst_pidm
  from fac_staff_all_sel
  where fac_staff_all_sel.sibinst_fcst_code = 'AC'
--  and fac_staff_all_sel.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
--                                                 from fac_staff_all_sel s1
--                                                 where s1.sibinst_pidm = fac_staff_all_sel.sibinst_pidm)
),
student_sel as(
  select 
  ps.sgbstdn_pidm
  from(
    select 
    sgbstdn.sgbstdn_pidm,
    sgbstdn.sgbstdn_stst_code,
    row_number() over(partition by sgbstdn.sgbstdn_pidm order by sgbstdn.sgbstdn_term_code_eff desc) order_num
    from aimsmgr.sgbstdn
    where not exists (select 'is a dup'
                      from aimsmgr.sprhold sprhold
                      where sprhold.sprhold_pidm = sgbstdn.sgbstdn_pidm
                      and sprhold.sprhold_hldd_code = 'DP')
  )ps
  where ps.sgbstdn_stst_code = 'AS'
  and ps.order_num = 1
),
applicant_sel as(
  select
  saradap.saradap_pidm
  from aimsmgr.saradap
  where saradap.saradap_term_code_entry >= (select min(stvterm.stvterm_code)
                                            from aimsmgr.stvterm
                                            where stvterm.stvterm_end_date >= trunc(sysdate))
  and not exists (select 'is a dup'
                  from aimsmgr.sprhold sprhold
                  where sprhold.sprhold_pidm = saradap.saradap_pidm
                  and sprhold.sprhold_hldd_code = 'DP')
),
pop_sel as(
  select
  fac_staff_sel.sibinst_pidm pop_sel_pidm
  from fac_staff_sel
  union all
  select
  student_sel.sgbstdn_pidm
  from student_sel
  union all
  select 
  applicant_sel.saradap_pidm
  from applicant_sel
)
select
distinct pop_sel_pidm pidm__c,
contact.id,
um_demographic.umid umid__c,
um_demographic.last_name lastname,
um_demographic.legal_first_name firstname,
um_demographic.a1_street_line1 mailingstreet,
um_demographic.a1_city mailingcity,
um_demographic.a1_state_code mailingstate,
um_demographic.a1_zip mailingpostalcode,
um_demographic.mo_area_code || um_demographic.mo_phone_number mobilephone,
um_demographic.a1_area_code || um_demographic.a1_phone_number homephone,
to_char(um_demographic.birthdate, 'YYYY-MM-DD') birthdate,
case
  when um_demographic.deceased_ind is not null and um_demographic.deceased_ind = 'Y' then 'true'
  else 'false'
end hed__deceased__c,
um_demographic.report_ethnicity hed__ethnicity__c,
case 
  when nvl(um_demographic.gender, 'X') = 'F' then um_demographic.gender_desc
  else 'Male'
end hed__gender__c,
case
  when nvl(um_demographic.veteran_ind, 'X') = 'Y' then 'true'
  else 'false'
end hed__military_service__c,
um_demographic.hm_email_1 hed__alternateemail__c,
'Home' hed__preferredphone__c,
case 
  when um_demographic.ca_email is not null then 'University' 
  when um_demographic.ca_email is null and um_demographic.hm_email_1 is not null then 'Alternate'
  else 'University'
end hed__preferred_email__c,
'Mailing (A1)' hed__primary_address_type__c,
case  
  when um_demographic.ca_email is not null then um_demographic.ca_email
  else 'umflint.' || um_demographic.umid || '@umflint.edu'
end hed__universityemail__c,
case
  when nvl(um_demographic.intl_ind, 'X') = 'Y' then 'true'
  else 'false'
end international_indicator__c,
um_demographic.pref_first_name preferred_first_name__c,
um_demographic.uic_id uic_code__c,
case
  when nvl(um_demographic.confidentiality_ind, 'X') = 'Y' then 'true'
  else 'false'
end confidentiality_ind__c,
contact.accountid

from pop_sel
inner join um_demographic on um_demographic.dm_pidm = pop_sel.pop_sel_pidm
                          and um_demographic.umid not like ('ADV-%')
                          and um_demographic.duplicate_ind is null
                          and nvl(um_demographic.hm_email_1, 'XX') not like ('%, %')
left outer join contact contact on contact.pidm__c = pop_sel.pop_sel_pidm
--  where um_demographic.dm_pidm in (55342, 10475)
--where ca_email = 'false'

order by 1
;
