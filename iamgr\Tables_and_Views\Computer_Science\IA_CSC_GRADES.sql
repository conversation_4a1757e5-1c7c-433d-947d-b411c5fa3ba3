CREATE OR REPLACE VIEW ia_csc_grades
AS
  (SELECT popcel.*
  FROM
    (WITH data_pull AS
    (SELECT term_desc,
      subject
      || ' '
      ||course AS subject_course,
      section,
      Grade,
      CASE
        WHEN Grade IN ('A-','.A-','IA-','A','.A','IA','A+','.A+','IA+','P','S','.Y','Y')
        THEN 'A'
        WHEN Grade IN ('B-','.B-','IB-','B','.B','IB','B+','.B+','IB+')
        THEN 'B'
        WHEN Grade IN ('.-C-','-C-','C-','.C-','IC-','C','.C','IC','C+','.C+','IC+')
        THEN 'C'
        WHEN Grade IN ('D-','.D-','ID-','D','.D','ID','D+','.D+','ID+')
        THEN 'D'
        WHEN Grade IN ('E-','.E-','IE-','E','.E','IE','E+','.E+','IE+','N')
        THEN 'E'
        WHEN Grade IN ('.W','W')
        THEN 'W'
        WHEN Grade IN ('.I','I')
        THEN 'I'
        ELSE Grade
      END Grade_Dist
    FROM UM_STUDENT_TRANSCRIPT
    WHERE type = 'I'
    AND -- Institutional only, no transfer
      subject     IN ('CSC','CIS')
    AND Grade NOT IN ('.I','I')
    AND term LIKE '%10'
    AND term >= '201110'
    ),
    uniq_course AS
    ( SELECT DISTINCT SUBJECT_COURSE FROM data_pull
    ),
    uniq_course_random AS
    ( SELECT DISTINCT SUBJECT_COURSE,
      dbms_random.string('U',5) AS CID
    FROM uniq_course
    )
  SELECT term_desc,
    SUBJECT_COURSE,
    CID,
    SECTION,
    GRADE,
    GRADE_DIST
  FROM data_pull NATURAL
  JOIN uniq_course_random
    )popcel
  );
