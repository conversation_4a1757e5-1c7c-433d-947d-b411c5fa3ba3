/*******************************************************************************
IPEDS 
Fall Collection: 12-Month Enrollment for July 1 � June 30 of FY
Applies to: 4-year Institutions
File Type: Key Value Pair (*.TXT)
Part A: Unduplicated Count
Part C: Distance Education Status
Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
Be sure to update the FY before running
Export as comma dilimeted .TXT file
Remove trailing commas from upload file
Do not use enclosurs
*******************************************************************************/
/*******************************************************************************
                       Part A: Unduplicated Count
*******************************************************************************/
SELECT
 *
FROM
 (
  WITH dp1 AS (
   SELECT
    ROW_NUMBER()
    OVER(PARTITION BY sd_pidm
         ORDER BY sd_term_code
    )          AS min_term,
    sd_pidm,
    sd_term_code,
    full_part_time_ind_umf,
    ia_student_type_code,
    ia_gender  AS gender,
    ipeds_gender_code,
    report_ethnicity,
    ipeds_race_code,
    report_level_code,
    CASE
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'F'
          AND ia_student_type_code = 'F' THEN
      '1'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'F'
          AND ia_student_type_code = 'T' THEN
      '2'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'F'
          AND ia_student_type_code IN ( 'C', 'R' ) THEN
      '3'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'F'
          AND ia_student_type_code IN ( 'D', 'E', 'G', 'S', 'X' ) THEN
      '7'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'P'
          AND ia_student_type_code = 'F' THEN
      '15'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'P'
          AND ia_student_type_code = 'T' THEN
      '16'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'P'
          AND ia_student_type_code IN ( 'C', 'R' ) THEN
      '17'
     WHEN report_level_code = 'UG'
          AND full_part_time_ind_umf = 'P'
          AND ia_student_type_code IN ( 'D', 'E', 'G', 'S', 'X' ) THEN
      '21'
     WHEN report_level_code = 'GR' THEN
      '99'
     ELSE
      report_level_code
      || '-'
      || full_part_time_ind_umf
      || '-'
      || ia_student_type_code
    END        line_a
   FROM
    ia_td_student_data
   WHERE
     registered_ind = 'Y'
    AND fy = (
     SELECT
      substr(last_aidy_code, 1, 2)
      || '-'
      || substr(last_aidy_code, 3, 4) fy
     FROM
      um_current_term
    )
  )
  SELECT
   'UNITID=171146'                       a,
   'SURVSECT=E1D'                        b,
   'PART=A'                              c,
   'LINE=' || dp1.line_a                  d,
   'RACE=' || dp1.ipeds_race_code          e,
   'SEX=' || dp1.ipeds_gender_code         f,
   'COUNT=' || COUNT(dp1.sd_pidm)          g
  FROM
   dp1
  WHERE
   min_term = '1'
  GROUP BY
   'UNITID=171146',
   'SURVSECT=E1D',
   'PART=A',
   'LINE=' || dp1.line_a,
   'RACE=' || dp1.ipeds_race_code,
   'SEX=' || dp1.ipeds_gender_code
 ) popsel
UNION ALL

/*******************************************************************************
                       Part C: Distance Education Status
*******************************************************************************/
SELECT
 *
FROM
 (
  WITH dp1 AS (
   SELECT
    ROW_NUMBER()
    OVER(PARTITION BY sd_pidm
         ORDER BY sd_term_code
    )    AS min_term,
    sd_pidm,
    sd.sd_term_code,
    sd.online_courses_only_ind,
    CASE
     WHEN online_courses_only_ind = 'Y' THEN
      1
     ELSE
      0
    END  online_courses_only_code,
    CASE
     WHEN online_courses_only_ind = 'N'
          AND (
      SELECT
       COUNT(*)
      FROM
       td_registration_detail
      WHERE
        td_registration_detail.pidm = sd.sd_pidm
       AND td_registration_detail.td_term_code = sd.sd_term_code
       AND td_registration_detail.section_number LIKE 'W%'
     ) > 0 THEN
      'Y'
     ELSE
      'N'
    END  some_online_ind,
    CASE
     WHEN online_courses_only_ind = 'N'
          AND (
      SELECT
       COUNT(*)
      FROM
       td_registration_detail
      WHERE
        td_registration_detail.pidm = sd.sd_pidm
       AND td_registration_detail.td_term_code = sd.sd_term_code
       AND td_registration_detail.section_number LIKE 'W%'
     ) > 0 THEN
      '1'
     ELSE
      '0'
    END  some_online_code,
    CASE
     WHEN sd.report_level_code = 'UG'
          AND sd.ia_student_type_code IN ( 'F', 'T', 'C', 'R' ) THEN
      '1'
     WHEN sd.report_level_code = 'UG'
          AND sd.ia_student_type_code IN ( 'D', 'E', 'G', 'S', 'X' ) THEN
      '2'
     WHEN sd.report_level_code = 'GR' THEN
      '3'
     ELSE
      sd.report_level_code
      || '-'
      || sd.ia_student_type_code
    END  d
   FROM
    ia_td_student_data sd
   WHERE
     sd.registered_ind = 'Y'
    AND sd.fy = (
     SELECT
      substr(last_aidy_code, 1, 2)
      || '-'
      || substr(last_aidy_code, 3, 4) fy
     FROM
      um_current_term
    )
  ), cnt1 AS (
   SELECT
    dp1.sd_pidm,
    SUM(dp1.online_courses_only_code)      online_only_count,
    SUM(dp1.some_online_code)              some_online_count,
    COUNT(dp1.sd_term_code)                term_count
   FROM
    dp1
   GROUP BY
    dp1.sd_pidm
  ), fy_course_type AS (
   SELECT
    cnt1.*,
    CASE
     WHEN cnt1.online_only_count = cnt1.term_count THEN
      'fy_online_only'
     WHEN cnt1.online_only_count = 0
          AND cnt1.some_online_count = 0 THEN
      'fy_in_class_only'
     ELSE
      'fy_some_online'
    END fy_course_type_ind
   FROM
    cnt1
  )
  , ee AS (
   SELECT
    d,
    COUNT(DISTINCT dp1.sd_pidm) e
   FROM
         dp1 
    INNER JOIN fy_course_type ON fy_course_type.sd_pidm = dp1.sd_pidm
   WHERE
     fy_course_type_ind = 'fy_online_only'
    AND dp1.min_term = '1'
   GROUP BY
    d
  )
  , es AS (
   SELECT
    d,
    COUNT(DISTINCT dp1.sd_pidm) f
   FROM
         dp1 
    INNER JOIN fy_course_type ON fy_course_type.sd_pidm = dp1.sd_pidm
   WHERE
     fy_course_type_ind = 'fy_some_online'
    AND dp1.min_term = '1'
   GROUP BY
    d
  )

  SELECT
   'UNITID=171146'                   a,
   'SURVSECT=E1D'                    b,
   'PART=C'                          c,
   'LINE=' || ee.d                     d,
   'ENROLL_EXCLUSIVE=' || ee.e        e,
   'ENROLL_SOME=' || es.f             f,
   CAST(NULL AS VARCHAR2(16))        g
  FROM
   ee
   LEFT OUTER JOIN es ON ee.d = es.d
 ) popsel2
UNION ALL

/*******************************************************************************
    Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
*******************************************************************************/
SELECT
 'UNITID=171146'  a,
 'SURVSECT=E1D'   b,
 'PART=B'         c,
/****CREDHRSU Credit hour instructional activity at the undergraduate level****/	
 'CREDHRSU='
 || (
  SELECT
   SUM(total_credit_hours_umf)
  FROM
   ia_td_student_data
  WHERE
    registered_ind = 'Y'
   AND fy = (
    SELECT
     substr(last_aidy_code, 1, 2)
     || '-'
     || substr(last_aidy_code, 3, 4) fy
    FROM
     um_current_term
   )
   AND ipeds_class_code = 'UG'
 )                d,
/****CONTHRS Contact hour instructional activity (undergraduate level only)****/	              
 'CONTHRS='       e,          
/******CREDHRSG Credit hour instructional activity at the graduate level*******/	               
 'CREDHRSG='
 || (
  SELECT
   SUM(total_credit_hours_umf)
  FROM
   ia_td_student_data
  WHERE
    registered_ind = 'Y'
   AND fy = (
    SELECT
     substr(last_aidy_code, 1, 2)
     || '-'
     || substr(last_aidy_code, 3, 4) fy
    FROM
     um_current_term
   )
   AND ( ipeds_class_code = 'GR'
         OR ipeds_class_code = 'Drs-Acad' )
 )                f,
/******RDOCFTE Reported Doctor's degree-professional practice student FTE******/	             
 'RDOCFTE='
 || (
  SELECT
   round(SUM(total_credit_hours_umf / 16), 0) fyes
  FROM
   ia_td_student_data
  WHERE
    registered_ind = 'Y'
   AND fy = --'20-21'
    (
    SELECT
     substr(last_aidy_code, 1, 2)
     || '-'
     || substr(last_aidy_code, 3, 4) fy
    FROM
     um_current_term
   )
   AND ipeds_class_code = 'Drs-Prof'
 )                g
FROM
 dual
ORDER BY
 3,
 4,
 5;
--,