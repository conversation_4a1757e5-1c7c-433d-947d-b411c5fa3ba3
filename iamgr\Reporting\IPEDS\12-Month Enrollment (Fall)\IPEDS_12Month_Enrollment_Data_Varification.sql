/*******************************************************************************
--Part A: Unduplicated Count
*******************************************************************************/
with 
dp1 as(
select
row_number() OVER(PARTITION BY SD_PIDM ORDER BY SD_TERM_CODE ) MIN_TERM,
--sd.fy,
sd.sd_pidm,
sd.sd_term_code,
sd.ia_gender,
--SD.GENDER,
sd.REPORT_ETHNICITY,
sd.full_part_time_ind_umf,
sd.report_level_code,

sd.ia_student_type_code,
case
when  sd.ia_student_type_code = 'F' then 'First-time'
when  sd.ia_student_type_code = 'T' then 'transfer-in'
when  sd.ia_student_type_code in ('N','C','R','G','S','X') and sd.report_level_code = 'GR' then 'Graduate'
when  sd.ia_student_type_code in ('C','R')  and sd.report_level_code = 'UG' then 'Continuing/readmit'
when  sd.ia_student_type_code in ('D','E','G','S','X') then 'non-degree/certificate-seeking'
else sd.ia_student_type_code ||'-'|| sd.report_level_code
end student_type
--,

--end line_a_desc,
--ipeds_class_code,
--TOTAL_CREDIT_HOURS_UMF

from
IA_TD_STUDENT_DATA SD
where
sd.registered_ind = 'Y' and 
sd.fy = '21-22'                                                 --UPDATE THIS

)
select 
dp1.*

from dp1 
WHERE MIN_TERM = 1
;

/*******************************************************************************
--Part E: Duel Enrolled Students
*******************************************************************************/

select * from (
with 
DP1 as(
select
row_number() OVER(PARTITION BY SD_PIDM ORDER BY SD_TERM_CODE ) MIN_TERM,
sd_pidm,
sd_term_code,
gender,
report_ethnicity,
ia_student_type_code
from
IA_TD_STUDENT_DATA
where
registered_ind = 'Y' and 
fy = '22-23' 

)

SELECT
gender,
report_ethnicity,
count(DP1.sd_pidm) 
from DP1 
WHERE MIN_TERM = 1
and ia_student_type_code in ('D','E')
group by
gender,
report_ethnicity

)Popsel ;
/*******************************************************************************
--Part D: Gender Unknown
*******************************************************************************/


with 
DP1 as(
select
row_number() OVER(PARTITION BY SD_PIDM ORDER BY SD_TERM_CODE ) MIN_TERM,
sd_pidm,
sd_term_code,
GENDER,
REPORT_LEVEL_CODE

from
IA_TD_STUDENT_DATA
where
registered_ind = 'Y' and 
fy = '21-22'                                                                              --Update This
              
)
SELECT DP1.*
from 
dp1
where min_term = 1
and gender = 'N'
;

/*******************************************************************************
--Part C: Distance Education Status
*******************************************************************************/

WITH DP1 AS(
SELECT
sd_pidm,
sd.sd_term_code,
sd.online_courses_only_ind,
case 
when online_courses_only_ind = 'Y' then 1
else 0
end online_courses_only_code,
case 
    when online_courses_only_ind = 'N' and 
     (select count(*)
      from td_registration_detail 
      where td_registration_detail.pidm = sd.sd_pidm
      and td_registration_detail.td_term_code = sd.sd_term_code
      and td_registration_detail.section_number like 'W%') > 0 then 'Y'
    else 'N'
end some_online_ind,
case 
    when online_courses_only_ind = 'N' and 
     (select count(*)
      from td_registration_detail 
      where td_registration_detail.pidm = sd.sd_pidm
      and td_registration_detail.td_term_code = sd.sd_term_code
      and td_registration_detail.section_number like 'W%') > 0 then '1'
    else '0'
end some_online_code,
case 
when sd.report_level_code = 'UG'  and sd.ia_student_type_code in ('F','T','C','R') then '1'
when sd.report_level_code = 'UG'  and sd.ia_student_type_code in ('D','E','G','S','X') then '2'
when sd.report_level_code = 'GR'  then '3'
else sd.report_level_code||'-'|| sd.ia_student_type_code
end D,
case 
when sd.report_level_code = 'UG'  and sd.ia_student_type_code in ('F','T','C','R') then 'Degree Cert Seeking UG'
when sd.report_level_code = 'UG'  and sd.ia_student_type_code in ('D','E','G','S','X') then 'Non-Degree Non-Cert Seeking UG'
when sd.report_level_code = 'GR'  then 'GR'
else sd.report_level_code||'-'|| sd.ia_student_type_code
end D_desc
from ia_td_student_data sd
where sd.registered_ind = 'Y'
and sd.fy = '21-22'                                              --UPDATE THIS
),cnt1 as (
select 
dp1.sd_pidm,
sum (dp1.online_courses_only_code) online_only_count,
sum (dp1.some_online_code) some_online_count,
count (dp1.sd_term_code) term_count
from dp1
group by
dp1.sd_pidm
),fy_course_type as (
select 
cnt1.*,
case 
when cnt1.online_only_count = cnt1.term_count then 'enrolled_exclusively_distance'
when cnt1.online_only_count = 0 and cnt1.some_online_count = 0 then 'not_enrolled_in_any_distance'
else 'enrolled_in_at_least_one_but_not_all'
end fy_course_type_ind
from cnt1
),min_term as(
select
dp1.sd_pidm,
min (dp1.sd_term_code) min_term
from dp1
group by dp1.sd_pidm
)
select 
min_term.sd_pidm,
min_term.min_term,
dp1.d_desc,
fy_course_type.online_only_count,
fy_course_type.some_online_count,
fy_course_type.term_count,
fy_course_type.fy_course_type_ind
from dp1 
inner join min_term on dp1.sd_pidm = min_term.sd_pidm 
and dp1.sd_term_code = min_term.min_term
inner join fy_course_type on fy_course_type.sd_pidm = min_term.sd_pidm
;

/*******************************************************************************
--Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
*******************************************************************************/

select
sd_pidm,
sd_term_code,
fy,
ipeds_class_code,
to_number(total_credit_hours_umf)total_credit_hours_umf
from ia_td_student_data
where
fy='21-22'                                                      --UPDATE THIS
;
--,