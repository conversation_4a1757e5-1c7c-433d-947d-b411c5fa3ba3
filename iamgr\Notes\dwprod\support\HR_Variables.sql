SELECT
  IA_DWPROD_HR_PERSONAL_DATA.EMPLID,--EMPLID
  IA_DWPROD_HR_PERSONAL_DATA.LAST_NAME ||','||IA_DWPROD_HR_PERSONAL_DATA.FIRST_NAME NAME, --NAME
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPTID DEPT, --DEPT
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_DESCR DEPT_DESC,--DEPT_DESCR
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_GRP DEPT_GRP,--DEPT_GRP
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_GRP_CAMPUS CAMPUS, --CAMPUS
  IA_DWPROD_HR_JOB.JOBCODE,--JO<PERSON>ODE
  IA_DWPROD_HR_JOB.JOBCODE_DESCR,--JOBCODE DESCR
  IA_DWPROD_HR_JOB.JOB_FAMILY JF,--JOB FAMILY CODE
  IA_DWPROD_HR_JOB.JOB_FAMILY_DESCR, --JOB FAMILY DESCRITPTION
  --est
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_GRP_CAMPUS_DESCR CAMPUS_DESCR,--est descr
  IA_DWPROD_HR_JOB.BEN_FAMILY,
  --Ben Config 4,
  --Ben Config 9,
  --IPEDS
  --IPEDS DESCR
  --Full-Part
  IA_DWPROD_HR_JOB.TENURE_STATUS, --Tenure
  IA_DWPROD_HR_JOB.TENURE_STATUS_DESCRSHORT, --Tenure Descr,
  IA_DWPROD_HR_JOB.FTR_RATE,  --Rate
  IA_DWPROD_HR_JOB.APPT_PERIOD, --Appt Period
  IA_DWPROD_HR_JOB.APPT_PERIOD_DESCR,  --Appt Period Descr
  IA_DWPROD_HR_JOB.PAY_FREQUENCY,  --Comp_Freq
  IA_DWPROD_HR_JOB.PAY_FREQUENCY_DESCRSHORT,  --Comp Freq Descr
  --Rank
  --Rank Descr
  --Hiredate
  IA_DWPROD_HR_ETHNICITY_DTL.ETHNICITY_CODE--race code
  IA_DWPROD_HR_ETHNICITY_DTL.ETHNICITY_DESCRSHORT,  --race_descr
  IA_DWPROD_HR_DEMOGRAPHICS.SEX--sex
  IA_DWPROD_HR_DEMOGRAPHICS.SEX_DESCRSHORT,  --gender descr
  IA_DWPROD_HR_DEMOGRAPHICS.CITIZENSHIP_STATUS--citizen code
  IA_DWPROD_HR_DEMOGRAPHICS.CITIZENSHIP_STATUS_DESCR,  --citizen descr
 IA_DWPROD_HR_PERSONAL_DATA.HIGHEST_EDUC_LVL --highest level edu
 IA_DWPROD_HR_PERSONAL_DATA.HIGHEST_EDUC_LVL_DESCRSHORT --highest level edu desc
 IA_DWPROD_HR_PERSONAL_DATA.HR_MAJ_DEGREE1_TXT --deg1
 IA_DWPROD_HR_PERSONAL_DATA.HR_MAJ_DEGREE2_TXT --deg2
  IA_DWPROD_HR_JOB.JOB_EFFDT,  --appt_sart_date
  IA_DWPROD_HR_JOB.JOB_END_DT,  --appt_end_date
  --job grp
  --job_grp descr
  --soc_cd
  --soc_cd_descr



  IA_DWPROD_HR_JOB.JOB_EFFSEQ,

  IA_DWPROD_HR_JOB.APPT_DEPT_DESCR,
  
  IA_DWPROD_HR_JOB.REG_TEMP,
  IA_DWPROD_HR_JOB.FTR_START_DATE,
  IA_DWPROD_HR_JOB.FTR_END_DATE,


  IA_DWPROD_HR_JOB.FTE,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.SHORTCODE,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_END_DT,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.SHORTCODE_DESCR,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.FUNDING_DEPT_DESCR,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DIST_PCT,
  (IA_DWPROD_HR_JOB.COMPRATE * IA_DWPROD_HR_DEPT_BUDGET_ERN.DIST_PCT) / 100,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DIST_PCT * IA_DWPROD_HR_JOB.STD_HOURS/100,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.PERCENT_EFFORT,
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_EFFDT,
  IA_DWPROD_HR_JOB.EMPL_RCD,
  
  sum(IA_DWPROD_HR_JOB.STD_HOURS),
  sum(IA_DWPROD_HR_JOB.FTE)
FROM
  IA_DWPROD_HR_PERSONAL_DATA,
  IA_DWPROD_HR_JOB,
  IA_DWPROD_HR_DEMOGRAPHICS,
  IA_DWPROD_HR_ETHNICITY_DTL,
  IA_DWPROD_HR_DEPT_BUDGET_ERN
WHERE
  ( IA_DWPROD_HR_PERSONAL_DATA.EMPLID=IA_DWPROD_HR_DEMOGRAPHICS.EMPLID  )
  AND  ( IA_DWPROD_HR_PERSONAL_DATA.EMPLID=IA_DWPROD_HR_JOB.EMPLID  )
  AND  ( IA_DWPROD_HR_JOB.EMPLID=IA_DWPROD_HR_DEPT_BUDGET_ERN.EMPLID(+)  )
  AND  ( IA_DWPROD_HR_JOB.EMPL_RCD=IA_DWPROD_HR_DEPT_BUDGET_ERN.EMPL_RCD(+)  )
  AND  ( IA_DWPROD_HR_JOB.APPT_DEPTID=IA_DWPROD_HR_DEPT_BUDGET_ERN.APPT_DEPTID(+)  )
  AND  ( ( 'Max Sequence' = 'All Sequences') or ( IA_DWPROD_HR_JOB.JOB_EFFSEQ =   
  ( select max(a_JOB.JOB_EFFSEQ) 
       from IA_DWPROD_HR_JOB a_JOB where 
         a_JOB.EMPLID=IA_DWPROD_HR_JOB.EMPLID and
         a_JOB.EMPL_RCD=IA_DWPROD_HR_JOB.EMPL_RCD and
         a_JOB.JOB_EFFDT=IA_DWPROD_HR_JOB.JOB_EFFDT and 'Max Sequence' = 'Max Sequence'
    ) 
) 
 
or 
(
	IA_DWPROD_HR_JOB.EMPLID IS NULL and

	IA_DWPROD_HR_JOB.EMPL_RCD is NULL
)  )
  AND  ( IA_DWPROD_HR_JOB.JOB_EFFDT<=IA_DWPROD_HR_DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_END_DT  )
  AND  ( IA_DWPROD_HR_DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_EFFDT <= IA_DWPROD_HR_JOB.JOB_END_DT  )
  AND  ( IA_DWPROD_HR_ETHNICITY_DTL.EMPLID(+)=IA_DWPROD_HR_DEMOGRAPHICS.EMPLID  )
  AND  
  (
   IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_GRP_CAMPUS  IN  ( 'UM_FLINT'  )
   AND
   IA_DWPROD_HR_JOB.EMPL_STATUS  IN  ( 'A','P'  )
  )
GROUP BY
  IA_DWPROD_HR_PERSONAL_DATA.EMPLID, 
  IA_DWPROD_HR_JOB.EMPL_RCD, 
  IA_DWPROD_HR_PERSONAL_DATA.LAST_NAME, 
  IA_DWPROD_HR_PERSONAL_DATA.FIRST_NAME, 
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_DESCR, 
  IA_DWPROD_HR_PERSONAL_DATA.INST_DEPT_GRP_CAMPUS_DESCR, 
  IA_DWPROD_HR_DEMOGRAPHICS.SEX_DESCRSHORT, 
  IA_DWPROD_HR_DEMOGRAPHICS.CITIZENSHIP_STATUS_DESCR, 
  IA_DWPROD_HR_ETHNICITY_DTL.ETHNICITY_DESCRSHORT, 
  IA_DWPROD_HR_JOB.JOB_EFFDT, 
  IA_DWPROD_HR_JOB.JOB_EFFSEQ, 
  IA_DWPROD_HR_JOB.JOB_END_DT, 
  IA_DWPROD_HR_JOB.APPT_DEPT_DESCR, 
  IA_DWPROD_HR_JOB.JOBCODE_DESCR, 
  IA_DWPROD_HR_JOB.REG_TEMP, 
  IA_DWPROD_HR_JOB.FTR_START_DATE, 
  IA_DWPROD_HR_JOB.FTR_END_DATE, 
  IA_DWPROD_HR_JOB.FTR_RATE, 
  IA_DWPROD_HR_JOB.APPT_PERIOD_DESCR, 
  IA_DWPROD_HR_JOB.FTE, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.SHORTCODE, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_END_DT, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.SHORTCODE_DESCR, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.FUNDING_DEPT_DESCR, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DIST_PCT, 
  (IA_DWPROD_HR_JOB.COMPRATE * IA_DWPROD_HR_DEPT_BUDGET_ERN.DIST_PCT) / 100, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DIST_PCT * IA_DWPROD_HR_JOB.STD_HOURS/100, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.PERCENT_EFFORT, 
  IA_DWPROD_HR_DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_EFFDT