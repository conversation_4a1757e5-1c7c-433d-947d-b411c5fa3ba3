--CREATE OR REPLACE DIRECTORY 
--MY_PDF_DIR AS 
--'/u02/plsql_pdfs';

DECLARE
 CURSOR c_emp
 IS
 SELECT EMPLOYEE_ID,
 LAST_NAME,
 JOB_ID,
 MANAGER_ID,
 HIRE_DATE,
 SALARY,
 COMMISSION_PCT,
 DEPARTMENT_ID
 FROM hr.employees;

v_hdr VARCHAR (1000);
 v_dtl VARCHAR (1000);
 v_rec NUMBER (10) := 0;
 v_sal NUMBER (10) := 0;
 v_comm NUMBER (10) := 0;
 BEGIN
 /* First line to initialize the package*/
 pdf_builder_pkg.init;
 /* Set the font to bold for heading*/
 pdf_builder_pkg.set_font ('helvetica', 'b');
 /* Write a line using pdf_builder_pkg.write procedure*/
 pdf_builder_pkg.write ('Employee Report');
 /* Set the font to normal */
 pdf_builder_pkg.set_font ('helvetica');
 pdf_builder_pkg.write ('Printed Date: ' || SYSDATE, -1, p_alignment => 'right');
 pdf_builder_pkg.write ('________________________________________________________________________________');
 pdf_builder_pkg.write (' ' || CHR (10) || CHR (13));
 /* Setting font courier for better alignment*/
 pdf_builder_pkg.set_font ('courier');
 v_hdr :=
 RPAD ('Emp No.', 10, ' ')
 || RPAD ('Emp Name', 12, ' ')
 || RPAD ('JOB_ID', 10, ' ')
 || RPAD ('Manager', 10, ' ')
 || RPAD ('Hire Date', 12, ' ')
 || RPAD ('Salary', 10, ' ')
 || RPAD ('COMMISSION_PCT', 10, ' ');
 pdf_builder_pkg.write (v_hdr);
 pdf_builder_pkg.set_font ('helvetica');
 pdf_builder_pkg.
 write (
 '________________________________________________________________________________');
 pdf_builder_pkg.write (CHR (10) || CHR (13));
 pdf_builder_pkg.set_font ('courier');

FOR c IN c_emp
 LOOP
 v_dtl :=
 RPAD (c.EMPLOYEE_ID, 10, ' ')
 || RPAD (c.LAST_NAME, 12, ' ')
 || RPAD (c.JOB_ID, 10, ' ')
 || RPAD (c.MANAGER_ID, 10, ' ')
 || RPAD (c.HIRE_DATE, 12, ' ')
 || RPAD (c.SALARY, 10, ' ')
 || RPAD (c.COMMISSION_PCT, 10, ' ')
 || CHR (10)
 || CHR (13);
 pdf_builder_pkg.write (v_dtl);
 v_rec := v_rec + 1;
 v_sal := v_sal + c.SALARY;
 v_comm := v_comm + NVL (c.COMMISSION_PCT, 0);
 END LOOP;

pdf_builder_pkg.set_font ('helvetica');
 pdf_builder_pkg.write ('________________________________________________________________________________');
 pdf_builder_pkg.set_font ('courier');
 pdf_builder_pkg.write ('Records Count:' || v_rec);
 pdf_builder_pkg.write ('Total Salary:' || v_sal);
 pdf_builder_pkg.write ('Total Comm:' || v_comm);
 /* Saving the PDF file by passing directory name and file name */
 pdf_builder_pkg.save_pdf ('MY_PDF_DIR', 'emp_report.pdf');
 end;