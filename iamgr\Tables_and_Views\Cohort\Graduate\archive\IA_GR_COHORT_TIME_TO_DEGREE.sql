select 
IA_GR_COHORT_POP.CO_GR_PIDM,
IA_GR_COHORT_POP.PRIMARY_LEVEL_CODE,  
IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY,
IA_GR_COHORT_POP.START_DATE,
IA_GR_COHORT_POP.PRIMARY_MAJOR_1,
IA_GR_COHORT_POP.PRIMARY_PROGRAM,
IA_GR_COHORT_POP.PRIMARY_CONC_1,
IA_UM_DEGREE.DEGREE_CODE,
IA_UM_DEGREE.GRAD_DATE,
round(((trunc(IA_UM_DEGREE.GRAD_DATE) - trunc(IA_GR_COHORT_POP.START_DATE))/365),2) time_to_degree


from 
IA_GR_COHORT_POP
left outer join IA_UM_DEGREE on IA_UM_DEGREE.PIDM = IA_GR_COHORT_POP.CO_GR_PIDM and
                             IA_UM_DEGREE.LEVEL_CODE = IA_GR_COHORT_POP.PRIMARY_LEVEL_CODE and
                             IA_UM_DEGREE.PRIMARY_MAJOR_1 = IA_GR_COHORT_POP.PRIMARY_MAJOR_1 and
                             IA_UM_DEGREE.PRIMARY_PROGRAM = IA_GR_COHORT_POP.PRIMARY_PROGRAM and
                             IA_UM_DEGREE.PRIMARY_CONC_1 = IA_GR_COHORT_POP.PRIMARY_CONC_1
;

