/*
This query is designed to pull the FTIAC cohort and format it for the IA_CW_COHORT Table.
The rules to be in the cohort are:
Student is a full time, registered, FTIAC, UG in the FAll term
  or the student is a full time or part time, registered, FTIAC, UG in the prior Summer term 
  and the student returned in the Fall term as a full time, registered, continuing, UG, in Fall term
  
Students starting in Fall 2010 were rolled to the Fall as a student_type_code = 'F'
Prior to 2010 they did not roll and you will need to use the query labeled 2007-2009
*/
select
sd_pidm CO_PIDM,
sd_term_code CO_TERM_CODE_KEY, -- fall term code
sd_term_desc CO_TERM_DESC, -- fall term desc
ia_student_type_code CO_STUDENT_TYPE_CODE,
ia_student_type_desc CO_STUDNET_TYPE_DESC

from td_student_data td_started
where primary_level_code = 'UG'
and registered_ind = 'Y'
and ia_student_type_code = 'F'
and sd_term_code = '201110'-- fall term code
and full_part_time_ind_umf = 'F'

order by sd_pidm