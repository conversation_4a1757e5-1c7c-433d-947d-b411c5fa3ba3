create or replace view ia_gr_unpushed_apps as (

/*******************************************************************************
--  Grad Programs un-pushed apps
*******************************************************************************/
SELECT
 sarhead.sarhead_aidm studentkey,
''ss_number,
sarctrl_join.spriden_id studentno,
REPLACE(sarpers_join.sarpers_last_name,',','')l_name,
REPLACE(sarpers_join.sarpers_first_name,',','')f_name,
REPLACE(sarpers_join.sarpers_middle_name1,',','')m_name,
''pref_fname,
REPLACE(sarpers_join.sarpers_former_name,',','')prev_lname,
REPLACE(sarpers_join.sarpers_prefix,',','')n_prefix,
REPLACE(sarpers_join.sarpers_suffix,',','')n_suffix,
''isource_cd,
''entry_stat,
REPLACE((SELECT stvnatn.stvnatn_nation
  FROM aimsmgr.stvnatn
  WHERE stvnatn.stvnatn_edi_equiv = sarpers_join.sarpers_natn_cde_citz
  ),',','') citizen_cd,
''hs_grad_yr,
  (SELECT stvterm.stvterm_desc
  FROM aimsmgr.stvterm
  WHERE stvterm.stvterm_code = sarhead.sarhead_term_code_entry
  ) entry_term,
''stage,
''market_seg,
''trakset,
''counselor,
''cancel_dt,
''stopcomm,
''ct_filter,
''admit_cd,
''admt_cd_dt,
sarpers_join.sarpers_birth_dte birth_dt,
''op_hous_dt,
''visit_dt,
''deposit_ex,
''deposit_pd,
''dep_amount,
''academ_lev,
''admit_type,
''commuter,
''church_cd,
''denom_cd,
''employ_cd,
''eps_cd,
''ethnic_cd,
''fa_app,
''faid,
sarpers_join.sarpers_gender gender,
''loading,
''m_status,
''military,
''oren_sess,
''priority,
''st_int_lvl,
''site_cd,
''subinst_cd,
''trad,
''visa_num,
''create_by,
''create_dt,
''mod_by,
''mod_dt,
REPLACE(sarpers_join.saraddr_street_line1,',','')saddress1,
REPLACE(sarpers_join.saraddr_street_line2,',','')saddress2,
''saddress3,
REPLACE(sarpers_join.saraddr_city,',','')scity,
sarpers_join.saraddr_stat_cde sstate,
REPLACE(sarpers_join.saraddr_zip,',','')szip_code,
''scounty,
''scounty_cd,
''scntry_cd,
  CASE
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', '')), 0) > 7
    THEN '('
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 1, 3)
      || ')'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 4, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 7, 4)
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', '')), 0) <= 7
    THEN SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 1, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 4, 4)
  END phone_number,
  CASE
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', '')), 0) > 7
    THEN '('
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 1, 3)
      || ')'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 4, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 7, 4)
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', '')), 0) <= 7
    THEN SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 1, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 4, 4)
  END shomephone,
''sworkphone,
''sphone_ext,
''sfaxphone,
''sotherphon,
sarpers_join.email_address semail,
''p_name,
''p_salut,
''paddress1,
''paddress2,
''paddress3,
''pcity,
''pstate,
''pzip_code,
''pcounty,
''pcounty_cd,
''pcntry_cd,
''phomephone,
''pworkphone,
''pphone_ext,
''pfax_phone,
''potherphon,
''pemail,
''r_name,
''r_salut,
''raddress1,
''raddress2,
''raddress3,
''rcity,
''rstate,
''rzip_code,
''rcounty,
''rcounty_cd,
''rcntry_cd,
''rhomephone,
''rworkphone,
''rphone_ext,
''rfax_phone,
''rotherphon,
''remail,
saretry_join.sorcmjr_majr_code major1,
''major2,
''major3,
''interest1,
''interest2,
''interest3,
''othercoll1,
''othercoll2,
''othercoll3,
''hscode,
''hssource,
''hssize,
''hscl_rank,
''hsgpa,
''hsgpascale,
''hsstartde,
''hsenddt,
''hsdegree,
''hsgraddt,
''clcode,
''clquality,
''clsource,
''clgpa,
''clgpascale,
''clstarted,
''clenddt,
''cldegree,
''clgraddt,
''cl1code,
''cl1quality,
''cl1source,
''cl1gpa,
''cl1gpascal,
''cl1started,
''cl1enddt,
''cl1degree,
''cl1graddt,
''cl2code,
''cl2quality,
''cl2source,
''cl2gpa,
''cl2gpascal,
''cl2started,
''cl2enddt,
''cl2degree,
''cl2graddt,
  sarhead.SARHEAD_ACTIVITY_DATE actdate,
''actnrmper,
''actnrmtype,
''actsource,
''actenglish,
''actmath,
''actreading,
''actscience,
''actcmposit,
''satdate,
''satnrmper,
''satnrmtype,
''satsource,
''satverbal,
''satmath,
''sattotal,
''lochsgpa,
''locclgpa,
''s100date,
''s200date,
''s300date,
''s400date,
''s500date,
''s600date,
''com01date,
''com01type,
''com01name,
''com02date,
''com02type,
''com02name,
''com03date,
''com03type,
''com03name,
''com04date,
''com04type,
''com04name,
''com05date,
''com05type,
''com05name,
''com06date,
''com06type,
''com06name,
''com07date,
''com07type,
''com07name,
''com08date,
''com08type,
''com08name,
''com09date,
''com09type,
''com09name,
''com10date,
''com10type,
''com10name,
''com11date,
''com11type,
''com11name,
''com12date,
''com12type,
''com12name,
''com13date,
''com13type,
''com13name,
''com14date,
''com14type,
''com14name,
''com15date,
''com15type,
''com15name,
''trk01date,
''trk01paper,
''trk02date,
''trk02paper,
''trk03date,
''trk03paper,
''trk04date,
''trk04paper,
''trk05date,
''trk05paper,
''trk06date,
''trk06paper,
''trk07date,
''trk07paper,
''trk08date,
''trk08paper,
''trk09date,
''trk09paper,
''trk10date,
''trk10paper,
''trk11date,
''trk11paper,
''trk12date,
''trk12paper,
''trk13date,
''trk13paper,
''erpid,
''sisid,
''actwriting,
''satwriting,
''gmat_date,
''gmat_tot,
''gre_date,
''gre_tot

--  sarhead.sarhead_appl_seqno appl_seqno,
--  sarctrl_join.sarctrl_appl_no_saradap,
--  sarhead.sarhead_prev_applied_resp prev_um_appl,
--  sarhead.sarhead_prev_attend_resp prev_um_course,
--  sarpers_join.sarpers_nickname,
--  (SELECT stvnatn.stvnatn_nation
--  FROM aimsmgr.stvnatn
--  WHERE stvnatn.stvnatn_code = sarpers_join.saraddr_natn_cde
--  ) nation,
--  CASE
--    WHEN sarpers_join.sarpers_ethn_category = '1'
--    THEN 'Not Hispanic or Latino'
--    WHEN sarpers_join.sarpers_ethn_category = '2'
--    THEN 'Hispanic or Latino'
--    ELSE 'None'
--  END new_ethnicity,
--  (SELECT stvlang.stvlang_desc
--  FROM aimsmgr.stvlang
--  WHERE stvlang.stvlang_code = sarpers_join.sarpers_lang_cde_native
--  ) language_native,
--  (SELECT stvnatn.stvnatn_nation
--  FROM aimsmgr.stvnatn
--  WHERE stvnatn.stvnatn_edi_equiv = sarpers_join.sarpers_natn_cde_birth
--  ) birth_country,
--  CASE
--    WHEN NVL(LENGTH(saretry_join.sorcmjr_desc), 0) > 0
--    THEN saretry_join.sorcmjr_desc
--    ELSE
--      (SELECT stvmajr.stvmajr_desc
--      FROM aimsmgr.stvmajr
--      WHERE stvmajr.stvmajr_code = saretry_join.sorcmjr_majr_code
--      )
--  END major,
--  saretry_join.sorccon_majr_code_conc concentration_code,
--  (SELECT stvmajr.stvmajr_desc
--  FROM aimsmgr.stvmajr
--  WHERE stvmajr.stvmajr_code = saretry_join.sorccon_majr_code_conc
--  ) concentration,
--  TO_CHAR(sarhead.sarhead_complete_date, 'MM/DD/YYYY HH12:MI AM') complete_date,
--  (SELECT stvvtyp_join.stvvtyp_desc
--  FROM aimsmgr.sarrfno
--  INNER JOIN
--    ( SELECT stvvtyp.stvvtyp_code, stvvtyp.stvvtyp_desc FROM aimsmgr.stvvtyp
--    )stvvtyp_join
--  ON stvvtyp_join.stvvtyp_code   = sarrfno.sarrfno_ref_no
--  WHERE sarrfno.sarrfno_aidm     = sarhead.sarhead_aidm
--  AND sarrfno.sarrfno_appl_seqno = sarhead.sarhead_appl_seqno
--  AND sarrfno.sarrfno_seqno      = 1
--  AND sarrfno.sarrfno_rfql_cde   = 'V2'
--  ) visa_type,
--  (SELECT sarrfno.sarrfno_ref_no
--  FROM aimsmgr.sarrfno
--  WHERE sarrfno.sarrfno_aidm     = sarhead.sarhead_aidm
--  AND sarrfno.sarrfno_appl_seqno = sarhead.sarhead_appl_seqno
--  AND sarrfno.sarrfno_seqno      = 2
--  AND sarrfno.sarrfno_rfql_cde   = 30
--  ) sevis_id_number,
--  (SELECT sabnstu.sabnstu_id
--  FROM aimsmgr.sabnstu
--  WHERE sabnstu.sabnstu_aidm = sarhead.sarhead_aidm
--  ) web_app_id
  
FROM aimsmgr.sarhead
INNER JOIN
  (SELECT saretry.saretry_aidm,
    saretry.saretry_appl_seqno,
    sarefos_m_join.sorcmjr_majr_code,
    sarefos_m_join.sorcmjr_desc,
    sarefos_c_join.sorccon_majr_code_conc
  FROM aimsmgr.saretry
  INNER JOIN
    (SELECT sarefos.sarefos_aidm,
      sarefos.sarefos_appl_seqno,
      sarefos.sarefos_etry_seqno,
      sarefos.sarefos_flvl_cde,
      sorcmjr_join.sorcmjr_majr_code,
      sorcmjr_join.sorcmjr_desc
    FROM aimsmgr.sarefos
    INNER JOIN
      (SELECT sorcmjr.sorcmjr_cmjr_rule,
        sorcmjr.sorcmjr_majr_code,
        sorcmjr.sorcmjr_desc
      FROM aimsmgr.sorcmjr
      )sorcmjr_join
    ON sorcmjr_join.sorcmjr_cmjr_rule = sarefos.sarefos_lfos_rule
    WHERE sarefos.sarefos_seqno       =
      (SELECT MAX(s7.sarefos_seqno)
      FROM aimsmgr.sarefos s7
      WHERE s7.sarefos_aidm     = sarefos.sarefos_aidm
      AND s7.sarefos_appl_seqno = sarefos.sarefos_appl_seqno
      AND s7.sarefos_etry_seqno = sarefos.sarefos_etry_seqno
      AND s7.sarefos_flvl_cde   = sarefos.sarefos_flvl_cde
      )
    )sarefos_m_join ON sarefos_m_join.sarefos_aidm = saretry.saretry_aidm
  AND sarefos_m_join.sarefos_appl_seqno            = saretry.saretry_appl_seqno
  AND sarefos_m_join.sarefos_flvl_cde              = 'M'
  LEFT OUTER JOIN
    (SELECT sarefos.sarefos_aidm,
      sarefos.sarefos_appl_seqno,
      sarefos.sarefos_etry_seqno,
      sarefos.sarefos_flvl_cde,
      sorccon_join.sorccon_majr_code_conc
    FROM aimsmgr.sarefos
    INNER JOIN
      (SELECT sorccon.sorccon_ccon_rule,
        sorccon.sorccon_majr_code_conc
      FROM aimsmgr.sorccon
      )sorccon_join
    ON sorccon_join.sorccon_ccon_rule = sarefos.sarefos_lfos_rule
    WHERE sarefos.sarefos_seqno       =
      (SELECT MAX(s7.sarefos_seqno)
      FROM aimsmgr.sarefos s7
      WHERE s7.sarefos_aidm     = sarefos.sarefos_aidm
      AND s7.sarefos_appl_seqno = sarefos.sarefos_appl_seqno
      AND s7.sarefos_etry_seqno = sarefos.sarefos_etry_seqno
      AND s7.sarefos_flvl_cde   = sarefos.sarefos_flvl_cde
      )
    )sarefos_c_join ON sarefos_c_join.sarefos_aidm = saretry.saretry_aidm
  AND sarefos_c_join.sarefos_appl_seqno            = saretry.saretry_appl_seqno
  AND sarefos_c_join.sarefos_flvl_cde              = 'C'
  )saretry_join ON saretry_join.saretry_aidm       = sarhead.sarhead_aidm
AND saretry_join.saretry_appl_seqno                = sarhead.sarhead_appl_seqno
INNER JOIN
  (SELECT sarpers.sarpers_aidm,
    sarpers.sarpers_appl_seqno,
    sarpers.sarpers_prefix,
    sarpers.sarpers_first_name,
    sarpers.sarpers_middle_name1,
    sarpers.sarpers_last_name,
    sarpers.sarpers_suffix,
    sarpers.sarpers_former_name,
    sarpers.sarpers_nickname,
    sarpers.sarpers_gender,
    sarpers.sarpers_birth_dte,
    sarpers.sarpers_city_birth,
    sarpers.sarpers_stat_cde_birth,
    sarpers.sarpers_natn_cde_birth,
    sarpers.sarpers_ethn_category,
    sarpers.sarpers_lang_cde_native,
    sarpers.sarpers_natn_cde_citz,
    sarpers.sarpers_citz_cde,
    sarphon_phon_join.sarphon_phone phone_number,
    sarphon_cell_phon_join.sarphon_phone cell_phone,
    sarphon_email_join.sarphon_phone email_address,
    saraddr_join.saraddr_street_line1,
    saraddr_join.saraddr_street_line2,
    saraddr_join.saraddr_city,
    saraddr_join.saraddr_stat_cde,
    saraddr_join.saraddr_zip,
    saraddr_join.saraddr_natn_cde
  FROM aimsmgr.sarpers
  INNER JOIN
    (SELECT saraddr.saraddr_aidm,
      saraddr.saraddr_appl_seqno,
      saraddr.saraddr_pers_seqno,
      saraddr.saraddr_street_line1,
      saraddr.saraddr_street_line2,
      saraddr.saraddr_city,
      saraddr.saraddr_stat_cde,
      saraddr.saraddr_zip,
      saraddr.saraddr_natn_cde
    FROM aimsmgr.saraddr
    WHERE saraddr.saraddr_seqno =
      (SELECT MAX(s5.saraddr_seqno)
      FROM aimsmgr.saraddr s5
      WHERE s5.saraddr_aidm     = saraddr.saraddr_aidm
      AND s5.saraddr_appl_seqno = saraddr.saraddr_appl_seqno
      AND s5.saraddr_pers_seqno = saraddr.saraddr_pers_seqno
      AND s5.saraddr_lcql_cde   = saraddr.saraddr_lcql_cde
      )
    AND saraddr.saraddr_lcql_cde = 'F'
    )saraddr_join
  ON saraddr_join.saraddr_aidm        = sarpers.sarpers_aidm
  AND saraddr_join.saraddr_appl_seqno = sarpers.sarpers_appl_seqno
  AND saraddr_join.saraddr_pers_seqno = sarpers.sarpers_seqno
  LEFT OUTER JOIN
    (SELECT sarphon.sarphon_aidm,
      sarphon.sarphon_appl_seqno,
      sarphon.sarphon_pers_seqno,
      sarphon.sarphon_pqlf_cde,
      sarphon.sarphon_phone
    FROM aimsmgr.sarphon
    WHERE sarphon.sarphon_seqno =
      (SELECT MAX(s6.sarphon_seqno)
      FROM aimsmgr.sarphon s6
      WHERE s6.sarphon_aidm     = sarphon.sarphon_aidm
      AND s6.sarphon_appl_seqno = sarphon.sarphon_appl_seqno
      AND s6.sarphon_pers_seqno = sarphon.sarphon_pers_seqno
      AND s6.sarphon_pqlf_cde   = sarphon.sarphon_pqlf_cde
      )
    )sarphon_phon_join
  ON sarphon_phon_join.sarphon_aidm        = sarpers.sarpers_aidm
  AND sarphon_phon_join.sarphon_appl_seqno = sarpers.sarpers_appl_seqno
  AND sarphon_phon_join.sarphon_pers_seqno = sarpers.sarpers_seqno
  AND sarphon_phon_join.sarphon_pqlf_cde   = 'HP' -- phone number
  LEFT OUTER JOIN
    (SELECT sarphon.sarphon_aidm,
      sarphon.sarphon_appl_seqno,
      sarphon.sarphon_pers_seqno,
      sarphon.sarphon_pqlf_cde,
      sarphon.sarphon_phone
    FROM aimsmgr.sarphon
    WHERE sarphon.sarphon_seqno =
      (SELECT MAX(s6.sarphon_seqno)
      FROM aimsmgr.sarphon s6
      WHERE s6.sarphon_aidm     = sarphon.sarphon_aidm
      AND s6.sarphon_appl_seqno = sarphon.sarphon_appl_seqno
      AND s6.sarphon_pers_seqno = sarphon.sarphon_pers_seqno
      AND s6.sarphon_pqlf_cde   = sarphon.sarphon_pqlf_cde
      )
    )sarphon_cell_phon_join
  ON sarphon_cell_phon_join.sarphon_aidm        = sarpers.sarpers_aidm
  AND sarphon_cell_phon_join.sarphon_appl_seqno = sarpers.sarpers_appl_seqno
  AND sarphon_cell_phon_join.sarphon_pers_seqno = sarpers.sarpers_seqno
  AND sarphon_cell_phon_join.sarphon_pqlf_cde   = 'MO' -- cell phone number
  LEFT OUTER JOIN
    (SELECT sarphon.sarphon_aidm,
      sarphon.sarphon_appl_seqno,
      sarphon.sarphon_pers_seqno,
      sarphon.sarphon_pqlf_cde,
      sarphon.sarphon_phone
    FROM aimsmgr.sarphon
    WHERE sarphon.sarphon_seqno =
      (SELECT MAX(s6.sarphon_seqno)
      FROM aimsmgr.sarphon s6
      WHERE s6.sarphon_aidm     = sarphon.sarphon_aidm
      AND s6.sarphon_appl_seqno = sarphon.sarphon_appl_seqno
      AND s6.sarphon_pers_seqno = sarphon.sarphon_pers_seqno
      AND s6.sarphon_pqlf_cde   = sarphon.sarphon_pqlf_cde
      )
    )sarphon_email_join
  ON sarphon_email_join.sarphon_aidm        = sarpers.sarpers_aidm
  AND sarphon_email_join.sarphon_appl_seqno = sarpers.sarpers_appl_seqno
  AND sarphon_email_join.sarphon_pers_seqno = sarpers.sarpers_seqno
  AND sarphon_email_join.sarphon_pqlf_cde   = 'EM' -- email address
  WHERE sarpers.sarpers_seqno               =
    (SELECT MAX(s4.sarpers_seqno)
    FROM aimsmgr.sarpers s4
    WHERE s4.sarpers_aidm     = sarpers.sarpers_aidm
    AND s4.sarpers_appl_seqno = sarpers.sarpers_appl_seqno
    )
  )sarpers_join ON sarpers_join.sarpers_aidm = sarhead.sarhead_aidm
AND sarpers_join.sarpers_appl_seqno          = sarhead.sarhead_appl_seqno
LEFT OUTER JOIN
  (SELECT sarctrl.sarctrl_aidm,
    sarctrl.sarctrl_appl_seqno,
    sarctrl.sarctrl_appl_no_saradap,
    spriden_join.spriden_pidm,
    spriden_join.spriden_id
  FROM aimsmgr.sarctrl
  INNER JOIN
    (SELECT spriden.spriden_pidm,
      spriden.spriden_id,
      spriden.spriden_change_ind
    FROM aimsmgr.spriden
    )spriden_join
  ON spriden_join.spriden_pidm               = sarctrl.sarctrl_pidm
  AND spriden_join.spriden_change_ind       IS NULL
  )sarctrl_join ON sarctrl_join.sarctrl_aidm = sarhead.sarhead_aidm
AND sarctrl_join.sarctrl_appl_seqno          = sarhead.sarhead_appl_seqno
WHERE sarhead.sarhead_process_ind           != 'P'
AND (sarhead.sarhead_wapp_code               = 'W4'
OR sarhead.sarhead_wapp_code                 = 'W5')
AND sarhead.sarhead_term_code_entry         >= '201810'
--ORDER BY sarpers_last_name,
--  sarpers_first_name
--  ,
--  appl_seqno DESC 
  )
  ;
