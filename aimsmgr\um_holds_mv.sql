--Last refresh 05-27-2022 11:55 AM
SELECT 
  OWNER, 
  MVIEW_NAME, 
  to_char(last_refresh_date, 'yyyymmddhh24miss') LAST_REFRESH_DATE,
  to_char(last_refresh_date, 'MM-DD-YYYY HH:MI AM') LAST_REFRESH_DAY
FROM all_mviews
WHERE owner = 'AIMSMGR'
AND mview_name = 'UM_HOLDS_MV';

----Create MV with refresh schedule  
--CREATE MATERIALIZED VIEW UM_HOLDS_MV
--REFRESH
--ON DEMAND
---- START WITH (SYSDATE) 
-- START WITH TO_DATE('05-28-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
-- NEXT (SYSDATE + 1) WITH ROWID
--AS SELECT * FROM um_holds;

--Alter refresh schedule MV
ALTER MATERIALIZED VIEW UM_HOLDS_MV 
REFRESH
ON DEMAND
 START WITH TO_DATE('05-28-2022 06:00 AM','MM-DD-YYYY HH:MI AM')
 NEXT (SYSDATE + 1) WITH ROWID
  ;
  
--Force a refresh of MV
EXEC DBMS_MVIEW.refresh('UM_HOLDS_MV');