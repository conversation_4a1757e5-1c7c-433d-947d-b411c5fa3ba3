---*-WRITE SECURITY s0063s-X;USER:LFAULKNE;History Honor Society - Phi Alpha Theta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by <PERSON> 01-23-2014
---*-WRITE SECURITY s0063s-X;USER:B<PERSON><PERSON><PERSON>;History Honor Society - Phi Alpha Theta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by <PERSON> 01-23-2014
---*-WRITE SECURITY s0063s-X;USER:ABEAUCHE;History Honor Society - Phi Alpha Theta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by <PERSON><PERSON> 01-15-2019
---*-WRITE SECURITY s0063s-X;USER:SHELEW;History Honor Society - Phi Alpha Theta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by <PERSON><PERSON> Robertson 02-5-2019
---*-WRITE SECURITY s0063s-X;USER:MARKPY;History Honor Society - Phi Alpha Theta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by <PERSON><PERSON> Robertson 02-5-2019

---*-WRITE SECURITY s0064s-X;USER:MKIETZMA;English Honor Society - Sigma Tau Delta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Debbie Samida 01-29-2014
---*-WRITE SECURITY s0064s-X;USER:MANDYST;English Honor Society - Sigma Tau Delta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Debbie Samida 01-29-2014
---*-WRITE SECURITY s0064s-X;USER:AIBRUBAK;English Honor Society - Sigma Tau Delta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Debbie Samida 01-29-2014
--
---*-WRITE SECURITY s0065s-X;USER:LMCTIER;Sociology Honor Society - Alpha Kappa Delta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Debbie Samida 02-14-2014
---*-WRITE SECURITY s0065s-X;USER:HLAUBE;Sociology Honor Society - Alpha Kappa Delta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Debbie Samida 02-14-2014
---*-WRITE SECURITY s0065s-X;USER:MANDYST;Sociology Honor Society - Alpha Kappa Delta (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Debbie Samida 02-14-2014
--
---*-WRITE SECURITY s0066s-X;USER:ABEAUCHE;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019
---*-WRITE SECURITY s0066s-X;USER:SHELEW;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019
---*-WRITE SECURITY s0066s-X;USER:LFAULKNE;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019
---*-WRITE SECURITY s0066s-X;USER:BITZK;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019
---*-WRITE SECURITY s0066s-X;USER:MANDYST;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019
---*-WRITE SECURITY s0066s-X;USER:KIMSAKS;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019
---*-WRITE SECURITY s0066s-X;USER:MARKPY;Political Science Honor Society - Pi Sigma Alpha (DataMart);S;Honors Info;&REPOSITORY.EVAL; Deployed by Gentry Robertson 04-10-2019




create or replace view HNR_history_honor_society_eligibility_list as
with pop_sel as(
--"s0063s00 History Honor Society Eligibility List  Run on: <+0>&DATEMDYY <+0> "
--"(Criteria:  Undergraduate level only, Registered for term selected, "
--"            Overall GPA 3.0 or higher, at least 12 credits in HIS completed at UM-Flint,"
--"            HIS subject GPA of 3.1 or higher)"
-- matches wf
    select 
    um_student_data.*
    from um_student_data
    
--    where um_student_data.sd_term_code = '202310'
    where um_student_data.student_status_code = 'AS'
    and um_student_data.registered_ind = 'Y'
    and um_student_data.report_level_code = 'UG' 
    and um_student_data.overall_gpa >= 3.0
--    and um_student_data.overall_hours_earned >= 12
--    and ((um_student_data.report_level_code = 'UG' and um_student_data.overall_gpa >= 3.0) or
--
    --and sd_pidm = 62003 -- 52503 -- 40727
    order by
    sd_pidm
)
, history_sel as(
    select
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code,
    sum(um_student_transcript.hours) hist_credit_hours,
    sum(case when shrgrde.shrgrde_gpa_ind = 'Y' then um_student_transcript.hours else 0 end) hist_gpa_hours,
    sum(case when shrgrde.shrgrde_gpa_ind = 'Y' then um_student_transcript.hours else 0 end * shrgrde.shrgrde_quality_points) hist_quality_points    
    from pop_sel
    inner join um_student_transcript on um_student_transcript.pidm = pop_sel.sd_pidm
                                     and um_student_transcript.levl = pop_sel.report_level_code
                                     and um_student_transcript.type = 'I'
                                     and um_student_transcript.subject = 'HIS'
                                     and um_student_transcript.grade not in ('YW', 'Y', '*', 'I', 'W', 'IW')
                                     and nvl(um_student_transcript.repeat_ind, 'I') in ('I', 'A')
    left outer join aimsmgr.shrgrde_ext shrgrde on shrgrde.shrgrde_code = um_student_transcript.grade
                                           and shrgrde.shrgrde_levl_code = um_student_transcript.levl

    group by
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code
)
select 
history_sel.report_level_code,
um_student_data.sd_term_code,
um_student_data.sd_term_desc,
um_demographic.last_name,
um_demographic.first_name,
um_demographic.middle_initial,
um_demographic.name_suffix,
um_demographic.umid,
um_demographic.a1_street_line1,
um_demographic.a1_street_line2,
um_demographic.a1_city,
um_demographic.a1_state_code,
um_demographic.a1_state_desc,
um_demographic.a1_zip,
um_demographic.a1_area_code,
um_demographic.a1_phone_number,
um_demographic.mo_area_code,
um_demographic.mo_phone_number,
um_demographic.ca_email,
um_student_data.primary_level_code,
um_student_data.primary_level_desc,
um_student_data.primary_major_1,
um_student_data.primary_major_1_desc,
um_student_data.primary_major_2,
um_student_data.primary_major_2_desc,
um_student_data.second_major_1,
um_student_data.second_major_1_desc,
um_student_data.primary_minor_1,
um_student_data.primary_minor_1_desc,
um_student_data.overall_hours_earned,
um_student_data.overall_gpa,
--history_sel.hist_course_count,
history_sel.hist_credit_hours,
history_sel.hist_gpa_hours,
trunc((case when history_sel.hist_gpa_hours <> 0 then (history_sel.hist_quality_points / history_sel.hist_gpa_hours) else 0 end), 3) hist_gpa

from history_sel
inner join um_student_data on um_student_data.sd_pidm = history_sel.pidm
                           and um_student_data.sd_term_code = history_sel.sd_term_code
inner join um_demographic on um_demographic.dm_pidm = history_sel.pidm
where (case when history_sel.hist_gpa_hours <> 0 then (history_sel.hist_quality_points / history_sel.hist_gpa_hours) else 0 end) >= 3.1
and history_sel.hist_credit_hours >= 12

order by
1, 2, 3
;

create or replace view HNR_english_honor_society_eligibility_list as
with pop_sel as(
-- s0064s 
--"Undergraduate Level Criteria:  Registered for selected term, "
--"Overall GPA 3.0 or higher, ENG GPA 3.5 or higher, completed 3 or more terms at UM-Flint, completed 2 English courses beyond ENG 112."
--" "
--"Graduate Level Criteria: Registered for selected term, ENGL major, Overall GPA 3.3 or higher, 6 or more graduate ENG credits completed"
--" "
--"s0064s00 English Honor Society Eligibility List  Run on: <+0>&DATEMDYY <+0> "
-- matches wf except for the U2 person,  maybe not include these?
    select 
    um_student_data.*,
    (
        select 
        --shrtgpa_pidm,
        count(distinct shrtgpa_term_code)
        from aimsmgr.shrtgpa_ext shrtgpa
        where shrtgpa_gpa_type_ind = 'I'
        and shrtgpa_pidm = um_student_data.sd_pidm
    ) term_count
    
    -- kind of off by one between shrtgpa ans um_student_transcript
    --(
    --select
    --count(distinct um_student_transcript.term)
    --from um_student_transcript
    --where um_student_transcript.pidm = um_student_data.sd_pidm
    --and um_student_transcript.type in ('I')
    --) trans_count,
    
    from um_student_data
    
--    where um_student_data.sd_term_code >= '202310'
    where um_student_data.student_status_code = 'AS'
    and um_student_data.registered_ind = 'Y'
    and um_student_data.student_type_code in ('C','F','N','R','T')
    and ((um_student_data.report_level_code = 'UG' and um_student_data.overall_gpa >= 3.0) or
         (um_student_data.overall_gpa >= 3.3 and (um_student_data.primary_major_1 = 'ENGL' or
                                                  um_student_data.primary_major_2 = 'ENGL' or
                                                  um_student_data.second_major_1 = 'ENGL' or
                                                  um_student_data.second_major_2 = 'ENGL' or
                                                  um_student_data.third_major_1 = 'ENGL' or
                                                  um_student_data.third_major_2 = 'ENGL')))
    --and sd_pidm = 62003 -- 52503 -- 40727
    order by
    sd_pidm
)
, eng_sel as(
    select
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code,
    pop_sel.term_count,
    sum(um_student_transcript.hours) eng_credit_hours,
    sum(case when shrgrde.shrgrde_gpa_ind = 'Y' then um_student_transcript.hours else 0 end) eng_gpa_hours,
    sum(case when shrgrde.shrgrde_gpa_ind = 'Y' then um_student_transcript.hours else 0 end * shrgrde.shrgrde_quality_points) eng_quality_points,
    sum(case when um_student_transcript.course > '112' and um_student_transcript.course != '150' then 1 else 0 end) eng_course_count
    
    from pop_sel
    inner join um_student_transcript on um_student_transcript.pidm = pop_sel.sd_pidm
                                     and um_student_transcript.levl = pop_sel.report_level_code
                                     and um_student_transcript.type = 'I'
                                     and um_student_transcript.subject = 'ENG'
                                     and um_student_transcript.grade not in ('YW', 'Y', '*', 'I', 'W', 'IW')
                                     and nvl(um_student_transcript.repeat_ind, 'I') in ('I', 'A')
    left outer join aimsmgr.shrgrde_ext shrgrde on shrgrde.shrgrde_code = um_student_transcript.grade
                                           and shrgrde.shrgrde_levl_code = um_student_transcript.levl
    
    where (pop_sel.term_count > 2 and pop_sel.report_level_code = 'UG') or
          (pop_sel.report_level_code = 'GR')
    
    group by
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code,
    pop_sel.term_count
)
select 
eng_sel.report_level_code,
um_student_data.sd_term_code,
um_student_data.sd_term_desc,
um_demographic.last_name,
um_demographic.first_name,
um_demographic.middle_initial,
um_demographic.name_suffix,
um_demographic.umid,
um_demographic.a1_street_line1,
um_demographic.a1_street_line2,
um_demographic.a1_city,
um_demographic.a1_state_code,
um_demographic.a1_state_desc,
um_demographic.a1_zip,
um_demographic.a1_area_code,
um_demographic.a1_phone_number,
um_demographic.mo_area_code,
um_demographic.mo_phone_number,
um_demographic.ca_email,
um_student_data.primary_level_code,
um_student_data.primary_level_desc,
um_student_data.primary_major_1,
um_student_data.primary_major_1_desc,
um_student_data.primary_major_2,
um_student_data.primary_major_2_desc,
um_student_data.second_major_1,
um_student_data.second_major_1_desc,
um_student_data.primary_minor_1,
um_student_data.primary_minor_1_desc,
um_student_data.overall_hours_earned,
um_student_data.overall_gpa,
eng_sel.eng_course_count eng_courses_beyond_112,
eng_sel.eng_credit_hours,
eng_sel.eng_gpa_hours,
trunc((case when eng_sel.eng_gpa_hours <> 0 then (eng_sel.eng_quality_points / eng_sel.eng_gpa_hours) else 0 end), 3) eng_gpa,
eng_sel.term_count

from eng_sel
inner join um_student_data on um_student_data.sd_pidm = eng_sel.pidm
                           and um_student_data.sd_term_code = eng_sel.sd_term_code
inner join um_demographic on um_demographic.dm_pidm = eng_sel.pidm
where ((case when eng_sel.eng_gpa_hours <> 0 then (eng_sel.eng_quality_points / eng_sel.eng_gpa_hours) else 0 end) >= 3.5)
and ((eng_sel.term_count >= 3 and eng_sel.eng_course_count >= 2 and eng_sel.report_level_code = 'UG') or
     (eng_sel.eng_credit_hours >= 6 and eng_sel.report_level_code = 'GR'))

order by
1, 2, 3

--group by
--pop_sel.report_level_code
;

create or replace view HNR_sociology_honor_society_eligibility_list as
with pop_sel as(
-- s0065s
--"s0065s00 Sociology Honor Society Eligibility List  Run on: <+0>&DATEMDYY <+0> "
--"(Criteria:  Undergraduate level only, Registered for term selected, "
--"            SOC major or minor, Overall Earned hours 55 or more, "
--"            Overall GPA 3.3 or higher, at least 4 courses in SOC, "
--"            2 completed at UM-Flint, SOC subject GPA of 3.0 or higher)"
    select 
    um_student_data.*
    from um_student_data
    
--    where um_student_data.sd_term_code = '202310'
    where um_student_data.student_status_code = 'AS'
    and um_student_data.registered_ind = 'Y'
    and um_student_data.report_level_code = 'UG' 
    and um_student_data.overall_gpa >= 3.3
    and um_student_data.overall_hours_earned >= 55

    and (um_student_data.primary_major_1 = 'SOC' or
         um_student_data.primary_major_2 = 'SOC' or
         um_student_data.primary_minor_1 = 'SOC' or
         um_student_data.primary_minor_2 = 'SOC' or
         um_student_data.second_major_1 = 'SOC' or
         um_student_data.second_major_2 = 'SOC' or
         um_student_data.second_minor_1 = 'SOC' or
         um_student_data.second_minor_2 = 'SOC' or
         um_student_data.third_major_1 = 'SOC' or
         um_student_data.third_major_2 = 'SOC' or
         um_student_data.third_minor_1 = 'SOC' or
         um_student_data.third_minor_2 = 'SOC')
    --and sd_pidm = 62003 -- 52503 -- 40727
    order by
    sd_pidm
)
, social_sel as(
    select
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code,
    sum(um_student_transcript.hours) soc_credit_hours,
    sum(case when nvl(shrgrde.shrgrde_gpa_ind, 'X') = 'Y' then um_student_transcript.hours else 0 end) soc_gpa_hours,
    sum(case when nvl(shrgrde.shrgrde_gpa_ind, 'X') = 'Y' then um_student_transcript.hours else 0 end * shrgrde.shrgrde_quality_points) soc_quality_points,
    sum(case when um_student_transcript.type = 'I' then 1 else 0 end) soc_i_ctr,
    sum(case when um_student_transcript.type = 'T' then 1 else 0 end) soc_t_ctr,
    sum(case when um_student_transcript.type in ('I', 'T') then 1 else 0 end) soc_it_ctr
    
    from pop_sel
    inner join um_student_transcript on um_student_transcript.pidm = pop_sel.sd_pidm
                                     and um_student_transcript.levl = pop_sel.report_level_code
                                     and um_student_transcript.subject = 'SOC'
                                     and um_student_transcript.grade not in ('YW', 'Y', '*', 'I', 'W', 'IW')
                                     and nvl(um_student_transcript.repeat_ind, 'I') in ('I', 'A')
    left outer join aimsmgr.shrgrde_ext shrgrde on shrgrde.shrgrde_code = um_student_transcript.grade
                                           and shrgrde.shrgrde_levl_code = um_student_transcript.levl
    
    group by
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code
)
select 
social_sel.report_level_code,
um_student_data.sd_term_code,
um_student_data.sd_term_desc,
um_demographic.last_name,
um_demographic.first_name,
um_demographic.middle_initial,
um_demographic.name_suffix,
um_demographic.umid,
um_demographic.a1_street_line1,
um_demographic.a1_street_line2,
um_demographic.a1_city,
um_demographic.a1_state_code,
um_demographic.a1_state_desc,
um_demographic.a1_zip,
um_demographic.a1_area_code,
um_demographic.a1_phone_number,
um_demographic.mo_area_code,
um_demographic.mo_phone_number,
um_demographic.ca_email,
um_student_data.primary_level_code,
um_student_data.primary_level_desc,
um_student_data.primary_major_1,
um_student_data.primary_major_1_desc,
um_student_data.primary_major_2,
um_student_data.primary_major_2_desc,
um_student_data.primary_minor_1,
um_student_data.primary_minor_1_desc,
um_student_data.primary_minor_2,
um_student_data.primary_minor_2_desc,
um_student_data.second_major_1,
um_student_data.second_major_1_desc,
um_student_data.second_major_2,
um_student_data.second_major_2_desc,
um_student_data.second_minor_1,
um_student_data.second_minor_1_desc,
um_student_data.second_minor_2,
um_student_data.second_minor_2_desc,
um_student_data.overall_hours_earned,
um_student_data.overall_gpa,
social_sel.soc_it_ctr,
social_sel.soc_i_ctr,
social_sel.soc_t_ctr,
social_sel.soc_credit_hours,
social_sel.soc_gpa_hours,
social_sel.soc_quality_points,
trunc((case when nvl(social_sel.soc_gpa_hours, 0) <> 0 then (social_sel.soc_quality_points / social_sel.soc_gpa_hours) else 0 end), 3) soc_gpa

from social_sel
inner join um_student_data on um_student_data.sd_pidm = social_sel.pidm
                           and um_student_data.sd_term_code = social_sel.sd_term_code
inner join um_demographic on um_demographic.dm_pidm = social_sel.pidm
where (case when nvl(social_sel.soc_gpa_hours, 0) <> 0 then (social_sel.soc_quality_points / social_sel.soc_gpa_hours) else 0 end) >= 3.0
and social_sel.soc_i_ctr >= 2
and social_sel.soc_it_ctr >= 4

order by
1, 2, 3
;

create or replace view HNR_political_science_honor_society_eligibility_list as
with pop_sel as(
-- s0066s
--"s0066s00 Political Science Honor Society Eligibility List  Run on: <+0>&DATEMDYY <+0> "
--"(Criteria:  Undergraduate level only, Registered for term selected, Overall GPA 3.0 or higher, "
--"            at least 10 credits in POL completed at UM-Flint, POL subject GPA of 3.0 or higher)"
    select 
    um_student_data.*    
    from um_student_data
    
--    where um_student_data.sd_term_code = '202310'
    where um_student_data.student_status_code = 'AS'
    and um_student_data.registered_ind = 'Y'
    and um_student_data.report_level_code = 'UG' 
    and um_student_data.overall_gpa >= 3.0
    and um_student_data.overall_hours_earned >= 10
--    and sd_pidm = 168091 --62003 -- 52503 -- 40727

    order by
    sd_pidm
)
, pol_sel as(
    select
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code,
    sum(um_student_transcript.hours) pol_credit_hours,
    sum(case when nvl(shrgrde.shrgrde_gpa_ind, 'X') = 'Y' then um_student_transcript.hours else 0 end) pol_gpa_hours,
    sum(case when nvl(shrgrde.shrgrde_gpa_ind, 'X') = 'Y' then um_student_transcript.hours else 0 end * shrgrde.shrgrde_quality_points) pol_quality_points,
    sum(case when um_student_transcript.type = 'I' then 1 else 0 end) pol_i_ctr --,
--    sum(case when um_student_transcript.type = 'T' then 1 else 0 end) pol_t_ctr,
--    sum(case when um_student_transcript.type in ('I', 'T') then 1 else 0 end) pol_it_ctr
    
    from pop_sel
    inner join um_student_transcript on um_student_transcript.pidm = pop_sel.sd_pidm
                                     and um_student_transcript.levl = pop_sel.report_level_code
                                     and um_student_transcript.type = 'I'
                                     and um_student_transcript.subject = 'POL'
                                     and um_student_transcript.course >= '200'
                                     and um_student_transcript.grade not in ('YW', 'Y', '*', 'I', 'W', 'IW')
                                     and nvl(um_student_transcript.repeat_ind, 'I') in ('I', 'A')
    left outer join aimsmgr.shrgrde_ext shrgrde on shrgrde.shrgrde_code = um_student_transcript.grade
                                           and shrgrde.shrgrde_levl_code = um_student_transcript.levl
    
    group by
    um_student_transcript.pidm,
    pop_sel.sd_term_code,
    pop_sel.report_level_code
    
    order by
    1, 2, 3
--;
)
select 
pol_sel.report_level_code,
um_student_data.sd_term_code,
um_student_data.sd_term_desc,
um_demographic.last_name,
um_demographic.first_name,
um_demographic.middle_initial,
um_demographic.name_suffix,
um_demographic.dm_pidm,
um_demographic.umid,
um_demographic.a1_street_line1,
um_demographic.a1_street_line2,
um_demographic.a1_city,
um_demographic.a1_state_code,
um_demographic.a1_state_desc,
um_demographic.a1_zip,
um_demographic.a1_area_code,
um_demographic.a1_phone_number,
um_demographic.mo_area_code,
um_demographic.mo_phone_number,
um_demographic.ca_email,
um_student_data.primary_level_code,
um_student_data.primary_level_desc,
um_student_data.primary_major_1,
um_student_data.primary_major_1_desc,
um_student_data.primary_major_2,
um_student_data.primary_major_2_desc,
um_student_data.second_major_1,
um_student_data.second_major_1_desc,
um_student_data.primary_minor_1,
um_student_data.primary_minor_1_desc,
um_student_data.overall_hours_earned,
um_student_data.overall_gpa,
--pol_sel.pol_it_ctr,
pol_sel.pol_i_ctr,
--pol_sel.pol_t_ctr,
pol_sel.pol_credit_hours,
pol_sel.pol_gpa_hours,
pol_sel.pol_quality_points,
trunc((case when nvl(pol_sel.pol_gpa_hours, 0) <> 0 then (pol_sel.pol_quality_points / pol_sel.pol_gpa_hours) else 0 end), 3) pol_gpa

from pol_sel
inner join um_student_data on um_student_data.sd_pidm = pol_sel.pidm
                           and um_student_data.sd_term_code = pol_sel.sd_term_code
inner join um_demographic on um_demographic.dm_pidm = pol_sel.pidm
where (case when nvl(pol_sel.pol_gpa_hours, 0) <> 0 then (pol_sel.pol_quality_points / pol_sel.pol_gpa_hours) else 0 end) >= 3.0
and pol_sel.pol_credit_hours >= 10

order by
1, 2, 3
;


