create or replace view load_next_2_reg_term_daily as
with t1 as(
    select
    ct.day_of_term,
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.run_date = trunc(sysdate)
    and ct.term_code = (select um_current_term.next_2_bridge_term from um_current_term)
    group by
    ct.day_of_term,
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc
)
, t2 as(
    select 
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc,
    sum(ct.head_count) head_count,
    sum(ct.total_credit_hours) total_credit_hours
    from um_reg_student_daily ct
    where ct.day_of_term = (select distinct t1.day_of_term from t1)
    and ct.term_code = (select distinct (t1.term_code - 100) from t1) --'202030'
    group by
    ct.term_code,
    ct.term_desc,
    ct.rpt_levl_code,
    ct.rpt_level_desc,
    ct.rpt_student_type_code,
    ct.rpt_student_type_desc
)
, td as(
    select 
    td_term_code,
    report_level_code,
    report_level_desc,
    student_type_code,
    student_type_desc,
    sum(head_count) head_count,
    sum(total_credit_hours) total_credit_hours
    from td_term_summary
    where td_term_code = (select distinct (t1.term_code - 100) from t1)
    group by
    td_term_code,
    report_level_code,
    report_level_desc,
    student_type_code,
    student_type_desc
)
, pop_sel as(
    select
    dot.day_of_term,
    dot.term_code,
    dot.term_desc,
    cds.level_code,
    cds.level_desc,
    cds.student_type,
    cds.student_desc
    from (
        select
        t1.rpt_levl_code level_code,
        t1.rpt_level_desc level_desc,
        t1.rpt_student_type_code student_type,
        t1.rpt_student_type_desc student_desc
        from t1
        union
        select
        t2.rpt_levl_code,
        t2.rpt_level_desc,
        t2.rpt_student_type_code,
        t2.rpt_student_type_desc
        from t2    
        union
        select
        td.report_level_code,
        td.report_level_desc,
        td.student_type_code,
        td.student_type_desc
        from td
    ) cds
    inner join(
        select
        distinct t1.day_of_term,
        t1.term_code,
        t1.term_desc
        from t1
    ) dot on dot.day_of_term != 9999999
)
select
pop_sel.day_of_term,
pop_sel.term_code,
pop_sel.term_desc,
pop_sel.level_code,
pop_sel.level_desc,
pop_sel.student_type,
pop_sel.student_desc,
nvl(t1.head_count, 0) t1_head_count,
nvl(t1.total_credit_hours, 0) t1_total_credit_hours,
nvl(t2.head_count, 0) t2_head_count,
nvl(t2.total_credit_hours, 0) t2_total_credit_hours,
nvl(td.head_count, 0) td_head_count,
nvl(td.total_credit_hours, 0) td_total_credit_hours

from pop_sel
left outer join t1 on t1.rpt_levl_code = pop_sel.level_code
                   and t1.rpt_student_type_code = pop_sel.student_type
left outer join t2 on t2.rpt_levl_code = pop_sel.level_code
                   and t2.rpt_student_type_code = pop_sel.student_type
left outer join td on td.report_level_code = pop_sel.level_code
                   and td.student_type_code = pop_sel.student_type

order by
3, 4
;