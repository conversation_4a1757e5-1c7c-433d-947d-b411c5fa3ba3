SELECT 
IA_STUDENT_TYPE_CODE,
PRIMARY_LEVEL_CODE,
COUNT (*)
FROM IA_TD_STUDENT_DATA
WHERE SD_TERM_CODE = 201810
AND REGISTERED_IND = 'Y'
AND IA_STUDENT_TYPE_CODE IN ('F','T')
GROUP BY
IA_STUDENT_TYPE_CODE,
PRIMARY_LEVEL_CODE
;

SELECT 
CO_PIDM,
CO_TERM_CODE_KEY,
CO_IA_STUDENT_TYPE_CODE
FROM IA_COHORT_POP
WHERE 
CO_TERM_CODE_KEY = 201810

MINUS

SELECT 
CO_PIDM,
CO_TERM_CODE_KEY,
CO_IA_STUDENT_TYPE_CODE
FROM IA_COHORT_POP_UNDUP
WHERE 
CO_TERM_CODE_KEY = 201810
;

SELECT SD_PIDM, SD_TERM_CODE, IA_STUDENT_TYPE_CODE
FROM IA_TD_STUDENT_DATA WHERE SD_PIDM = 166452 AND REGISTERED_IND = 'Y'
;

SELECT
count (*),
AVG (HSCH_GPA),
AVG (SAT_TOTAL_COMBINED_S10)
FROM IA_TD_STUDENT_DATA sd
left join um_test_score ts on ts.pidm = sd.sd_pidm
WHERE SD_TERM_CODE = 201810
AND REGISTERED_IND = 'Y'
AND IA_STUDENT_TYPE_CODE IN ('F')
;

SELECT SD_PIDM, SD_TERM_CODE, IA_STUDENT_TYPE_CODE
FROM IA_TD_STUDENT_DATA WHERE SD_PIDM = 166452 AND REGISTERED_IND = 'Y'
;
create table IA_GR_COHORT_PERSIST_TBL as (
select * from IA_GR_COHORT_PERSIST)
;

