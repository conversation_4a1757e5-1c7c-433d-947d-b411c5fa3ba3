--In class only students
select count(distinct sd_pidm)from td_student_data 
left join
(select distinct pidm,SECTION_NUMBER from td_registration_detail where SECTION_NUMBER like 'W%' and TD_TERM_CODE = '201510') popcel
on td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and sd_term_code = '201510' and online_courses_only_ind = 'N' and popcel.SECTION_NUMBER is null
;
--Mixed students
select count(distinct sd_pidm)from td_student_data 
inner join
(select distinct pidm from td_registration_detail where SECTION_NUMBER like 'W%' and TD_TERM_CODE = '201510') popcel
on td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and sd_term_code = '201510' and online_courses_only_ind = 'N' 
;
--Online only students
select count(distinct sd_pidm)from td_student_data 
where registered_ind = 'Y' and sd_term_code = '201510' and online_courses_only_ind = 'Y' 
;
--IN CLASS ONLY GRADUATES
select COUNT(*) from td_demographic inner join
(select distinct sd_pidm, IA_STUDENT_TYPE_CODE,SD_TERM_CODE, Report_Level_Code from td_student_data 
left join
(select distinct pidm,SECTION_NUMBER from td_registration_detail where SECTION_NUMBER like 'W%' and TD_TERM_CODE = '201510') popcel
on td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and sd_term_code = '201510' and online_courses_only_ind = 'N' and popcel.SECTION_NUMBER is null) POPCEL1
on td_demographic.dm_pidm = POPCEL1.sd_pidm and  td_demographic.td_term_code = POPCEL1.sd_term_code AND POPCEL1.Report_Level_Code = 'GR'
;
--IN CLASS ONLY DEGREE/SEEKING UNDERGRADS
select COUNT(*) from td_demographic inner join
(select distinct sd_pidm, IA_STUDENT_TYPE_CODE,SD_TERM_CODE, Report_Level_Code from td_student_data 
left join
(select distinct pidm,SECTION_NUMBER from td_registration_detail where SECTION_NUMBER like 'W%' and TD_TERM_CODE = '201510') popcel
on td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and sd_term_code = '201510' and online_courses_only_ind = 'N' and popcel.SECTION_NUMBER is null) POPCEL1
on td_demographic.dm_pidm = POPCEL1.sd_pidm and  td_demographic.td_term_code = POPCEL1.sd_term_code AND POPCEL1.Report_Level_Code = 'UG'
and (
IA_STUDENT_TYPE_CODE = 'C' or 
IA_STUDENT_TYPE_CODE = 'R' or
IA_STUDENT_TYPE_CODE = 'F' or
IA_STUDENT_TYPE_CODE = 'T' )
;
--IN CLASS ONLY NON-DEGREE/SEEKING UNDERGRADS
select COUNT(*) from td_demographic inner join
(select distinct sd_pidm, IA_STUDENT_TYPE_CODE,SD_TERM_CODE, Report_Level_Code from td_student_data 
left join
(select distinct pidm,SECTION_NUMBER from td_registration_detail where SECTION_NUMBER like 'W%' and TD_TERM_CODE = '201510') popcel
on td_student_data.sd_pidm = popcel.pidm
where registered_ind = 'Y' and sd_term_code = '201510' and online_courses_only_ind = 'N' and popcel.SECTION_NUMBER is null) POPCEL1
on td_demographic.dm_pidm = POPCEL1.sd_pidm and  td_demographic.td_term_code = POPCEL1.sd_term_code AND POPCEL1.Report_Level_Code = 'UG'
and (
IA_STUDENT_TYPE_CODE = 'D' or 
IA_STUDENT_TYPE_CODE = 'E' or
IA_STUDENT_TYPE_CODE = 'G' or
IA_STUDENT_TYPE_CODE = 'S' or
IA_STUDENT_TYPE_CODE = 'X'
)
;
--NEW NON-DEGREE/SEEKING UNDERGRADS (D5)
WITH DP1 AS(
SELECT 
SD.SD_PIDM,
SD.SD_TERM_CODE
FROM IA_TD_STUDENT_DATA SD
WHERE 
IA_STUDENT_TYPE_CODE IN ('D','E','G','S') 
AND SD_TERM_CODE = 201810
AND SD.REPORT_LEVEL_CODE = 'UG'
AND REGISTERED_IND = 'Y'
),MT AS(
SELECT
DP1.SD_PIDM,
MIN(SD1.SD_TERM_CODE)MIN_TERM_CODE
FROM DP1
LEFT JOIN IA_TD_STUDENT_DATA SD1 ON DP1.SD_PIDM = SD1.SD_PIDM
WHERE SD1.REGISTERED_IND = 'Y'
GROUP BY DP1.SD_PIDM
)
SELECT COUNT(*) NEW_NCFDS
FROM MT
WHERE MIN_TERM_CODE IN ('201740','201810')

;