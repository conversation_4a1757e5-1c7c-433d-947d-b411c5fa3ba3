SELECT 
DISTINCT 
STVMAJR.STVMAJR_DESC MAJR_DESC, 
STVMAJR.STVMAJR_CODE MAJR_CODE,
STVMAJR.STVMAJR_CIPC_CODE MAJR_CIPC_CODE,
SUBSTR(STVMAJR.STVMAJR_CIPC_CODE,1,2)||'.'||SUBSTR(STVMAJR.STVMAJR_CIPC_CODE,3,6) CIP_CODE,
NVL (STVMAJR.STVMAJR_VALID_MAJOR_IND,'N') MAJR_IND,
NVL (STVMAJR.STVMAJR_VALID_MINOR_IND,'N')MINR_IND,
NVL (STVMAJR.STVMAJR_VALID_CONCENTRATN_IND,'N')CONC_IND,
STVMAJR.STVMAJR_ACTIVITY_DATE
from STVMAJR
WHERE
----STVMAJR_ACTIVITY_DATE >= '01-JUL-16'
----AND 
STVMAJR_VALID_MAJOR_IND = 'Y'
or STVMAJR_VALID_MINOR_IND = 'Y' 
or STVMAJR_VALID_CONCENTRATN_IND = 'Y'
ORDER BY
STVMAJR_CIPC_CODE
;