--------------------------------------------------------
--  File created - Saturday-December-09-2023   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View TRANSFER_SCHOLARSHIP_OSHER_CRANKSTART
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "UGMGR"."TRANSFER_SCHOLARSHIP_OSHER_CRANKSTART" ("UMID", "FIRST_NAME", "LAST_NAME", "UNIQNAME", "CA_EMAIL", "HM_EMAIL_1", "TERM_CODE_ENTRY_DESC", "SORDEGR_ATTEND_FROM", "SORDEGR_ATTEND_TO", "SORDEGR_DEGC_CODE", "SORDEGR_ACTIVITY_DATE", "SORDEGR_PRIMARY_IND", "SORPCOL_SBGI_CODE", "SORGPAT_GPA", "SORDEGR_HOURS_TRANSFERRED", "RECEIVED") AS 
  select  distinct 
d.UMID, FIRST_NAME, LAST_NAME,UNIQNAME, CA_EMAIL, HM_EMAIL_1,TERM_CODE_ENTRY_DESC,
SORDEGR_ATTEND_FROM, SORDEGR_ATTEND_TO,SORDEGR_DEGC_CODE,SORDEGR_ACTIVITY_DATE,SORDEGR_PRIMARY_IND,
SORPCOL_SBGI_CODE,
SORGPAT_GPA,SORDEGR_HOURS_TRANSFERRED,
case when  SARCHKL_RECEIVE_DATE is null then 'N'
else 'Y' end as RECEIVED
from aimsmgr.um_demographic d
join aimsmgr.um_admissions_applicant a on a.ad_pidm=d.dm_pidm
join aimsmgr.sorpcol_ext c on c.SORPCOL_PIDM=d.dm_pidm
join aimsmgr.SORDEGR_ext cg on cg.SORDEGR_PIDM=c.SORPCOL_PIDM
join <EMAIL> g on g.SORGPAT_PIDM=d.dm_pidm
join aimsmgr.sarchkl_ext k on k.SARCHKL_PIDM=d.dm_pidm
where 
TERM_CODE_ENTRY='202310'
and STYP_CODE='T'
and APST_CODE='D'
and APDC_CODE_1 in ('AD','A2')
--and SORDEGR_PRIMARY_IND='Y'
--and f.fund_code in ('OSHER','CRANKS')
;
