with dp1 as (
select 
co_pidm, co_term_code_key, co_ia_student_type_code 
from iamgr.IA_COHORT_POP_UNDUP_TBL
minus
select 
co_pidm, co_term_code_key , co_ia_student_type_code
from CO_POP_UNDUP_TBL where co_term_code_key like '%10'
)
select dp1.*, rm.co_pidm pidm, rm.co_ia_student_type_code  from dp1 
left join CO_POP_REMOVED rm on dp1.co_pidm = rm.co_pidm
and dp1.co_term_code_key = rm.co_term_code_key
;

with dp1 as (

select 
co_pidm, co_term_code_key , co_ia_student_type_code
from CO_POP_UNDUP_TBL where co_term_code_key like '%10'

minus

select 
co_pidm, co_term_code_key, co_ia_student_type_code 
from iamgr.IA_COHORT_POP_UNDUP_TBL

)

select dp1.*
--, rm.co_pidm pidm
--, 
--rm.co_ia_student_type_code  
from dp1 
--left join CO_POP_REMOVED rm on dp1.co_pidm = rm.co_pidm
--and dp1.co_term_code_key = rm.co_term_code_key
;
/*
co_pop_undup_tbl

200810	F	633
200810	T	692
200910	F	920
200910	T	723
201010	F	768
201010	T	885
201110	F	781
201110	T	928
201210	F	686
201210	T	908
201310	F	626
201310	T	814
201410	F	724
201410	T	785
201510	F	662
201510	T	771
201610	F	642
201610	T	720
201710	F	640
201710	T	696
201810	F	688
201810	T	677
201910	F	669
201910	T	637
202010	F	604
202010	T	561
202110	F	559
202110	T	482
202210	F	493
202210	T	475
202310	F	521
202310	T	520
*/


select 
co_term_code_key , co_ia_student_type_code, count(*) headcount
from CO_POP_UNDUP_TBL where co_term_code_key like '%10'
group by
co_term_code_key , co_ia_student_type_code
order by 1,2
;

select 
co_term_code_key , co_ia_student_type_code, count(*) headcount
from iamgr.IA_COHORT_POP_UNDUP_TBL where co_term_code_key like '%10'
group by
co_term_code_key , co_ia_student_type_code
order by 1,2
;
  select count(*) from co_pop_undup;
   select count(*) from co_pop_undup_tbl;
   select count(*) from co_ten_yr_nsc_tbl;
   select count (*) from co_persist_nsc_tbl;
   
   
/*--------------------------------------------------------
students with duplicate records removed on 060123.  The Fall records were kept
while the winter or spring records were removed from co_pop_undup
 99559	2
105880	2
191579	2
134460	2
139854	2
131893	2
--------------------------------------------------------*/

SELECT * FROM co_pop_undup_tbl 
WHERE CO_PIDM IN ('99559','105880','191579','134460','139854','131893')
;

SELECT COUNT (*) FROM CO_POP_UNDUP;