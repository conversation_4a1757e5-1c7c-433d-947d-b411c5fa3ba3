CREATE OR REPLACE package zdwgrdr as 

  procedure p_update_fa_keys;
  procedure p_update_fa_predictive_enroll;

end zdwgrdr;
/


CREATE OR REPLACE package body zdwgrdr as



---- release query
--select 
--predictive_enrollment.row_number,
----predictive_enrollment.ad_pidm,
----predictive_enrollment.umid,
--predictive_enrollment.hash_id,
--predictive_enrollment.term_code_entry,
--predictive_enrollment.aidy_code,
--predictive_enrollment.appl_no,
--predictive_enrollment.appl_date,
--predictive_enrollment.apdc_code_1,
--predictive_enrollment.ever_admitted_date,
--predictive_enrollment.apdc_date_1,
--predictive_enrollment.admt_code,
--predictive_enrollment.styp_code,
--predictive_enrollment.primary_program,
--predictive_enrollment.primary_major_1,
--predictive_enrollment.primary_conc_1,
--predictive_enrollment.gr_program,
--predictive_enrollment.pcol_code_1,
--predictive_enrollment.pcol_desc_1,  
--predictive_enrollment.resd_code,
--predictive_enrollment.a1_state_code,
--predictive_enrollment.a1_zip,
--predictive_enrollment.a1_county_desc,
--predictive_enrollment.a1_nation_desc,
--predictive_enrollment.pr_nation_desc,
--predictive_enrollment.nation_citizen_desc,
--predictive_enrollment.gender,
--predictive_enrollment.report_ethnicity,
--predictive_enrollment.intl_ind,
--predictive_enrollment.veteran_ind,
--predictive_enrollment.sordegr_gpa_transferred,
--predictive_enrollment.enroll_deposit_ind,
--predictive_enrollment.age,
--predictive_enrollment.miles_from_48502,
--predictive_enrollment.sordegr_degc_code,
--predictive_enrollment.stvdegc_acat_code,
--predictive_enrollment.gre_rev_gen_verbal,
--predictive_enrollment.gre_rev_gen_verbal_percentile,
--predictive_enrollment.gre_rev_gen_quantitative,
--predictive_enrollment.gre_rev_gen_quant_percentile,
--predictive_enrollment.gre_rev_gen_writing,
--predictive_enrollment.gre_rev_gen_writing_percentile,
--predictive_enrollment.gmat_total_score,
--predictive_enrollment.gmat_total_percentile,
--predictive_enrollment.gmat_verbal_score,
--predictive_enrollment.gmat_verbal_percentile,
--predictive_enrollment.gmat_quantitative_score,
--predictive_enrollment.gmat_quantitative_percentile,
--predictive_enrollment.gmat_writing_score,
--predictive_enrollment.gmat_writing_percentile,
--predictive_enrollment.ielts_overall,
--predictive_enrollment.toefl,
--predictive_enrollment.registered_ind_appl_term,
--predictive_enrollment.registered_ind_one_year_later,
--fa_predictive_enrollment.aidy_scholarship_offered_total,
--fa_predictive_enrollment.aidy_scholarship_total,
--fa_predictive_enrollment.term_scholarship_offered_total,
--fa_predictive_enrollment.term_scholarship_total,
--fa_predictive_enrollment.aidy_grant_offered_total,
--fa_predictive_enrollment.aidy_grant_total,
--fa_predictive_enrollment.term_grant_offered_total,
--fa_predictive_enrollment.term_grant_total,
--fa_predictive_enrollment.aidy_loan_offered_total,
--fa_predictive_enrollment.aidy_loan_total,
--fa_predictive_enrollment.term_loan_offered_total,
--fa_predictive_enrollment.term_loan_total,
--fa_predictive_enrollment.aidy_fellowship_total,
--fa_predictive_enrollment.term_fellowship_total,
--fa_predictive_enrollment.efc
--from grmgr.predictive_enrollment
--inner join fa_predictive_enrollment on fa_predictive_enrollment.pidm = predictive_enrollment.ad_pidm
--                                    and fa_predictive_enrollment.aidy_code = predictive_enrollment.aidy_code
--                                    and fa_predictive_enrollment.term_code = predictive_enrollment.term_code_entry
--                                    and fa_predictive_enrollment.appl_no = predictive_enrollment.appl_no
--order by row_number
--;

  procedure p_update_fa_keys as
  begin
--    drop table fa_predictive_enrollment;
    execute immediate('truncate table fa_predictive_enrollment');
  
--    create table fa_predictive_enrollment as
--    insert into fa_predictive_enrollment
--    select
--    ad_pidm pidm,
--    umid,
--    aidy_code,
--    term_code_entry term_code,
--    appl_no,
--    null term_scholarship_total,
--    null aidy_scholarship_total,
--    null term_grant_total,
--    null aidy_grant_total,
--    null term_loan_total,
--    null aidy_loan_total,
--    null term_fellowship_total,
--    null aidy_fellowship_total,
--    null efc
--    from grmgr.predictive_enrollment
--    order by 3 desc, 2;
  
    commit;
    
  end p_update_fa_keys;

--  Scholarship total amount - term of admission	do we have 10D table copies for Finaid?	ask FinAid to identify	
--  Scholarship total amount - aid year	10th day		
--  Grant amount - term of admission	10th day		
--  Grant amount - aid year	10th day		
--  Fellowship amount - term of admission	10th day		How does FinAid enter the KCP fellowship?
--  Fellowship amount - aid year	10th day		
-- Loans
--  efc	10th day		
  
  procedure p_update_fa_predictive_enroll as

  aidy_scholarship_total_amount number := 0;
  aidy_scholarship_offered_amt number := 0;

  term_scholarship_total_amount number := 0;		
  term_scholarship_offered_amt number := 0;
  
  aidy_grant_total_amount number := 0;
  aidy_grant_offered_amt number := 0;
  
  term_grant_total_amount number := 0;		
  term_grant_offered_amt number := 0;
  
  aidy_loan_total_amount number := 0;
  aidy_loan_offered_amt number := 0;
  
  term_loan_total_amount number := 0;		
  term_loan_offered_amt number := 0;
  
  aidy_fellowship_total_amount number := 0;
  term_fellowship_total_amount number := 0;

  efc number := 0;
  efc_tmp number := 0;

  cursor pop_sel_cur is
  select
  predictive_enrollment.ad_pidm pidm,
  predictive_enrollment.umid,
  predictive_enrollment.aidy_code,
  predictive_enrollment.term_code_entry term_code,
  predictive_enrollment.appl_no
  from grmgr.predictive_enrollment;
  
  type pop_sel_array is table of pop_sel_cur%rowtype index by binary_integer;
  pop_sel_rec pop_sel_array;
  
  cursor aidy_schol_cur(pidm_in number, aidy_code_in varchar2) is
  select
  rprawrd.rprawrd_pidm,
  rprawrd.rprawrd_aidy_code,
  sum(rprawrd.rprawrd_orig_offer_amt) sum_rprawrd_orig_offer_amt,
  sum(rprawrd.rprawrd_paid_amt) sum_rprawrd_amt
  from aimsmgr.rprawrd_ext rprawrd
  inner join(
    select 
    rfrbase.rfrbase_ftyp_code,
    rfrbase.rfrbase_fund_code
    from aimsmgr.rfrbase_ext rfrbase
  ) rfrbase on rprawrd.rprawrd_fund_code = rfrbase.rfrbase_fund_code
            and rfrbase.rfrbase_ftyp_code = 'SCHL'
  where rprawrd.rprawrd_pidm = pidm_in
  and rprawrd.rprawrd_aidy_code = aidy_code_in
  group by 
  rprawrd.rprawrd_pidm,
  rprawrd.rprawrd_aidy_code
  ;
  
  type aidy_schol_array is table of aidy_schol_cur%rowtype index by binary_integer;
  aidy_schol_rec aidy_schol_array;

  cursor term_schol_cur(pidm_in number, term_code_in varchar2) is
  select
  rpratrm.rpratrm_pidm,
  sum(rpratrm.rpratrm_orig_offer_amt) sum_rpratrm_orig_offer_amt,
  sum(rpratrm.rpratrm_paid_amt) sum_rpratrm_paid_amt
  from aimsmgr.rpratrm_ext rpratrm
  inner join(
    select 
    rfrbase.rfrbase_ftyp_code,
    rfrbase.rfrbase_fund_code
    from aimsmgr.rfrbase_ext rfrbase
  )rfrbase on rpratrm.rpratrm_fund_code = rfrbase.rfrbase_fund_code
           and rfrbase.rfrbase_ftyp_code = 'SCHL'
  where rpratrm.rpratrm_pidm = pidm_in
  and rpratrm.rpratrm_period = term_code_in
  group by 
  rpratrm.rpratrm_pidm
  ;

  type term_schol_array is table of term_schol_cur%rowtype index by binary_integer;
  term_schol_rec term_schol_array;      

  cursor aidy_grant_cur(pidm_in number, aidy_code_in varchar2) is
  select
  rprawrd.rprawrd_pidm,
  rprawrd.rprawrd_aidy_code,
  sum(rprawrd.rprawrd_orig_offer_amt) sum_rprawrd_orig_offer_amt,
  sum(rprawrd.rprawrd_paid_amt) sum_rprawrd_paid_amt
  from aimsmgr.rprawrd_ext rprawrd
  inner join(
    select 
    rfrbase.rfrbase_ftyp_code,
    rfrbase.rfrbase_fund_code
    from aimsmgr.rfrbase_ext rfrbase
  ) rfrbase on rprawrd.rprawrd_fund_code = rfrbase.rfrbase_fund_code
            and rfrbase.rfrbase_ftyp_code = 'GRNT'
  where rprawrd.rprawrd_pidm = pidm_in
  and rprawrd.rprawrd_aidy_code = aidy_code_in
  group by 
  rprawrd.rprawrd_pidm,
  rprawrd.rprawrd_aidy_code
  ;

  type aidy_grant_array is table of aidy_grant_cur%rowtype index by binary_integer;
  aidy_grant_rec aidy_grant_array;      

  cursor term_grant_cur(pidm_in number, term_code_in varchar2) is
  select
  rpratrm.rpratrm_pidm,
  rpratrm.rpratrm_aidy_code,
  sum(rpratrm.rpratrm_orig_offer_amt) sum_rpratrm_orig_offer_amt,
  sum(rpratrm.rpratrm_paid_amt) sum_rpratrm_paid_amt
  from aimsmgr.rpratrm_ext rpratrm
  inner join(
    select 
    rfrbase.rfrbase_ftyp_code,
    rfrbase.rfrbase_fund_code
    from aimsmgr.rfrbase_ext rfrbase
  ) rfrbase on rpratrm.rpratrm_fund_code = rfrbase.rfrbase_fund_code
            and rfrbase.rfrbase_ftyp_code = 'GRNT'
  where rpratrm.rpratrm_pidm = pidm_in
  and rpratrm.rpratrm_period = term_code_in
  group by 
  rpratrm.rpratrm_pidm,
  rpratrm.rpratrm_aidy_code
  ;

  type term_grant_array is table of term_grant_cur%rowtype index by binary_integer;
  term_grant_rec term_grant_array;       

  cursor aidy_loan_cur(pidm_in number, aidy_code_in varchar2) is
  select
  rprawrd.rprawrd_pidm,
  rprawrd.rprawrd_aidy_code,
  sum(rprawrd.rprawrd_orig_offer_amt) sum_rprawrd_orig_offer_amt,
  sum(rprawrd.rprawrd_paid_amt) sum_rprawrd_paid_amt
  from aimsmgr.rprawrd_ext rprawrd
  inner join(
    select 
    rfrbase.rfrbase_ftyp_code,
    rfrbase.rfrbase_fund_code
    from aimsmgr.rfrbase_ext rfrbase
  ) rfrbase on rprawrd.rprawrd_fund_code = rfrbase.rfrbase_fund_code
            and rfrbase.rfrbase_ftyp_code = 'LOAN'
  where rprawrd.rprawrd_pidm = pidm_in
  and rprawrd.rprawrd_aidy_code = aidy_code_in
  group by 
  rprawrd.rprawrd_pidm,
  rprawrd.rprawrd_aidy_code
  ;

  type aidy_loan_array is table of aidy_loan_cur%rowtype index by binary_integer;
  aidy_loan_rec aidy_loan_array;      

  cursor term_loan_cur(pidm_in number, term_code_in varchar2) is
  select
  rpratrm.rpratrm_pidm,
  rpratrm.rpratrm_aidy_code,
  sum(rpratrm.rpratrm_orig_offer_amt) sum_rpratrm_orig_offer_amt,
  sum(rpratrm.rpratrm_paid_amt) sum_rpratrm_paid_amt
  from aimsmgr.rpratrm_ext rpratrm
  inner join(
    select 
    rfrbase.rfrbase_ftyp_code,
    rfrbase.rfrbase_fund_code
    from aimsmgr.rfrbase_ext rfrbase
  ) rfrbase on rpratrm.rpratrm_fund_code = rfrbase.rfrbase_fund_code
            and rfrbase.rfrbase_ftyp_code = 'LOAN'
  where rpratrm.rpratrm_pidm = pidm_in
  and rpratrm.rpratrm_period = term_code_in
  group by 
  rpratrm.rpratrm_pidm,
  rpratrm.rpratrm_aidy_code
  ;

  type term_loan_array is table of term_loan_cur%rowtype index by binary_integer;
  term_loan_rec term_loan_array;       

--******************************************************************************
-- Couldn't get queries to get this data, GR had only two from the set that 
-- had values so these where entered into the table manually.
--
--  cursor aidy_fellowship_cur(pidm_in number, aidy_code_in varchar2) is
--  select
--
--  ;
--  
--  type aidy_fellowship_array is table of aidy_fellowship_cur%rowtype index by binary_integer;
--  aidy_fellowship_rec aidy_fellowship_array;

--  cursor term_fellowship_cur(pidm_in number, term_code_in varchar2) is
--  select
--
--  ;
--  
--  type term_fellowship_array is table of term_fellowship_cur%rowtype index by binary_integer;
--  term_fellowship_rec term_fellowship_array;
--********************

  cursor efc_cur(pidm_in number, aidy_code_in varchar2) is
  select
  rcrapp1.rcrapp1_pidm,
  rcrapp1.rcrapp1_aidy_code,
  rcrapp2.rcrapp2_pell_pgi
  from aimsmgr.rcrapp1_ext rcrapp1
  inner join(
    select 
    rcrapp2.rcrapp2_pidm,
    rcrapp2.rcrapp2_aidy_code,
    rcrapp2.rcrapp2_pell_pgi,
    rcrapp2.rcrapp2_infc_code,
    rcrapp2.rcrapp2_seq_no
    from aimsmgr.rcrapp2_ext rcrapp2
  )rcrapp2 on rcrapp2.rcrapp2_pidm = rcrapp1.rcrapp1_pidm
           and rcrapp2.rcrapp2_aidy_code = rcrapp1.rcrapp1_aidy_code
           and rcrapp2.rcrapp2_seq_no = rcrapp1.rcrapp1_seq_no
           and rcrapp2.rcrapp2_infc_code = 'EDE'
  where rcrapp1.rcrapp1_infc_code = 'EDE'
  and rcrapp1.rcrapp1_curr_rec_ind = 'Y'
  and rcrapp1.rcrapp1_pidm = pidm_in
  and rcrapp1.rcrapp1_aidy_code = aidy_code_in;

  type efc_array is table of efc_cur%rowtype index by binary_integer;
  efc_rec efc_array;

  begin
    execute immediate('truncate table fa_predictive_enrollment');
  
    open pop_sel_cur;
    fetch pop_sel_cur bulk collect into pop_sel_rec;
    close pop_sel_cur;
    
    for i in 1..pop_sel_rec.count
    loop
          
      aidy_scholarship_offered_amt := null;
      aidy_scholarship_total_amount := null;
      open aidy_schol_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
      fetch aidy_schol_cur bulk collect into aidy_schol_rec;
      close aidy_schol_cur;

      if aidy_schol_rec.count > 0 then
        aidy_scholarship_offered_amt := aidy_schol_rec(1).sum_rprawrd_orig_offer_amt;
        aidy_scholarship_total_amount := aidy_schol_rec(1).sum_rprawrd_amt;
      end if;

      term_scholarship_offered_amt := null;
      term_scholarship_total_amount := null;		
      open term_schol_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).term_code);
      fetch term_schol_cur bulk collect into term_schol_rec;
      close term_schol_cur;

      if term_schol_rec.count > 0 then
        term_scholarship_offered_amt := term_schol_rec(1).sum_rpratrm_orig_offer_amt;
        term_scholarship_total_amount := term_schol_rec(1).sum_rpratrm_paid_amt;
      end if;

      aidy_grant_offered_amt := null;
      aidy_grant_total_amount := null;
      open aidy_grant_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
      fetch aidy_grant_cur bulk collect into aidy_grant_rec;
      close aidy_grant_cur;

      if aidy_grant_rec.count > 0 then
        aidy_grant_offered_amt := aidy_grant_rec(1).sum_rprawrd_orig_offer_amt;
        aidy_grant_total_amount := aidy_grant_rec(1).sum_rprawrd_paid_amt;
      end if;

      term_grant_offered_amt := null;
      term_grant_total_amount := null;	
      open term_grant_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).term_code);
      fetch term_grant_cur bulk collect into term_grant_rec;
      close term_grant_cur;

      if term_grant_rec.count > 0 then
        term_grant_offered_amt := term_grant_rec(1).sum_rpratrm_orig_offer_amt;
        term_grant_total_amount := term_grant_rec(1).sum_rpratrm_paid_amt;
      end if;

      aidy_loan_offered_amt := null;
      aidy_loan_total_amount := null;
      open aidy_loan_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
      fetch aidy_loan_cur bulk collect into aidy_loan_rec;
      close aidy_loan_cur;

      if aidy_loan_rec.count > 0 then
        aidy_loan_offered_amt := aidy_loan_rec(1).sum_rprawrd_orig_offer_amt;
        aidy_loan_total_amount := aidy_loan_rec(1).sum_rprawrd_paid_amt;
      end if;

      term_loan_offered_amt := null;
      term_loan_total_amount := null;	
      open term_loan_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).term_code);
      fetch term_loan_cur bulk collect into term_loan_rec;
      close term_loan_cur;

      if term_loan_rec.count > 0 then
        term_loan_offered_amt := term_loan_rec(1).sum_rpratrm_orig_offer_amt;
        term_loan_total_amount := term_loan_rec(1).sum_rpratrm_paid_amt;
      end if;

      aidy_fellowship_total_amount := null;
/*
-- from brad
UMID 27369575
pidm 189607
Term code 201520
Fellowship amount $2000.00 (AY 14-15)

UMID 59011217
pidm 122138
term code 201610
Fellowship amount $4912.00 (AY 15-16)
*/
      if pop_sel_rec(i).pidm = 189607 and pop_sel_rec(i).aidy_code = '1415' then 
        aidy_fellowship_total_amount := 2000;
      elsif pop_sel_rec(i).pidm = 122138 and pop_sel_rec(i).aidy_code = '1516' then 
        aidy_fellowship_total_amount := 4912;
      else 
        aidy_fellowship_total_amount := null;
      end if;
--      open aidy_fellowship_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
--      fetch aidy_fellowship_cur bulk collect into aidy_fellowship_rec;
--      close aidy_fellowship_cur;
--
--      if aidy_fellowship_rec.count > 0 then
--        aidy_fellowship_total_amount := nvl(aidy_fellowship_rec(1). , 0);
--      end if;

      term_fellowship_total_amount := null;
      if pop_sel_rec(i).pidm = 189607 and pop_sel_rec(i).term_code = '201520' then 
        term_fellowship_total_amount := 2000;
      elsif pop_sel_rec(i).pidm = 122138 and pop_sel_rec(i).term_code = '201610' then 
        term_fellowship_total_amount := 4912;
      else 
        term_fellowship_total_amount := null;
      end if;
--      open term_fellowship_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).term_code);
--      fetch term_fellowship_cur bulk collect into term_fellowship_rec;
--      close term_fellowship_cur;
--
--      if term_fellowship_rec.count > 0 then
--        term_fellowship_total_amount := nvl(term_fellowship_rec(1). , 0);
--      end if;
  
      open efc_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
      fetch efc_cur bulk collect into efc_rec;
      close efc_cur;
      
      efc := null;
      if efc_rec.count > 0 then
        efc := efc_rec(1).rcrapp2_pell_pgi;
      end if; 
      
      insert into fa_predictive_enrollment(
      fa_predictive_enrollment.pidm,
      fa_predictive_enrollment.umid,
      fa_predictive_enrollment.aidy_code,
      fa_predictive_enrollment.term_code,
      fa_predictive_enrollment.appl_no,

      fa_predictive_enrollment.aidy_scholarship_offered_total,
      fa_predictive_enrollment.aidy_scholarship_total,
      fa_predictive_enrollment.term_scholarship_offered_total,
      fa_predictive_enrollment.term_scholarship_total,

      fa_predictive_enrollment.aidy_grant_offered_total,
      fa_predictive_enrollment.aidy_grant_total,
      fa_predictive_enrollment.term_grant_offered_total,
      fa_predictive_enrollment.term_grant_total,

      fa_predictive_enrollment.aidy_loan_offered_total,
      fa_predictive_enrollment.aidy_loan_total,
      fa_predictive_enrollment.term_loan_offered_total,
      fa_predictive_enrollment.term_loan_total,

      fa_predictive_enrollment.aidy_fellowship_total,
      fa_predictive_enrollment.term_fellowship_total,

      fa_predictive_enrollment.efc
      )values(
      pop_sel_rec(i).pidm,
      pop_sel_rec(i).umid,
      pop_sel_rec(i).aidy_code,
      pop_sel_rec(i).term_code,
      pop_sel_rec(i).appl_no,

      aidy_scholarship_offered_amt,
      aidy_scholarship_total_amount,
      term_scholarship_offered_amt,
      term_scholarship_total_amount,

      aidy_grant_offered_amt,
      aidy_grant_total_amount,
      term_grant_offered_amt,
      term_grant_total_amount,

      aidy_loan_offered_amt,
      aidy_loan_total_amount,
      term_loan_offered_amt,
      term_loan_total_amount,

      aidy_fellowship_total_amount,
      term_fellowship_total_amount,

      efc
      );
      
      commit;
    end loop;

    commit;
  end p_update_fa_predictive_enroll;

end zdwgrdr;
/
