--------------------------------------------------------
-- CO_NSC_STUDENT_DATA
--------------------------------------------------------

--CREATE TABLE CO_NSC_STUDENT_DATA_TBL_BAC AS
--SELECT * FROM CO_NSC_STUDENT_DATA_TBL;
--
--DROP TABLE CO_NSC_STUDENT_DATA_TBL;
TRUNCATE TABLE CO_NSC_STUDENT_DATA_TBL;

--CREATE TABLE CO_NSC_STUDENT_DATA_TBL AS
INSERT INTO CO_NSC_STUDENT_DATA_TBL 
with dp1 as (
  select 
distinct
CO.co_pidm,
CO.co_term_code_key,
nsce.mapped_term_code TERM_CODE_ENROLLMENT_BEGIN,
nsce.REGISTERED_IND,
--nsce.PIDM CO_PIDM,
nsce.COLLEGE_NAME,
nsce.TWOYEAR_FOURYEAR,
nsce.ENROLLMENT_MAJOR
from co_pop_undup_tbl co
INNER join nsc_enrollment nsce on co.co_pidm = nsce.pidm 
and co.co_term_code_key < nsce.mapped_term_code
--and min_term_ind= 1
order by 2,1
),dp2 as (
select 
   ROW_NUMBER()
 OVER(PARTITION BY co_pidm, TERM_CODE_ENROLLMENT_BEGIN
--NSCE.mapped_term_code 
      ORDER BY
       TERM_CODE_ENROLLMENT_BEGIN
 )                                    min_term_ind,
dp1.*
from dp1
)
select * from dp2
where min_term_ind = 1

;

--desc nsc_enrollment;