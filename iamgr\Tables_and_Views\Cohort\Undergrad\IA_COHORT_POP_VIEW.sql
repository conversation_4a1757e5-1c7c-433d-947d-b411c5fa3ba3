/*This script is designed to build a cohort table. A cohort is defined as the students that enter the University 
in the Fall or prior summer as a FTIAC or Transfer and is denoted in the CO_IA_STUDENT_TYPE_CODE field.

In 2010 the registrars office began coding FTIAC students that started in summer and continued to fall as 
FTIAC in the banner system for fall, while the Transfers who started in summer were never rolled in the system.  
AIMS created a field called IA_STUDENT_TYPE_CODE for Institutional Analysis use that would roll the summer transfers 
to fall starting in Fall 2010.  

Student Ethnicity is another field that has changed over time and needed to be normalized to current standards.  
Prior to 2010 the codes were in a format that did not include multi-racial and Non-resident Alien and were manually 
calculated with an algorithm using race_codes and residency_codes. In 2010 the banner system began using the current 
federal and state definitions for race and ethnicity and stored them in a new filed called report_ethnicity.

With all of the changes in definitions to students over the course of time there was no way to analyse cohort data in 
a consistent fashion.  The ia_cohort_pop table was developed to rectify this issue.  The "2011 to present" portion 
of the query will pull the student data from the AIMSPROD data mart that has the correct Ethnicity and has the summer 
students that continue to fall roll to Fall as either a FTIAC or Transfer.
*/

create or replace view ia_cohort_pop as

-------2011 to present
select
td_student_data.sd_pidm as CO_PIDM,
td_student_data.sd_term_code CO_TERM_CODE_KEY, -- fall term code
td_student_data.ia_student_type_code CO_IA_STUDENT_TYPE_CODE,
td_student_data.full_part_time_ind_umf CO_FULL_PART_IND_UMF,
td_demographic.report_ethnicity as CO_ETHNICITY,
td_demographic.gender as CO_GENDER,
td_student_data.PRIMARY_DEGREE_CODE

from td_student_data
 
inner join td_demographic on td_demographic.dm_pidm = td_student_data.sd_pidm
                          and td_demographic.td_term_code = td_student_data.sd_term_code
where 
primary_level_code = 'UG' and  
registered_ind = 'Y' and 
(ia_student_type_code = 'F' or ia_student_type_code = 'T') and 
sd_term_code like '%10' and sd_term_code >= '201210'  /*filters the term code to pull only 
														fall terms greater than or equal to 
														fall 2011*/
--and td_student_data.SD_PIDM
--('119792','105486','5386','104501','98986','104976','117199','115845','110424')--deceased students removed from cohort
union

select 
CO_PIDM,
CO_TERM_CODE_KEY,
CO_IA_STUDENT_TYPE_CODE,
CO_FULL_PART_IND_UMF,
CO_ETHNICITY,
CO_GENDER,
td_student_data.PRIMARY_DEGREE_CODE

from 
ia_co_all_2008_2010  
inner join td_student_data on ia_co_all_2008_2010.co_pidm = td_student_data.sd_pidm
                          and ia_co_all_2008_2010.CO_TERM_CODE_KEY = td_student_data.sd_term_code
where (primary_level_code = 'UG' 
or CO_PIDM in (76842))--Student Miss Labeled primary_level_code as U2 should be UG
--('81213','79301','83123','90228','97702','88478','98190')

union

select 
CO_PIDM,
CO_TERM_CODE_KEY,
CO_IA_STUDENT_TYPE_CODE,
CO_FULL_PART_IND_UMF,
CO_ETHNICITY,
CO_GENDER,
td_student_data.PRIMARY_DEGREE_CODE

from 
ia_co_all_2007
inner join td_student_data on ia_co_all_2007.co_pidm = td_student_data.sd_pidm
                          and ia_co_all_2007.CO_TERM_CODE_KEY = td_student_data.sd_term_code
where primary_level_code = 'UG'
--('66971','73896','75325','70353')
;