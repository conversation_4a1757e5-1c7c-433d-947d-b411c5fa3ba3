/*Truncate Backup*/
TRUNCATE TABLE IA_FY_COHORT_POP_UNDUP_TBL_BAC;

/*Backup*/
INSERT INTO IA_FY_COHORT_POP_UNDUP_TBL_BAC
SELECT *
FROM IA_FY_COHORT_POP_UNDUP_TBL;

/*Varification of Backup*/
SELECT COUNT (*) FROM IA_FY_COHORT_POP_UNDUP_TBL;
SELECT COUNT (*) FROM IA_FY_COHORT_POP_UNDUP_TBL_BAC;

/*Total ftiac and transfer ug students for the fy is : 1513 students*/
SELECT 
COUNT (SD_PIDM)
FROM IA_TD_STUDENT_DATA SD
WHERE SD.PRIMARY_LEVEL_CODE  = 'UG'
AND SD.REGISTERED_IND        = 'Y'
AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
AND SD.FY         = '20-21'
;

/*Total distinct FTIAC and transfer ug students for the fy is : 1435 Students*/
SELECT 
COUNT (DISTINCT SD_PIDM)
FROM IA_TD_STUDENT_DATA SD
WHERE SD.PRIMARY_LEVEL_CODE  = 'UG'
AND SD.REGISTERED_IND        = 'Y'
AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
AND SD.FY         = '20-21'
;

/*Insert New FY Cohort Set*/
INSERT INTO IA_FY_COHORT_POP_UNDUP_TBL
WITH DP1 AS ( 
  SELECT  
  row_number() over (partition by sd_pidm order by fy_term_code desc) dup_ind,
   FY.FY,
    to_date('01-SEP-'
  ||(to_number(SUBSTR(FY_TERM_CODE,3,2))-1),'DD-MON-YY') start_date,
    SD.SD_PIDM CO_PIDM,
    FY.FY_CO_TERM_CODE,
     FY.FY_TERM_CODE,
    SD.IA_STUDENT_TYPE_CODE CO_IA_STUDENT_TYPE_CODE,
    SD.FULL_PART_TIME_IND_UMF CO_FULL_PART_IND_UMF,
    DM.REPORT_ETHNICITY CO_ETHNICITY,
    DM.GENDER CO_GENDER,
    SD.PRIMARY_DEGREE_CODE

  FROM TD_STUDENT_DATA SD
  INNER JOIN TD_DEMOGRAPHIC DM
  ON DM.DM_PIDM       = SD.SD_PIDM
  AND DM.TD_TERM_CODE = SD.SD_TERM_CODE
  LEFT JOIN IA_CW_FY_TERM FY
  ON SD.SD_TERM_CODE           = FY.FY_TERM_CODE
  WHERE SD.PRIMARY_LEVEL_CODE  = 'UG'
  AND SD.REGISTERED_IND        = 'Y'
  AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
  AND FY.FY   = '20-21'
     )
  SELECT 
--  dup_ind,
  FY,
  CO_PIDM,
  FY_CO_TERM_CODE,
  FY_TERM_CODE,
  CO_IA_STUDENT_TYPE_CODE,
  CO_FULL_PART_IND_UMF,
  CO_GENDER,
  PRIMARY_DEGREE_CODE,
  START_DATE,
  CO_ETHNICITY
  from dp1
  where dup_ind = 1
  order by 3
  ;
  
 /*Removed records*/
WITH DP1 AS ( 
  SELECT  
  row_number() over (partition by sd_pidm order by fy_term_code desc) dup_ind,
   FY.FY,
    to_date('01-SEP-'
  ||(to_number(SUBSTR(FY_TERM_CODE,3,2))-1),'DD-MON-YY') start_date,
    SD.SD_PIDM CO_PIDM,
    FY.FY_CO_TERM_CODE,
     FY.FY_TERM_CODE,
    SD.IA_STUDENT_TYPE_CODE CO_IA_STUDENT_TYPE_CODE,
    SD.FULL_PART_TIME_IND_UMF CO_FULL_PART_IND_UMF,
    DM.REPORT_ETHNICITY CO_ETHNICITY,
    DM.GENDER CO_GENDER,
    SD.PRIMARY_DEGREE_CODE

  FROM TD_STUDENT_DATA SD
  INNER JOIN TD_DEMOGRAPHIC DM
  ON DM.DM_PIDM       = SD.SD_PIDM
  AND DM.TD_TERM_CODE = SD.SD_TERM_CODE
  LEFT JOIN IA_CW_FY_TERM FY
  ON SD.SD_TERM_CODE           = FY.FY_TERM_CODE
  WHERE SD.PRIMARY_LEVEL_CODE  = 'UG'
  AND SD.REGISTERED_IND        = 'Y'
  AND SD.IA_STUDENT_TYPE_CODE IN ('F','T')
  AND FY.FY   = '20-21'
     )
  SELECT 
--  dup_ind,
  FY,
  CO_PIDM,
  FY_CO_TERM_CODE,
  FY_TERM_CODE,
  CO_IA_STUDENT_TYPE_CODE,
  CO_FULL_PART_IND_UMF,
  CO_GENDER,
  PRIMARY_DEGREE_CODE,
  START_DATE,
  CO_ETHNICITY
  from dp1
  where dup_ind != 1
  order by 3
  ;
  
/*Headcount Comparison dinstinct count shoud mach count*/
select count (distinct co_pidm) distinct_headcount
from IA_FY_COHORT_POP_UNDUP_TBL
where fy = '20-21'
;
select count (co_pidm) 
from IA_FY_COHORT_POP_UNDUP_TBL headcount
where fy = '20-21'
;