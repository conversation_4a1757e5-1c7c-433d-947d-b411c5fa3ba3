select
IA_UM_STUDENT_DATA.SD_TERM_DESC as Term,
IA_UM_STUDENT_DATA.umid,
IA_UM_STUDENT_DATA.first_name,
IA_UM_STUDENT_DATA.last_name,
IA_UM_STUDENT_DATA.ca_email as UMF_Email,
IA_UM_STUDENT_DATA.hm_email_1 as Personal_Email,
IA_UM_STUDENT_DATA.a1_area_code || IA_UM_STUDENT_DATA.a1_phone_number as Phone_Number,
IA_UM_STUDENT_DATA.gender,
IA_UM_STUDENT_DATA.OVERALL_GPA

from 
IA_UM_STUDENT_DATA

where
--IA_UM_STUDENT_DATA.report_ethnicity = 'Black or African American' and
IA_UM_STUDENT_DATA.sd_term_code = '201610' and  --This gets updated to term desired.
IA_UM_STUDENT_DATA.registered_ind = 'Y' and
IA_UM_STUDENT_DATA.REPORT_LEVEL_CODE = 'UG' and
IA_UM_STUDENT_DATA.PRIMARY_ADMIT_CODE = 'CH' and
IA_UM_STUDENT_DATA.IA_STUDENT_TYPE_CODE = 'F'
--IA_UM_STUDENT_DATA.overall_gpa >= 3.0


order by
IA_UM_STUDENT_DATA.last_name asc
