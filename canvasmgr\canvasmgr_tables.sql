/**********************************************************************************
This selection retrieves the table structure for several tables in the database.
Each table is described using the "desc" command, which provides information 
about the table's columns, data types, and nullability.
The tables included in this selection are:
UDP_ACADEMIC_TERM_EXT
UDP_ANNOTATION_EXT
UDP_COURSE_GRADE_EXT
UDP_COURSE_OFFERING_EXT
UDP_COURSE_SECTION_ENROLLMENT_EXT
UDP_COURSE_SECTION_EXT
UDP_LEARNER_ACTIVITY_EXT
UDP_LEARNER_ACTIVITY_RESULT_EXT
UDP_PERSON_EMAIL_EXT
UDP_PERSON_EXT
***********************************************************************************/

SQL> desc <PERSON>DP_ACADEMIC_TERM_EXT;
Name	Null?	Type
ACADEMIC_TERM_ID	NOT NULL	
NUMBER(19)
LE_TERM_BEGIN_DATE		
DATE
LE_TERM_END_DATE		
DATE
NAME		
VARCHAR2(4000)
TERM_BEGIN_DATE		
DATE
TERM_END_DATE		
DATE
TERM_TYPE		
VARCHAR2(4000)
TERM_YEAR		
NUMBER

SQL> desc UDP_ANNOTATION_EXT
Name	Null?	Type
ANNOTATION_ID	NOT NULL	
NUMBER
AUTHOR_ID		
NUMBER
LEARNER_ACTIVITY_RESULT_ID		
NUMBER
AUTHOR_NAME		
VARCHAR2(4000)
BODY_VALUE		
VARCHAR2(4000)
CREATED_DATE		
VARCHAR2(4000)
MESSAGE_CHARACTER_COUNT		
NUMBER
MESSAGE_LINE_COUNT		
NUMBER
MESSAGE_SIZE_BYTES		
NUMBER
MESSAGE_WORD_COUNT		
NUMBER
UPDATED_DATE		
VARCHAR2(4000)

SQL> desc UDP_COURSE_GRADE_EXT;
Name	Null?	Type
COURSE_SECTION_ID	NOT NULL	
NUMBER(19)
PERSON_ID	NOT NULL	
NUMBER(19)
CREATED_DATE		
TIMESTAMP(6)
LE_CURRENT_SCORE		
NUMBER(10,2)
LE_FINAL_SCORE		
NUMBER(10,2)
LE_FINAL_SCORE_STATUS		
VARCHAR2(4000)
LE_HIDDEN_CURRENT_SCORE		
NUMBER(10,2)
LE_HIDDEN_FINAL_SCORE		
NUMBER(10,2)
UPDATED_DATE		
TIMESTAMP(6)

SQL> desc UDP_COURSE_OFFERING_EXT;
Name	Null?	Type
COURSE_OFFERING_ID	NOT NULL	
NUMBER(19)
ACADEMIC_TERM_ID		
NUMBER(19)
LEARNING_ENVIRONMENT_ORGANIZATION_ID		
NUMBER(19)
START_DATE		
DATE
END_DATE		
DATE
LE_START_DATE		
DATE
LE_END_DATE		
DATE
LE_CODE		
VARCHAR2(4000)
LE_STATUS		
VARCHAR2(4000)
COURSE_NUM		
VARCHAR2(4000)
COURSE_SUBJ		
VARCHAR2(4000)
SYLLABUS_CONTENT		
VARCHAR2(4000)
TITLE		
VARCHAR2(4000)

SQL> desc UDP_COURSE_SECTION_ENROLLMENT_EXT;
Name	Null?	Type
COURSE_SECTION_ID	NOT NULL	
NUMBER(19)
PERSON_ID	NOT NULL	
NUMBER(19)
ROLE		
VARCHAR2(4000)
ROLE_STATUS		
VARCHAR2(4000)
COMPLETED_DATE		
TIMESTAMP(6)
CREATED_DATE		
TIMESTAMP(6)
ENROLLMENT_STATUS		
VARCHAR2(4000)
ENTRY_DATE		
TIMESTAMP(6)
EXIT_DATE		
TIMESTAMP(6)
UPDATED_DATE		
TIMESTAMP(6)

SQL> desc UDP_COURSE_SECTION_EXT;
Name	Null?	Type
COURSE_SECTION_ID	NOT NULL	
NUMBER(19)
COURSE_OFFERING_ID		
NUMBER(19)
LE_CURRENT_COURSE_OFFERING_ID		
NUMBER(19)
LE_ORIGINAL_COURSE_OFFERING_ID		
NUMBER(19)
CLASS_NUMBER		
VARCHAR2(4000)
CREATED_DATE		
TIMESTAMP(6)
LE_NAME		
VARCHAR2(4000)
MAX_ENROLLMENT		
NUMBER
SECTION_NUMBER		
VARCHAR2(4000)
STATUS		
VARCHAR2(4000)
TYPE		
VARCHAR2(4000)
UPDATED_DATE		
TIMESTAMP(6)

SQL> desc UDP_LEARNER_ACTIVITY_EXT;
Name	Null?	Type
LEARNER_ACTIVITY_ID	NOT NULL	
NUMBER(19)
COURSE_OFFERING_ID		
NUMBER(19)
LEARNER_ACTIVITY_GROUP_ID		
NUMBER(19)
ALL_DAY_DATE		
TIMESTAMP(6)
ALLOWABLE_SUBMISSION_TYPES		
VARCHAR2(4000)
CREATED_DATE		
TIMESTAMP(6)
DESCRIPTION		
VARCHAR2(4000)
DROP_RULE		
VARCHAR2(4000)
DUE_DATE		
TIMESTAMP(6)
GRADE_TYPE		
VARCHAR2(4000)
LOCKED_DATE		
TIMESTAMP(6)
PEER_REVIEW_COUNT		
NUMBER
PEER_REVIEWS_DUE_DATE		
TIMESTAMP(6)
POINTS_POSSIBLE		
NUMBER(20,3)
POSITION		
NUMBER
STATUS		
VARCHAR2(4000)
TITLE		
VARCHAR2(4000)
UNLOCKED_DATE		
TIMESTAMP(6)
UPDATED_DATE		
TIMESTAMP(6)
VISIBILITY		
VARCHAR2(4000)

SQL> desc UDP_LEARNER_ACTIVITY_RESULT_EXT;
Name	Null?	Type
LEARNER_ACTIVITY_RESULT_ID	NOT NULL	
NUMBER
GRADER_ID		
NUMBER
LEARNER_ACTIVITY_ID		
NUMBER
LEARNER_GROUP_ID		
NUMBER
PERSON_ID		
NUMBER
QUIZ_ID		
NUMBER
QUIZ_RESULT_ID		
NUMBER
ATTEMPT		
NUMBER
BODY		
VARCHAR2(4000)
CREATED_DATE		
TIMESTAMP
GRADE		
VARCHAR2(10)
GRADE_STATE		
VARCHAR2(40)
GRADEBOOK_STATUS		
VARCHAR2(10)
GRADED_DATE		
TIMESTAMP
GRADING_STATUS		
VARCHAR2(40)
POSTED_AT		
TIMESTAMP
PROCESS_ATTEMPTS		
NUMBER
SCORE		
NUMBER(38,5)
SUBMISSION_COMMENTS_COUNT		
NUMBER
TYPE		
VARCHAR2(100)

SQL> desc UDP_PERSON_EMAIL_EXT;
Name	Null?	Type
PERSON_EMAIL_ID	NOT NULL	
NUMBER(19)
PERSON_ID		
NUMBER(19)
EMAIL_ADDRESS		
VARCHAR2(4000)
EMAIL_TYPE		
VARCHAR2(4000)

SQL> desc UDP_PERSON_EXT;
Name	Null?	Type
PERSON_ID	NOT NULL	
NUMBER(19)
FED_IDENTITY		
VARCHAR2(4000)
FIRST_NAME		
VARCHAR2(4000)
LAST_NAME		
VARCHAR2(4000)
LE_ACCOUNT_STATUS		
VARCHAR2(4000)
MIDDLE_NAME		
VARCHAR2(4000)
NAME		
VARCHAR2(4000)
PREFERRED_FIRST_NAME		
VARCHAR2(4000)
SEX		
VARCHAR2(4000)
ZIP_CODE_AT_TIME_OF_APPLICATION		
VARCHAR2(4000)
