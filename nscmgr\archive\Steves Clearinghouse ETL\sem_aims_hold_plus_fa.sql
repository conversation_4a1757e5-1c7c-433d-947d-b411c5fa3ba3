drop table sem_aims_hold_plus_fa;

create table sem_aims_hold_plus_fa as
select 
sem_aims_hold_plus.order_num,
sem_aims_hold_plus.pidm,
sem_aims_hold_plus.umid,
sem_aims_hold_plus.last_term,
sem_aims_hold_plus.academic_year,
sem_aims_hold_plus.primary_level_code level_code,
sem_aims_hold_plus.primary_level_desc level_desc,
sem_aims_hold_plus.class_code,
sem_aims_hold_plus.class_desc,
sem_aims_hold_plus.overall_hours_earned,
case
  when sem_aims_hold_plus.overall_hours_earned is not null then
    case
      when sem_aims_hold_plus.overall_hours_earned <= 24 then '0-24'
      when sem_aims_hold_plus.overall_hours_earned > 24 and sem_aims_hold_plus.overall_hours_earned <= 54 then '>24-54'
      when sem_aims_hold_plus.overall_hours_earned > 54 and sem_aims_hold_plus.overall_hours_earned <= 84 then '>54-84'
      else '> 85'
    end
  else null
end overall_hours_group,

sem_aims_hold_plus.overall_gpa,
case
  when sem_aims_hold_plus.overall_gpa is not null then
    case
      when sem_aims_hold_plus.overall_gpa <= .5 then '0.0-0.5'
      when sem_aims_hold_plus.overall_gpa > .5 and sem_aims_hold_plus.overall_gpa <= 1 then '0.5-1.0'
      when sem_aims_hold_plus.overall_gpa > 1 and sem_aims_hold_plus.overall_gpa <= 1.5 then '1.0-1.5'
      when sem_aims_hold_plus.overall_gpa > 1.5 and sem_aims_hold_plus.overall_gpa <= 2.0 then '1.5-2.0'
      when sem_aims_hold_plus.overall_gpa > 2.0 and sem_aims_hold_plus.overall_gpa <= 2.5 then '2.0-2.5'
      when sem_aims_hold_plus.overall_gpa > 2.5 and sem_aims_hold_plus.overall_gpa <= 3.0 then '2.5-3.0'
      when sem_aims_hold_plus.overall_gpa > 3.0 and sem_aims_hold_plus.overall_gpa <= 3.5 then '3.0-3.5'
      when sem_aims_hold_plus.overall_gpa > 3.5 and sem_aims_hold_plus.overall_gpa <= 4.0 then '3.5-4.0'
      when sem_aims_hold_plus.overall_gpa > 4 then '> 4.0'
    end 
  else null
end overall_gpa_group,
sem_aims_hold_plus.um_major_code major_code,
sem_aims_hold_plus.um_major_desc,
sem_aims_hold_plus.county_code,
sem_aims_hold_plus.county_desc,
sem_aims_hold_plus.commutable,

sem_aims_hold_plus.college_code_after_um_1,
sem_aims_hold_plus.college_name_after_um_1,
sem_aims_hold_plus.college_state_after_um_1,
sem_aims_hold_plus.major_after_um_1,

sem_aims_hold_plus.attended_2_yr_after_um,
sem_aims_hold_plus.attended_4_yr_after_um,
case
  when sem_aims_hold_plus.attended_2_yr_after_um = 'Y' and sem_aims_hold_plus.attended_4_yr_after_um = 'Y' then '2 and 4 year after'
  when sem_aims_hold_plus.attended_2_yr_after_um = 'Y' and sem_aims_hold_plus.attended_4_yr_after_um = 'N' then '2 year after'
  when sem_aims_hold_plus.attended_2_yr_after_um = 'N' and sem_aims_hold_plus.attended_4_yr_after_um = 'Y' then '4 year after'
  else null
end two_four_year_after_group,

sem_aims_hold_plus.attended_public_after_um,
sem_aims_hold_plus.attended_private_after_um,
case
  when sem_aims_hold_plus.attended_public_after_um = 'Y' and sem_aims_hold_plus.attended_private_after_um = 'Y' then 'Public and Private after'
  when sem_aims_hold_plus.attended_public_after_um = 'N' and sem_aims_hold_plus.attended_private_after_um = 'Y' then 'Private after'
  when sem_aims_hold_plus.attended_public_after_um = 'Y' and sem_aims_hold_plus.attended_private_after_um = 'N' then 'Public after'
  else null
end public_private_after_group,

sem_aims_hold_plus.aidy_code,
sem_aims_hold_plus.efc,
case
  when sem_aims_hold_plus.efc is not null then
    case
      when sem_aims_hold_plus.efc <= 25000 then '0-25k'
      when sem_aims_hold_plus.efc > 25000 and sem_aims_hold_plus.efc <= 50000 then '25k-50k'
      when sem_aims_hold_plus.efc > 50000 and sem_aims_hold_plus.efc <= 75000 then '50k-75k'
      when sem_aims_hold_plus.efc > 75000 and sem_aims_hold_plus.efc <= 100000 then '75k-100k'
      else '> 100k'
    end
  else null
end efc_group,

case
  when sem_aims_hold_plus.fasfa_ind = 'Y' then 'Y'
  else null
end fasfa_ind,

sem_aims_hold_plus.sapr_code,
sem_aims_hold_plus.sapr_status,
sem_aims_hold_plus.sapr_pckg_ind,
case
  when sem_aims_hold_plus.sapr_pckg_ind is not null then
    case
      when sem_aims_hold_plus.sapr_pckg_ind = 'N' then 'Satisfactory'
      else 'Unsatisfactory'
    end
  else null
end sapr_ind_group,

sem_aims_hold_plus.rcrapp2_c_depend_status,
case
  when sem_aims_hold_plus.rcrapp2_c_depend_status is not null then
    case
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '1' then 'Independent'
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '2' then 'Dependent'
      else null
    end
  else null
end dependent_status,
case
  when sem_aims_hold_plus.rcrapp2_c_depend_status is not null then
    case
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '1' then 57500
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '2' then 31000
      else null
    end
  else null
end loan_max,

sem_aims_hold_plus.family_income,
case 
  when sem_aims_hold_plus.family_income is not null then
    case
      when sem_aims_hold_plus.family_income <= 50000 then '<= 50000'
      else '> 50000'
    end
  else null
end family_income_group,

sem_aims_hold_plus.total_pell_usage_lds4_pell_leu rcrlds4_pell_leu,
case
  when sem_aims_hold_plus.total_pell_usage is not null then to_char(sem_aims_hold_plus.total_pell_usage, '9990.000') || '%'
  else null
end total_pell_usage,
case 
  when sem_aims_hold_plus.total_pell_usage is not null then
    case
      when sem_aims_hold_plus.total_pell_usage <= 450 then '0-450%'
      when sem_aims_hold_plus.total_pell_usage > 450 and sem_aims_hold_plus.total_pell_usage <= 600 then '450%-600%'
      else 'Over 600%'
    end
  else null
end pell_usage_group,


sem_aims_hold_plus.rcrlds4_agt_comb_total,
case
  when sem_aims_hold_plus.rcrlds4_agt_comb_total is not null then
    case
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '1' then (57500 - sem_aims_hold_plus.rcrlds4_agt_comb_total)
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '2' then (31000 - sem_aims_hold_plus.rcrlds4_agt_comb_total)
      else null
    end
  else null
end loan_eligibity_remaining,

case
  when sem_aims_hold_plus.rcrlds4_agt_comb_total is not null then
    case
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '1' then case
                                                                    when (57500 - sem_aims_hold_plus.rcrlds4_agt_comb_total) <= 0 then '<= 0'
                                                                    when (57500 - sem_aims_hold_plus.rcrlds4_agt_comb_total) > 0 and (57500 - sem_aims_hold_plus.rcrlds4_agt_comb_total) <= 5000 then '1-5000'
                                                                    when (57500 - sem_aims_hold_plus.rcrlds4_agt_comb_total) > 5000 and (57500 - sem_aims_hold_plus.rcrlds4_agt_comb_total) <= 10000 then '5001-10000'
                                                                    else '> 10000'
                                                                  end
      when sem_aims_hold_plus.rcrapp2_c_depend_status = '2' then case
                                                                    when (31000 - sem_aims_hold_plus.rcrlds4_agt_comb_total) <= 0 then '<= 0'
                                                                    when (31000 - sem_aims_hold_plus.rcrlds4_agt_comb_total) > 0 and (31000 - sem_aims_hold_plus.rcrlds4_agt_comb_total) <= 5000 then '1-5000'
                                                                    when (31000 - sem_aims_hold_plus.rcrlds4_agt_comb_total) > 5000 and (31000 - sem_aims_hold_plus.rcrlds4_agt_comb_total) <= 10000 then '5001-10000'
                                                                    else '> 10000'
                                                                  end
      else null
    end
  else null
end loan_elig_remaining_group

from sem_aims_hold_plus

order by sem_aims_hold_plus.order_num;
