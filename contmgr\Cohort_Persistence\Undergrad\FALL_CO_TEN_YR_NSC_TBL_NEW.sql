-- Refactored version of FALL_CO_TEN_YR_NSC_TBL.sql using CTEs and LEFT JOINs for performance and readability

WITH cohort_base AS (
  SELECT
    CO.CO_PIDM,
    CO.CO_TERM_CODE_KEY,
    CO.PRIMARY_DEGREE_CODE,
    CO.START_DATE
  FROM CO_POP_UNDUP_TBL CO
  WHERE CO.CO_TERM_CODE_KEY LIKE '%10'
),
td_fall AS (
  SELECT
    TD.SD_PIDM,
    TD.SD_TERM_CODE,
    TD.REGISTERED_IND AS FRST_FALL_TERM_REG_IND,
    TD.STUDENT_TYPE_CODE AS FRST_FALL_STD_TYPE,
    TD.PRIMARY_MAJOR_1 AS FRST_FALL_MJR_CODE,
    TD.PRIMARY_COLLEGE_CODE AS FRST_FALL_COL_CODE
  FROM TD_STUDENT_DATA TD
),
nsc_fall AS (
  SELECT
    NSC.CO_PIDM,
    NSC.TERM_CODE_ENROLLMENT_BEGIN,
    NSC.REGISTERED_IND AS FRST_FALL_TERM_REG_NSC,
    NSC.COLLEGE_NAME AS FRST_FALL_COL_NSC,
    NSC.ENROLLMENT_MAJOR AS FRST_FALL_MJR_NSC
  FROM CO_NSC_STUDENT_DATA_TBL NSC
),
um_fall AS (
  SELECT
    UM.SD_PIDM,
    UM.SD_TERM_CODE,
    UM.TERM_GPA AS FRST_FALL_TERM_GPA,
    UM.MOST_RECENT_ASTD_CODE AS FRST_FALL_ASTD_CODE,
    UM.TERM_HOURS_ATTEMPTED AS FRST_FALL_CH_ATMPT,
    CASE WHEN UM.TERM_HOURS_ATTEMPTED > 0 THEN ROUND(UM.TERM_HOURS_PASSED / UM.TERM_HOURS_ATTEMPTED, 2) END AS FRST_FALL_CH_CMPLTN
  FROM UM_STUDENT_DATA UM
),
nsc_enrolled AS (
  SELECT
    NSC.CO_PIDM,
    MIN(NSC.TWOYEAR_FOURYEAR) AS TWOFOUR_NSC_ENROLLED
  FROM CO_NSC_STUDENT_DATA_TBL NSC
  GROUP BY NSC.CO_PIDM
),
nsc_degree AS (
  SELECT
    NSCD.CO_PIDM,
    NSCD.TWOYEAR_FOURYEAR AS TWOFOUR_NSC_DEGREE
  FROM CO_NSC_DEGREE_TBL NSCD
),
umf_grad_date AS (
  SELECT
    UMD.PIDM,
    UMD.DEGREE_CODE,
    MIN(UMD.GRAD_DATE) AS UMF_GRAD_DATE
  FROM um_degree UMD
  WHERE UMD.DEGREE_STATUS = 'AW'
    AND UMD.LEVEL_CODE = 'UG'
  GROUP BY UMD.PIDM, UMD.DEGREE_CODE
)
INSERT INTO CO_TEN_YR_NSC_TBL
SELECT DISTINCT
  CB.CO_PIDM,
  CB.CO_TERM_CODE_KEY,
  -- FIRST FALL
  TD.SD_TERM_CODE AS FRST_FALL_TERM_CODE,
  TD.FRST_FALL_TERM_REG_IND,
  NSC.FRST_FALL_TERM_REG_NSC,
  NSC.FRST_FALL_COL_NSC,
  NSC.FRST_FALL_MJR_NSC,
  TD.FRST_FALL_STD_TYPE,
  TD.FRST_FALL_MJR_CODE,
  TD.FRST_FALL_COL_CODE,
  UM.FRST_FALL_TERM_GPA,
  UM.FRST_FALL_ASTD_CODE,
  UM.FRST_FALL_CH_ATMPT,
  UM.FRST_FALL_CH_CMPLTN,
  ENR.TWOFOUR_NSC_ENROLLED,
  DEG.TWOFOUR_NSC_DEGREE,
  -- Add additional columns for other terms using similar LEFT JOINs and CTEs
  UGD.UMF_GRAD_DATE
FROM cohort_base CB
LEFT JOIN td_fall TD
  ON TD.SD_PIDM = CB.CO_PIDM AND TD.SD_TERM_CODE = CB.CO_TERM_CODE_KEY
LEFT JOIN nsc_fall NSC
  ON NSC.CO_PIDM = CB.CO_PIDM AND NSC.TERM_CODE_ENROLLMENT_BEGIN = CB.CO_TERM_CODE_KEY
LEFT JOIN um_fall UM
  ON UM.SD_PIDM = CB.CO_PIDM AND UM.SD_TERM_CODE = CB.CO_TERM_CODE_KEY
LEFT JOIN nsc_enrolled ENR
  ON ENR.CO_PIDM = CB.CO_PIDM
LEFT JOIN nsc_degree DEG
  ON DEG.CO_PIDM = CB.CO_PIDM
LEFT JOIN umf_grad_date UGD
  ON UGD.PIDM = CB.CO_PIDM AND UGD.DEGREE_CODE = CB.PRIMARY_DEGREE_CODE
;