spool c:\users\<USER>\desktop\output.csv

select
AIMSMGR.td_demographic.dm_pidm,
AIMSMGR.td_demographic.td_term_code,
AIMSMGR.td_demographic.report_ethnicity,
AIMSMGR.td_demographic.gender,
AIMSMGR.td_demographic.last_name,
td_sd_join.primary_level_code,
td_sd_join.student_type_code,
td_sd_join.student_status_code,
td_sd_join.full_part_time_ind_umf

from AIMSMGR.td_demographic

left outer join(
select
td_student_data.sd_pidm,
td_student_data.sd_term_code,
td_student_data.primary_level_code,
td_student_data.student_type_code,
td_student_data.student_status_code,
td_student_data.full_part_time_ind_umf

from td_student_data
)  td_sd_join on td_sd_join.sd_pidm = AIMSMGR.td_demographic.dm_pidm
             and td_sd_join.sd_term_code = AIMSMGR.td_demographic.td_term_code
              

where 
AIMSMGR.td_demographic.last_name = 'Getty'
and
AIMSMGR.td_demographic.td_term_code ='201310'