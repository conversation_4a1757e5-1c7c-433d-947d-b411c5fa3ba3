
select 
td_demographic.dm_pidm pidm,
td_demographic.td_term_code term_code,
REGEXP_REPLACE(td_demographic.hsch_gpa, '[^0-9.]+', '') hsch_gpa,
td_demographic.hsch_grad_date hsch_grad_date,
REGEXP_REPLACE(td_demographic.pcol_gpa_transferred_1, '[^0-9.]+', '') pcol_gpa,
td_demographic.pcol_trans_recv_date_1,
REGEXP_REPLACE(test_join.act_english, '[^0-9.]+', '') act_eng,
test_join.ACT_ENGLISH_DATE act_eng_date,
REGEXP_REPLACE(test_join.act_math, '[^0-9.]+', '') act_math,
test_join.ACT_MATH_DATE,
REGEXP_REPLACE(test_join.act_reading, '[^0-9.]+', '') act_reading,
test_join.ACT_READING_DATE,
REGEXP_REPLACE(test_join.ACT_SCIENCE_REASONING, '[^0-9.]+', '') ACT_SCI_REASON,
test_join.ACT_SCIENCE_REASONING_DATE,
REGEXP_REPLACE(test_join.ACT_COMPOSITE, '[^0-9.]+', '') ACT_composit,
test_join.ACT_COMPOSITE_DATE,
REGEXP_REPLACE(test_join.SAT_VERBAL, '[^0-9.]+', '') sat_verbal,                            
test_join.SAT_VERBAL_DATE,
REGEXP_REPLACE(test_join.SAT_MATHEMATICS, '[^0-9.]+', '')sat_math,                            
test_join.SAT_MATHEMATICS_DATE sat_math_date,
REGEXP_REPLACE(test_join.GMAT_TOTAL_SCORE, '[^0-9.]+', '')gmat_total_score,                           
test_join.GMAT_TOTAL_SCORE_DATE,
REGEXP_REPLACE(test_join.GMAT_VERBAL_SCORE, '[^0-9.]+', '')gmat_verbal_score,                          
test_join.GMAT_VERBAL_SCORE_DATE,
REGEXP_REPLACE(test_join.GMAT_QUANTITATIVE_SCORE, '[^0-9.]+', '')gmat_quant_score,                    
test_join.GMAT_QUANTITATIVE_SCORE_DATE gmat_quant_score_date,
REGEXP_REPLACE(test_join.GRE_VERBAL, '[^0-9.]+', '')gre_verbal,                                
test_join.GRE_VERBAL_DATE gre_verbal_date,
REGEXP_REPLACE(test_join.GRE_QUANTITATIVE, '[^0-9.]+', '')gre_quantitative,                           
test_join.GRE_QUANTITATIVE_DATE gre_quantitative_date,
REGEXP_REPLACE(test_join.GRE_ANALYTICAL_WRITING, '[^0-9.]+', '')gre_anal_writ,                     
test_join.GRE_ANALYTICAL_WRITING_DATE gre_anal_writ,
REGEXP_REPLACE(test_join.MATH_PLACEMENT, '[^0-9.]+', '')math_placement,
test_join.MATH_PLACEMENT_DATE math_placement,
test_join.ENG_WRIT_PLACEMENT_DESC,
test_join.ENG_WRIT_PLACEMENT_DATE,
REGEXP_REPLACE(test_join.COMP_SC_PLACEMENT, '[^0-9.]+', '')comp_sc_placement,
test_join.COMP_SC_PLACEMENT_DATE


from
td_demographic

inner join (
SELECT 
* 
FROM
td_test_score
) test_join on test_join.pidm = td_demographic.dm_pidm
           and test_join.td_term_code = td_demographic.td_term_code
           
