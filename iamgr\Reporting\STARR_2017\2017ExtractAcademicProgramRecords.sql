with pop_sel as(
  select
  spriden.spriden_pidm,
  spriden.spriden_id,
  shrtgpa_join.shrtgpa_term_code,
  shrtgpa_join.sgbstdn_levl_code,
  (select stvmajr.stvmajr_desc from stvmajr where stvmajr.stvmajr_code = shrtgpa_join.sgbstdn_majr_code_1) sgbstdn_majr_code_1_desc,
  (select stvmajr.stvmajr_cipc_code from stvmajr where stvmajr.stvmajr_code = shrtgpa_join.sgbstdn_majr_code_1) stvmajr_cipc_code,
  shrtgpa_join.stvdegc_acat_code,
  shrtgpa_join.sgbstdn_styp_code,
  shrtgpa_join.shrtgpa_gpa,
  (select sum(shrtgpa.shrtgpa_gpa_hours) from shrtgpa where shrtgpa.shrtgpa_pidm = spriden.spriden_pidm
                                                      and shrtgpa.shrtgpa_levl_code = shrtgpa_join.sgbstdn_levl_code
                                                      and shrtgpa.shrtgpa_term_code <= shrtgpa_join.shrtgpa_term_code) overall_gpa_hours,
  (select sum(shrtgpa.shrtgpa_quality_points) from shrtgpa where shrtgpa.shrtgpa_pidm = spriden.spriden_pidm
                                                           and shrtgpa.shrtgpa_levl_code = shrtgpa_join.sgbstdn_levl_code
                                                           and shrtgpa.shrtgpa_term_code <= shrtgpa_join.shrtgpa_term_code) overall_quality_points

  from spriden
  -- start term of our student popsel
  inner join(
    select 
    max(stvterm.stvterm_code) as start_term_code
    from stvterm
    where stvterm.stvterm_code = '201630'
  ) start_term on start_term.start_term_code <> '999999'
  -- end term of our student pop sel
  inner join(
    select max(stvterm.stvterm_code) as end_term_code
    from stvterm
    where stvterm.stvterm_code = '201740'
  ) end_term on end_term.end_term_code <> '999999'
  -- our begining of time (first banner term)
  inner join(
    select 
    stvterm.stvterm_code as bobt_term_code
    from stvterm
    where stvterm.stvterm_code = '199910'
  ) begining_of_banner_term on begining_of_banner_term.bobt_term_code <> '999999'
  inner join(
    select
    shrtgpa.shrtgpa_pidm,
    shrtgpa.shrtgpa_term_code,
    shrtgpa.shrtgpa_gpa,
    shrtgpa.shrtgpa_gpa_type_ind,
    sgbstdn_join.sgbstdn_levl_code,
    sgbstdn_join.sgbstdn_degc_code_1,
    sgbstdn_join.stvdegc_acat_code,
    sgbstdn_join.sgbstdn_majr_code_1,
    sgbstdn_join.sgbstdn_styp_code
    from shrtgpa
    inner join(
      select
      sgbstdn.sgbstdn_pidm,
      sgbstdn.sgbstdn_term_code_eff,
      sgbstdn.sgbstdn_levl_code,
      sgbstdn.sgbstdn_degc_code_1,
      (select stvdegc.stvdegc_acat_code from stvdegc where stvdegc.stvdegc_code = sgbstdn.sgbstdn_degc_code_1) stvdegc_acat_code,
      sgbstdn.sgbstdn_styp_code,
      sgbstdn.sgbstdn_majr_code_1
      from sgbstdn
    ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                   and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
                   and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                             from sgbstdn sgbstdn_1
                                                             where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                             and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
  )shrtgpa_join on shrtgpa_join.shrtgpa_pidm = spriden.spriden_pidm
                and shrtgpa_join.shrtgpa_term_code >= begining_of_banner_term.bobt_term_code
                and shrtgpa_join.shrtgpa_term_code <= end_term.end_term_code
                and shrtgpa_join.shrtgpa_gpa_type_ind = 'I'
  
  -- has to have a UID
  inner join(
    select 
    goradid.goradid_pidm,
    goradid.goradid_additional_id
    from goradid
    where goradid.goradid_adid_code = 'UIC'
    and goradid.goradid_additional_id is not null
    and goradid.goradid_additional_id != '9175170611' -- this one has two uics in banner
  ) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
  -- spbpers on not dead
  inner join(
    select
    spbpers.spbpers_pidm
    from spbpers
    where spbpers.spbpers_dead_ind is null
  )spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
  where spriden.spriden_change_ind is null
  and exists (select
              'has course work in their primary level in these terms'
              from shrtgpa
              inner join(
                select
                sgbstdn.sgbstdn_pidm,
                sgbstdn.sgbstdn_term_code_eff,
                sgbstdn.sgbstdn_levl_code
                from sgbstdn
              ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                             and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                       from sgbstdn sgbstdn_1
                                                                       where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                       and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                             and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
              where shrtgpa.shrtgpa_gpa_type_ind = 'I'
              and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
              and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
              and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
)
select

pop_sel.spriden_id as "LocalStudentId",

--to_char(to_number(substr(pop_sel.shrtgpa_term_code, 1, 4)) - 1) || '-' || substr(pop_sel.shrtgpa_term_code, 1, 4)
--as "AcademicYearDesignator",

--(select to_char(stvterm.stvterm_start_date, 'YYYY-MM') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code) 
--as "SessionDesignator",

-- 2017
(select to_char(stvterm.stvterm_start_date, 'YYYY-MM-DD') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code) 
as "SessionBeginDate",
(select to_char(stvterm.stvterm_end_date, 'YYYY-MM-DD') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code) 
as "SessionEndDate",

case substr(pop_sel.shrtgpa_term_code, 5, 2)
  when '10' then 'Fall'
  when '20' then 'Winter'
  when '30' then 'Spring'
  when '40' then 'Summer'
  else 'Other'
end as "SessionName",

substr(pop_sel.stvmajr_cipc_code, 1, 2) || '.' || substr(pop_sel.stvmajr_cipc_code, 3, 6)
as "ProgramCIPCode",

'Major' as "AcademicProgramType",

substr(pop_sel.sgbstdn_majr_code_1_desc, 1, 60) as "AcademicProgramName"
from pop_sel
--where pop_sel.spriden_pidm in (91183)
order by (select to_char(stvterm.stvterm_start_date, 'YYYY-MM') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code)


