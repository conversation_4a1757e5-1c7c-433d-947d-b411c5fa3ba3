select 
--sum_cur.pidm,
sum_cur.grad_yr,
sum_cur.level_code,
sum_cur.pell_ind,
--pell_recieved
count(sum_cur.pidm) student_count
from (
  with grad_cur as(
    select
    distinct um_degree.pidm,
    um_degree.level_code,
    '14-15' grad_yr
    from um_degree
    where um_degree.grad_term_code in ('201440','201510','201520','201530')
    and um_degree.degree_status = 'AW'
    union all
    select 
    distinct um_degree.pidm,
    um_degree.level_code,
    '15-16' grad_yr
    from um_degree
    where 
    um_degree.grad_term_code in ('201540','201610','201620','201630')
    and um_degree.degree_status = 'AW'
  )
  select
  grad_cur.pidm,
  grad_cur.grad_yr,
  pell_recieved,
  case
    when grad_cur.level_code in ('UG','U2','U3') then 'UG'
    else 'GR'
  end level_code,
  case
    when pell_join.pell_recieved > 0 then 'Y'
    else 'N'
  end pell_ind
  from grad_cur
  left outer join(
    select
    IA_FA_FUND_BASE.fa_pidm,
 sum(IA_FA_FUND_BASE.FA_PAID_AMT) pell_recieved
--    sum(nvl(IA_FA_FUND_BASE.FA_PAID_AMT, 0)) pell_recieved
    from IA_FA_FUND_BASE
    where IA_FA_FUND_BASE.FA_FUND_CODE = '1PELL'
    group by IA_FA_FUND_BASE.fa_pidm
  )pell_join on pell_join.fa_pidm = grad_cur.pidm
)sum_cur
group by sum_cur.grad_yr, sum_cur.level_code, sum_cur.pell_ind
order by sum_cur.grad_yr, sum_cur.level_code, sum_cur.pell_ind
;
