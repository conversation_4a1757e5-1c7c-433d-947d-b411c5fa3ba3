create or replace PACKAGE BODY "ZDMCOUPUD" AS
--------------------------------------------------------------------------------
-------------------------------------STEP 1-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 1A: Open Cursor to get a count of records in the co_pop_undup_tbl for the 
current term
*******************************************************************************/
  CURSOR current_term_cur IS
  SELECT
    COUNT(*) cntr
  FROM
    co_gr_pop_tbl
  WHERE
    co_gr_term_code_key = (select current_term from um_current_term);  

/*******************************************************************************
STEP 1B: Declaration of current_term_array
*******************************************************************************/
  TYPE current_term_array IS
    TABLE OF current_term_cur%rowtype;

  --createing a pointer to a row of the current_term_array
  current_term_rec current_term_array;

--------------------------------------------------------------------------------
-------------------------------------STEP 2-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 2A: create procededure to update cohort population fIN aid tables
*******************************************************************************/
  PROCEDURE p1_update_co_pop AS

 /*******************************************************************************
STEP 2B: Open curser to build a single cell array with the count of records in 
the cursor built in STEP 1B.
*******************************************************************************/
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Beginning p_update_co_pop procedure');
    dbms_output.put_line('Opening current_term_cur');
--    BEGIN
      OPEN current_term_cur;
      FETCH current_term_cur
      BULK COLLECT INTO current_term_rec;
      CLOSE current_term_cur;
      dbms_output.put_line('Closing current_term_cur');
--    EXCEPTION
--      WHEN OTHERS THEN
--        dbms_output.put_line('Opening current_term_cur failed');
--    END;

/*******************************************************************************
STEP 2C: Branching if statment to determine if the current term data is loaded
or not into the cohort population table.
*******************************************************************************/
    IF
      current_term_rec.count > 0
      AND current_term_rec(1).cntr = 0
    THEN
      dbms_output.put_line('Current term data not present, begin updating cohort tables');

/*******************************************************************************
STEP 2D:  Trancate and backup cohort population and FA tables for UG and GR
*******************************************************************************/
      dbms_output.put_line('Truncating co_pop_undup_tbl_bac table');
      EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_POP_UNDUP_TBL_BAC' );
      dbms_output.put_line('Truncating co_gr_pop_tbl_bac table');
      EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_POP_TBL_BAC' );
      dbms_output.put_line('Truncating co_fa_tbl_bac table');
      EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_FA_TBL_BAC' );

/*******************************************************************************
STEP 2E: Insert into backup cohort population tables for UG 
and GR
*******************************************************************************/ 
--UG Backup
      dbms_output.put_line('Inserting data into co_pop_undup_tbl_bac');
--      BEGIN
        INSERT INTO co_pop_undup_tbl_bac
          SELECT
            *
          FROM
            co_pop_undup_tbl;

--      EXCEPTION
--        WHEN OTHERS THEN
--          dbms_output.put_line('Inserting data into co_pop_undup_tbl_bac failed');
--      END;
--GR Backup 
      dbms_output.put_line('Inserting data into co_gr_pop_tbl_bac');
--      BEGIN
        INSERT INTO co_gr_pop_tbl_bac
          SELECT
            *
          FROM
            co_gr_pop_tbl;

--      EXCEPTION
--        WHEN OTHERS THEN
--          dbms_output.put_line('Inserting data into co_gr_pop_tbl_bac failed');
--      END;
--Fin Aid Backup
      dbms_output.put_line('Inserting data into co_fa_tbl_bac');
--      BEGIN
        INSERT INTO co_fa_tbl_bac
          SELECT
            *
          FROM
            co_fa_tbl;

--      EXCEPTION
--        WHEN OTHERS THEN
--          dbms_output.put_line('Inserting data into co_fa_tbl_bac failed');
--      END;

      COMMIT;


/*******************************************************************************
STEP 2F: insert current term data into cohort population tables
*******************************************************************************/ 
--UG cohort table update
      dbms_output.put_line('Inserting data into co_pop_undup_tbl');
      INSERT INTO co_pop_undup_tbl
        SELECT
          *
        FROM
          co_pop_undup
        WHERE
          co_term_code_key = (
            SELECT
              current_term
            FROM
              um_current_term
          );

--GR cohort table update    
      dbms_output.put_line('Inserting data into co_gr_pop_tbl');
      INSERT INTO co_gr_pop_tbl
        SELECT
          *
        FROM
          CO_GR_POP
        WHERE
          co_gr_term_code_key = (
            SELECT
              current_term
            FROM
              um_current_term
          );

      COMMIT;

/*******************************************************************************
STEP 2G: Update Fin Aid Table
*******************************************************************************/
      dbms_output.put_line('Inserting data into CO_FA_TBL');
      p_co_fa_tbl;
      COMMIT;
    ELSE
      dbms_output.put_line('Current term already loaded with: '
                           || current_term_rec(1).cntr
                           || ' Students');
    END IF;

/*******************************************************************************
STEP 2H: End the procedures for step 2
*******************************************************************************/
  dbms_output.put_line('Ending p_update_co_pop procedure');
  END p1_update_co_pop;

--------------------------------------------------------------------------------
-------------------------------------STEP 3-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 3A: create procededure to update NSC student and degree tables
*******************************************************************************/
  PROCEDURE p2_update_co_nsc AS

/*******************************************************************************
STEP 3B:  Trancate  NSC student and degree backup tables
*******************************************************************************/
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Beginning p_update_co_nsc procedure');
    dbms_output.put_line('Truncating co_nsc_student_data_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_NSC_STUDENT_DATA_TBL_BAC' );
    dbms_output.put_line('Truncating co_nsc_degree_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_NSC_DEGREE_TBL_BAC' );

/*******************************************************************************
STEP 3C: Insert current term data into backup NSC student and degree tables
*******************************************************************************/ 
--Student data table
    dbms_output.put_line('Inserting data into co_nsc_student_data_tbl_bac');
    INSERT INTO co_nsc_student_data_tbl_bac
      SELECT
        *
      FROM
        co_nsc_student_data_tbl; 

--Degree data table
    dbms_output.put_line('Inserting data into co_nsc_degree_tbl_bac');
    INSERT INTO co_nsc_degree_tbl_bac
      SELECT
        *
      FROM
        co_nsc_degree_tbl;

    COMMIT;

/*******************************************************************************
STEP 3D: Trancate NSC student and degree tables
*******************************************************************************/
    dbms_output.put_line('Truncating co_nsc_student_data_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_NSC_STUDENT_DATA_TBL' );
    dbms_output.put_line('Truncating co_nsc_degree_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_NSC_DEGREE_TBL' );

/*******************************************************************************
STEP 3E: Insert data into NSC student and degree tables
*******************************************************************************/  
--Student data table
    dbms_output.put_line('Inserting data into co_nsc_student_data_tbl');
    p_co_nsc_student_data_tbl;

--Degree data table
    dbms_output.put_line('Inserting data into co_nsc_degree_tbl');
    p_co_nsc_degree_tbl;
    COMMIT;
  dbms_output.put_line('Ending p_update_co_nsc procedure');
  END p2_update_co_nsc;
--------------------------------------------------------------------------------
-------------------------------------STEP 4-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 4A: create procededure to update 10 year nsc tables
*******************************************************************************/
  PROCEDURE p3_update_co_ten_yr_nsc AS
/*******************************************************************************
STEP 4B:  Trancate 10 yr backup tables for UG and GR
*******************************************************************************/
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Truncating co_ten_yr_nsc_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_TEN_YR_NSC_TBL_BAC' );
    dbms_output.put_line('Truncating co_gr_ten_yr_nsc_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_TEN_YR_NSC_TBL_BAC' );

/*******************************************************************************
STEP 4C: Insert current term data into backup cohort population tables for UG 
and GR
*******************************************************************************/ 
--UG Backup
    dbms_output.put_line('Inserting data into co_ten_yr_nsc_tbl_bac');
    INSERT INTO co_ten_yr_nsc_tbl_bac
      SELECT
        *
      FROM
        co_ten_yr_nsc_tbl; 

--GR Backup 
    dbms_output.put_line('Inserting data into co_gr_ten_yr_nsc_tbl_bac');
    INSERT INTO co_gr_ten_yr_nsc_tbl_bac
      SELECT
        *
      FROM
        co_gr_ten_yr_nsc_tbl;

    COMMIT;  

/*******************************************************************************
STEP 4D: Trancate 10 yr tables for UG and GR
*******************************************************************************/
    dbms_output.put_line('Truncating co_ten_yr_nsc_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_TEN_YR_NSC_TBL' );
    dbms_output.put_line('Truncating co_gr_ten_yr_nsc_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_TEN_YR_NSC_TBL' );

/*******************************************************************************
STEP 4E: Insert data into 10 yr tables for UG and GR
*******************************************************************************/  
--UG
    dbms_output.put_line('Inserting data into fall_co_ten_yr_nsc_tbl');
    p_fall_co_ten_yr_nsc_tbl;
    dbms_output.put_line('Inserting data into win_co_ten_yr_nsc_tbl');
    p_win_co_ten_yr_nsc_tbl;
    dbms_output.put_line('Inserting data into spr_co_ten_yr_nsc_tbl');
--    p_spr_co_ten_yr_nsc_tbl;

--GR
    dbms_output.put_line('Inserting data into fall_co_gr_ten_yr_nsc_tbl');
    p_fall_co_gr_ten_yr_nsc_tbl;
    dbms_output.put_line('Inserting data into win_co_gr_ten_yr_nsc_tbl');
    p_win_co_gr_ten_yr_nsc_tbl;
    dbms_output.put_line('Inserting data into spr_co_gr_ten_yr_nsc_tbl');
--    p_spr_co_gr_ten_yr_nsc_tbl;
    dbms_output.put_line('Inserting data into sum_co_gr_ten_yr_nsc_tbl');
--    p_sum_co_gr_ten_yr_nsc_tbl;
    COMMIT;
  END p3_update_co_ten_yr_nsc;

--------------------------------------------------------------------------------
-------------------------------------STEP 5-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 5A: create procededure to update persistence tables
*******************************************************************************/
  PROCEDURE p4_update_co_persist AS

/*******************************************************************************
STEP 5B:  Trancate persist backup tables for UG and GR
*******************************************************************************/
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Truncating co_persist_nsc_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_PERSIST_NSC_TBL_BAC' );
    dbms_output.put_line('Truncating co_gr_persist_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_PERSIST_TBL_BAC' );

/*******************************************************************************
STEP 5C: Insert current term data into backup cohort population tables for UG 
and GR
*******************************************************************************/ 
--UG Backup
    dbms_output.put_line('Inserting data into co_persist_nsc_tbl_bac');
    INSERT INTO co_persist_nsc_tbl_bac
      SELECT
        *
      FROM
        co_persist_nsc_tbl; 

--GR Backup 
    dbms_output.put_line('Inserting data into co_gr_persist_tbl_bac');
    INSERT INTO co_gr_persist_tbl_bac
      SELECT
        *
      FROM
        co_gr_persist_tbl;

    COMMIT;  

/*******************************************************************************
STEP 5D: Trancate persist tables for UG and GR
*******************************************************************************/
    dbms_output.put_line('Truncating co_persist_nsc_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_PERSIST_NSC_TBL' );
    dbms_output.put_line('Truncating co_gr_persist_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_PERSIST_TBL' );


/*******************************************************************************
STEP 5E: Insert data into persist tables for UG and GR
*******************************************************************************/  
--UG
    dbms_output.put_line('Inserting data into co_persist_nsc_tbl');
    p_co_persist_nsc_tbl;
--GR
    dbms_output.put_line('Inserting data into co_gr_persist_tbl');
    p_co_gr_persist_tbl;
    COMMIT;
  END p4_update_co_persist;

--------------------------------------------------------------------------------  
-------------------------------------STEP 6-------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
STEP 6A: create procededure to update most recent data table
*******************************************************************************/
  PROCEDURE p5_update_most_recent AS

/*******************************************************************************
STEP 6B:  Trancate most recent backup data table
*******************************************************************************/
  BEGIN
    dbms_output.enable(NULL);
--UG
    dbms_output.put_line('Truncating co_most_recent_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_MOST_RECENT_TBL_BAC' );
--GR   
    dbms_output.put_line('Truncating co_gr_most_recent_tbl_bac table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_MOST_RECENT_TBL_BAC' );

/*******************************************************************************
STEP 6C: Insert most recent backup data table
*******************************************************************************/
--UG
    dbms_output.put_line('Inserting data into co_most_recent_tbl_bac');
    INSERT INTO co_most_recent_tbl_bac
      SELECT
        *
      FROM
        co_most_recent_tbl;
--GR
    dbms_output.put_line('Inserting data into co_gr_most_recent_tbl_bac');
    INSERT INTO co_gr_most_recent_tbl_bac
      SELECT
        *
      FROM
        co_gr_most_recent_tbl;
    COMMIT;  

/*******************************************************************************
STEP 6D: Trancate most recent backup data table
*******************************************************************************/
--UG
    dbms_output.put_line('Truncating co_most_recent_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_MOST_RECENT_TBL' );
--GR
    dbms_output.put_line('Truncating co_gr_most_recent_tbl table');
    EXECUTE IMMEDIATE ( 'TRUNCATE TABLE CO_GR_MOST_RECENT_TBL' );


/*******************************************************************************
STEP 6E: Insert most recent data table
*******************************************************************************/
--UG    
    dbms_output.put_line('Inserting data into co_most_recent_tbl');
    p_most_recent_tbl;
--GR    
    dbms_output.put_line('Inserting data into co_gr_most_recent_tbl');
    p_gr_most_recent_tbl;    

    COMMIT;
  END p5_update_most_recent;

--------------------------------------------------------------------------------  
-------------------------------------UNDO--------------------------------------
--------------------------------------------------------------------------------
/*******************************************************************************
UNDO: create procededure to undo all steps by truncating and restoring from backup
*******************************************************************************/

 -- Undo for Step 2: Cohort population and FA tables
  PROCEDURE undo_p1_update_co_pop AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_pop_undup_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_pop_undup_tbl');
    INSERT INTO co_pop_undup_tbl SELECT * FROM co_pop_undup_tbl_bac;

    dbms_output.put_line('Restoring co_gr_pop_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_pop_tbl');
    INSERT INTO co_gr_pop_tbl SELECT * FROM co_gr_pop_tbl_bac;

    dbms_output.put_line('Restoring co_fa_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_fa_tbl');
    INSERT INTO co_fa_tbl SELECT * FROM co_fa_tbl_bac;

    COMMIT;
  END undo_p1_update_co_pop;

  -- Undo for Step 3: NSC student and degree tables
  PROCEDURE undo_p2_update_co_nsc AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_nsc_student_data_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_nsc_student_data_tbl');
    INSERT INTO co_nsc_student_data_tbl SELECT * FROM co_nsc_student_data_tbl_bac;

    dbms_output.put_line('Restoring co_nsc_degree_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_nsc_degree_tbl');
    INSERT INTO co_nsc_degree_tbl SELECT * FROM co_nsc_degree_tbl_bac;

    COMMIT;
  END undo_p2_update_co_nsc;

  -- Undo for Step 4: 10 year NSC tables
  PROCEDURE undo_p3_update_co_ten_yr_nsc AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_ten_yr_nsc_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_ten_yr_nsc_tbl');
    INSERT INTO co_ten_yr_nsc_tbl SELECT * FROM co_ten_yr_nsc_tbl_bac;

    dbms_output.put_line('Restoring co_gr_ten_yr_nsc_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_ten_yr_nsc_tbl');
    INSERT INTO co_gr_ten_yr_nsc_tbl SELECT * FROM co_gr_ten_yr_nsc_tbl_bac;

    COMMIT;
  END undo_p3_update_co_ten_yr_nsc;

  -- Undo for Step 5: Persistence tables
  PROCEDURE undo_p4_update_co_persist AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_persist_nsc_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_persist_nsc_tbl');
    INSERT INTO co_persist_nsc_tbl SELECT * FROM co_persist_nsc_tbl_bac;

    dbms_output.put_line('Restoring co_gr_persist_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_persist_tbl');
    INSERT INTO co_gr_persist_tbl SELECT * FROM co_gr_persist_tbl_bac;

    COMMIT;
  END undo_p4_update_co_persist;

  -- Undo for Step 6: Most recent data tables
  PROCEDURE undo_p5_update_most_recent AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_most_recent_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_most_recent_tbl');
    INSERT INTO co_most_recent_tbl SELECT * FROM co_most_recent_tbl_bac;

    dbms_output.put_line('Restoring co_gr_most_recent_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_most_recent_tbl');
    INSERT INTO co_gr_most_recent_tbl SELECT * FROM co_gr_most_recent_tbl_bac;

    COMMIT;
  END undo_p5_update_most_recent;



END ZDMCOUPUD;