SELECT
  M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNT,
  M_GLDW1.REV_EXP_ACT_LEDGER.FUND_CODE,
  M_GLDW1.REV_EXP_ACT_LEDGER.DEPTID,
  M_GLDW1.REV_EXP_ACT_LEDGER.PROGRAM_CODE,
  M_GLDW1.REV_EXP_ACT_LEDGER.FISCAL_YEAR,
  M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNTING_PERIOD,
  sum(M_GLDW1.REV_EXP_ACT_LEDGER.DOLLAR_AMOUNT),
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE
FROM
  M_GLDW1.REV_EXP_ACT_LEDGER,
  M_GLDW1.CURR_ACCOUNT_VW,
  M_GLDW1.CURR_FN_DEPT_VW  REV_EXP_ACT_CURR_FN_DEPT_VW
WHERE
  ( M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNT=M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT  )
  AND  ( M_GLDW1.REV_EXP_ACT_LEDGER.DEPTID=REV_EXP_ACT_CURR_FN_DEPT_VW.DEPTID  )
  AND  
  (
   M_GLDW1.REV_EXP_ACT_LEDGER.FUND_CODE  IN  ('10000','20000','25000','30000','40000','50000','52000','80000','82000')
   AND
   M_GLDW1.REV_EXP_ACT_LEDGER.FISCAL_YEAR  IN  (2015)
   AND
   M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNTING_PERIOD  BETWEEN  9  AND  9
   AND
   REV_EXP_ACT_CURR_FN_DEPT_VW.DEPT_GRP_CAMPUS  IN  ( 'UM_FLINT'  )
  )
GROUP BY
  M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNT, 
  M_GLDW1.REV_EXP_ACT_LEDGER.FUND_CODE, 
  M_GLDW1.REV_EXP_ACT_LEDGER.DEPTID, 
  M_GLDW1.REV_EXP_ACT_LEDGER.PROGRAM_CODE, 
  M_GLDW1.REV_EXP_ACT_LEDGER.FISCAL_YEAR, 
  M_GLDW1.REV_EXP_ACT_LEDGER.ACCOUNTING_PERIOD, 
  M_GLDW1.CURR_ACCOUNT_VW.ACCOUNT_TYPE


