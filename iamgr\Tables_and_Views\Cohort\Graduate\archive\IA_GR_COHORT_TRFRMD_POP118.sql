/*
This query identifies the students in the Graduated Cohort files who had a
curriculum (major, program, level) change after his or her cohort term causing
the student to be lost in the Graduate Cohort Data Model when in fact the 
student was retained.
*/

create table IA_GR_COHORT_TRFMD_POP118 AS(
select
missing_persons.CO_GR_PIDM,
missing_persons.UMID,
missing_persons.CO_GR_TERM_CODE_KEY,
missing_persons.START_DATE,
missing_persons.CO_GR_IA_STUDENT_TYPE_CODE,
missing_persons.CO_GR_FULL_PART_IND_UMF,
missing_persons.CO_GR_ETHNICITY,
missing_persons.CO_GR_GENDER,
ia_td_student_data.primary_level_code,
missing_persons.CITIZENSHIP_CODE,
missing_persons.CITIZENSHIP_DESC,
missing_persons.PCOL_GPA_TRANSFERRED_1,
missing_persons.TRANS_GPA,
missing_persons.PCOL_DESC_1,
ia_td_student_data.primary_major_1,
ia_td_student_data.primary_program,
ia_td_student_data.PRIMARY_CONC_1

from (select
CO_GR_PIDM,
UMID,
CO_GR_TERM_CODE_KEY,
START_DATE,
CO_GR_IA_STUDENT_TYPE_CODE,
CO_GR_FULL_PART_IND_UMF,
CO_GR_ETHNICITY,
CO_GR_GENDER,
PRIMARY_LEVEL_CODE,
CITIZENSHIP_CODE,
CITIZENSHIP_DESC,
PCOL_GPA_TRANSFERRED_1,
TRANS_GPA,
PCOL_DESC_1,
PRIMARY_MAJOR_1,
PRIMARY_PROGRAM,
PRIMARY_CONC_1
from
IA_GR_COHORT_PERSIST
where 
SCND_FALL_PERSIST = 'Lost')missing_persons
inner join ia_td_student_data on ia_td_student_data.sd_pidm = missing_persons.CO_GR_PIDM and
                                      ia_td_student_data.sd_term_code = TO_CHAR((to_number(missing_persons.CO_GR_TERM_CODE_KEY) + 100))
where ia_td_student_data.registered_ind = 'Y'
)
;
