create or replace view ia_alumni_Donor as (
select * from (
with data_pull as (
select
FY,
dart_id,
active_ind,
major_giving_prospect,
constituency_type,
formatted_name,
Full_name,
Title,
F_name,
L_name,
Gender,
age,
degrees,
spouse,
spouse_full_name,
spouse_degrees,
household_dart_id,
Mailing_name,
informal_mailing_name
from
ia_ALUMNI_DONOR_RAW
)
select data_pull.*,
case
when degrees like '%Flint%' Then 'Y'
else 'N'
end flint_degree_ind,
case
when degrees like 'A.B%' 
or degrees like 'B.A%' 
or degrees like 'B.S%' 
or degrees like 'B.F%' 
or degrees like 'B.B%' 
or degrees like 'B.G%' 
or degrees like 'Teach.Cert%'
then 'UG'
else 'GR'
end level_code
from data_pull)popcel
)
;