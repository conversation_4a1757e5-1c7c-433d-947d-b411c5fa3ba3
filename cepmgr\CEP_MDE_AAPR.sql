CREATE OR REPLACE VIEW cep_mde_aapr AS
 SELECT
  co.*,
  cer.pic,
  cer.licensetype,
  cer.programtype,
  cer.initialissuedsy,
  cer.issuedate,
  cer.endorsements,
  
    CASE
   When  cer.endorsements is null then 'No Record with MDE'
   WHEN  instr(cer.endorsements, ', , ,')  = '0' THEN
    'Additional Endorsements'
   ELSE
    'No Additional Endorsements'
  END  additional_endorsements,
  
--  CASE
--   WHEN ( instr(nvl(cer.endorsements, ', , ,'), ', , ,') ) = '0' THEN
--    'Additional Endorsements'
--   ELSE
--    'No Additional Endorsements'
--  END  additional_endorsements,
  CASE
   WHEN cer.pic IS NULL THEN
    'Not Certified'
   ELSE
    'Certified'
  END  certified_completer,
  nvl(edeff.effective_or_higher,'No Record with MDE') effective_or_higher,
  nvl(edeff.teach_in_area_endorsed,'No Record with MDE') teach_in_area_endorsed
 FROM
  umflint_persist_edu_prog_mv  co
  LEFT JOIN umflint_educator_cert        cer ON co.umid = lpad(cer.studentid, 8, '0')
  LEFT JOIN cep_rep_emp_and_edeff        edeff ON cer.pic = edeff.pic

--where caep_program_group  ('','')
  ;

SELECT --3300
*
FROM
 cep_mde_aapr;