--------------------------------------------------------
--  DDL for View IA_TD_STUDENT_DATA
--------------------------------------------------------

  CREATE OR REPLACE VIEW TAB_TD_STUDENT_DATA as
  (

Select
SD.SD_PIDM,
SD.SD_TERM_CODE,
fy.fy,
fy.fafy,
SD.SD_TERM_DESC,
SD.UMID,
DM.FIRST_GENERATION_STUDENT_IND,
--DM.SSN,

--case
--when PRIMARY_PROGRAM in ('DAP-HP-CRNA','DNP-HP','DNP-HP-NP','DPT-HP','DAP-CRNA'
--,'DNP','DNP-NR','DNP-NR-NP','DNP-NP','DPT','CERG-PTPP','CERG-PTPP-RS','CERG PTPP',
--'CERG-PTPP RS','CERG NUR','CERG-NUR','CERG NUR G2') then 'Drs-Prof' 
--when PRIMARY_PROGRAM in ('EDD','PHD') then 'Drs-Acad' 
--else class_code
--end HEIDI_Class_Code,



case
when INTL_IND is NULL and ia_student_type_code = 'F' then 'Dom_Freshman'
when INTL_IND is NULL and ia_student_type_code = 'T' then 'Dom_Transfer'
when INTL_IND is NULL and ia_student_type_code = 'N' then 'Dom_New_Grad'
when INTL_IND is NULL and ia_student_type_code in ('G','D','E','S') then 'Dom_Other'
when INTL_IND = 'Y' and ia_student_type_code = 'F' then 'Int_Freshman'
when INTL_IND = 'Y' and ia_student_type_code = 'T' then 'Int_Transfer'
when INTL_IND = 'Y' and ia_student_type_code = 'N' then 'Int_New_Grad'
when INTL_IND = 'Y' and ia_student_type_code in ('G','D','E','S') then 'Int_Other'
else 'Continuing and Other'
end sem_groups,


SD.IA_PROJECTED_STUDENT_TYPE,
SD.IA_PROJECTED_STUDENT_TYPE_DESC,
SD.IA_STUDENT_TYPE_CODE,
SD.IA_STUDENT_TYPE_DESC,
DECODE(DM.gender,'F','F','M')IA_GENDER,
substr(primary_major_1_cipc_code,1,2)IA_CIP_FAMILY,
SD.HONORS_PROGRAM,
SD.REGISTERED_IND,
SD.TERM_REGISTERED_HOURS,
SD.FULL_PART_TIME_IND,
SD.STUDENT_STATUS_CODE,
SD.STUDENT_STATUS_DESC,
SD.STUDENT_TYPE_CODE,
SD.STUDENT_TYPE_DESC,
SD.DEEP_IND,
SD.CLASS_CODE,
SD.CLASS_DESC,
SD.HOUSING_IND,
SD.RESIDENCY_CODE,
SD.RESIDENCY_DESC,
SD.PRIMARY_PROGRAM,
SD.PRIMARY_PROGRAM_DESC,
SD.PRIMARY_LEVEL_CODE,
SD.PRIMARY_LEVEL_DESC,
SD.PRIMARY_COLLEGE_CODE,
SD.PRIMARY_COLLEGE_DESC,
SD.PRIMARY_DEGREE_CODE,
SD.PRIMARY_DEGREE_DESC,
SD.PRIMARY_ADMIT_CODE,
SD.PRIMARY_ADMIT_DESC,
SD.PRIMARY_ADMIT_TERM,
SD.PRIMARY_ADMIT_TERM_DESC,
SD.PRIMARY_MAJOR_1,
SD.PRIMARY_MAJOR_1_CIPC_CODE,

SUBSTR(PRIMARY_MAJOR_1_CIPC_CODE,1,2) ||'.'|| 
SUBSTR(PRIMARY_MAJOR_1_CIPC_CODE,3,6) "MAJOR_1_DOT_CIP_CODE",

SD.PRIMARY_MAJOR_1_DESC,
SD.PRIMARY_MAJOR_2,
SD.PRIMARY_MAJOR_2_CIPC_CODE,
SD.PRIMARY_MAJOR_2_DESC,
SD.PRIMARY_MINOR_1,
SD.PRIMARY_MINOR_1_DESC,
SD.PRIMARY_CONC_1,
SD.PRIMARY_CONC_1_DESC,
SD.TERM_HOURS_ATTEMPTED,
SD.TERM_HOURS_EARNED,
SD.TERM_GPA_HOURS,
SD.TERM_GPA,
SD.TERM_HOURS_PASSED,
SD.TERM_ASTD_CODE,
SD.TERM_ASTD_DESC,
SD.OVERALL_HOURS_ATTEMPTED,
SD.OVERALL_HOURS_EARNED,
SD.OVERALL_GPA_HOURS,
SD.OVERALL_GPA,
SD.INST_GPA,
SD.REPORT_LEVEL_CODE,
SD.PROJECTED_STUDENT_TYPE,
SD.ORIG_STUDENT_TYPE_CODE,
SD.ORIG_STUDENT_TYPE_DESC,
SD.ONLINE_COURSES_ONLY_IND,
SD.FULL_PART_TIME_IND_UMF,
SD.TOTAL_CREDIT_HOURS_UMF,
SD.INST_HOURS_EARNED,
NVL(SD.EDUC_GOAL,'00')EDUC_GOAL,
NVL(SD.EDUC_GOAL_DESC,'Undeclared')EDUC_GOAL_DESC,
SD.PRIMARY_CONC_1_CIPC_CODE,
SD.PRIMARY_CONC_2_CIPC_CODE,
SD.MOST_RECENT_LEVL_CODE,
SD.ENR_STATUS_DATE,
DM.VETERAN_IND,
DM.VETERAN_BENIFITS_ELIGIBILE,
SD.VA_CERT_DATE VETERAN_CERT_DATE,
SD.VA_CERT_HOURS VETERAN_CERT_HOURS,
SD.VA_CERT_TERM VETERAN_CERT_TERM,
SD.VA_CERT_TERM_IND VETERAN_CERT_TERM_IND,
SD.VA_CERT_TYPE VETERAN_CERT_TYPE,
SD.VA_CERT_TYPE_DESC VETERAN_CERT_TYPE_DESC,
SD.AR_ACCOUNT_BALANCE,
SD.AR_AMOUNT_DUE,
DM.FIRST_NAME,
DM.MIDDLE_INITIAL,
DM.LAST_NAME,
DM.NAME_SUFFIX,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT DM_PIDM,
    TD_TERM_CODE
    FROM TD_DEMOGRAPHIC dm
    WHERE dm.a1_county_code IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    and dm.dm_pidm = sd.sd_pidm
    and dm.TD_TERM_CODE = sd.sd_term_code
    )
  ), 'No') Commutable_ind_A1,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT AD.AD_pidm,
      MAX(AD.APST_DATE)
    FROM TD_ADMISSIONS_APPLICANT AD
    WHERE INST_ACCEPTED_APP_ANY_DATE_IND        = 'Y' --was admitted
    AND registered_ind                          = 'Y' --enrolled
    AND AD.ad_pidm         = sd.sd_pidm
    AND AD.term_code_entry = sd.sd_term_code
    AND AD.COUNTY_CODE_ADMIT IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    GROUP BY AD_pidm
    )
  ), 'No') Commutable_ind_origin,

DM.A1_STREET_LINE1,
DM.A1_STREET_LINE2,
DM.A1_STREET_LINE3,
DM.A1_CITY,
DM.A1_STATE_CODE,
DM.A1_STATE_DESC,
DM.A1_ZIP,
DM.A1_COUNTY_CODE,
DM.A1_COUNTY_DESC,
DM.A1_NATION_CODE,
DM.A1_NATION_DESC,
DM.A1_AREA_CODE,
DM.A1_PHONE_NUMBER,
DM.A1_DISTANCE_FROM_CAMPUS_BY_ZIP,
DM.PR_CITY,
DM.PR_COUNTY_CODE,
DM.PR_COUNTY_DESC,
DM.PR_STATE_CODE,
DM.PR_STREET_LINE1,
DM.PR_STREET_LINE2,
DM.PR_ZIP,
DM.PR_ZIP5,
--DM.NR_STREET_LINE1,
--DM.NR_STREET_LINE2,
--DM.NR_STREET_LINE3,
--DM.NR_CITY,
--DM.NR_STATE_CODE,
--DM.NR_STATE_DESC,
--DM.NR_ZIP,
--DM.NR_COUNTY_CODE,
--DM.NR_COUNTY_DESC,
--DM.NR_NATION_CODE,
--DM.NR_NATION_DESC,
--DM.NR_AREA_CODE,
--DM.NR_PHONE_NUMBER,
DM.CA_EMAIL,
DM.HM_EMAIL_1,
DM.BIRTHDATE,
DM.AGE,
DM.GENDER,
DM.GENDER_DESC,
DM.CITIZENSHIP_CODE,
DM.CITIZENSHIP_DESC,
DM.INTL_IND,
DM.VISA_TYPE_CODE,
DM.VISA_TYPE_DESC,
DM.NATION_CITIZEN_CODE,
DM.NATION_CITIZEN_DESC,
DM.REPORT_ETHNICITY,
DECODE(DM.REPORT_ETHNICITY,
      'Nonresident Alien','N',
      'Hispanic or Latino','Y',
      'American Indian or Alaska Native','Y',
      'Asian','N',
      'Black or African American','Y',
      'Native Hawaiian and Other Pacific Islander','Y',
      'White','N',
      'Two or more races','Y','UK')REPORT_URM,
DM.HSCH_CODE,
DM.HSCH_DESC,
DM.HSCH_GRAD_DATE,
DM.HSCH_RANK,
DM.HSCH_PERCENTILE,
DM.HSCH_SIZE,
DM.HSCH_GPA,
DM.HSCH_STREET_LINE_1,
DM.HSCH_STREET_LINE_2,
DM.HSCH_CITY,
DM.HSCH_STATE,
DM.HSCH_ZIP,
DM.PCOL_CODE_1,
DM.PCOL_DESC_1,
DM.PCOL_TRANS_RECV_DATE_1,
DM.PCOL_CODE_2,
DM.PCOL_DESC_2,
DM.PCOL_DEGC_CODE_1,
DM.PCOL_DEGC_DATE_1,
DM.PCOL_HOURS_TRANSFERRED_1,
DM.PCOL_GPA_TRANSFERRED_1,
DM.GRADUATED_IND,
DM.VISA_NUMBER,
DM.DECEASED_IND,
DM.DECEASED_DATE


from td_student_data SD
left join TD_DEMOGRAPHIC DM on DM.TD_TERM_CODE = SD.SD_TERM_CODE and
                             DM.DM_PIDM = SD.SD_PIDM
left join  IA_CW_FY_TERM fy
on sd.sd_term_code = fy.fy_term_code

)
;
