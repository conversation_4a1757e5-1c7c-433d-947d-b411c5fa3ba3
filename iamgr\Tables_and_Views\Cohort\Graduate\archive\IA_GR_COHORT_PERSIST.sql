create OR REPLACE VIEW IA_GR_COHORT_PERSIST AS (
SELECT 
IA_GR_COHORT_POP.*,

----------------------------Second Fall Persistence-----------------------------
CASE 
   WHEN  SCND_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (SCND_FALL_TERM_REG_IND = 'Y' AND SCND_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END SCND_FALL_PERSIST,

----------------------------Third Fall Persistence------------------------------
CASE 
   WHEN  THRD_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (THRD_FALL_TERM_REG_IND = 'Y' AND THRD_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END THRD_FALL_PERSIST,
----------------------------Fourth Fall Persistence-----------------------------
CASE 
   WHEN  FRTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (FRTH_FALL_TERM_REG_IND = 'Y' AND FRTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END FRTH_FALL_PERSIST,
----------------------------Fifth Fall Persistence------------------------------
CASE 
   WHEN  FFTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (FFTH_FALL_TERM_REG_IND = 'Y' AND FFTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END FFTH_FALL_PERSIST,
   
----------------------------Sixth Fall Persistence------------------------------
CASE 
   WHEN  SXTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (SXTH_FALL_TERM_REG_IND = 'Y' AND SXTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END SXTH_FALL_PERSIST,

----------------------------Seventh Fall Persistence----------------------------
CASE 
   WHEN  SVNTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (SVNTH_FALL_TERM_REG_IND = 'Y' AND SVNTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END SVNTH_FALL_PERSIST,
   
----------------------------Eighth Fall Persistence----------------------------
CASE 
   WHEN  EIGTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
   WHEN (EIGTH_FALL_TERM_REG_IND = 'Y' AND EIGTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
   ELSE 'Lost'
   END EIGTH_FALL_PERSIST

------------------------------Ninth Fall Persistence----------------------------
--CASE 
--   WHEN  NINTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
--   WHEN (NINTH_FALL_TERM_REG_IND = 'Y' AND NINTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
--   ELSE 'Lost'
--   END NINTH_FALL_PERSIST,
--   
------------------------------Tenth Fall Persistence----------------------------
--CASE 
--   WHEN  TENTH_FALL_GRAD_IND = 'Y' THEN 'Grad'
--   WHEN (TENTH_FALL_TERM_REG_IND = 'Y' AND TENTH_FALL_GRAD_IND IS NULL) THEN 'Retain'
--   ELSE 'Lost'
--   END TENTH_FALL_PERSIST

FROM 
IA_GR_COHORT_SIX_YR
inner join 
IA_GR_COHORT_POP on IA_GR_COHORT_POP.CO_GR_PIDM = IA_GR_COHORT_SIX_YR.CO_GR_PIDM and
                           IA_GR_COHORT_POP.CO_GR_TERM_CODE_KEY = IA_GR_COHORT_SIX_YR.FRST_FALL_TERM_CODE     
);
