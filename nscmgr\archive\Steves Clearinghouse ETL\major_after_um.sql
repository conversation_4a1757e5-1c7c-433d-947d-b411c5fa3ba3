declare


cursor update_cur is
with st_cur as(
  select 
  sem_st_hold.pidm,
  sem_st_hold.collegecode,
  sem_st_hold.collegename,
  sem_st_hold.enrollmentmajor1,
  to_date(sem_st_hold.enrollmentbegin, 'YYYYMMDD') enrollmentbegin,
  row_number() over(partition by sem_st_hold.pidm order by sem_st_hold.enrollmentbegin) order_num
  from sem_st_hold
  inner join sem_aims_hold_plus on sem_aims_hold_plus.pidm = sem_st_hold.pidm
                                and sem_aims_hold_plus.college_code_after_um_1 = sem_st_hold.collegecode
                                and to_date(sem_st_hold.enrollmentbegin, 'YYYYMMDD') >= (select stvterm_end_date from stvterm where stvterm_code = sem_aims_hold_plus.last_term)
  where sem_st_hold.enrollmentbegin is not null
--  and sem_st_hold.pidm = 142824
  order by to_date(sem_st_hold.enrollmentbegin, 'YYYYMMDD')
)
select
st_cur.pidm,
st_cur.collegecode,
st_cur.collegename,
st_cur.enrollmentmajor1,
st_cur.enrollmentbegin,
st_cur.order_num
from st_cur
where st_cur.order_num = 1


type update_table is table of update_cur%rowtype index by binary_integer;
update_rec update_table;

begin
  open update_cur;
  fetch update_cur bulk collect into update_rec;
  close update_cur;
  
  for i in 1..update_rec.count
  loop
    if update_rec(i).enrollmentmajor1 is not null then
      update sem_aims_hold_plus set
      sem_aims_hold_plus.major_after_um_1 = update_rec(i).enrollmentmajor1
      where sem_aims_hold_plus.pidm = update_rec(i).pidm;
    end if;
  end loop;

  commit;
end;



select count(*)
from sem_aims_hold_plus
where college_code_after_um_1 is not null



--select *
--from sem_aims_hold_plus_fa