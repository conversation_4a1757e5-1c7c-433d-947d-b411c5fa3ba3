create or replace PROCEDURE P_NSC_IPEDS_OUTCOMES_MEASURES (
    fy_in           VARCHAR2,
    stype_in        VARCHAR2,
    fullpart_in     VARCHAR2, 
    pellind_in      VARCHAR2)
IS
    v_sql          CLOB;
    v_table_name   VARCHAR2(30);
    
    table_counter   integer := 0;
    sql_statement   varchar2(3200) := '';
    lv_fy           varchar2(64) := '';
    
BEGIN
    -- Generate the temporary table name based on input parameters
    lv_fy := replace(fy_in, '-', '_');
    dbms_output.put_line('lv_fy = ' || lv_fy);
    
    v_table_name := 'temp_om_' || lv_fy || '_' || stype_in || fullpart_in || pellind_in;
    
    -- whack table if exists
    begin
        sql_statement :=
        'select ' ||
        'count(user_tables.table_name) ' ||
        'from user_tables ' ||
        'where user_tables.table_name = upper(:val)';

        execute immediate(sql_statement) into table_counter using v_table_name;

        dbms_output.put_line('table_name = ' || v_table_name);
        dbms_output.put_line('table_counter = ' || table_counter);
        dbms_output.put_line('sql_statement = ' || sql_statement);
        
        if table_counter > 0 then
            sql_statement := 'drop table ' || v_table_name || ' cascade constraints';        

            dbms_output.put_line('sql_statement = ' || sql_statement);
            execute immediate(sql_statement);        
        end if;
        
        sql_statement := 'create table ' || v_table_name ||
                      '(
                        a varchar2(256),
                        b varchar2(256),
                        c varchar2(256),
                        d varchar2(256),
                        e varchar2(256),
                        f varchar2(256),
                        g varchar2(256),
                        h varchar2(256),
                        i varchar2(256),
                        j varchar2(256),
                        k varchar2(256),
                        l varchar2(256)
                       )';
            
        dbms_output.put_line('sql_statement = ' || sql_statement);
        execute immediate(sql_statement);

    end;

    -- Ensure the table name is valid and does not exceed 30 characters
    -- IF LENGTH(v_table_name) > 30 THEN
    --     RAISE_APPLICATION_ERROR(-20001, 'Table name exceeds 30 characters');
    -- END IF;

    -- Construct the dynamic SQL query to create and populate the temporary table
    v_sql := '
        insert into ' || v_table_name || '
        WITH BASE_SEL AS (
            SELECT DISTINCT
                SD.FIRST_NAME,
                SD.MIDDLE_INITIAL,
                SD.LAST_NAME,
                SD.NAME_SUFFIX,
                SD.BIRTHDATE,
                CO.FY_CO_TERM_CODE,
                SD.SD_PIDM
            FROM IA_TD_STUDENT_DATA SD
            INNER JOIN IA_FY_COHORT_POP_UNDUP_TBL CO
                ON CO.CO_PIDM = SD.SD_PIDM
                AND CO.FY_TERM_CODE = SD.SD_TERM_CODE
            INNER JOIN IA_FY_COHORT_FA FA
                ON FA.CO_PIDM = SD.SD_PIDM
                AND FA.FY_TERM_CODE = SD.SD_TERM_CODE
            INNER JOIN IA_FY_HLC_STUDY_TBL HLC
                ON HLC.CO_PIDM = SD.SD_PIDM
                AND HLC.FY_TERM_CODE = SD.SD_TERM_CODE
            WHERE CO.FY = :v1
                AND CO.CO_IA_STUDENT_TYPE_CODE = :v2
                AND CO.CO_FULL_PART_IND_UMF = :v3
                AND FA.PELL_GRANT_IND = :v4
        ),
        POP_SEL AS (
            -- THIS SECTION CREATES A HEADER FILE.
            SELECT
                1 ORDER_NUM,
                ''H1'' "A",
                ''002327'' "B",
                ''00'' "C",
                ''UNIVERSITY OF MICHIGAN FLINT'' "D",
                TO_CHAR(SYSDATE, ''YYYYMMDD'') "E",
                ''CO'' "F",
                ''I'' "G",
                NULL "H",
                NULL "I",
                NULL "J",
                NULL "K",
                NULL "L"
            FROM DUAL
            UNION ALL
            -- THIS SECTION PULLS THE STUDENT RECORDS FOR THE PAYLOAD.
            SELECT
                2 ORDER_NUM,
                ''D1'' "A",
                NULL "B",
                CASE
                    WHEN (FIRST_NAME IS NULL OR FIRST_NAME = ''.'') THEN ''Jon''
                    ELSE SUBSTR(FIRST_NAME,1,20)
                END "C",
                SUBSTR(REGEXP_REPLACE(TRIM(MIDDLE_INITIAL), ''[[:punct:] ]'',NULL), 1 ,1) "D",
                CASE
                    WHEN (LAST_NAME IS NULL OR LAST_NAME = ''.'') THEN ''Doe''
                    ELSE SUBSTR(LAST_NAME,1,20)
                END "E",
                SUBSTR(REGEXP_REPLACE(TRIM(NAME_SUFFIX), ''[[:punct:] ]'',NULL),1,5) "F",
                TO_CHAR(BIRTHDATE, ''YYYYMMDD'') "G",
                TO_CHAR(TO_NUMBER(SUBSTR(FY_CO_TERM_CODE,1,4))-1 ||''0915'') "H",
                NULL "I",
                ''002327'' "J",
                ''00'' "K",
                TO_CHAR(SD_PIDM) "L"
            FROM BASE_SEL
            UNION ALL
            -- THIS IS TO COUNT THE NUMBER OF RECORDS AND APPEND A TRAILER RECORD
            SELECT
                3 ORDER_NUM,
                ''T1'' "A",
                TO_CHAR(COUNT(SD_PIDM)+2) "B",
                NULL "C",
                NULL "D",
                NULL "E",
                NULL "F",
                NULL "G",
                NULL "H",
                NULL "I",
                NULL "J",
                NULL "K",
                NULL "L"
            FROM BASE_SEL
        )
        SELECT
            POP_SEL.A,
            POP_SEL.B,
            POP_SEL.C,
            POP_SEL.D,
            POP_SEL.E,
            POP_SEL.F,
            POP_SEL.G,
            POP_SEL.H,
            POP_SEL.I,
            POP_SEL.J,
            POP_SEL.K,
            POP_SEL.L
        FROM POP_SEL
        ORDER BY ORDER_NUM ASC
    ';

    -- Print the dynamic SQL for debugging
    DBMS_OUTPUT.PUT_LINE(v_sql);

    -- Execute the dynamic SQL statement to create and populate the temporary table
    EXECUTE IMMEDIATE v_sql using fy_in, stype_in, fullpart_in, pellind_in;

    DBMS_OUTPUT.PUT_LINE('Temporary table created: ' || v_table_name);
EXCEPTION
    WHEN OTHERS THEN
        -- If an error occurs, drop the temporary table if it was created
        BEGIN
            EXECUTE IMMEDIATE 'DROP TABLE ' || v_table_name;
        EXCEPTION
            WHEN OTHERS THEN
                NULL;
        END;
--        RAISE;
END;