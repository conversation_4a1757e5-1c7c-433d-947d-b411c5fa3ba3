TRUNCATE TABLE CO_PERSIST_NSC_TBL_BAC;

INSERT INTO CO_PERSIST_NSC_TBL_BAC
SELECT * FROM CO_PERSIST_NSC_TBL;
COMMIT;

SELECT COUNT (*) FROM CO_PERSIST_NSC_TBL_BAC;
SELECT COUNT (*) FROM CO_PERSIST_NSC_TBL;

SELECT * FROM CO_PERSIST_NSC_TBL;

TRUNCATE TABLE CO_PERSIST_NSC_TBL;

INSERT INTO CO_PERSIST_NSC_TBL
  (SELECT CO_POP_UNDUP_TBL.*,
      ----------------------------FIRST WINTER PERSISTENCE----------------------
      CASE
        WHEN FRST_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRST_WIN_TERM_REG_IND IS NULL
        OR FRST_WIN_TERM_REG_IND      = 'N')
        AND FRST_WIN_TERM_REG_NSC    IS NULL)
        THEN 'LOST'
        WHEN ((FRST_WIN_TERM_REG_IND IS NULL
        OR FRST_WIN_TERM_REG_IND      = 'N')
        AND FRST_WIN_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRST_WIN_TERM_REG_IND IS NULL
        OR FRST_WIN_TERM_REG_IND      = 'N')
        AND FRST_WIN_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRST_WIN_PERSIST,
      ------------------------------FIRST SPRING PERSISTENCE--------------------
      CASE
        WHEN FRST_SPR_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRST_SPR_TERM_REG_IND IS NULL
        OR FRST_SPR_TERM_REG_IND      = 'N')
        AND FRST_SPR_TERM_REG_NSC    IS NULL)
        THEN 'LOST'
        WHEN ((FRST_SPR_TERM_REG_IND IS NULL
        OR FRST_SPR_TERM_REG_IND      = 'N')
        AND FRST_SPR_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRST_SPR_TERM_REG_IND IS NULL
        OR FRST_SPR_TERM_REG_IND      = 'N')
        AND FRST_SPR_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRST_SPR_PERSIST,
      ----------------------------FIRST SUMMER PERSISTENCE----------------------
      CASE
        WHEN FRST_SUM_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRST_SUM_TERM_REG_IND IS NULL
        OR FRST_SUM_TERM_REG_IND      = 'N')
        AND FRST_SUM_TERM_REG_NSC    IS NULL)
        THEN 'LOST'
        WHEN ((FRST_SUM_TERM_REG_IND IS NULL
        OR FRST_SUM_TERM_REG_IND      = 'N')
        AND FRST_SUM_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRST_SUM_TERM_REG_IND IS NULL
        OR FRST_SUM_TERM_REG_IND      = 'N')
        AND FRST_SUM_TERM_REG_NSC     = 'Y'
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRST_SUM_PERSIST,
      ----------------------------SECOND FALL PERSISTENCE-----------------------
      CASE
        WHEN SCND_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC    IS NULL
        AND SCND_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND SCND_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SCND_FALL_PERSIST,
      ----------------------------SECOND WINTER PERSISTENCE---------------------
      CASE
        WHEN SCND_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_TERM_REG_NSC    IS NULL
        AND SCND_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_NSC         = 'Y'
        AND SCND_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_TERM_REG_NSC     = 'Y'
        AND SCND_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SCND_WIN_TERM_REG_IND IS NULL
        OR SCND_WIN_TERM_REG_IND      = 'N')
        AND SCND_WIN_GRAD_IND        IS NULL
        AND SCND_WIN_TERM_REG_NSC     = 'Y'
        AND SCND_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SCND_WIN_PERSIST,
      ----------------------------THIRD FALL PERSISTENCE------------------------
      CASE
        WHEN THRD_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC    IS NULL
        AND THRD_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND THRD_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END THRD_FALL_PERSIST,
      ----------------------------THIRD WINTER PERSISTENCE----------------------
      CASE
        WHEN THRD_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_TERM_REG_NSC    IS NULL
        AND THRD_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_NSC         = 'Y'
        AND THRD_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_TERM_REG_NSC     = 'Y'
        AND THRD_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((THRD_WIN_TERM_REG_IND IS NULL
        OR THRD_WIN_TERM_REG_IND      = 'N')
        AND THRD_WIN_GRAD_IND        IS NULL
        AND THRD_WIN_TERM_REG_NSC     = 'Y'
        AND THRD_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END THRD_WIN_PERSIST,
      ----------------------------FOURTH FALL PERSISTENCE-----------------------
      CASE
        WHEN FRTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC    IS NULL
        AND FRTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRTH_FALL_PERSIST,
      ----------------------------FOURTH WINTER PERSISTENCE---------------------
      CASE
        WHEN FRTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_TERM_REG_NSC    IS NULL
        AND FRTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_NSC         = 'Y'
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_TERM_REG_NSC     = 'Y'
        AND FRTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRTH_WIN_TERM_REG_IND IS NULL
        OR FRTH_WIN_TERM_REG_IND      = 'N')
        AND FRTH_WIN_GRAD_IND        IS NULL
        AND FRTH_WIN_TERM_REG_NSC     = 'Y'
        AND FRTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRTH_WIN_PERSIST,
      ----------------------------FIFTH FALL PERSISTENCE------------------------
      CASE
        WHEN FFTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FFTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC    IS NULL
        AND FFTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_NSC         = 'Y'
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC     = 'Y'
        AND FFTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC     = 'Y'
        AND FFTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FFTH_FALL_PERSIST,
      ----------------------------FIFTH WINTER PERSISTENCE----------------------
      CASE
        WHEN FFTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FFTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FFTH_WIN_TERM_REG_IND IS NULL
        OR FFTH_WIN_TERM_REG_IND      = 'N')
        AND FFTH_WIN_GRAD_IND        IS NULL
        AND FFTH_WIN_TERM_REG_NSC    IS NULL
        AND FFTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FFTH_WIN_TERM_REG_IND IS NULL
        OR FFTH_WIN_TERM_REG_IND      = 'N')
        AND FFTH_WIN_GRAD_IND        IS NULL
        AND FFTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FFTH_WIN_TERM_REG_IND IS NULL
        OR FFTH_WIN_TERM_REG_IND      = 'N')
        AND FFTH_WIN_GRAD_NSC         = 'Y'
        AND FFTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FFTH_WIN_TERM_REG_IND IS NULL
        OR FFTH_WIN_TERM_REG_IND      = 'N')
        AND FFTH_WIN_GRAD_IND        IS NULL
        AND FFTH_WIN_TERM_REG_NSC     = 'Y'
        AND FFTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FFTH_WIN_TERM_REG_IND IS NULL
        OR FFTH_WIN_TERM_REG_IND      = 'N')
        AND FFTH_WIN_GRAD_IND        IS NULL
        AND FFTH_WIN_TERM_REG_NSC     = 'Y'
        AND FFTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FFTH_WIN_PERSIST,
      ----------------------------SIXTH FALL PERSISTENCE------------------------
      CASE
        WHEN SXTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SXTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC    IS NULL
        AND SXTH_FALL_GRAD_NSC        IS NULL )
        THEN 'LOST'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_NSC         = 'Y'
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC     = 'Y'
        AND SXTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC     = 'Y'
        AND SXTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SXTH_FALL_PERSIST,
      ----------------------------SIXTH WINTER PERSISTENCE----------------------
      CASE
        WHEN SXTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SXTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SXTH_WIN_TERM_REG_IND IS NULL
        OR SXTH_WIN_TERM_REG_IND      = 'N')
        AND SXTH_WIN_GRAD_IND        IS NULL
        AND SXTH_WIN_TERM_REG_NSC    IS NULL
        AND SXTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SXTH_WIN_TERM_REG_IND IS NULL
        OR SXTH_WIN_TERM_REG_IND      = 'N')
        AND SXTH_WIN_GRAD_IND        IS NULL
        AND SXTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE       IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SXTH_WIN_TERM_REG_IND IS NULL
        OR SXTH_WIN_TERM_REG_IND      = 'N')
        AND SXTH_WIN_GRAD_NSC         = 'Y'
        AND SXTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE        = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SXTH_WIN_TERM_REG_IND IS NULL
        OR SXTH_WIN_TERM_REG_IND      = 'N')
        AND SXTH_WIN_GRAD_IND        IS NULL
        AND SXTH_WIN_TERM_REG_NSC     = 'Y'
        AND SXTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED     IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SXTH_WIN_TERM_REG_IND IS NULL
        OR SXTH_WIN_TERM_REG_IND      = 'N')
        AND SXTH_WIN_GRAD_IND        IS NULL
        AND SXTH_WIN_TERM_REG_NSC     = 'Y'
        AND SXTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SXTH_WIN_PERSIST,
      ----------------------------SEVENTH FALL PERSISTENCE----------------------
      CASE
        WHEN SVNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SVNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC    IS NULL
        AND SVNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_NSC         = 'Y'
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND SVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND SVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SVNTH_FALL_PERSIST,
      ----------------------------SEVENTH WINTER PERSISTENCE--------------------
      CASE
        WHEN SVNTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SVNTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SVNTH_WIN_TERM_REG_IND IS NULL
        OR SVNTH_WIN_TERM_REG_IND      = 'N')
        AND SVNTH_WIN_GRAD_IND        IS NULL
        AND SVNTH_WIN_TERM_REG_NSC    IS NULL
        AND SVNTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SVNTH_WIN_TERM_REG_IND IS NULL
        OR SVNTH_WIN_TERM_REG_IND      = 'N')
        AND SVNTH_WIN_GRAD_IND        IS NULL
        AND SVNTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SVNTH_WIN_TERM_REG_IND IS NULL
        OR SVNTH_WIN_TERM_REG_IND      = 'N')
        AND SVNTH_WIN_GRAD_NSC         = 'Y'
        AND SVNTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SVNTH_WIN_TERM_REG_IND IS NULL
        OR SVNTH_WIN_TERM_REG_IND      = 'N')
        AND SVNTH_WIN_GRAD_IND        IS NULL
        AND SVNTH_WIN_TERM_REG_NSC     = 'Y'
        AND SVNTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SVNTH_WIN_TERM_REG_IND IS NULL
        OR SVNTH_WIN_TERM_REG_IND      = 'N')
        AND SVNTH_WIN_GRAD_IND        IS NULL
        AND SVNTH_WIN_TERM_REG_NSC     = 'Y'
        AND SVNTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SVNTH_WIN_PERSIST,
      ----------------------------EIGTH FALL PERSISTENCE------------------------
      CASE
        WHEN EIGTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN EIGTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC    IS NULL
        AND EIGTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_NSC         = 'Y'
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC     = 'Y'
        AND EIGTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC     = 'Y'
        AND EIGTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END EIGTH_FALL_PERSIST,
      ----------------------------EIGTH WINTER PERSISTENCE----------------------
      CASE
        WHEN EIGTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN EIGTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((EIGTH_WIN_TERM_REG_IND IS NULL
        OR EIGTH_WIN_TERM_REG_IND      = 'N')
        AND EIGTH_WIN_GRAD_IND        IS NULL
        AND EIGTH_WIN_TERM_REG_NSC    IS NULL
        AND EIGTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((EIGTH_WIN_TERM_REG_IND IS NULL
        OR EIGTH_WIN_TERM_REG_IND      = 'N')
        AND EIGTH_WIN_GRAD_IND        IS NULL
        AND EIGTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((EIGTH_WIN_TERM_REG_IND IS NULL
        OR EIGTH_WIN_TERM_REG_IND      = 'N')
        AND EIGTH_WIN_GRAD_NSC         = 'Y'
        AND EIGTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((EIGTH_WIN_TERM_REG_IND IS NULL
        OR EIGTH_WIN_TERM_REG_IND      = 'N')
        AND EIGTH_WIN_GRAD_IND        IS NULL
        AND EIGTH_WIN_TERM_REG_NSC     = 'Y'
        AND EIGTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((EIGTH_WIN_TERM_REG_IND IS NULL
        OR EIGTH_WIN_TERM_REG_IND      = 'N')
        AND EIGTH_WIN_GRAD_IND        IS NULL
        AND EIGTH_WIN_TERM_REG_NSC     = 'Y'
        AND EIGTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END EIGTH_WIN_PERSIST,
      ----------------------------NINTH FALL PERSISTENCE------------------------
      CASE
        WHEN NINTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN NINTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((NINTH_FALL_TERM_REG_IND IS NULL
        OR NINTH_FALL_TERM_REG_IND      = 'N')
        AND NINTH_FALL_GRAD_IND        IS NULL
        AND NINTH_FALL_TERM_REG_NSC    IS NULL
        AND NINTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((NINTH_FALL_TERM_REG_IND IS NULL
        OR NINTH_FALL_TERM_REG_IND      = 'N')
        AND NINTH_FALL_GRAD_IND        IS NULL
        AND NINTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((NINTH_FALL_TERM_REG_IND IS NULL
        OR NINTH_FALL_TERM_REG_IND      = 'N')
        AND NINTH_FALL_GRAD_NSC         = 'Y'
        AND NINTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((NINTH_FALL_TERM_REG_IND IS NULL
        OR NINTH_FALL_TERM_REG_IND      = 'N')
        AND NINTH_FALL_GRAD_IND        IS NULL
        AND NINTH_FALL_TERM_REG_NSC     = 'Y'
        AND NINTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((NINTH_FALL_TERM_REG_IND IS NULL
        OR NINTH_FALL_TERM_REG_IND      = 'N')
        AND NINTH_FALL_GRAD_IND        IS NULL
        AND NINTH_FALL_TERM_REG_NSC     = 'Y'
        AND NINTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END NINTH_FALL_PERSIST,
      ----------------------------NINTH WINTER PERSISTENCE----------------------
      CASE
        WHEN NINTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN NINTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((NINTH_WIN_TERM_REG_IND IS NULL
        OR NINTH_WIN_TERM_REG_IND      = 'N')
        AND NINTH_WIN_GRAD_IND        IS NULL
        AND NINTH_WIN_TERM_REG_NSC    IS NULL
        AND NINTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((NINTH_WIN_TERM_REG_IND IS NULL
        OR NINTH_WIN_TERM_REG_IND      = 'N')
        AND NINTH_WIN_GRAD_IND        IS NULL
        AND NINTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((NINTH_WIN_TERM_REG_IND IS NULL
        OR NINTH_WIN_TERM_REG_IND      = 'N')
        AND NINTH_WIN_GRAD_NSC         = 'Y'
        AND NINTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((NINTH_WIN_TERM_REG_IND IS NULL
        OR NINTH_WIN_TERM_REG_IND      = 'N')
        AND NINTH_WIN_GRAD_IND        IS NULL
        AND NINTH_WIN_TERM_REG_NSC     = 'Y'
        AND NINTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((NINTH_WIN_TERM_REG_IND IS NULL
        OR NINTH_WIN_TERM_REG_IND      = 'N')
        AND NINTH_WIN_GRAD_IND        IS NULL
        AND NINTH_WIN_TERM_REG_NSC     = 'Y'
        AND NINTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END NINTH_WIN_PERSIST,
      ----------------------------TENTH FALL PERSISTENCE------------------------
      CASE
        WHEN TENTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN TENTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((TENTH_FALL_TERM_REG_IND IS NULL
        OR TENTH_FALL_TERM_REG_IND      = 'N')
        AND TENTH_FALL_GRAD_IND        IS NULL
        AND TENTH_FALL_TERM_REG_NSC    IS NULL
        AND TENTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((TENTH_FALL_TERM_REG_IND IS NULL
        OR TENTH_FALL_TERM_REG_IND      = 'N')
        AND TENTH_FALL_GRAD_IND        IS NULL
        AND TENTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((TENTH_FALL_TERM_REG_IND IS NULL
        OR TENTH_FALL_TERM_REG_IND      = 'N')
        AND TENTH_FALL_GRAD_NSC         = 'Y'
        AND TENTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((TENTH_FALL_TERM_REG_IND IS NULL
        OR TENTH_FALL_TERM_REG_IND      = 'N')
        AND TENTH_FALL_GRAD_IND        IS NULL
        AND TENTH_FALL_TERM_REG_NSC     = 'Y'
        AND TENTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((TENTH_FALL_TERM_REG_IND IS NULL
        OR TENTH_FALL_TERM_REG_IND      = 'N')
        AND TENTH_FALL_GRAD_IND        IS NULL
        AND TENTH_FALL_TERM_REG_NSC     = 'Y'
        AND TENTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END TENTH_FALL_PERSIST,
      ----------------------------TENTH WINTER PERSISTENCE----------------------
      CASE
        WHEN TENTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN TENTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((TENTH_WIN_TERM_REG_IND IS NULL
        OR TENTH_WIN_TERM_REG_IND      = 'N')
        AND TENTH_WIN_GRAD_IND        IS NULL
        AND TENTH_WIN_TERM_REG_NSC    IS NULL
        AND TENTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((TENTH_WIN_TERM_REG_IND IS NULL
        OR TENTH_WIN_TERM_REG_IND      = 'N')
        AND TENTH_WIN_GRAD_IND        IS NULL
        AND TENTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((TENTH_WIN_TERM_REG_IND IS NULL
        OR TENTH_WIN_TERM_REG_IND      = 'N')
        AND TENTH_WIN_GRAD_NSC         = 'Y'
        AND TENTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((TENTH_WIN_TERM_REG_IND IS NULL
        OR TENTH_WIN_TERM_REG_IND      = 'N')
        AND TENTH_WIN_GRAD_IND        IS NULL
        AND TENTH_WIN_TERM_REG_NSC     = 'Y'
        AND TENTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((TENTH_WIN_TERM_REG_IND IS NULL
        OR TENTH_WIN_TERM_REG_IND      = 'N')
        AND TENTH_WIN_GRAD_IND        IS NULL
        AND TENTH_WIN_TERM_REG_NSC     = 'Y'
        AND TENTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END TENTH_WIN_PERSIST,
      ----------------------------ELEVENTH FALL PERSISTENCE---------------------
      CASE
        WHEN ELEVENTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN ELEVENTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((ELEVENTH_FALL_TERM_REG_IND IS NULL
        OR ELEVENTH_FALL_TERM_REG_IND      = 'N')
        AND ELEVENTH_FALL_GRAD_IND        IS NULL
        AND ELEVENTH_FALL_TERM_REG_NSC    IS NULL
        AND ELEVENTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((ELEVENTH_FALL_TERM_REG_IND IS NULL
        OR ELEVENTH_FALL_TERM_REG_IND      = 'N')
        AND ELEVENTH_FALL_GRAD_IND        IS NULL
        AND ELEVENTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((ELEVENTH_FALL_TERM_REG_IND IS NULL
        OR ELEVENTH_FALL_TERM_REG_IND      = 'N')
        AND ELEVENTH_FALL_GRAD_NSC         = 'Y'
        AND ELEVENTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((ELEVENTH_FALL_TERM_REG_IND IS NULL
        OR ELEVENTH_FALL_TERM_REG_IND      = 'N')
        AND ELEVENTH_FALL_GRAD_IND        IS NULL
        AND ELEVENTH_FALL_TERM_REG_NSC     = 'Y'
        AND ELEVENTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((ELEVENTH_FALL_TERM_REG_IND IS NULL
        OR ELEVENTH_FALL_TERM_REG_IND      = 'N')
        AND ELEVENTH_FALL_GRAD_IND        IS NULL
        AND ELEVENTH_FALL_TERM_REG_NSC     = 'Y'
        AND ELEVENTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END ELEVENTH_FALL_PERSIST,
      ----------------------------ELEVENTH WINTER PERSISTENCE-------------------
      CASE
        WHEN ELEVENTH_WIN_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN ELEVENTH_WIN_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((ELEVENTH_WIN_TERM_REG_IND IS NULL
        OR ELEVENTH_WIN_TERM_REG_IND      = 'N')
        AND ELEVENTH_WIN_GRAD_IND        IS NULL
        AND ELEVENTH_WIN_TERM_REG_NSC    IS NULL
        AND ELEVENTH_WIN_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((ELEVENTH_WIN_TERM_REG_IND IS NULL
        OR ELEVENTH_WIN_TERM_REG_IND      = 'N')
        AND ELEVENTH_WIN_GRAD_IND        IS NULL
        AND ELEVENTH_WIN_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((ELEVENTH_WIN_TERM_REG_IND IS NULL
        OR ELEVENTH_WIN_TERM_REG_IND      = 'N')
        AND ELEVENTH_WIN_GRAD_NSC         = 'Y'
        AND ELEVENTH_WIN_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((ELEVENTH_WIN_TERM_REG_IND IS NULL
        OR ELEVENTH_WIN_TERM_REG_IND      = 'N')
        AND ELEVENTH_WIN_GRAD_IND        IS NULL
        AND ELEVENTH_WIN_TERM_REG_NSC     = 'Y'
        AND ELEVENTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((ELEVENTH_WIN_TERM_REG_IND IS NULL
        OR ELEVENTH_WIN_TERM_REG_IND      = 'N')
        AND ELEVENTH_WIN_GRAD_IND        IS NULL
        AND ELEVENTH_WIN_TERM_REG_NSC     = 'Y'
        AND ELEVENTH_WIN_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END ELEVENTH_WIN_PERSIST 
    ---------------------------END SELECT---------------------------------------
    FROM CO_TEN_YR_NSC_TBL
    INNER JOIN CO_POP_UNDUP_TBL
    ON CO_POP_UNDUP_TBL.CO_PIDM = CO_TEN_YR_NSC_TBL.CO_PIDM
    and CO_POP_UNDUP_TBL.CO_TERM_CODE_KEY = CO_TEN_YR_NSC_TBL.CO_TERM_CODE_KEY
      --where CO_POP_UNDUP_TBL.co_pidm = '72357'
  );

