create table IA_HLC_STUDY_TBL as (
SELECT
  /***************************************
  Cohort Un-duped
  ***************************************/
  IA_COHORT_POP_UNDUP_TBL.CO_PIDM,
  IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY,
  IA_COHORT_POP_UNDUP_TBL.CO_IA_STUDENT_TYPE_CODE,
  IA_COHORT_POP_UNDUP_TBL.CO_FULL_PART_IND_UMF,
  IA_COHORT_POP_UNDUP_TBL.CO_ETHNICITY,
  IA_COHORT_POP_UNDUP_TBL.CO_GENDER,
  /***************************************
  Cohort Demographic
  ***************************************/
  IA_TD_STUDENT_DATA.hsch_gpa,
  CASE
    WHEN IA_TD_STUDENT_DATA.hsch_gpa < 2.7
    THEN 1
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 2.7
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.0
    THEN 2
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 3.01
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.25
    THEN 3
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 3.26
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.50
    THEN 4
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 3.51
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.6
    THEN 5
    WHEN IA_TD_STUDENT_DATA.hsch_gpa > 3.6
    THEN 6
    ELSE 7
  END hsch_gpa_bin,
  CASE
    WHEN IA_TD_STUDENT_DATA.hsch_gpa < 2.7
    THEN 'Below 2.7'
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 2.7
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.0
    THEN '2.7-3.0'
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 3.01
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.25
    THEN '3.01-3.25'
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 3.26
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.50
    THEN '3.26-3.50'
    WHEN IA_TD_STUDENT_DATA.hsch_gpa >= 3.51
    AND IA_TD_STUDENT_DATA.hsch_gpa  <=3.6
    THEN '3.51-3.6'
    WHEN IA_TD_STUDENT_DATA.hsch_gpa > 3.6
    THEN 'Above 3.6'
    ELSE 'No Record'
  END hsch_gpa_bin_desc,
  IA_TD_STUDENT_DATA.PCOL_GPA_TRANSFERRED_1   AS PCOL_GPA,
  IA_TD_STUDENT_DATA.PCOL_DEGC_CODE_1         AS PCOL_DEGC_CODE,
  IA_TD_STUDENT_DATA.PCOL_HOURS_TRANSFERRED_1 AS PCOL_HOURS,
  NVL( IA_TD_STUDENT_DATA.VETERAN_IND,'N') Veteran_ind,
  /***************************************
  Student Data
  ***************************************/
  --IA_TD_STUDENT_DATA.ORIG_STUDENT_TYPE_CODE, --these are already in the IA_COHORT_SIX_YR
  IA_TD_STUDENT_DATA.CLASS_CODE,
  IA_TD_STUDENT_DATA.primary_admit_code,
  IA_TD_STUDENT_DATA.residency_code,
  IA_TD_STUDENT_DATA.primary_major_1,
  NVL(IA_TD_STUDENT_DATA.HOUSING_IND,'N') housing_ind,
  IA_TD_STUDENT_DATA.TOTAL_CREDIT_HOURS_UMF,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT ia_td_student_data.sd_PIDM
    FROM ia_td_student_data
    WHERE ia_td_student_data.sd_PIDM       = IA_COHORT_POP_UNDUP_TBL.co_pidm
    AND ia_td_student_data.sd_TERM_CODE    = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
    AND ia_td_student_data.A1_COUNTY_CODE IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    )
  ), 'No') Commutible_ind,
  /***************************************
  Student Success Variables
  join to IA_COHORT_SIX_YR for projected
  values  inner join on pidm
  ***************************************/
  CASE
    WHEN first_winter_term_gpa.overall_gpa >= 2.0
    THEN 'Yes'
    ELSE 'No'
  END success_after_1st_term,
  most_recent.MOST_RECENT_ASTD_CODE,
  most_recent.overall_gpa,
  most_recent.STUDENT_STATUS_CODE,
  test_scores.act_composite,
  test_scores.act_math,
  test_scores.act_english,
  /***************************************
  CSI Qualitative Variables - cohort term
  ***************************************/
  ia_nl_csi_2014.WORK_DURING_SCHOOL,
  ia_nl_csi_2014.DESIRE_TO_TRANSFER,
  ia_nl_csi_2014.MOTHERS_EDU,
  ia_nl_csi_2014.FATHERS_EDU,
  -- calculate First Generation student
  CASE
    WHEN (ia_nl_csi_2014.MOTHERS_EDU IS NULL
    AND ia_nl_csi_2014.FATHERS_EDU   IS NULL)
    THEN NULL
    WHEN (ia_nl_csi_2014.MOTHERS_EDU = 'High School Diploma'
    OR ia_nl_csi_2014.MOTHERS_EDU    ='Some High School'
    OR ia_nl_csi_2014.MOTHERS_EDU    = 'Elementary')
    AND (ia_nl_csi_2014.FATHERS_EDU IS NULL)
    THEN 'Yes'
    WHEN (ia_nl_csi_2014.MOTHERS_EDU IS NULL)
    AND (ia_nl_csi_2014.FATHERS_EDU   = 'High School Diploma'
    OR ia_nl_csi_2014.FATHERS_EDU     ='Some High School'
    OR ia_nl_csi_2014.FATHERS_EDU     = 'Elementary')
    THEN 'Yes'
    WHEN (ia_nl_csi_2014.MOTHERS_EDU = 'High School Diploma'
    OR ia_nl_csi_2014.MOTHERS_EDU    ='Some High School'
    OR ia_nl_csi_2014.MOTHERS_EDU    = 'Elementary')
    AND (ia_nl_csi_2014.FATHERS_EDU  = 'High School Diploma'
    OR ia_nl_csi_2014.FATHERS_EDU    ='Some High School'
    OR ia_nl_csi_2014.FATHERS_EDU    = 'Elementary')
    THEN 'Yes'
    ELSE 'No'
  END Frst_Gen_Stud,
  -- end first generatin student calculation
  /************************************************
  CSI Stanine Variables - cohort term
  Summary scores are expressed on a stanine scale:
  1 = very low, 5 = average, 9 = very high
  Calculating Stanines
  Result Ranking 4% 7% 12% 17% 20% 17% 12% 7% 4%
  Stanine        1   2   3   4   5   6   7   8   9
  **************************************************/
  ia_nl_csi_2014.DROPOUT_PRONENESS_STANINE,
  ia_nl_csi_2014.EDUCATIONAL_STRESS_STANINE,
  ia_nl_csi_2014.RECEPTIVITY_TO_HELP_STANINE,
  ia_nl_csi_2014.PREDICTED_ACAD_DIFF_STANINE
  /***************************************
  From and Joins
  ***************************************/
FROM IA_COHORT_POP_UNDUP_TBL
INNER JOIN ia_td_student_data
ON ia_td_student_data.sd_pidm       = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND ia_td_student_data.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  -- Pull First Fall GPA
--left outer JOIN
--  ( SELECT sd_pidm, sd_term_code, TERM_GPA FROM um_student_data
--  ) first_term_gpa
--ON first_term_gpa.sd_pidm       = IA_COHORT_POP_UNDUP_TBL.co_pidm
--AND first_term_gpa.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
  --Pull First Winter GPA
left outer JOIN
  (SELECT sd_pidm,
    sd_term_code,
    TERM_GPA,
    overall_gpa
    FROM um_student_data
  ) first_winter_term_gpa
ON first_winter_term_gpa.sd_pidm       = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND first_winter_term_gpa.sd_term_code = TO_CHAR((to_number(IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY) + 10))
  --pull most recent academic standing
Left Outer join
  (SELECT sd_pidm,
    TERM_GPA,
    sd_term_code,
    OVERALL_GPA,
    MOST_RECENT_ASTD_CODE,
    STUDENT_STATUS_CODE,
    UMID
  FROM um_student_data
  ) most_recent
ON most_recent.sd_pidm       = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND most_recent.sd_term_code = '201610' -- THIS NEEDDS TO BE UPDATED EACH FALL TERM
  -- Pull ACT Composite
LEFT JOIN
  (SELECT pidm,
    act_composite,
    act_math,
    act_english,
    td_term_code
  FROM td_test_score
  ) test_scores
ON test_scores.pidm          = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND test_scores.td_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
LEFT outer JOIN ia_nl_csi_2014
ON ia_nl_csi_2014.student_ID = most_recent.umid
)
