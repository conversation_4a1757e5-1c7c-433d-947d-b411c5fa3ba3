--------------------------------------------------------
--  DDL for View IA_REGISTRATION_TRACKER
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "IAMGR"."IA_REGISTRATION_TRACKER" ("SD_PIDM", "SD_TERM_CODE", "CURRENT_ENROLLED_IND", "WINTER_ENROLLED_IND", "REGISTERED_IND", "FULL_PART_TIME_IND_UMF", "PRIMARY_LEVEL_CODE", "PRIMARY_ADMIT_CODE", "PRIMARY_COLLEGE_CODE", "TOTAL_CREDIT_HOURS_UMF", "IA_STUDENT_TYPE_CODE", "PRIMARY_MAJOR_1", "INTL_IND", "VETERAN_IND", "REPORT_ETHNICITY", "UMID", "FIRST_NAME", "LAST_NAME", "CA_EMAIL") AS 
  (
    SELECT
      td_student_data.sd_pidm,
      td_student_data.sd_term_code,
      (
        SELECT
          td1.registered_ind
        FROM
          um_student_data td1
        WHERE
            td1.sd_term_code = to_char((TO_NUMBER(td_student_data.sd_term_code) + 100))
          AND td1.sd_pidm = td_student_data.sd_pidm
          AND td_student_data.sd_term_code LIKE '%10'
      ) current_enrolled_ind,--REGISTERED AT UMF for Fall
      (
        SELECT
          td1.registered_ind
        FROM
          um_student_data td1
        WHERE
            td1.sd_term_code = to_char((TO_NUMBER(td_student_data.sd_term_code) + 10))
          AND td1.sd_pidm = td_student_data.sd_pidm
          AND td_student_data.sd_term_code LIKE '%10'
      ) winter_enrolled_ind,--REGISTERED AT UMF for Fall
      td_student_data.registered_ind,
      td_student_data.full_part_time_ind_umf,
      td_student_data.primary_level_code,
      td_student_data.primary_admit_code,
      td_student_data.primary_college_code,
      td_student_data.total_credit_hours_umf,
      td_student_data.ia_student_type_code,
      td_student_data.primary_major_1,
      td_demographic.intl_ind,
      td_demographic.veteran_ind,
      td_demographic.report_ethnicity,
      td_demographic.umid,
      td_demographic.first_name,
      td_demographic.last_name,
      td_demographic.ca_email
    FROM
      td_student_data
      LEFT OUTER JOIN td_demographic ON td_demographic.td_term_code = td_student_data.sd_term_code
                                        AND td_demographic.dm_pidm = td_student_data.sd_pidm
    WHERE
      sd_term_code >= '201610' 
--and sd_term_code like '%10'
  )
;
