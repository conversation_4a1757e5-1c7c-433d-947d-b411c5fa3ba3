Begin

--FTIAC COHORTS
p_nsc_ipeds_outcomes_measures('16-17','F','F','Y');--Update FY 
p_nsc_ipeds_outcomes_measures('16-17','F','F','N');--Update FY 
p_nsc_ipeds_outcomes_measures('16-17','F','P','Y');--Update FY 
p_nsc_ipeds_outcomes_measures('16-17','F','P','N');--Update FY 

--TRANSFER COHORTS
p_nsc_ipeds_outcomes_measures('16-17','T','F','Y');--Update FY 
p_nsc_ipeds_outcomes_measures('16-17','T','F','N');--Update FY 
p_nsc_ipeds_outcomes_measures('16-17','T','P','Y');--Update FY 
p_nsc_ipeds_outcomes_measures('16-17','T','P','N');--Update FY 

end;

--FTIAC COHORTS
SELECT
'16-17' FY_IN,
'F' STYPE_IN,
'F' FULLPART_IN,
'Y' PELLIND_IN
FROM
DUAL
UNION
SELECT
'16-17' FY_IN,
'F' STYPE_IN,
'F' FULLPART_IN,
'N' PELLIND_IN
FROM
DUAL
UNION
SELECT
'16-17' FY_IN,
'F' STYPE_IN,
'P' FULLPART_IN,
'Y' PELLIND_IN
FROM
DUAL
UNION
SELECT
'16-17' FY_IN,
'F' STYPE_IN,
'P' FULLPART_IN,
'N' PELLIND_IN
FROM
DUAL
UNION
--TRANSFER COHORTS
SELECT
'16-17' FY_IN,
'T' STYPE_IN,
'F' FULLPART_IN,
'Y' PELLIND_IN
FROM
DUAL
UNION
SELECT
'16-17' FY_IN,
'T' STYPE_IN,
'F' FULLPART_IN,
'N' PELLIND_IN
FROM
DUAL
UNION
SELECT
'16-17' FY_IN,
'T' STYPE_IN,
'P' FULLPART_IN,
'Y' PELLIND_IN
FROM
DUAL
UNION
SELECT
'16-17' FY_IN,
'T' STYPE_IN,
'P' FULLPART_IN,
'N' PELLIND_IN
FROM
DUAL;