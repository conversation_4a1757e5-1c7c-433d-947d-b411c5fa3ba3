CREATE OR REPLACE PACKAGE "IMPRPTS" as 

  procedure p_table_pull;

  procedure p_import_rcrapp1;
  procedure p_import_rcrapp3;
  procedure p_import_rcrapp4;
  procedure p_import_rcrlds4;
  procedure p_import_rorstat;
  procedure p_import_gtvsdax;

  procedure p_import_spbpers_add;
  procedure p_import_sibinst;
  procedure p_import_tbbdetc;
  procedure p_import_tbrmemo;
  procedure p_import_tbraccd;

  procedure p_import_rpratrm;
  procedure p_import_rrrareq;

  procedure p_import_stvclas;
  procedure p_import_stvgmod;
  procedure p_import_gerattd;
  procedure p_import_scbcrse;
  procedure p_import_sfrstcr;
  procedure p_import_sgbstdn;
  procedure p_import_sgrchrt;
  procedure p_import_sgrsatt;
  procedure p_import_shrdgmr;
  procedure p_import_shrgrde;
  procedure p_import_shrtckn;
  procedure p_import_shrttrm;
  procedure p_import_ssbsect;


  procedure p_fix_majors(term_code_in varchar2);

end imprpts;

/


CREATE OR REPLACE PACKAGE BODY "IMPRPTS" as

  procedure p_table_pull as
  begin
    p_import_rcrapp1;
    p_import_rcrapp3;
    p_import_rcrapp4;
    p_import_rcrlds4;
    p_import_rorstat;
    p_import_gtvsdax;
    p_import_spbpers_add;
    p_import_sibinst;

    p_import_stvgmod;
    p_import_tbbdetc;
    p_import_tbrmemo;
    p_import_tbraccd;
    p_import_rpratrm;
    p_import_rrrareq;
    p_import_gerattd;
    p_import_scbcrse;
    p_import_sfrstcr;
    p_import_sgbstdn;
    p_import_sgrchrt;
    p_import_sgrsatt;
    p_import_shrdgmr;
    p_import_shrgrde;
    p_import_shrtckn;
    p_import_shrttrm;
    p_import_ssbsect;
    p_import_stvclas;

  end p_table_pull;

  procedure p_import_stvclas as
  begin
    execute immediate('truncate table stvclas_ext');

    insert into stvclas_ext(
    stvclas_ext.stvclas_code,
    stvclas_ext.stvclas_desc,
    stvclas_ext.stvclas_activity_date,
    stvclas_ext.stvclas_edi_equiv,
    stvclas_ext.STVCLAS_LMS_EQUIV    
    )
    select
    stvclas.stvclas_code,
    stvclas.stvclas_desc,
    stvclas.stvclas_activity_date,
    stvclas.stvclas_edi_equiv,
    stvclas.STVCLAS_LMS_EQUIV    
    from stvclas@aimsmgr201040;

    commit;
  end;

  procedure p_import_ssbsect as
  begin
    execute immediate('truncate table ssbsect_ext');

    insert into ssbsect_ext(
    ssbsect_ext.SSBSECT_TERM_CODE,
    ssbsect_ext.SSBSECT_CRN,
    ssbsect_ext.SSBSECT_PTRM_CODE,
    ssbsect_ext.SSBSECT_SUBJ_CODE,
    ssbsect_ext.SSBSECT_CRSE_NUMB,
    ssbsect_ext.SSBSECT_SEQ_NUMB,
    ssbsect_ext.SSBSECT_SSTS_CODE,
    ssbsect_ext.SSBSECT_SCHD_CODE,
    ssbsect_ext.SSBSECT_CAMP_CODE,
    ssbsect_ext.SSBSECT_CRSE_TITLE,
    ssbsect_ext.SSBSECT_CREDIT_HRS,
    ssbsect_ext.SSBSECT_BILL_HRS,
    ssbsect_ext.SSBSECT_GMOD_CODE,
    ssbsect_ext.SSBSECT_SAPR_CODE,
    ssbsect_ext.SSBSECT_SESS_CODE,
    ssbsect_ext.SSBSECT_LINK_IDENT,
    ssbsect_ext.SSBSECT_PRNT_IND,
    ssbsect_ext.SSBSECT_GRADABLE_IND,
    ssbsect_ext.SSBSECT_TUIW_IND,
    ssbsect_ext.SSBSECT_REG_ONEUP,
    ssbsect_ext.SSBSECT_PRIOR_ENRL,
    ssbsect_ext.SSBSECT_PROJ_ENRL,
    ssbsect_ext.SSBSECT_MAX_ENRL,
    ssbsect_ext.SSBSECT_ENRL,
    ssbsect_ext.SSBSECT_SEATS_AVAIL,
    ssbsect_ext.SSBSECT_TOT_CREDIT_HRS,
    ssbsect_ext.SSBSECT_CENSUS_ENRL,
    ssbsect_ext.SSBSECT_CENSUS_ENRL_DATE,
    ssbsect_ext.SSBSECT_ACTIVITY_DATE,
    ssbsect_ext.SSBSECT_PTRM_START_DATE,
    ssbsect_ext.SSBSECT_PTRM_END_DATE,
    ssbsect_ext.SSBSECT_PTRM_WEEKS,
    ssbsect_ext.SSBSECT_RESERVED_IND,
    ssbsect_ext.SSBSECT_WAIT_CAPACITY,
    ssbsect_ext.SSBSECT_WAIT_COUNT,
    ssbsect_ext.SSBSECT_WAIT_AVAIL,
    ssbsect_ext.SSBSECT_LEC_HR,
    ssbsect_ext.SSBSECT_LAB_HR,
    ssbsect_ext.ssbsect_oth_hr,
    ssbsect_ext.ssbsect_cont_hr,
    ssbsect_ext.ssbsect_acct_code,
    ssbsect_ext.SSBSECT_ACCL_CODE,
    ssbsect_ext.SSBSECT_CENSUS_2_DATE,
    ssbsect_ext.SSBSECT_ENRL_CUT_OFF_DATE,
    ssbsect_ext.SSBSECT_ACAD_CUT_OFF_DATE,
    ssbsect_ext.SSBSECT_DROP_CUT_OFF_DATE,
    ssbsect_ext.SSBSECT_CENSUS_2_ENRL,
    ssbsect_ext.SSBSECT_VOICE_AVAIL,
    ssbsect_ext.SSBSECT_CAPP_PREREQ_TEST_IND,
    ssbsect_ext.SSBSECT_GSCH_NAME,
    ssbsect_ext.SSBSECT_BEST_OF_COMP,
    ssbsect_ext.SSBSECT_SUBSET_OF_COMP,
    ssbsect_ext.SSBSECT_INSM_CODE,
    ssbsect_ext.SSBSECT_REG_FROM_DATE,
    ssbsect_ext.SSBSECT_REG_TO_DATE,
    ssbsect_ext.SSBSECT_LEARNER_REGSTART_FDATE,
    ssbsect_ext.SSBSECT_LEARNER_REGSTART_TDATE,
    ssbsect_ext.SSBSECT_DUNT_CODE,
    ssbsect_ext.SSBSECT_NUMBER_OF_UNITS,
    ssbsect_ext.SSBSECT_NUMBER_OF_EXTENSIONS,
    ssbsect_ext.SSBSECT_DATA_ORIGIN,
    ssbsect_ext.SSBSECT_USER_ID,
    ssbsect_ext.ssbsect_intg_cde
    )
    select
    ssbsect.SSBSECT_TERM_CODE,
    ssbsect.SSBSECT_CRN,
    ssbsect.SSBSECT_PTRM_CODE,
    ssbsect.SSBSECT_SUBJ_CODE,
    ssbsect.SSBSECT_CRSE_NUMB,
    ssbsect.SSBSECT_SEQ_NUMB,
    ssbsect.SSBSECT_SSTS_CODE,
    ssbsect.SSBSECT_SCHD_CODE,
    ssbsect.SSBSECT_CAMP_CODE,
    ssbsect.SSBSECT_CRSE_TITLE,
    ssbsect.SSBSECT_CREDIT_HRS,
    ssbsect.SSBSECT_BILL_HRS,
    ssbsect.SSBSECT_GMOD_CODE,
    ssbsect.SSBSECT_SAPR_CODE,
    ssbsect.SSBSECT_SESS_CODE,
    ssbsect.SSBSECT_LINK_IDENT,
    ssbsect.SSBSECT_PRNT_IND,
    ssbsect.SSBSECT_GRADABLE_IND,
    ssbsect.SSBSECT_TUIW_IND,
    ssbsect.SSBSECT_REG_ONEUP,
    ssbsect.SSBSECT_PRIOR_ENRL,
    ssbsect.SSBSECT_PROJ_ENRL,
    ssbsect.SSBSECT_MAX_ENRL,
    ssbsect.SSBSECT_ENRL,
    ssbsect.SSBSECT_SEATS_AVAIL,
    ssbsect.SSBSECT_TOT_CREDIT_HRS,
    ssbsect.SSBSECT_CENSUS_ENRL,
    ssbsect.SSBSECT_CENSUS_ENRL_DATE,
    ssbsect.SSBSECT_ACTIVITY_DATE,
    ssbsect.SSBSECT_PTRM_START_DATE,
    ssbsect.SSBSECT_PTRM_END_DATE,
    ssbsect.SSBSECT_PTRM_WEEKS,
    ssbsect.SSBSECT_RESERVED_IND,
    ssbsect.SSBSECT_WAIT_CAPACITY,
    ssbsect.SSBSECT_WAIT_COUNT,
    ssbsect.SSBSECT_WAIT_AVAIL,
    ssbsect.SSBSECT_LEC_HR,
    ssbsect.SSBSECT_LAB_HR,
    ssbsect.ssbsect_oth_hr,
    ssbsect.ssbsect_cont_hr,
    ssbsect.ssbsect_acct_code,
    ssbsect.SSBSECT_ACCL_CODE,
    ssbsect.SSBSECT_CENSUS_2_DATE,
    ssbsect.SSBSECT_ENRL_CUT_OFF_DATE,
    ssbsect.SSBSECT_ACAD_CUT_OFF_DATE,
    ssbsect.SSBSECT_DROP_CUT_OFF_DATE,
    ssbsect.SSBSECT_CENSUS_2_ENRL,
    ssbsect.SSBSECT_VOICE_AVAIL,
    ssbsect.SSBSECT_CAPP_PREREQ_TEST_IND,
    ssbsect.SSBSECT_GSCH_NAME,
    ssbsect.SSBSECT_BEST_OF_COMP,
    ssbsect.SSBSECT_SUBSET_OF_COMP,
    ssbsect.SSBSECT_INSM_CODE,
    ssbsect.SSBSECT_REG_FROM_DATE,
    ssbsect.SSBSECT_REG_TO_DATE,
    ssbsect.SSBSECT_LEARNER_REGSTART_FDATE,
    ssbsect.SSBSECT_LEARNER_REGSTART_TDATE,
    ssbsect.SSBSECT_DUNT_CODE,
    ssbsect.ssbsect_number_of_units,
    ssbsect.SSBSECT_NUMBER_OF_EXTENSIONS,
    ssbsect.SSBSECT_DATA_ORIGIN,
    ssbsect.SSBSECT_USER_ID,
    ssbsect.ssbsect_intg_cde
    from ssbsect@aimsmgr201040;

    commit;  
  end;

  procedure p_import_shrttrm as
  begin
    execute immediate('truncate table shrttrm_ext');

    insert into shrttrm_ext(
    shrttrm_ext.shrttrm_pidm,
    shrttrm_ext.SHRTTRM_TERM_CODE,
    shrttrm_ext.SHRTTRM_UPDATE_SOURCE_IND,
    shrttrm_ext.SHRTTRM_PRE_CATALOG_IND,
    shrttrm_ext.SHRTTRM_RECORD_STATUS_IND,
    shrttrm_ext.SHRTTRM_RECORD_STATUS_DATE,
    shrttrm_ext.SHRTTRM_GRADE_MAILING_DATE,
    shrttrm_ext.SHRTTRM_GRADE_MAILER_CHG_DATE,
    shrttrm_ext.SHRTTRM_GRADE_MAILER_DUP,
    shrttrm_ext.SHRTTRM_GRADE_MAILER_DUP_DATE,
    shrttrm_ext.SHRTTRM_EXAM_CODE,
    shrttrm_ext.SHRTTRM_CODE_TRANSCRIPT_DIST,
    shrttrm_ext.SHRTTRM_ASTD_CODE_END_OF_TERM,
    shrttrm_ext.SHRTTRM_ASTD_DATE_END_OF_TERM,
    shrttrm_ext.SHRTTRM_ACTIVITY_DATE,
    shrttrm_ext.SHRTTRM_ASTD_CODE_DL,
    shrttrm_ext.SHRTTRM_ASTD_DATE_DL,
    shrttrm_ext.SHRTTRM_WRSN_CODE,
    shrttrm_ext.SHRTTRM_SBGI_CODE_TRANS,
    shrttrm_ext.SHRTTRM_PREV_CODE,
    shrttrm_ext.SHRTTRM_PREV_DATE,
    shrttrm_ext.SHRTTRM_CAST_CODE,
    shrttrm_ext.shrttrm_cast_date
    )
    select
    shrttrm.shrttrm_pidm,
    shrttrm.SHRTTRM_TERM_CODE,
    shrttrm.SHRTTRM_UPDATE_SOURCE_IND,
    shrttrm.SHRTTRM_PRE_CATALOG_IND,
    shrttrm.SHRTTRM_RECORD_STATUS_IND,
    shrttrm.SHRTTRM_RECORD_STATUS_DATE,
    shrttrm.SHRTTRM_GRADE_MAILING_DATE,
    shrttrm.SHRTTRM_GRADE_MAILER_CHG_DATE,
    shrttrm.SHRTTRM_GRADE_MAILER_DUP,
    shrttrm.SHRTTRM_GRADE_MAILER_DUP_DATE,
    shrttrm.SHRTTRM_EXAM_CODE,
    shrttrm.SHRTTRM_CODE_TRANSCRIPT_DIST,
    shrttrm.SHRTTRM_ASTD_CODE_END_OF_TERM,
    shrttrm.SHRTTRM_ASTD_DATE_END_OF_TERM,
    shrttrm.SHRTTRM_ACTIVITY_DATE,
    shrttrm.SHRTTRM_ASTD_CODE_DL,
    shrttrm.SHRTTRM_ASTD_DATE_DL,
    shrttrm.SHRTTRM_WRSN_CODE,
    shrttrm.SHRTTRM_SBGI_CODE_TRANS,
    shrttrm.SHRTTRM_PREV_CODE,
    shrttrm.SHRTTRM_PREV_DATE,
    shrttrm.SHRTTRM_CAST_CODE,
    shrttrm.shrttrm_cast_date
    from shrttrm@aimsmgr201040;

    commit;
  end;

  procedure p_import_shrtckn as
  begin
    execute immediate('truncate table shrtckn_ext');

    insert into shrtckn_ext(
    shrtckn_ext.SHRTCKN_PIDM,
    shrtckn_ext.SHRTCKN_TERM_CODE,
    shrtckn_ext.SHRTCKN_SEQ_NO,
    shrtckn_ext.SHRTCKN_CRN,
    shrtckn_ext.SHRTCKN_SUBJ_CODE,
    shrtckn_ext.SHRTCKN_CRSE_NUMB,
    shrtckn_ext.SHRTCKN_COLL_CODE,
    shrtckn_ext.SHRTCKN_CAMP_CODE,
    shrtckn_ext.SHRTCKN_DEPT_CODE,
    shrtckn_ext.SHRTCKN_DIVS_CODE,
    shrtckn_ext.SHRTCKN_SESS_CODE,
    shrtckn_ext.SHRTCKN_CRSE_TITLE,
    shrtckn_ext.SHRTCKN_REG_SEQ,
    shrtckn_ext.SHRTCKN_COURSE_COMMENT,
    shrtckn_ext.SHRTCKN_REPEAT_COURSE_IND,
    shrtckn_ext.SHRTCKN_ACTIVITY_DATE,
    shrtckn_ext.SHRTCKN_PTRM_CODE,
    shrtckn_ext.SHRTCKN_SEQ_NUMB,
    shrtckn_ext.SHRTCKN_PTRM_START_DATE,
    shrtckn_ext.SHRTCKN_PTRM_END_DATE,
    shrtckn_ext.SHRTCKN_CONT_HR,
    shrtckn_ext.SHRTCKN_SCHD_CODE,
    shrtckn_ext.SHRTCKN_REPEAT_SYS_IND,
    shrtckn_ext.SHRTCKN_REG_START_DATE,
    shrtckn_ext.SHRTCKN_REG_COMPLETION_DATE,
    shrtckn_ext.SHRTCKN_NUMBER_OF_EXTENSIONS,
    shrtckn_ext.shrtckn_long_course_title
    )
    select
    shrtckn.SHRTCKN_PIDM,
    shrtckn.SHRTCKN_TERM_CODE,
    shrtckn.SHRTCKN_SEQ_NO,
    shrtckn.SHRTCKN_CRN,
    shrtckn.SHRTCKN_SUBJ_CODE,
    shrtckn.SHRTCKN_CRSE_NUMB,
    shrtckn.SHRTCKN_COLL_CODE,
    shrtckn.SHRTCKN_CAMP_CODE,
    shrtckn.SHRTCKN_DEPT_CODE,
    shrtckn.SHRTCKN_DIVS_CODE,
    shrtckn.SHRTCKN_SESS_CODE,
    shrtckn.SHRTCKN_CRSE_TITLE,
    shrtckn.SHRTCKN_REG_SEQ,
    shrtckn.SHRTCKN_COURSE_COMMENT,
    shrtckn.SHRTCKN_REPEAT_COURSE_IND,
    shrtckn.SHRTCKN_ACTIVITY_DATE,
    shrtckn.SHRTCKN_PTRM_CODE,
    shrtckn.SHRTCKN_SEQ_NUMB,
    shrtckn.SHRTCKN_PTRM_START_DATE,
    shrtckn.SHRTCKN_PTRM_END_DATE,
    shrtckn.SHRTCKN_CONT_HR,
    shrtckn.SHRTCKN_SCHD_CODE,
    shrtckn.SHRTCKN_REPEAT_SYS_IND,
    shrtckn.SHRTCKN_REG_START_DATE,
    shrtckn.SHRTCKN_REG_COMPLETION_DATE,
    shrtckn.SHRTCKN_NUMBER_OF_EXTENSIONS,
    shrtckn.shrtckn_long_course_title
    from shrtckn@aimsmgr201040;

    commit;
  end;  

  procedure p_import_shrgrde as
  begin
    execute immediate('truncate table shrgrde_ext');

    insert into shrgrde_ext(
    shrgrde_ext.shrgrde_code,
    shrgrde_ext.shrgrde_abbrev,
    shrgrde_ext.shrgrde_levl_code,
    shrgrde_ext.shrgrde_term_code_effective,
    shrgrde_ext.shrgrde_quality_points,
    shrgrde_ext.shrgrde_attempted_ind,
    shrgrde_ext.shrgrde_completed_ind,
    shrgrde_ext.shrgrde_passed_ind,
    shrgrde_ext.shrgrde_gpa_ind,
    shrgrde_ext.shrgrde_activity_date,
    shrgrde_ext.shrgrde_grde_status_ind,
    shrgrde_ext.shrgrde_term_code_expired,
    shrgrde_ext.shrgrde_traditional_ind,
    shrgrde_ext.shrgrde_numeric_value,
    shrgrde_ext.shrgrde_web_entry_ind,
    shrgrde_ext.shrgrde_repeat_include_ind,
    shrgrde_ext.SHRGRDE_IMPCMP_IND)
    select
    shrgrde.shrgrde_code,
    shrgrde.shrgrde_abbrev,
    shrgrde.shrgrde_levl_code,
    shrgrde.shrgrde_term_code_effective,
    shrgrde.shrgrde_quality_points,
    shrgrde.shrgrde_attempted_ind,
    shrgrde.shrgrde_completed_ind,
    shrgrde.shrgrde_passed_ind,
    shrgrde.shrgrde_gpa_ind,
    shrgrde.shrgrde_activity_date,
    shrgrde.shrgrde_grde_status_ind,
    shrgrde.shrgrde_term_code_expired,
    shrgrde.shrgrde_traditional_ind,
    shrgrde.shrgrde_numeric_value,
    shrgrde.shrgrde_web_entry_ind,
    shrgrde.shrgrde_repeat_include_ind,
    shrgrde.shrgrde_impcmp_ind    
    from shrgrde@aimsmgr201040;

    commit;
  end;

  procedure p_import_shrdgmr as
  begin
    execute immediate('truncate table shrdgmr_ext');

    insert into shrdgmr_ext(
    shrdgmr_ext.SHRDGMR_PIDM,
    shrdgmr_ext.SHRDGMR_SEQ_NO,
    shrdgmr_ext.SHRDGMR_DEGC_CODE,
    shrdgmr_ext.SHRDGMR_DEGS_CODE,
    shrdgmr_ext.SHRDGMR_LEVL_CODE,
    shrdgmr_ext.SHRDGMR_COLL_CODE_1,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_1,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_MINR_1,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_1,
    shrdgmr_ext.SHRDGMR_COLL_CODE_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_MINR_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_2,
    shrdgmr_ext.SHRDGMR_APPL_DATE,
    shrdgmr_ext.SHRDGMR_GRAD_DATE,
    shrdgmr_ext.SHRDGMR_ACYR_CODE_BULLETIN,
    shrdgmr_ext.SHRDGMR_ACTIVITY_DATE,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_MINR_1_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_1_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_1_3,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_MINR_2_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_2_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_2_3,
    shrdgmr_ext.SHRDGMR_TERM_CODE_STUREC,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_1_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_2_2,
    shrdgmr_ext.SHRDGMR_CAMP_CODE,
    shrdgmr_ext.SHRDGMR_TERM_CODE_GRAD,
    shrdgmr_ext.SHRDGMR_ACYR_CODE,
    shrdgmr_ext.SHRDGMR_GRST_CODE,
    shrdgmr_ext.SHRDGMR_FEE_IND,
    shrdgmr_ext.shrdgmr_fee_date,
    shrdgmr_ext.SHRDGMR_AUTHORIZED,
    shrdgmr_ext.SHRDGMR_TERM_CODE_COMPLETED,
    shrdgmr_ext.SHRDGMR_DEGC_CODE_DUAL,
    shrdgmr_ext.SHRDGMR_LEVL_CODE_DUAL,
    shrdgmr_ext.SHRDGMR_DEPT_CODE_DUAL,
    shrdgmr_ext.SHRDGMR_COLL_CODE_DUAL,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_DUAL,
    shrdgmr_ext.SHRDGMR_DEPT_CODE,
    shrdgmr_ext.SHRDGMR_DEPT_CODE_2,
    shrdgmr_ext.SHRDGMR_PROGRAM,
    shrdgmr_ext.SHRDGMR_TERM_CODE_CTLG_1,
    shrdgmr_ext.SHRDGMR_DEPT_CODE_1_2,
    shrdgmr_ext.SHRDGMR_DEPT_CODE_2_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_121,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_122,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_123,
    shrdgmr_ext.SHRDGMR_TERM_CODE_CTLG_2,
    shrdgmr_ext.SHRDGMR_CAMP_CODE_2,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_221,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_222,
    shrdgmr_ext.SHRDGMR_MAJR_CODE_CONC_223,
    shrdgmr_ext.SHRDGMR_CURR_RULE_1,
    shrdgmr_ext.SHRDGMR_CMJR_RULE_1_1,
    shrdgmr_ext.SHRDGMR_CCON_RULE_11_1,
    shrdgmr_ext.SHRDGMR_CCON_RULE_11_2,
    shrdgmr_ext.SHRDGMR_CCON_RULE_11_3,
    shrdgmr_ext.SHRDGMR_CMJR_RULE_1_2,
    shrdgmr_ext.SHRDGMR_CCON_RULE_12_1,
    shrdgmr_ext.SHRDGMR_CCON_RULE_12_2,
    shrdgmr_ext.SHRDGMR_CCON_RULE_12_3,
    shrdgmr_ext.SHRDGMR_CMNR_RULE_1_1,
    shrdgmr_ext.SHRDGMR_CMNR_RULE_1_2,
    shrdgmr_ext.SHRDGMR_CURR_RULE_2,
    shrdgmr_ext.SHRDGMR_CMJR_RULE_2_1,
    shrdgmr_ext.SHRDGMR_CCON_RULE_21_1,
    shrdgmr_ext.SHRDGMR_CCON_RULE_21_2,
    shrdgmr_ext.SHRDGMR_CCON_RULE_21_3,
    shrdgmr_ext.SHRDGMR_CMJR_RULE_2_2,
    shrdgmr_ext.SHRDGMR_CCON_RULE_22_1,
    shrdgmr_ext.SHRDGMR_CCON_RULE_22_2,
    shrdgmr_ext.SHRDGMR_CCON_RULE_22_3,
    shrdgmr_ext.SHRDGMR_CMNR_RULE_2_1,
    shrdgmr_ext.SHRDGMR_CMNR_RULE_2_2,
    shrdgmr_ext.shrdgmr_data_origin,
    shrdgmr_ext.shrdgmr_user_id  
    )
    select
    shrdgmr.SHRDGMR_PIDM,
    shrdgmr.SHRDGMR_SEQ_NO,
    shrdgmr.SHRDGMR_DEGC_CODE,
    shrdgmr.SHRDGMR_DEGS_CODE,
    shrdgmr.SHRDGMR_LEVL_CODE,
    shrdgmr.SHRDGMR_COLL_CODE_1,
    shrdgmr.SHRDGMR_MAJR_CODE_1,
    shrdgmr.SHRDGMR_MAJR_CODE_MINR_1,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_1,
    shrdgmr.SHRDGMR_COLL_CODE_2,
    shrdgmr.SHRDGMR_MAJR_CODE_2,
    shrdgmr.SHRDGMR_MAJR_CODE_MINR_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_2,
    shrdgmr.SHRDGMR_APPL_DATE,
    shrdgmr.SHRDGMR_GRAD_DATE,
    shrdgmr.SHRDGMR_ACYR_CODE_BULLETIN,
    shrdgmr.SHRDGMR_ACTIVITY_DATE,
    shrdgmr.SHRDGMR_MAJR_CODE_MINR_1_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_1_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_1_3,
    shrdgmr.SHRDGMR_MAJR_CODE_MINR_2_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_2_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_2_3,
    shrdgmr.SHRDGMR_TERM_CODE_STUREC,
    shrdgmr.SHRDGMR_MAJR_CODE_1_2,
    shrdgmr.SHRDGMR_MAJR_CODE_2_2,
    shrdgmr.SHRDGMR_CAMP_CODE,
    shrdgmr.SHRDGMR_TERM_CODE_GRAD,
    shrdgmr.SHRDGMR_ACYR_CODE,
    shrdgmr.SHRDGMR_GRST_CODE,
    shrdgmr.SHRDGMR_FEE_IND,
    shrdgmr.shrdgmr_fee_date,
    shrdgmr.SHRDGMR_AUTHORIZED,
    shrdgmr.SHRDGMR_TERM_CODE_COMPLETED,
    shrdgmr.SHRDGMR_DEGC_CODE_DUAL,
    shrdgmr.SHRDGMR_LEVL_CODE_DUAL,
    shrdgmr.SHRDGMR_DEPT_CODE_DUAL,
    shrdgmr.SHRDGMR_COLL_CODE_DUAL,
    shrdgmr.SHRDGMR_MAJR_CODE_DUAL,
    shrdgmr.SHRDGMR_DEPT_CODE,
    shrdgmr.SHRDGMR_DEPT_CODE_2,
    shrdgmr.SHRDGMR_PROGRAM,
    shrdgmr.SHRDGMR_TERM_CODE_CTLG_1,
    shrdgmr.SHRDGMR_DEPT_CODE_1_2,
    shrdgmr.SHRDGMR_DEPT_CODE_2_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_121,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_122,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_123,
    shrdgmr.SHRDGMR_TERM_CODE_CTLG_2,
    shrdgmr.SHRDGMR_CAMP_CODE_2,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_221,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_222,
    shrdgmr.SHRDGMR_MAJR_CODE_CONC_223,
    shrdgmr.SHRDGMR_CURR_RULE_1,
    shrdgmr.SHRDGMR_CMJR_RULE_1_1,
    shrdgmr.SHRDGMR_CCON_RULE_11_1,
    shrdgmr.SHRDGMR_CCON_RULE_11_2,
    shrdgmr.SHRDGMR_CCON_RULE_11_3,
    shrdgmr.SHRDGMR_CMJR_RULE_1_2,
    shrdgmr.SHRDGMR_CCON_RULE_12_1,
    shrdgmr.SHRDGMR_CCON_RULE_12_2,
    shrdgmr.SHRDGMR_CCON_RULE_12_3,
    shrdgmr.SHRDGMR_CMNR_RULE_1_1,
    shrdgmr.SHRDGMR_CMNR_RULE_1_2,
    shrdgmr.SHRDGMR_CURR_RULE_2,
    shrdgmr.SHRDGMR_CMJR_RULE_2_1,
    shrdgmr.SHRDGMR_CCON_RULE_21_1,
    shrdgmr.SHRDGMR_CCON_RULE_21_2,
    shrdgmr.SHRDGMR_CCON_RULE_21_3,
    shrdgmr.SHRDGMR_CMJR_RULE_2_2,
    shrdgmr.SHRDGMR_CCON_RULE_22_1,
    shrdgmr.SHRDGMR_CCON_RULE_22_2,
    shrdgmr.SHRDGMR_CCON_RULE_22_3,
    shrdgmr.SHRDGMR_CMNR_RULE_2_1,
    shrdgmr.SHRDGMR_CMNR_RULE_2_2,
    shrdgmr.shrdgmr_data_origin,
    shrdgmr.shrdgmr_user_id  
    from shrdgmr@aimsmgr201040;

    commit;
  end;

  procedure p_import_sgrsatt as
  begin
    execute immediate('truncate table sgrsatt_ext');

    insert into sgrsatt_ext(
    sgrsatt_ext.sgrsatt_pidm,
    sgrsatt_ext.sgrsatt_term_code_eff,
    sgrsatt_ext.sgrsatt_atts_code,
    sgrsatt_ext.sgrsatt_activity_date
    )
    select
    sgrsatt.sgrsatt_pidm,
    sgrsatt.sgrsatt_term_code_eff,
    sgrsatt.sgrsatt_atts_code,
    sgrsatt.sgrsatt_activity_date
    from sgrsatt@aimsmgr201040;

    commit;
  end;

  procedure p_import_sgrchrt as
  begin
    execute immediate('truncate table sgrchrt_ext');

    insert into sgrchrt_ext(
    sgrchrt_ext.sgrchrt_pidm,
    sgrchrt_ext.sgrchrt_term_code_eff,
    sgrchrt_ext.sgrchrt_chrt_code,
    sgrchrt_ext.SGRCHRT_ACTIVE_IND,
    sgrchrt_ext.sgrchrt_crea_code,
    sgrchrt_ext.sgrchrt_activity_date)
    select
    sgrchrt.sgrchrt_pidm,
    sgrchrt.sgrchrt_term_code_eff,
    sgrchrt.sgrchrt_chrt_code,
    sgrchrt.SGRCHRT_ACTIVE_IND,
    sgrchrt.sgrchrt_crea_code,
    sgrchrt.sgrchrt_activity_date
    from sgrchrt@aimsmgr201040;

    commit;
  end;

  procedure p_import_sgbstdn as
  begin
    execute immediate('truncate table sgbstdn_ext');

    insert into sgbstdn_ext(
    sgbstdn_ext.SGBSTDN_PIDM,
    sgbstdn_ext.SGBSTDN_TERM_CODE_EFF,
    sgbstdn_ext.SGBSTDN_STST_CODE,
    sgbstdn_ext.sgbstdn_levl_code,
    sgbstdn_ext.sgbstdn_styp_code,
    sgbstdn_ext.SGBSTDN_TERM_CODE_MATRIC,
    sgbstdn_ext.SGBSTDN_TERM_CODE_ADMIT,
    sgbstdn_ext.SGBSTDN_EXP_GRAD_DATE,
    sgbstdn_ext.SGBSTDN_CAMP_CODE,
    sgbstdn_ext.SGBSTDN_FULL_PART_IND,
    sgbstdn_ext.SGBSTDN_SESS_CODE,
    sgbstdn_ext.SGBSTDN_RESD_CODE,
    sgbstdn_ext.SGBSTDN_COLL_CODE_1,
    sgbstdn_ext.SGBSTDN_DEGC_CODE_1,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_1,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_MINR_1,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_MINR_1_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_1,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_1_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_1_3,
    sgbstdn_ext.SGBSTDN_COLL_CODE_2,
    sgbstdn_ext.SGBSTDN_DEGC_CODE_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_MINR_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_MINR_2_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_2_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_2_3,
    sgbstdn_ext.SGBSTDN_ORSN_CODE,
    sgbstdn_ext.SGBSTDN_PRAC_CODE,
    sgbstdn_ext.SGBSTDN_ADVR_PIDM,
    sgbstdn_ext.SGBSTDN_GRAD_CREDIT_APPR_IND,
    sgbstdn_ext.SGBSTDN_CAPL_CODE,
    sgbstdn_ext.SGBSTDN_LEAV_CODE,
    sgbstdn_ext.SGBSTDN_LEAV_FROM_DATE,
    sgbstdn_ext.SGBSTDN_LEAV_TO_DATE,
    sgbstdn_ext.SGBSTDN_ASTD_CODE,
    sgbstdn_ext.SGBSTDN_TERM_CODE_ASTD,
    sgbstdn_ext.SGBSTDN_RATE_CODE,
    sgbstdn_ext.SGBSTDN_ACTIVITY_DATE,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_1_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_2_2,
    sgbstdn_ext.sgbstdn_edlv_code,
    sgbstdn_ext.SGBSTDN_INCM_CODE,
    sgbstdn_ext.SGBSTDN_ADMT_CODE,
    sgbstdn_ext.SGBSTDN_EMEX_CODE,
    sgbstdn_ext.SGBSTDN_APRN_CODE,
    sgbstdn_ext.SGBSTDN_TRCN_CODE,
    sgbstdn_ext.SGBSTDN_GAIN_CODE,
    sgbstdn_ext.SGBSTDN_VOED_CODE,
    sgbstdn_ext.SGBSTDN_BLCK_CODE,
    sgbstdn_ext.SGBSTDN_TERM_CODE_GRAD,
    sgbstdn_ext.SGBSTDN_ACYR_CODE,
    sgbstdn_ext.SGBSTDN_DEPT_CODE,
    sgbstdn_ext.SGBSTDN_SITE_CODE,
    sgbstdn_ext.SGBSTDN_DEPT_CODE_2,
    sgbstdn_ext.SGBSTDN_EGOL_CODE,
    sgbstdn_ext.SGBSTDN_DEGC_CODE_DUAL,
    sgbstdn_ext.SGBSTDN_LEVL_CODE_DUAL,
    sgbstdn_ext.SGBSTDN_DEPT_CODE_DUAL,
    sgbstdn_ext.SGBSTDN_COLL_CODE_DUAL,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_DUAL,
    sgbstdn_ext.SGBSTDN_BSKL_CODE,
    sgbstdn_ext.SGBSTDN_PRIM_ROLL_IND,
    sgbstdn_ext.SGBSTDN_PROGRAM_1,
    sgbstdn_ext.SGBSTDN_TERM_CODE_CTLG_1,
    sgbstdn_ext.SGBSTDN_DEPT_CODE_1_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_121,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_122,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_123,
    sgbstdn_ext.SGBSTDN_SECD_ROLL_IND,
    sgbstdn_ext.SGBSTDN_TERM_CODE_ADMIT_2,
    sgbstdn_ext.SGBSTDN_ADMT_CODE_2,
    sgbstdn_ext.SGBSTDN_PROGRAM_2,
    sgbstdn_ext.SGBSTDN_TERM_CODE_CTLG_2,
    sgbstdn_ext.SGBSTDN_LEVL_CODE_2,
    sgbstdn_ext.SGBSTDN_CAMP_CODE_2,
    sgbstdn_ext.SGBSTDN_DEPT_CODE_2_2,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_221,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_222,
    sgbstdn_ext.SGBSTDN_MAJR_CODE_CONC_223,
    sgbstdn_ext.SGBSTDN_CURR_RULE_1,
    sgbstdn_ext.SGBSTDN_CMJR_RULE_1_1,
    sgbstdn_ext.SGBSTDN_CCON_RULE_11_1,
    sgbstdn_ext.SGBSTDN_CCON_RULE_11_2,
    sgbstdn_ext.SGBSTDN_CCON_RULE_11_3,
    sgbstdn_ext.SGBSTDN_CMJR_RULE_1_2,
    sgbstdn_ext.SGBSTDN_CCON_RULE_12_1,
    sgbstdn_ext.SGBSTDN_CCON_RULE_12_2,
    sgbstdn_ext.SGBSTDN_CCON_RULE_12_3,
    sgbstdn_ext.SGBSTDN_CMNR_RULE_1_1,
    sgbstdn_ext.SGBSTDN_CMNR_RULE_1_2,
    sgbstdn_ext.SGBSTDN_CURR_RULE_2,
    sgbstdn_ext.SGBSTDN_CMJR_RULE_2_1,
    sgbstdn_ext.SGBSTDN_CCON_RULE_21_1,
    sgbstdn_ext.SGBSTDN_CCON_RULE_21_2,
    sgbstdn_ext.SGBSTDN_CCON_RULE_21_3,
    sgbstdn_ext.SGBSTDN_CMJR_RULE_2_2,
    sgbstdn_ext.SGBSTDN_CCON_RULE_22_1,
    sgbstdn_ext.SGBSTDN_CCON_RULE_22_2,
    sgbstdn_ext.SGBSTDN_CCON_RULE_22_3,
    sgbstdn_ext.SGBSTDN_CMNR_RULE_2_1,
    sgbstdn_ext.SGBSTDN_CMNR_RULE_2_2,
    sgbstdn_ext.SGBSTDN_PREV_CODE,
    sgbstdn_ext.SGBSTDN_TERM_CODE_PREV,
    sgbstdn_ext.SGBSTDN_CAST_CODE,
    sgbstdn_ext.sgbstdn_term_code_cast,
    sgbstdn_ext.sgbstdn_data_origin,
    sgbstdn_ext.sgbstdn_user_id)
    select
    sgbstdn.SGBSTDN_PIDM,
    sgbstdn.SGBSTDN_TERM_CODE_EFF,
    sgbstdn.SGBSTDN_STST_CODE,
    sgbstdn.sgbstdn_levl_code,
    sgbstdn.sgbstdn_styp_code,
    sgbstdn.SGBSTDN_TERM_CODE_MATRIC,
    sgbstdn.SGBSTDN_TERM_CODE_ADMIT,
    sgbstdn.SGBSTDN_EXP_GRAD_DATE,
    sgbstdn.SGBSTDN_CAMP_CODE,
    sgbstdn.SGBSTDN_FULL_PART_IND,
    sgbstdn.SGBSTDN_SESS_CODE,
    sgbstdn.SGBSTDN_RESD_CODE,
    sgbstdn.SGBSTDN_COLL_CODE_1,
    sgbstdn.SGBSTDN_DEGC_CODE_1,
    sgbstdn.SGBSTDN_MAJR_CODE_1,
    sgbstdn.SGBSTDN_MAJR_CODE_MINR_1,
    sgbstdn.SGBSTDN_MAJR_CODE_MINR_1_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_1,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_1_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_1_3,
    sgbstdn.SGBSTDN_COLL_CODE_2,
    sgbstdn.SGBSTDN_DEGC_CODE_2,
    sgbstdn.SGBSTDN_MAJR_CODE_2,
    sgbstdn.SGBSTDN_MAJR_CODE_MINR_2,
    sgbstdn.SGBSTDN_MAJR_CODE_MINR_2_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_2_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_2_3,
    sgbstdn.SGBSTDN_ORSN_CODE,
    sgbstdn.SGBSTDN_PRAC_CODE,
    sgbstdn.SGBSTDN_ADVR_PIDM,
    sgbstdn.SGBSTDN_GRAD_CREDIT_APPR_IND,
    sgbstdn.SGBSTDN_CAPL_CODE,
    sgbstdn.SGBSTDN_LEAV_CODE,
    sgbstdn.SGBSTDN_LEAV_FROM_DATE,
    sgbstdn.SGBSTDN_LEAV_TO_DATE,
    sgbstdn.SGBSTDN_ASTD_CODE,
    sgbstdn.SGBSTDN_TERM_CODE_ASTD,
    sgbstdn.SGBSTDN_RATE_CODE,
    sgbstdn.SGBSTDN_ACTIVITY_DATE,
    sgbstdn.SGBSTDN_MAJR_CODE_1_2,
    sgbstdn.SGBSTDN_MAJR_CODE_2_2,
    sgbstdn.sgbstdn_edlv_code,
    sgbstdn.SGBSTDN_INCM_CODE,
    sgbstdn.SGBSTDN_ADMT_CODE,
    sgbstdn.SGBSTDN_EMEX_CODE,
    sgbstdn.SGBSTDN_APRN_CODE,
    sgbstdn.SGBSTDN_TRCN_CODE,
    sgbstdn.SGBSTDN_GAIN_CODE,
    sgbstdn.SGBSTDN_VOED_CODE,
    sgbstdn.SGBSTDN_BLCK_CODE,
    sgbstdn.SGBSTDN_TERM_CODE_GRAD,
    sgbstdn.SGBSTDN_ACYR_CODE,
    sgbstdn.SGBSTDN_DEPT_CODE,
    sgbstdn.SGBSTDN_SITE_CODE,
    sgbstdn.SGBSTDN_DEPT_CODE_2,
    sgbstdn.SGBSTDN_EGOL_CODE,
    sgbstdn.SGBSTDN_DEGC_CODE_DUAL,
    sgbstdn.SGBSTDN_LEVL_CODE_DUAL,
    sgbstdn.SGBSTDN_DEPT_CODE_DUAL,
    sgbstdn.SGBSTDN_COLL_CODE_DUAL,
    sgbstdn.SGBSTDN_MAJR_CODE_DUAL,
    sgbstdn.SGBSTDN_BSKL_CODE,
    sgbstdn.SGBSTDN_PRIM_ROLL_IND,
    sgbstdn.SGBSTDN_PROGRAM_1,
    sgbstdn.SGBSTDN_TERM_CODE_CTLG_1,
    sgbstdn.SGBSTDN_DEPT_CODE_1_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_121,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_122,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_123,
    sgbstdn.SGBSTDN_SECD_ROLL_IND,
    sgbstdn.SGBSTDN_TERM_CODE_ADMIT_2,
    sgbstdn.SGBSTDN_ADMT_CODE_2,
    sgbstdn.SGBSTDN_PROGRAM_2,
    sgbstdn.SGBSTDN_TERM_CODE_CTLG_2,
    sgbstdn.SGBSTDN_LEVL_CODE_2,
    sgbstdn.SGBSTDN_CAMP_CODE_2,
    sgbstdn.SGBSTDN_DEPT_CODE_2_2,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_221,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_222,
    sgbstdn.SGBSTDN_MAJR_CODE_CONC_223,
    sgbstdn.SGBSTDN_CURR_RULE_1,
    sgbstdn.SGBSTDN_CMJR_RULE_1_1,
    sgbstdn.SGBSTDN_CCON_RULE_11_1,
    sgbstdn.SGBSTDN_CCON_RULE_11_2,
    sgbstdn.SGBSTDN_CCON_RULE_11_3,
    sgbstdn.SGBSTDN_CMJR_RULE_1_2,
    sgbstdn.SGBSTDN_CCON_RULE_12_1,
    sgbstdn.SGBSTDN_CCON_RULE_12_2,
    sgbstdn.SGBSTDN_CCON_RULE_12_3,
    sgbstdn.SGBSTDN_CMNR_RULE_1_1,
    sgbstdn.SGBSTDN_CMNR_RULE_1_2,
    sgbstdn.SGBSTDN_CURR_RULE_2,
    sgbstdn.SGBSTDN_CMJR_RULE_2_1,
    sgbstdn.SGBSTDN_CCON_RULE_21_1,
    sgbstdn.SGBSTDN_CCON_RULE_21_2,
    sgbstdn.SGBSTDN_CCON_RULE_21_3,
    sgbstdn.SGBSTDN_CMJR_RULE_2_2,
    sgbstdn.SGBSTDN_CCON_RULE_22_1,
    sgbstdn.SGBSTDN_CCON_RULE_22_2,
    sgbstdn.SGBSTDN_CCON_RULE_22_3,
    sgbstdn.SGBSTDN_CMNR_RULE_2_1,
    sgbstdn.SGBSTDN_CMNR_RULE_2_2,
    sgbstdn.SGBSTDN_PREV_CODE,
    sgbstdn.SGBSTDN_TERM_CODE_PREV,
    sgbstdn.SGBSTDN_CAST_CODE,
    sgbstdn.sgbstdn_term_code_cast,
    sgbstdn.sgbstdn_data_origin,
    sgbstdn.sgbstdn_user_id
    from sgbstdn@aimsmgr201040;

    commit;
  end;

  procedure p_import_sfrstcr as
  begin
    execute immediate('truncate table sfrstcr');

    insert into sfrstcr_ext(
    sfrstcr_ext.SFRSTCR_TERM_CODE,
    sfrstcr_ext.SFRSTCR_PIDM,
    sfrstcr_ext.SFRSTCR_CRN,
    sfrstcr_ext.SFRSTCR_CLASS_SORT_KEY,
    sfrstcr_ext.SFRSTCR_REG_SEQ,
    sfrstcr_ext.SFRSTCR_PTRM_CODE,
    sfrstcr_ext.SFRSTCR_RSTS_CODE,
    sfrstcr_ext.SFRSTCR_RSTS_DATE,
    sfrstcr_ext.SFRSTCR_ERROR_FLAG,
    sfrstcr_ext.SFRSTCR_MESSAGE,
    sfrstcr_ext.SFRSTCR_BILL_HR,
    sfrstcr_ext.SFRSTCR_WAIV_HR,
    sfrstcr_ext.SFRSTCR_CREDIT_HR,
    sfrstcr_ext.SFRSTCR_BILL_HR_HOLD,
    sfrstcr_ext.SFRSTCR_CREDIT_HR_HOLD,
    sfrstcr_ext.SFRSTCR_GMOD_CODE,
    sfrstcr_ext.SFRSTCR_GRDE_CODE,
    sfrstcr_ext.SFRSTCR_GRDE_CODE_MID,
    sfrstcr_ext.SFRSTCR_GRDE_DATE,
    sfrstcr_ext.SFRSTCR_DUPL_OVER,
    sfrstcr_ext.SFRSTCR_LINK_OVER,
    sfrstcr_ext.SFRSTCR_CORQ_OVER,
    sfrstcr_ext.SFRSTCR_PREQ_OVER,
    sfrstcr_ext.SFRSTCR_TIME_OVER,
    sfrstcr_ext.SFRSTCR_CAPC_OVER,
    sfrstcr_ext.SFRSTCR_LEVL_OVER,
    sfrstcr_ext.SFRSTCR_COLL_OVER,
    sfrstcr_ext.SFRSTCR_MAJR_OVER,
    sfrstcr_ext.SFRSTCR_CLAS_OVER,
    sfrstcr_ext.SFRSTCR_APPR_OVER,
    sfrstcr_ext.sfrstcr_appr_received_ind,
    sfrstcr_ext.SFRSTCR_ADD_DATE,
    sfrstcr_ext.SFRSTCR_ACTIVITY_DATE,
    sfrstcr_ext.SFRSTCR_LEVL_CODE,
    sfrstcr_ext.SFRSTCR_CAMP_CODE,
    sfrstcr_ext.SFRSTCR_RESERVED_KEY,
    sfrstcr_ext.SFRSTCR_ATTEND_HR,
    sfrstcr_ext.SFRSTCR_REPT_OVER,
    sfrstcr_ext.SFRSTCR_RPTH_OVER,
    sfrstcr_ext.SFRSTCR_TEST_OVER,
    sfrstcr_ext.SFRSTCR_CAMP_OVER,
    sfrstcr_ext.SFRSTCR_USER,
    sfrstcr_ext.SFRSTCR_DEGC_OVER,
    sfrstcr_ext.SFRSTCR_PROG_OVER,
    sfrstcr_ext.SFRSTCR_LAST_ATTEND,
    sfrstcr_ext.SFRSTCR_GCMT_CODE,
    sfrstcr_ext.sfrstcr_assess_activity_date,
    sfrstcr_ext.SFRSTCR_DATA_ORIGIN,
    sfrstcr_ext.SFRSTCR_DEPT_OVER,
    sfrstcr_ext.SFRSTCR_ATTS_OVER,
    sfrstcr_ext.SFRSTCR_CHRT_OVER,
    sfrstcr_ext.SFRSTCR_RMSG_CDE,
    sfrstcr_ext.SFRSTCR_WL_PRIORITY,
    sfrstcr_ext.SFRSTCR_WL_PRIORITY_ORIG,
    sfrstcr_ext.sfrstcr_grde_code_incmp_final,
    sfrstcr_ext.sfrstcr_incomplete_ext_date
    )
    select
    sfrstcr.SFRSTCR_TERM_CODE,
    sfrstcr.SFRSTCR_PIDM,
    sfrstcr.SFRSTCR_CRN,
    sfrstcr.SFRSTCR_CLASS_SORT_KEY,
    sfrstcr.SFRSTCR_REG_SEQ,
    sfrstcr.SFRSTCR_PTRM_CODE,
    sfrstcr.SFRSTCR_RSTS_CODE,
    sfrstcr.SFRSTCR_RSTS_DATE,
    sfrstcr.SFRSTCR_ERROR_FLAG,
    sfrstcr.SFRSTCR_MESSAGE,
    sfrstcr.SFRSTCR_BILL_HR,
    sfrstcr.SFRSTCR_WAIV_HR,
    sfrstcr.SFRSTCR_CREDIT_HR,
    sfrstcr.SFRSTCR_BILL_HR_HOLD,
    sfrstcr.SFRSTCR_CREDIT_HR_HOLD,
    sfrstcr.SFRSTCR_GMOD_CODE,
    sfrstcr.SFRSTCR_GRDE_CODE,
    sfrstcr.SFRSTCR_GRDE_CODE_MID,
    sfrstcr.SFRSTCR_GRDE_DATE,
    sfrstcr.SFRSTCR_DUPL_OVER,
    sfrstcr.SFRSTCR_LINK_OVER,
    sfrstcr.SFRSTCR_CORQ_OVER,
    sfrstcr.SFRSTCR_PREQ_OVER,
    sfrstcr.SFRSTCR_TIME_OVER,
    sfrstcr.SFRSTCR_CAPC_OVER,
    sfrstcr.SFRSTCR_LEVL_OVER,
    sfrstcr.SFRSTCR_COLL_OVER,
    sfrstcr.SFRSTCR_MAJR_OVER,
    sfrstcr.SFRSTCR_CLAS_OVER,
    sfrstcr.SFRSTCR_APPR_OVER,
    sfrstcr.sfrstcr_appr_received_ind,
    sfrstcr.SFRSTCR_ADD_DATE,
    sfrstcr.SFRSTCR_ACTIVITY_DATE,
    sfrstcr.SFRSTCR_LEVL_CODE,
    sfrstcr.SFRSTCR_CAMP_CODE,
    sfrstcr.SFRSTCR_RESERVED_KEY,
    sfrstcr.SFRSTCR_ATTEND_HR,
    sfrstcr.SFRSTCR_REPT_OVER,
    sfrstcr.SFRSTCR_RPTH_OVER,
    sfrstcr.SFRSTCR_TEST_OVER,
    sfrstcr.SFRSTCR_CAMP_OVER,
    sfrstcr.SFRSTCR_USER,
    sfrstcr.SFRSTCR_DEGC_OVER,
    sfrstcr.SFRSTCR_PROG_OVER,
    sfrstcr.SFRSTCR_LAST_ATTEND,
    sfrstcr.SFRSTCR_GCMT_CODE,
    sfrstcr.sfrstcr_assess_activity_date,
    sfrstcr.SFRSTCR_DATA_ORIGIN,
    sfrstcr.SFRSTCR_DEPT_OVER,
    sfrstcr.SFRSTCR_ATTS_OVER,
    sfrstcr.SFRSTCR_CHRT_OVER,
    sfrstcr.SFRSTCR_RMSG_CDE,
    sfrstcr.SFRSTCR_WL_PRIORITY,
    sfrstcr.SFRSTCR_WL_PRIORITY_ORIG,
    sfrstcr.sfrstcr_grde_code_incmp_final,
    sfrstcr.sfrstcr_incomplete_ext_date
    from sfrstcr@aimsmgr201040;

    commit;
  end;

  procedure p_import_scbcrse as
  begin
    execute immediate('truncate table scbcrse_ext');

    insert into scbcrse_ext(
    scbcrse_ext.SCBCRSE_SUBJ_CODE,
    scbcrse_ext.SCBCRSE_CRSE_NUMB,
    scbcrse_ext.SCBCRSE_EFF_TERM,
    scbcrse_ext.SCBCRSE_COLL_CODE,
    scbcrse_ext.SCBCRSE_DIVS_CODE,
    scbcrse_ext.SCBCRSE_DEPT_CODE,
    scbcrse_ext.scbcrse_csta_code,
    scbcrse_ext.SCBCRSE_TITLE,
    scbcrse_ext.SCBCRSE_CIPC_CODE,
    scbcrse_ext.SCBCRSE_CREDIT_HR_IND,
    scbcrse_ext.SCBCRSE_CREDIT_HR_LOW,
    scbcrse_ext.SCBCRSE_CREDIT_HR_HIGH,
    scbcrse_ext.SCBCRSE_LEC_HR_IND,
    scbcrse_ext.SCBCRSE_LEC_HR_LOW,
    scbcrse_ext.SCBCRSE_LEC_HR_HIGH,
    scbcrse_ext.SCBCRSE_LAB_HR_IND,
    scbcrse_ext.SCBCRSE_LAB_HR_LOW,
    scbcrse_ext.SCBCRSE_LAB_HR_HIGH,
    scbcrse_ext.SCBCRSE_OTH_HR_IND,
    scbcrse_ext.SCBCRSE_OTH_HR_LOW,
    scbcrse_ext.SCBCRSE_OTH_HR_HIGH,
    scbcrse_ext.SCBCRSE_BILL_HR_IND,
    scbcrse_ext.SCBCRSE_BILL_HR_LOW,
    scbcrse_ext.SCBCRSE_BILL_HR_HIGH,
    scbcrse_ext.SCBCRSE_APRV_CODE,
    scbcrse_ext.SCBCRSE_REPEAT_LIMIT,
    scbcrse_ext.SCBCRSE_PWAV_CODE,
    scbcrse_ext.SCBCRSE_TUIW_IND,
    scbcrse_ext.SCBCRSE_ADD_FEES_IND,
    scbcrse_ext.SCBCRSE_ACTIVITY_DATE,
    scbcrse_ext.SCBCRSE_CONT_HR_LOW,
    scbcrse_ext.SCBCRSE_CONT_HR_IND,
    scbcrse_ext.SCBCRSE_CONT_HR_HIGH,
    scbcrse_ext.SCBCRSE_CEU_IND,
    scbcrse_ext.SCBCRSE_REPS_CODE,
    scbcrse_ext.SCBCRSE_MAX_RPT_UNITS,
    scbcrse_ext.SCBCRSE_CAPP_PREREQ_TEST_IND,
    scbcrse_ext.SCBCRSE_DUNT_CODE,
    scbcrse_ext.SCBCRSE_NUMBER_OF_UNITS,
    scbcrse_ext.scbcrse_data_origin,
    scbcrse_ext.scbcrse_user_id)
    select
    scbcrse.SCBCRSE_SUBJ_CODE,
    scbcrse.SCBCRSE_CRSE_NUMB,
    scbcrse.SCBCRSE_EFF_TERM,
    scbcrse.SCBCRSE_COLL_CODE,
    scbcrse.SCBCRSE_DIVS_CODE,
    scbcrse.SCBCRSE_DEPT_CODE,
    scbcrse.scbcrse_csta_code,
    scbcrse.SCBCRSE_TITLE,
    scbcrse.SCBCRSE_CIPC_CODE,
    scbcrse.SCBCRSE_CREDIT_HR_IND,
    scbcrse.SCBCRSE_CREDIT_HR_LOW,
    scbcrse.SCBCRSE_CREDIT_HR_HIGH,
    scbcrse.SCBCRSE_LEC_HR_IND,
    scbcrse.SCBCRSE_LEC_HR_LOW,
    scbcrse.SCBCRSE_LEC_HR_HIGH,
    scbcrse.SCBCRSE_LAB_HR_IND,
    scbcrse.SCBCRSE_LAB_HR_LOW,
    scbcrse.SCBCRSE_LAB_HR_HIGH,
    scbcrse.SCBCRSE_OTH_HR_IND,
    scbcrse.SCBCRSE_OTH_HR_LOW,
    scbcrse.SCBCRSE_OTH_HR_HIGH,
    scbcrse.SCBCRSE_BILL_HR_IND,
    scbcrse.SCBCRSE_BILL_HR_LOW,
    scbcrse.SCBCRSE_BILL_HR_HIGH,
    scbcrse.SCBCRSE_APRV_CODE,
    scbcrse.SCBCRSE_REPEAT_LIMIT,
    scbcrse.SCBCRSE_PWAV_CODE,
    scbcrse.SCBCRSE_TUIW_IND,
    scbcrse.SCBCRSE_ADD_FEES_IND,
    scbcrse.SCBCRSE_ACTIVITY_DATE,
    scbcrse.SCBCRSE_CONT_HR_LOW,
    scbcrse.SCBCRSE_CONT_HR_IND,
    scbcrse.SCBCRSE_CONT_HR_HIGH,
    scbcrse.SCBCRSE_CEU_IND,
    scbcrse.SCBCRSE_REPS_CODE,
    scbcrse.SCBCRSE_MAX_RPT_UNITS,
    scbcrse.SCBCRSE_CAPP_PREREQ_TEST_IND,
    scbcrse.SCBCRSE_DUNT_CODE,
    scbcrse.SCBCRSE_NUMBER_OF_UNITS,
    scbcrse.scbcrse_data_origin,
    scbcrse.scbcrse_user_id
    from scbcrse@aimsmgr201140;

    commit;
  end;


  procedure p_import_gerattd as
  begin
    execute immediate('truncate table gerattd_ext');

    insert into gerattd_ext(
    gerattd_ext.gerattd_evnt_crn,
    gerattd_ext.GERATTD_FUNC_CODE,
    gerattd_ext.gerattd_pidm,
    gerattd_ext.gerattd_activity_date,
    gerattd_ext.gerattd_rsvp_code,
    gerattd_ext.gerattd_rsvp_date,
    gerattd_ext.gerattd_fees_code,
    gerattd_ext.gerattd_fee_date,
    gerattd_ext.gerattd_atyp_code,
    gerattd_ext.gerattd_name_tag_name,
    gerattd_ext.gerattd_place_card_name,
    gerattd_ext.gerattd_ticket_cnt,
    gerattd_ext.gerattd_menu_code,
    gerattd_ext.gerattd_attendance_ind,
    gerattd_ext.gerattd_involve_ind,
--    gerattd_ext.gerattd_comment,
    gerattd_ext.gerattd_fees_desc
    )
    select
    gerattd.gerattd_evnt_crn,
    gerattd.GERATTD_FUNC_CODE,
    gerattd.gerattd_pidm,
    gerattd.gerattd_activity_date,
    gerattd.gerattd_rsvp_code,
    gerattd.gerattd_rsvp_date,
    gerattd.gerattd_fees_code,
    gerattd.gerattd_fee_date,
    gerattd.gerattd_atyp_code,
    gerattd.gerattd_name_tag_name,
    gerattd.gerattd_place_card_name,
    gerattd.gerattd_ticket_cnt,
    gerattd.gerattd_menu_code,
    gerattd.gerattd_attendance_ind,
    gerattd.gerattd_involve_ind,
--    gerattd.gerattd_comment,
    gerattd.gerattd_fees_desc
    from gerattd@aimsmgr201040;

    commit;
  end;

  procedure p_import_rrrareq as
  begin
    execute immediate('truncate table rrrareq_ext');

    insert into rrrareq_ext(
    rrrareq_ext.RRRAREQ_AIDY_CODE,
    rrrareq_ext.RRRAREQ_PIDM,
    rrrareq_ext.RRRAREQ_TREQ_CODE,
    rrrareq_ext.RRRAREQ_ACTIVITY_DATE,
    rrrareq_ext.RRRAREQ_TREQ_DESC,
    rrrareq_ext.RRRAREQ_SAT_IND,
    rrrareq_ext.RRRAREQ_STAT_DATE,
    rrrareq_ext.RRRAREQ_EST_DATE,
    rrrareq_ext.RRRAREQ_TRST_CODE,
    rrrareq_ext.RRRAREQ_PCKG_IND,
    rrrareq_ext.RRRAREQ_DISB_IND,
    rrrareq_ext.RRRAREQ_FUND_CODE,
    rrrareq_ext.RRRAREQ_SYS_IND,
    rrrareq_ext.RRRAREQ_SBGI_CODE,
    rrrareq_ext.RRRAREQ_MEMO_IND,
    rrrareq_ext.RRRAREQ_USER_ID,
    rrrareq_ext.RRRAREQ_PERK_MPN_EXP_DATE,
    rrrareq_ext.RRRAREQ_SATISFIED_DATE,
    rrrareq_ext.RRRAREQ_MPN_FIRST_DISB_DATE,
    rrrareq_ext.RRRAREQ_MPN_SIGNED_DATE,
    rrrareq_ext.RRRAREQ_DATA_ORIGIN,
    rrrareq_ext.RRRAREQ_TRK_LTR_IND,
    rrrareq_ext.rrrareq_info_access_ind,
--    rrrareq_ext.rrrareq_period,
    rrrareq_ext.rrrareq_sbgi_type_ind,
    rrrareq_ext.rrrareq_term_code)
    select
    rrrareq.RRRAREQ_AIDY_CODE,
    rrrareq.RRRAREQ_PIDM,
    rrrareq.RRRAREQ_TREQ_CODE,
    rrrareq.RRRAREQ_ACTIVITY_DATE,
    rrrareq.RRRAREQ_TREQ_DESC,
    rrrareq.RRRAREQ_SAT_IND,
    rrrareq.RRRAREQ_STAT_DATE,
    rrrareq.RRRAREQ_EST_DATE,
    rrrareq.RRRAREQ_TRST_CODE,
    rrrareq.RRRAREQ_PCKG_IND,
    rrrareq.RRRAREQ_DISB_IND,
    rrrareq.RRRAREQ_FUND_CODE,
    rrrareq.RRRAREQ_SYS_IND,
    rrrareq.RRRAREQ_SBGI_CODE,
    rrrareq.RRRAREQ_MEMO_IND,
    rrrareq.RRRAREQ_USER_ID,
    rrrareq.RRRAREQ_PERK_MPN_EXP_DATE,
    rrrareq.RRRAREQ_SATISFIED_DATE,
    rrrareq.RRRAREQ_MPN_FIRST_DISB_DATE,
    rrrareq.RRRAREQ_MPN_SIGNED_DATE,
    rrrareq.RRRAREQ_DATA_ORIGIN,
    rrrareq.RRRAREQ_TRK_LTR_IND,
    rrrareq.rrrareq_info_access_ind,
--    rrrareq.rrrareq_period,
    rrrareq.rrrareq_sbgi_type_ind,
    rrrareq.rrrareq_term_code
    from rrrareq@aimsmgr201040;

    commit;
  end;

  procedure p_import_rpratrm as
  begin
    execute immediate('truncate table rpratrm_ext');

    insert into rpratrm_ext(
    rpratrm_ext.RPRATRM_AIDY_CODE,
    rpratrm_ext.RPRATRM_PIDM,
    rpratrm_ext.rpratrm_fund_code,
--    rpratrm_ext.RPRATRM_PERIOD,
    rpratrm_ext.RPRATRM_OFFER_AMT,
    rpratrm_ext.RPRATRM_OFFER_DATE,
    rpratrm_ext.RPRATRM_ACTIVITY_DATE,
    rpratrm_ext.RPRATRM_DISB_FINAL_IND,
    rpratrm_ext.RPRATRM_DIST_PCT,
    rpratrm_ext.RPRATRM_MEMO_EXP_DATE,
    rpratrm_ext.RPRATRM_ORIG_OFFER_AMT,
    rpratrm_ext.RPRATRM_ORIG_OFFER_DATE,
    rpratrm_ext.RPRATRM_ACCEPT_AMT,
    rpratrm_ext.RPRATRM_ACCEPT_DATE,
    rpratrm_ext.RPRATRM_DECLINE_AMT,
    rpratrm_ext.RPRATRM_DECLINE_DATE,
    rpratrm_ext.RPRATRM_CANCEL_AMT,
    rpratrm_ext.RPRATRM_CANCEL_DATE,
    rpratrm_ext.RPRATRM_AUTHORIZE_AMT,
    rpratrm_ext.RPRATRM_AUTHORIZE_DATE,
    rpratrm_ext.rpratrm_memo_amt,
    rpratrm_ext.RPRATRM_MEMO_DATE,
    rpratrm_ext.RPRATRM_PAID_AMT,
    rpratrm_ext.RPRATRM_PAID_DATE,
    rpratrm_ext.RPRATRM_PCKG_LOAD_IND,
    rpratrm_ext.RPRATRM_NSLDS_OVRD_IND,
    rpratrm_ext.RPRATRM_PELL_AWRD_LOAD_OPT,
    rpratrm_ext.RPRATRM_LOCK_IND,
    rpratrm_ext.RPRATRM_USER_ID,
    rpratrm_ext.RPRATRM_CIP_OVERRIDE_CODE,
    rpratrm_ext.RPRATRM_DATA_ORIGIN,
    rpratrm_ext.RPRATRM_MAJR_OVERRIDE_CODE,
    rpratrm_ext.rpratrm_override_disb_rule,
    rpratrm_ext.rpratrm_term_code)
    select
    rpratrm.RPRATRM_AIDY_CODE,
    rpratrm.RPRATRM_PIDM,
    rpratrm.rpratrm_fund_code,
--    rpratrm.RPRATRM_PERIOD,
    rpratrm.RPRATRM_OFFER_AMT,
    rpratrm.RPRATRM_OFFER_DATE,
    rpratrm.RPRATRM_ACTIVITY_DATE,
    rpratrm.RPRATRM_DISB_FINAL_IND,
    rpratrm.RPRATRM_DIST_PCT,
    rpratrm.RPRATRM_MEMO_EXP_DATE,
    rpratrm.RPRATRM_ORIG_OFFER_AMT,
    rpratrm.RPRATRM_ORIG_OFFER_DATE,
    rpratrm.RPRATRM_ACCEPT_AMT,
    rpratrm.RPRATRM_ACCEPT_DATE,
    rpratrm.RPRATRM_DECLINE_AMT,
    rpratrm.RPRATRM_DECLINE_DATE,
    rpratrm.RPRATRM_CANCEL_AMT,
    rpratrm.RPRATRM_CANCEL_DATE,
    rpratrm.RPRATRM_AUTHORIZE_AMT,
    rpratrm.RPRATRM_AUTHORIZE_DATE,
    rpratrm.rpratrm_memo_amt,
    rpratrm.RPRATRM_MEMO_DATE,
    rpratrm.RPRATRM_PAID_AMT,
    rpratrm.RPRATRM_PAID_DATE,
    rpratrm.RPRATRM_PCKG_LOAD_IND,
    rpratrm.RPRATRM_NSLDS_OVRD_IND,
    rpratrm.RPRATRM_PELL_AWRD_LOAD_OPT,
    rpratrm.RPRATRM_LOCK_IND,
    rpratrm.RPRATRM_USER_ID,
    rpratrm.RPRATRM_CIP_OVERRIDE_CODE,
    rpratrm.RPRATRM_DATA_ORIGIN,
    rpratrm.RPRATRM_MAJR_OVERRIDE_CODE,
    rpratrm.rpratrm_override_disb_rule,
    rpratrm.rpratrm_term_code    
    from rpratrm@aimsmgr201040;

    commit;
  end;

  procedure p_import_tbraccd as
  begin
    execute immediate('truncate table tbraccd_ext');  

    insert into tbraccd_ext(
    tbraccd_ext.tbraccd_pidm,
    tbraccd_ext.TBRACCD_TRAN_NUMBER,
    tbraccd_ext.TBRACCD_TERM_CODE,
    tbraccd_ext.TBRACCD_DETAIL_CODE,
    tbraccd_ext.TBRACCD_USER,
    tbraccd_ext.TBRACCD_ENTRY_DATE,
    tbraccd_ext.TBRACCD_AMOUNT,
    tbraccd_ext.TBRACCD_BALANCE,
    tbraccd_ext.TBRACCD_EFFECTIVE_DATE,
    tbraccd_ext.TBRACCD_BILL_DATE,
    tbraccd_ext.TBRACCD_DUE_DATE,
    tbraccd_ext.TBRACCD_DESC,
    tbraccd_ext.TBRACCD_RECEIPT_NUMBER,
    tbraccd_ext.TBRACCD_TRAN_NUMBER_PAID,
    tbraccd_ext.TBRACCD_CROSSREF_PIDM,
    tbraccd_ext.TBRACCD_CROSSREF_NUMBER,
    tbraccd_ext.TBRACCD_CROSSREF_DETAIL_CODE,
    tbraccd_ext.TBRACCD_SRCE_CODE,
    tbraccd_ext.TBRACCD_ACCT_FEED_IND,
    tbraccd_ext.TBRACCD_ACTIVITY_DATE,
    tbraccd_ext.TBRACCD_SESSION_NUMBER,
    tbraccd_ext.TBRACCD_CSHR_END_DATE,
    tbraccd_ext.TBRACCD_CRN,
    tbraccd_ext.TBRACCD_CROSSREF_SRCE_CODE,
    tbraccd_ext.TBRACCD_LOC_MDT,
    tbraccd_ext.TBRACCD_LOC_MDT_SEQ,
    tbraccd_ext.TBRACCD_RATE,
    tbraccd_ext.TBRACCD_UNITS,
    tbraccd_ext.TBRACCD_DOCUMENT_NUMBER,
    tbraccd_ext.TBRACCD_TRANS_DATE,
    tbraccd_ext.TBRACCD_PAYMENT_ID,
    tbraccd_ext.TBRACCD_INVOICE_NUMBER,
    tbraccd_ext.TBRACCD_STATEMENT_DATE,
    tbraccd_ext.TBRACCD_INV_NUMBER_PAID,
    tbraccd_ext.TBRACCD_CURR_CODE,
    tbraccd_ext.TBRACCD_EXCHANGE_DIFF,
    tbraccd_ext.TBRACCD_FOREIGN_AMOUNT,
    tbraccd_ext.TBRACCD_LATE_DCAT_CODE,
    tbraccd_ext.TBRACCD_FEED_DATE,
    tbraccd_ext.TBRACCD_FEED_DOC_CODE,
    tbraccd_ext.TBRACCD_ATYP_CODE,
    tbraccd_ext.TBRACCD_ATYP_SEQNO,
    tbraccd_ext.TBRACCD_CARD_TYPE_VR,
    tbraccd_ext.TBRACCD_CARD_EXP_DATE_VR,
    tbraccd_ext.TBRACCD_CARD_AUTH_NUMBER_VR,
    tbraccd_ext.TBRACCD_CROSSREF_DCAT_CODE,
    tbraccd_ext.TBRACCD_ORIG_CHG_IND,
    tbraccd_ext.TBRACCD_CCRD_CODE,
    tbraccd_ext.TBRACCD_MERCHANT_ID,
    tbraccd_ext.TBRACCD_TAX_REPT_YEAR,
    tbraccd_ext.TBRACCD_TAX_REPT_BOX,
    tbraccd_ext.TBRACCD_TAX_AMOUNT,
    tbraccd_ext.TBRACCD_TAX_FUTURE_IND,
    tbraccd_ext.tbraccd_data_origin,
    tbraccd_ext.tbraccd_create_source --,
--    tbraccd_ext.tbraccd_cpdt_ind --,
--    tbraccd_ext.tbraccd_aidy_code-- ,
--    tbraccd_ext.tbraccd_stsp_key_sequence
    )
    select
    tbraccd.tbraccd_pidm,
    tbraccd.TBRACCD_TRAN_NUMBER,
    tbraccd.TBRACCD_TERM_CODE,
    tbraccd.TBRACCD_DETAIL_CODE,
    tbraccd.TBRACCD_USER,
    tbraccd.TBRACCD_ENTRY_DATE,
    tbraccd.TBRACCD_AMOUNT,
    tbraccd.TBRACCD_BALANCE,
    tbraccd.TBRACCD_EFFECTIVE_DATE,
    tbraccd.TBRACCD_BILL_DATE,
    tbraccd.TBRACCD_DUE_DATE,
    tbraccd.TBRACCD_DESC,
    tbraccd.TBRACCD_RECEIPT_NUMBER,
    tbraccd.TBRACCD_TRAN_NUMBER_PAID,
    tbraccd.TBRACCD_CROSSREF_PIDM,
    tbraccd.TBRACCD_CROSSREF_NUMBER,
    tbraccd.TBRACCD_CROSSREF_DETAIL_CODE,
    tbraccd.TBRACCD_SRCE_CODE,
    tbraccd.TBRACCD_ACCT_FEED_IND,
    tbraccd.TBRACCD_ACTIVITY_DATE,
    tbraccd.TBRACCD_SESSION_NUMBER,
    tbraccd.TBRACCD_CSHR_END_DATE,
    tbraccd.TBRACCD_CRN,
    tbraccd.TBRACCD_CROSSREF_SRCE_CODE,
    tbraccd.TBRACCD_LOC_MDT,
    tbraccd.TBRACCD_LOC_MDT_SEQ,
    tbraccd.TBRACCD_RATE,
    tbraccd.TBRACCD_UNITS,
    tbraccd.TBRACCD_DOCUMENT_NUMBER,
    tbraccd.TBRACCD_TRANS_DATE,
    tbraccd.TBRACCD_PAYMENT_ID,
    tbraccd.TBRACCD_INVOICE_NUMBER,
    tbraccd.TBRACCD_STATEMENT_DATE,
    tbraccd.TBRACCD_INV_NUMBER_PAID,
    tbraccd.TBRACCD_CURR_CODE,
    tbraccd.TBRACCD_EXCHANGE_DIFF,
    tbraccd.TBRACCD_FOREIGN_AMOUNT,
    tbraccd.TBRACCD_LATE_DCAT_CODE,
    tbraccd.TBRACCD_FEED_DATE,
    tbraccd.TBRACCD_FEED_DOC_CODE,
    tbraccd.TBRACCD_ATYP_CODE,
    tbraccd.TBRACCD_ATYP_SEQNO,
    tbraccd.TBRACCD_CARD_TYPE_VR,
    tbraccd.TBRACCD_CARD_EXP_DATE_VR,
    tbraccd.TBRACCD_CARD_AUTH_NUMBER_VR,
    tbraccd.TBRACCD_CROSSREF_DCAT_CODE,
    tbraccd.TBRACCD_ORIG_CHG_IND,
    tbraccd.TBRACCD_CCRD_CODE,
    tbraccd.TBRACCD_MERCHANT_ID,
    tbraccd.TBRACCD_TAX_REPT_YEAR,
    tbraccd.TBRACCD_TAX_REPT_BOX,
    tbraccd.TBRACCD_TAX_AMOUNT,
    tbraccd.TBRACCD_TAX_FUTURE_IND,
    tbraccd.tbraccd_data_origin,
    tbraccd.tbraccd_create_source --,
--    tbraccd.tbraccd_cpdt_ind --,
--    tbraccd.tbraccd_aidy_code --,
--    tbraccd.tbraccd_stsp_key_sequence
    from tbraccd@aimsmgr201040;

    commit;
  end;

  procedure p_import_tbrmemo as
  begin
    execute immediate('truncate table tbrmemo_ext');

    insert into tbrmemo_ext(
    tbrmemo_ext.tbrmemo_pidm,
    tbrmemo_ext.tbrmemo_tran_number,
    tbrmemo_ext.tbrmemo_term_code,
    tbrmemo_ext.tbrmemo_detail_code,
    tbrmemo_ext.tbrmemo_amount,
    tbrmemo_ext.tbrmemo_user,
    tbrmemo_ext.tbrmemo_entry_date,
    tbrmemo_ext.tbrmemo_billing_ind,
    tbrmemo_ext.tbrmemo_desc,
    tbrmemo_ext.tbrmemo_release_date,
    tbrmemo_ext.tbrmemo_expiration_date,
    tbrmemo_ext.tbrmemo_effective_date,
    tbrmemo_ext.tbrmemo_activity_date,
    tbrmemo_ext.tbrmemo_srce_code,
    tbrmemo_ext.tbrmemo_crossref_pidm,
    tbrmemo_ext.tbrmemo_crossref_number,
    tbrmemo_ext.tbrmemo_crossref_detail_code,
    tbrmemo_ext.tbrmemo_crossref_srce_code,
    tbrmemo_ext.tbrmemo_atyp_code,
    tbrmemo_ext.tbrmemo_atyp_seqno,
    tbrmemo_ext.tbrmemo_data_origin,
    tbrmemo_ext.tbrmemo_create_user,
    tbrmemo_ext.tbrmemo_crossref_dcat_code --,
--    tbrmemo_ext.tbrmemo_aidy_code
    )
    select
    tbrmemo.tbrmemo_pidm,
    tbrmemo.tbrmemo_tran_number,
    tbrmemo.tbrmemo_term_code,
    tbrmemo.tbrmemo_detail_code,
    tbrmemo.tbrmemo_amount,
    tbrmemo.tbrmemo_user,
    tbrmemo.tbrmemo_entry_date,
    tbrmemo.tbrmemo_billing_ind,
    tbrmemo.tbrmemo_desc,
    tbrmemo.tbrmemo_release_date,
    tbrmemo.tbrmemo_expiration_date,
    tbrmemo.tbrmemo_effective_date,
    tbrmemo.tbrmemo_activity_date,
    tbrmemo.tbrmemo_srce_code,
    tbrmemo.tbrmemo_crossref_pidm,
    tbrmemo.tbrmemo_crossref_number,
    tbrmemo.tbrmemo_crossref_detail_code,
    tbrmemo.tbrmemo_crossref_srce_code,
    tbrmemo.tbrmemo_atyp_code,
    tbrmemo.tbrmemo_atyp_seqno,
    tbrmemo.tbrmemo_data_origin,
    tbrmemo.tbrmemo_create_user,
    tbrmemo.tbrmemo_crossref_dcat_code --,
--    tbrmemo.tbrmemo_aidy_code
    from tbrmemo@aimsmgr201040;

    commit;
  end;

  procedure p_import_tbbdetc as
  begin
    execute immediate('truncate table tbbdetc_ext');

    insert into tbbdetc_ext(
    tbbdetc_ext.tbbdetc_detail_code,
    tbbdetc_ext.TBBDETC_DESC,
    tbbdetc_ext.tbbdetc_type_ind,
    tbbdetc_ext.tbbdetc_priority,
    tbbdetc_ext.tbbdetc_like_term_ind,
    tbbdetc_ext.tbbdetc_dcat_code,
    tbbdetc_ext.tbbdetc_amount,
    tbbdetc_ext.tbbdetc_term_code,
    tbbdetc_ext.tbbdetc_effective_date,
    tbbdetc_ext.tbbdetc_refundable_ind,
    tbbdetc_ext.tbbdetc_receipt_ind,
    tbbdetc_ext.tbbdetc_refund_ind,
    tbbdetc_ext.TBBDETC_ACTIVITY_DATE,
    tbbdetc_ext.tbbdetc_payt_code,
    tbbdetc_ext.tbbdetc_prebill_print_ind,
    tbbdetc_ext.tbbdetc_gl_nos_enterable,
    tbbdetc_ext.tbbdetc_taxt_code,
    tbbdetc_ext.tbbdetc_tbdc_ind,
    tbbdetc_ext.tbbdetc_detail_code_ind,
    tbbdetc_ext.tbbdetc_detc_active_ind,
    tbbdetc_ext.tbbdetc_dird_ind,
    tbbdetc_ext.TBBDETC_TIV_IND,
    tbbdetc_ext.tbbdetc_inst_chg_ind,
    tbbdetc_ext.tbbdetc_like_aidy_ind,
    tbbdetc_ext.tbbdetc_payhist_ind --,
--    tbbdetc_ext.tbbdetc_abdc_ind
    )
    select
    tbbdetc.tbbdetc_detail_code,
    tbbdetc.TBBDETC_DESC,
    tbbdetc.tbbdetc_type_ind,
    tbbdetc.tbbdetc_priority,
    tbbdetc.tbbdetc_like_term_ind,
    tbbdetc.tbbdetc_dcat_code,
    tbbdetc.tbbdetc_amount,
    tbbdetc.tbbdetc_term_code,
    tbbdetc.tbbdetc_effective_date,
    tbbdetc.tbbdetc_refundable_ind,
    tbbdetc.tbbdetc_receipt_ind,
    tbbdetc.tbbdetc_refund_ind,
    tbbdetc.TBBDETC_ACTIVITY_DATE,
    tbbdetc.tbbdetc_payt_code,
    tbbdetc.tbbdetc_prebill_print_ind,
    tbbdetc.tbbdetc_gl_nos_enterable,
    tbbdetc.tbbdetc_taxt_code,
    tbbdetc.tbbdetc_tbdc_ind,
    tbbdetc.tbbdetc_detail_code_ind,
    tbbdetc.tbbdetc_detc_active_ind,
    tbbdetc.tbbdetc_dird_ind,
    tbbdetc.TBBDETC_TIV_IND,
    tbbdetc.tbbdetc_inst_chg_ind,
    tbbdetc.tbbdetc_like_aidy_ind,
    tbbdetc.tbbdetc_payhist_ind --,
--    tbbdetc.tbbdetc_abdc_ind
    from tbbdetc@aimsmgr201040;

    commit;
  end;


  procedure p_import_stvgmod as
  begin
    execute immediate('truncate table stvgmod_ext');

    insert into stvgmod_ext(
    stvgmod_ext.stvgmod_code,
    stvgmod_ext.stvgmod_desc,
    stvgmod_ext.stvgmod_activity_date,
    stvgmod_ext.stvgmod_vr_msg_no)
    select 
    stvgmod.stvgmod_code,
    stvgmod.stvgmod_desc,
    stvgmod.stvgmod_activity_date,
    stvgmod.stvgmod_vr_msg_no
    from stvgmod@aimsmgr201040;

    commit;
  end;

  procedure p_import_rcrapp1 as
  begin
    execute immediate('truncate table rcrapp1_ext');

    insert into rcrapp1_ext(
    rcrapp1_ext.rcrapp1_aidy_code, 
    rcrapp1_ext.rcrapp1_pidm, 
    rcrapp1_ext.rcrapp1_infc_code, 
    rcrapp1_ext.rcrapp1_seq_no, 
    rcrapp1_ext.rcrapp1_curr_rec_ind, 
    rcrapp1_ext.rcrapp1_creator_id, 
    rcrapp1_ext.rcrapp1_user_id, 
    rcrapp1_ext.rcrapp1_create_date, 
    rcrapp1_ext.rcrapp1_activity_date, 
    rcrapp1_ext.rcrapp1_coll_cde, 
    rcrapp1_ext.rcrapp1_acyr_code, 
    rcrapp1_ext.rcrapp1_last_name, 
    rcrapp1_ext.rcrapp1_first_name, 
    rcrapp1_ext.rcrapp1_mi, 
    rcrapp1_ext.rcrapp1_title, 
    rcrapp1_ext.rcrapp1_addr, 
    rcrapp1_ext.rcrapp1_city, 
    rcrapp1_ext.rcrapp1_stat_code, 
    rcrapp1_ext.rcrapp1_zip, 
    rcrapp1_ext.rcrapp1_ssn_prefix, 
    rcrapp1_ext.rcrapp1_ssn, 
    rcrapp1_ext.rcrapp1_birth_date, 
    rcrapp1_ext.rcrapp1_phone_area, 
    rcrapp1_ext.rcrapp1_phone_no, 
    rcrapp1_ext.rcrapp1_stat_code_res, 
    rcrapp1_ext.rcrapp1_start_res_mth_yr, 
    rcrapp1_ext.rcrapp1_rcpt_date, 
    rcrapp1_ext.rcrapp1_rev_rcpt_date, 
    rcrapp1_ext.rcrapp1_orig_comp_date, 
    rcrapp1_ext.rcrapp1_rev_comp_date, 
    rcrapp1_ext.rcrapp1_citz_ind, 
    rcrapp1_ext.rcrapp1_alien_reg_no, 
    rcrapp1_ext.rcrapp1_mrtl_status, 
    rcrapp1_ext.rcrapp1_hs_cde, 
    rcrapp1_ext.rcrapp1_exp_enroll_status, 
    rcrapp1_ext.rcrapp1_yr_in_coll, 
    rcrapp1_ext.rcrapp1_crse_of_study, 
    rcrapp1_ext.rcrapp1_exp_grad_mth_yr, 
    rcrapp1_ext.rcrapp1_degree_by_july, 
    rcrapp1_ext.rcrapp1_more_than_3_prev_coll, 
    rcrapp1_ext.rcrapp1_no_sch, 
    rcrapp1_ext.rcrapp1_prev_coll1, 
    rcrapp1_ext.rcrapp1_prev_coll2, 
    rcrapp1_ext.rcrapp1_prev_coll3, 
    rcrapp1_ext.rcrapp1_driver_lic_no, 
    rcrapp1_ext.rcrapp1_stat_code_lic, 
    rcrapp1_ext.rcrapp1_disl_wrk, 
    rcrapp1_ext.rcrapp1_disp_hme, 
    rcrapp1_ext.rcrapp1_fam_memb, 
    rcrapp1_ext.rcrapp1_no_in_coll, 
    rcrapp1_ext.rcrapp1_cash_amt, 
    rcrapp1_ext.rcrapp1_hme_val, 
    rcrapp1_ext.rcrapp1_hme_dbt, 
    rcrapp1_ext.rcrapp1_re_inv_val, 
    rcrapp1_ext.rcrapp1_re_inv_dbt, 
    rcrapp1_ext.rcrapp1_bus_farm_val, 
    rcrapp1_ext.rcrapp1_bus_farm_dbt, 
    rcrapp1_ext.rcrapp1_incl_farm, 
    rcrapp1_ext.rcrapp1_born_before_1_1_xx, 
    rcrapp1_ext.rcrapp1_us_vet, 
    rcrapp1_ext.rcrapp1_ward_of_court, 
    rcrapp1_ext.rcrapp1_has_legal_depend, 
    rcrapp1_ext.rcrapp1_tax_form_ind, 
    rcrapp1_ext.rcrapp1_no_exempt, 
    rcrapp1_ext.rcrapp1_us_inc, 
    rcrapp1_ext.rcrapp1_us_inc_tax_pd, 
    rcrapp1_ext.rcrapp1_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_sps_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_ss_bene, 
    rcrapp1_ext.rcrapp1_afdc, 
    rcrapp1_ext.rcrapp1_child_supp, 
    rcrapp1_ext.rcrapp1_oth_untax_inc, 
    rcrapp1_ext.rcrapp1_med_den_exp, 
    rcrapp1_ext.rcrapp1_tuit_pd, 
    rcrapp1_ext.rcrapp1_no_child_tuit, 
    rcrapp1_ext.rcrapp1_exp_tax_pd, 
    rcrapp1_ext.rcrapp1_exp_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_exp_sps_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_exp_oth_tax_inc, 
    rcrapp1_ext.rcrapp1_exp_untax_inc, 
    rcrapp1_ext.rcrapp1_exp_smr_inc, 
    rcrapp1_ext.rcrapp1_exp_acyr_inc, 
    rcrapp1_ext.rcrapp1_exp_sps_smr_inc, 
    rcrapp1_ext.rcrapp1_exp_sps_acyr_inc, 
    rcrapp1_ext.rcrapp1_exp_smr_oth_tax_inc, 
    rcrapp1_ext.rcrapp1_exp_acyr_oth_tax_inc, 
    rcrapp1_ext.rcrapp1_exp_smr_untax_inc, 
    rcrapp1_ext.rcrapp1_exp_acyr_untax_inc, 
    rcrapp1_ext.rcrapp1_gi_deab_amt, 
    rcrapp1_ext.rcrapp1_gi_deab_mth, 
    rcrapp1_ext.rcrapp1_veap_ben_amt, 
    rcrapp1_ext.rcrapp1_veap_ben_mth, 
    rcrapp1_ext.rcrapp1_oth_va_amt, 
    rcrapp1_ext.rcrapp1_oth_va_mth, 
    rcrapp1_ext.rcrapp1_clm_as_tax_exempt_2yr, 
    rcrapp1_ext.rcrapp1_clm_as_tax_exempt_1yr, 
    rcrapp1_ext.rcrapp1_clm_as_tax_exempt, 
    rcrapp1_ext.rcrapp1_when_rcvd_aid, 
    rcrapp1_ext.rcrapp1_1985_rsrc_4000_8788, 
    rcrapp1_ext.rcrapp1_1986_rsrc_4000_8788, 
    rcrapp1_ext.rcrapp1_1986_rsrc_4000_8889, 
    rcrapp1_ext.rcrapp1_1987_rsrc_4000_8889, 
    rcrapp1_ext.rcrapp1_1987_rsrc_4000_8990, 
    rcrapp1_ext.rcrapp1_1988_rsrc_4000_8990, 
    rcrapp1_ext.rcrapp1_1988_rsrc_4000_9091, 
    rcrapp1_ext.rcrapp1_1989_rsrc_4000_9091, 
    rcrapp1_ext.rcrapp1_no_aid_rsrc_4000_2yr, 
    rcrapp1_ext.rcrapp1_no_aid_rsrc_4000_1yr, 
    rcrapp1_ext.rcrapp1_line2_relship, 
    rcrapp1_ext.rcrapp1_line2_attend_coll, 
    rcrapp1_ext.rcrapp1_line3_relship, 
    rcrapp1_ext.rcrapp1_line3_attend_coll, 
    rcrapp1_ext.rcrapp1_line4_relship, 
    rcrapp1_ext.rcrapp1_line4_attend_coll, 
    rcrapp1_ext.rcrapp1_line5_relship, 
    rcrapp1_ext.rcrapp1_line5_attend_coll, 
    rcrapp1_ext.rcrapp1_line6_relship, 
    rcrapp1_ext.rcrapp1_line6_attend_coll, 
    rcrapp1_ext.rcrapp1_line7_relship, 
    rcrapp1_ext.rcrapp1_line7_attend_coll, 
    rcrapp1_ext.rcrapp1_line8_relship, 
    rcrapp1_ext.rcrapp1_line8_attend_coll, 
    rcrapp1_ext.rcrapp1_par_mrtl_status, 
    rcrapp1_ext.rcrapp1_par_older_age, 
    rcrapp1_ext.rcrapp1_par_stat_code_res, 
    rcrapp1_ext.rcrapp1_par_disl_wrk, 
    rcrapp1_ext.rcrapp1_par_disp_hme, 
    rcrapp1_ext.rcrapp1_par_fam_memb, 
    rcrapp1_ext.rcrapp1_par_no_in_coll, 
    rcrapp1_ext.rcrapp1_par_cash_amt, 
    rcrapp1_ext.rcrapp1_par_hme_val, 
    rcrapp1_ext.rcrapp1_par_hme_dbt, 
    rcrapp1_ext.rcrapp1_par_re_inv_val, 
    rcrapp1_ext.rcrapp1_par_re_inv_dbt, 
    rcrapp1_ext.rcrapp1_par_curr_inv_val, 
    rcrapp1_ext.rcrapp1_par_bus_farm_val, 
    rcrapp1_ext.rcrapp1_par_bus_farm_dbt, 
    rcrapp1_ext.rcrapp1_par_incl_farm, 
    rcrapp1_ext.rcrapp1_par_hme_yr_purch, 
    rcrapp1_ext.rcrapp1_par_hme_purch_amt, 
    rcrapp1_ext.rcrapp1_par_div_sep_rem_ind, 
    rcrapp1_ext.rcrapp1_par_tax_form_ind, 
    rcrapp1_ext.rcrapp1_par_no_exempt, 
    rcrapp1_ext.rcrapp1_par_us_inc, 
    rcrapp1_ext.rcrapp1_par_wages, 
    rcrapp1_ext.rcrapp1_par_int_inc, 
    rcrapp1_ext.rcrapp1_par_div_inc, 
    rcrapp1_ext.rcrapp1_par_bus_farm_inc, 
    rcrapp1_ext.rcrapp1_par_oth_tax_inc, 
    rcrapp1_ext.rcrapp1_par_adj_to_inc, 
    rcrapp1_ext.rcrapp1_par_us_inc_tax_pd, 
    rcrapp1_ext.rcrapp1_fath_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_moth_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_par_ss_bene, 
    rcrapp1_ext.rcrapp1_par_afdc, 
    rcrapp1_ext.rcrapp1_par_child_supp, 
    rcrapp1_ext.rcrapp1_par_oth_untax_inc, 
    rcrapp1_ext.rcrapp1_par_med_den_exp, 
    rcrapp1_ext.rcrapp1_par_tuit_pd, 
    rcrapp1_ext.rcrapp1_par_no_child_tuit, 
    rcrapp1_ext.rcrapp1_exp_par_tax_pd, 
    rcrapp1_ext.rcrapp1_exp_par_oth_tax_inc, 
    rcrapp1_ext.rcrapp1_exp_par_untax_inc, 
    rcrapp1_ext.rcrapp1_exp_fath_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_exp_moth_inc_fr_wrk, 
    rcrapp1_ext.rcrapp1_fed_coll_choice_1, 
    rcrapp1_ext.rcrapp1_fed_coll_choice_2, 
    rcrapp1_ext.rcrapp1_fed_coll_choice_3, 
    rcrapp1_ext.rcrapp1_coll_choice_no, 
    rcrapp1_ext.rcrapp1_in_default, 
    rcrapp1_ext.rcrapp1_owes_refund, 
    rcrapp1_ext.rcrapp1_consider_for_staf, 
    rcrapp1_ext.rcrapp1_bal_on_all_staf, 
    rcrapp1_ext.rcrapp1_recent_staf_bal, 
    rcrapp1_ext.rcrapp1_recent_staf_int_rate, 
    rcrapp1_ext.rcrapp1_recent_staf_fr_mth_yr, 
    rcrapp1_ext.rcrapp1_recent_staf_to_mth_yr, 
    rcrapp1_ext.rcrapp1_recent_staf_class, 
    rcrapp1_ext.rcrapp1_rqst_fa_pref, 
    rcrapp1_ext.rcrapp1_rqst_fa_fr_mth_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_to_mth_yr, 
    rcrapp1_ext.rcrapp1_rqst_staf_amt, 
    rcrapp1_ext.rcrapp1_rqst_staf_fa_fr_mth_yr, 
    rcrapp1_ext.rcrapp1_rqst_staf_fa_to_mth_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_summer_this_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_fall_this_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_winter_next_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_spring_next_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_summer_next_yr, 
    rcrapp1_ext.rcrapp1_rqst_fa_other, 
    rcrapp1_ext.rcrapp1_classification, 
    rcrapp1_ext.rcrapp1_exp_wrk_hrs, 
    rcrapp1_ext.rcrapp1_inst_hous_cde, 
    rcrapp1_ext.rcrapp1_coll_cde_1, 
    rcrapp1_ext.rcrapp1_hous_cde_1, 
    rcrapp1_ext.rcrapp1_coll_cde_2, 
    rcrapp1_ext.rcrapp1_hous_cde_2, 
    rcrapp1_ext.rcrapp1_coll_cde_3, 
    rcrapp1_ext.rcrapp1_hous_cde_3, 
    rcrapp1_ext.rcrapp1_coll_cde_4, 
    rcrapp1_ext.rcrapp1_hous_cde_4, 
    rcrapp1_ext.rcrapp1_coll_cde_5, 
    rcrapp1_ext.rcrapp1_hous_cde_5, 
    rcrapp1_ext.rcrapp1_coll_cde_6, 
    rcrapp1_ext.rcrapp1_hous_cde_6, 
    rcrapp1_ext.rcrapp1_coll_cde_7, 
    rcrapp1_ext.rcrapp1_hous_cde_7, 
    rcrapp1_ext.rcrapp1_coll_cde_8, 
    rcrapp1_ext.rcrapp1_hous_cde_8, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_1, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_2, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_3, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_4, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_5, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_6, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_7, 
    rcrapp1_ext.rcrapp1_addl_coll_cde_8, 
    rcrapp1_ext.rcrapp1_extra_request, 
    rcrapp1_ext.rcrapp1_rel_to_state, 
    rcrapp1_ext.rcrapp1_rel_to_us_ed, 
    rcrapp1_ext.rcrapp1_rel_us_ed_to_state, 
    rcrapp1_ext.rcrapp1_rel_us_ed_to_coll, 
    rcrapp1_ext.rcrapp1_bus_farm_supp_filed, 
    rcrapp1_ext.rcrapp1_signed, 
    rcrapp1_ext.rcrapp1_signed_sps, 
    rcrapp1_ext.rcrapp1_signed_fath, 
    rcrapp1_ext.rcrapp1_signed_moth, 
    rcrapp1_ext.rcrapp1_signed_par, 
    rcrapp1_ext.rcrapp1_signed_mth_day, 
    rcrapp1_ext.rcrapp1_signed_yr, 
    rcrapp1_ext.rcrapp1_depend_age_0_5, 
    rcrapp1_ext.rcrapp1_depend_age_6_12, 
    rcrapp1_ext.rcrapp1_depend_age_13_plus, 
    rcrapp1_ext.rcrapp1_cont_wrk_acyr, 
    rcrapp1_ext.rcrapp1_permit_draft_reg, 
    rcrapp1_ext.rcrapp1_pell_rel_msg, 
    rcrapp1_ext.rcrapp1_st_agency_rel_msg, 
    rcrapp1_ext.rcrapp1_verification_msg, 
    rcrapp1_ext.rcrapp1_verification_prty, 
    rcrapp1_ext.rcrapp1_ins, 
    rcrapp1_ext.rcrapp1_selective_service, 
    rcrapp1_ext.rcrapp1_spec_cond_ind, 
    rcrapp1_ext.rcrapp1_title_iv_default, 
    rcrapp1_ext.rcrapp1_inas_option, 
    rcrapp1_ext.rcrapp1_ede_source, 
    rcrapp1_ext.rcrapp1_ede_spec_cond_ind, 
    rcrapp1_ext.rcrapp1_used_trans_no, 
    rcrapp1_ext.rcrapp1_def_match_region, 
    rcrapp1_ext.rcrapp1_prev_coll4, 
    rcrapp1_ext.rcrapp1_1989_rsrc_4000_9192, 
    rcrapp1_ext.rcrapp1_1990_rsrc_4000_9192, 
    rcrapp1_ext.rcrapp1_in_def_owe_ref, 
    rcrapp1_ext.rcrapp1_par_ss_bene_stu_only, 
    rcrapp1_ext.rcrapp1_completion_date, 
    rcrapp1_ext.rcrapp1_par_remarriage_date, 
    rcrapp1_ext.rcrapp1_lowest_inc_filer, 
    rcrapp1_ext.rcrapp1_means_test_ben, 
    rcrapp1_ext.rcrapp1_par_recd_tanf, 
    rcrapp1_ext.rcrapp1_par_recd_ssi, 
    rcrapp1_ext.rcrapp1_par_means_test_ben, 
    rcrapp1_ext.rcrapp1_par_hous_stat, 
    rcrapp1_ext.rcrapp1_active_duty, 
    rcrapp1_ext.rcrapp1_par_food_stamps, 
    rcrapp1_ext.rcrapp1_par_school_lunch, 
    rcrapp1_ext.rcrapp1_par_recd_wic, 
    rcrapp1_ext.rcrapp1_recd_ssi, 
    rcrapp1_ext.rcrapp1_food_stamps, 
    rcrapp1_ext.rcrapp1_school_lunch, 
    rcrapp1_ext.rcrapp1_recd_tanf, 
    rcrapp1_ext.rcrapp1_recd_wic, 
    rcrapp1_ext.rcrapp1_surname_prefix, 
    rcrapp1_ext.rcrapp1_ctry_code_phone, 
    rcrapp1_ext.rcrapp1_addr2, 
    rcrapp1_ext.rcrapp1_addr3, 
    rcrapp1_ext.rcrapp1_addr4, 
    rcrapp1_ext.rcrapp1_house_number, 
    rcrapp1_ext.rcrapp1_recv_vet_benefits, 
    rcrapp1_ext.rcrapp1_type_vet_benefits, 
    rcrapp1_ext.rcrapp1_ira_distributions, 
    rcrapp1_ext.rcrapp1_untax_pensions, 
    rcrapp1_ext.rcrapp1_vets_non_ed_ben, 
    rcrapp1_ext.rcrapp1_oth_non_report_money, 
    rcrapp1_ext.rcrapp1_emancipated_minor, 
    rcrapp1_ext.rcrapp1_legal_guardian, 
    rcrapp1_ext.rcrapp1_unaccomp_youth_school, 
    rcrapp1_ext.rcrapp1_unaccomp_youth_hud, 
    rcrapp1_ext.rcrapp1_at_risk_homeless, 
    rcrapp1_ext.rcrapp1_par_ira_distributions, 
    rcrapp1_ext.rcrapp1_par_untax_pensions, 
    rcrapp1_ext.rcrapp1_par_vets_non_ed_ben, 
    rcrapp1_ext.rcrapp1_par_cntry_code_res, 
    rcrapp1_ext.rcrapp1_mail_addr, 
    rcrapp1_ext.rcrapp1_mail_city, 
    rcrapp1_ext.rcrapp1_mail_stat_code, 
    rcrapp1_ext.rcrapp1_mail_postal_cde, 
    rcrapp1_ext.rcrapp1_intl_phone_no, 
    rcrapp1_ext.rcrapp1_mail_country_cde 
    )
    select
    rcrapp1.rcrapp1_aidy_code, 
    rcrapp1.rcrapp1_pidm, 
    rcrapp1.rcrapp1_infc_code, 
    rcrapp1.rcrapp1_seq_no, 
    rcrapp1.rcrapp1_curr_rec_ind, 
    rcrapp1.rcrapp1_creator_id, 
    rcrapp1.rcrapp1_user_id, 
    rcrapp1.rcrapp1_create_date, 
    rcrapp1.rcrapp1_activity_date, 
    rcrapp1.rcrapp1_coll_cde, 
    rcrapp1.rcrapp1_acyr_code, 
    rcrapp1.rcrapp1_last_name, 
    rcrapp1.rcrapp1_first_name, 
    rcrapp1.rcrapp1_mi, 
    rcrapp1.rcrapp1_title, 
    rcrapp1.rcrapp1_addr, 
    rcrapp1.rcrapp1_city, 
    rcrapp1.rcrapp1_stat_code, 
    rcrapp1.rcrapp1_zip, 
    rcrapp1.rcrapp1_ssn_prefix, 
    rcrapp1.rcrapp1_ssn, 
    rcrapp1.rcrapp1_birth_date, 
    rcrapp1.rcrapp1_phone_area, 
    rcrapp1.rcrapp1_phone_no, 
    rcrapp1.rcrapp1_stat_code_res, 
    rcrapp1.rcrapp1_start_res_mth_yr, 
    rcrapp1.rcrapp1_rcpt_date, 
    rcrapp1.rcrapp1_rev_rcpt_date, 
    rcrapp1.rcrapp1_orig_comp_date, 
    rcrapp1.rcrapp1_rev_comp_date, 
    rcrapp1.rcrapp1_citz_ind, 
    rcrapp1.rcrapp1_alien_reg_no, 
    rcrapp1.rcrapp1_mrtl_status, 
    rcrapp1.rcrapp1_hs_cde, 
    rcrapp1.rcrapp1_exp_enroll_status, 
    rcrapp1.rcrapp1_yr_in_coll, 
    rcrapp1.rcrapp1_crse_of_study, 
    rcrapp1.rcrapp1_exp_grad_mth_yr, 
    rcrapp1.rcrapp1_degree_by_july, 
    rcrapp1.rcrapp1_more_than_3_prev_coll, 
    rcrapp1.rcrapp1_no_sch, 
    rcrapp1.rcrapp1_prev_coll1, 
    rcrapp1.rcrapp1_prev_coll2, 
    rcrapp1.rcrapp1_prev_coll3, 
    rcrapp1.rcrapp1_driver_lic_no, 
    rcrapp1.rcrapp1_stat_code_lic, 
    rcrapp1.rcrapp1_disl_wrk, 
    rcrapp1.rcrapp1_disp_hme, 
    rcrapp1.rcrapp1_fam_memb, 
    rcrapp1.rcrapp1_no_in_coll, 
    rcrapp1.rcrapp1_cash_amt, 
    rcrapp1.rcrapp1_hme_val, 
    rcrapp1.rcrapp1_hme_dbt, 
    rcrapp1.rcrapp1_re_inv_val, 
    rcrapp1.rcrapp1_re_inv_dbt, 
    rcrapp1.rcrapp1_bus_farm_val, 
    rcrapp1.rcrapp1_bus_farm_dbt, 
    rcrapp1.rcrapp1_incl_farm, 
    rcrapp1.rcrapp1_born_before_1_1_xx, 
    rcrapp1.rcrapp1_us_vet, 
    rcrapp1.rcrapp1_ward_of_court, 
    rcrapp1.rcrapp1_has_legal_depend, 
    rcrapp1.rcrapp1_tax_form_ind, 
    rcrapp1.rcrapp1_no_exempt, 
    rcrapp1.rcrapp1_us_inc, 
    rcrapp1.rcrapp1_us_inc_tax_pd, 
    rcrapp1.rcrapp1_inc_fr_wrk, 
    rcrapp1.rcrapp1_sps_inc_fr_wrk, 
    rcrapp1.rcrapp1_ss_bene, 
    rcrapp1.rcrapp1_afdc, 
    rcrapp1.rcrapp1_child_supp, 
    rcrapp1.rcrapp1_oth_untax_inc, 
    rcrapp1.rcrapp1_med_den_exp, 
    rcrapp1.rcrapp1_tuit_pd, 
    rcrapp1.rcrapp1_no_child_tuit, 
    rcrapp1.rcrapp1_exp_tax_pd, 
    rcrapp1.rcrapp1_exp_inc_fr_wrk, 
    rcrapp1.rcrapp1_exp_sps_inc_fr_wrk, 
    rcrapp1.rcrapp1_exp_oth_tax_inc, 
    rcrapp1.rcrapp1_exp_untax_inc, 
    rcrapp1.rcrapp1_exp_smr_inc, 
    rcrapp1.rcrapp1_exp_acyr_inc, 
    rcrapp1.rcrapp1_exp_sps_smr_inc, 
    rcrapp1.rcrapp1_exp_sps_acyr_inc, 
    rcrapp1.rcrapp1_exp_smr_oth_tax_inc, 
    rcrapp1.rcrapp1_exp_acyr_oth_tax_inc, 
    rcrapp1.rcrapp1_exp_smr_untax_inc, 
    rcrapp1.rcrapp1_exp_acyr_untax_inc, 
    rcrapp1.rcrapp1_gi_deab_amt, 
    rcrapp1.rcrapp1_gi_deab_mth, 
    rcrapp1.rcrapp1_veap_ben_amt, 
    rcrapp1.rcrapp1_veap_ben_mth, 
    rcrapp1.rcrapp1_oth_va_amt, 
    rcrapp1.rcrapp1_oth_va_mth, 
    rcrapp1.rcrapp1_clm_as_tax_exempt_2yr, 
    rcrapp1.rcrapp1_clm_as_tax_exempt_1yr, 
    rcrapp1.rcrapp1_clm_as_tax_exempt, 
    rcrapp1.rcrapp1_when_rcvd_aid, 
    rcrapp1.rcrapp1_1985_rsrc_4000_8788, 
    rcrapp1.rcrapp1_1986_rsrc_4000_8788, 
    rcrapp1.rcrapp1_1986_rsrc_4000_8889, 
    rcrapp1.rcrapp1_1987_rsrc_4000_8889, 
    rcrapp1.rcrapp1_1987_rsrc_4000_8990, 
    rcrapp1.rcrapp1_1988_rsrc_4000_8990, 
    rcrapp1.rcrapp1_1988_rsrc_4000_9091, 
    rcrapp1.rcrapp1_1989_rsrc_4000_9091, 
    rcrapp1.rcrapp1_no_aid_rsrc_4000_2yr, 
    rcrapp1.rcrapp1_no_aid_rsrc_4000_1yr, 
    rcrapp1.rcrapp1_line2_relship, 
    rcrapp1.rcrapp1_line2_attend_coll, 
    rcrapp1.rcrapp1_line3_relship, 
    rcrapp1.rcrapp1_line3_attend_coll, 
    rcrapp1.rcrapp1_line4_relship, 
    rcrapp1.rcrapp1_line4_attend_coll, 
    rcrapp1.rcrapp1_line5_relship, 
    rcrapp1.rcrapp1_line5_attend_coll, 
    rcrapp1.rcrapp1_line6_relship, 
    rcrapp1.rcrapp1_line6_attend_coll, 
    rcrapp1.rcrapp1_line7_relship, 
    rcrapp1.rcrapp1_line7_attend_coll, 
    rcrapp1.rcrapp1_line8_relship, 
    rcrapp1.rcrapp1_line8_attend_coll, 
    rcrapp1.rcrapp1_par_mrtl_status, 
    rcrapp1.rcrapp1_par_older_age, 
    rcrapp1.rcrapp1_par_stat_code_res, 
    rcrapp1.rcrapp1_par_disl_wrk, 
    rcrapp1.rcrapp1_par_disp_hme, 
    rcrapp1.rcrapp1_par_fam_memb, 
    rcrapp1.rcrapp1_par_no_in_coll, 
    rcrapp1.rcrapp1_par_cash_amt, 
    rcrapp1.rcrapp1_par_hme_val, 
    rcrapp1.rcrapp1_par_hme_dbt, 
    rcrapp1.rcrapp1_par_re_inv_val, 
    rcrapp1.rcrapp1_par_re_inv_dbt, 
    rcrapp1.rcrapp1_par_curr_inv_val, 
    rcrapp1.rcrapp1_par_bus_farm_val, 
    rcrapp1.rcrapp1_par_bus_farm_dbt, 
    rcrapp1.rcrapp1_par_incl_farm, 
    rcrapp1.rcrapp1_par_hme_yr_purch, 
    rcrapp1.rcrapp1_par_hme_purch_amt, 
    rcrapp1.rcrapp1_par_div_sep_rem_ind, 
    rcrapp1.rcrapp1_par_tax_form_ind, 
    rcrapp1.rcrapp1_par_no_exempt, 
    rcrapp1.rcrapp1_par_us_inc, 
    rcrapp1.rcrapp1_par_wages, 
    rcrapp1.rcrapp1_par_int_inc, 
    rcrapp1.rcrapp1_par_div_inc, 
    rcrapp1.rcrapp1_par_bus_farm_inc, 
    rcrapp1.rcrapp1_par_oth_tax_inc, 
    rcrapp1.rcrapp1_par_adj_to_inc, 
    rcrapp1.rcrapp1_par_us_inc_tax_pd, 
    rcrapp1.rcrapp1_fath_inc_fr_wrk, 
    rcrapp1.rcrapp1_moth_inc_fr_wrk, 
    rcrapp1.rcrapp1_par_ss_bene, 
    rcrapp1.rcrapp1_par_afdc, 
    rcrapp1.rcrapp1_par_child_supp, 
    rcrapp1.rcrapp1_par_oth_untax_inc, 
    rcrapp1.rcrapp1_par_med_den_exp, 
    rcrapp1.rcrapp1_par_tuit_pd, 
    rcrapp1.rcrapp1_par_no_child_tuit, 
    rcrapp1.rcrapp1_exp_par_tax_pd, 
    rcrapp1.rcrapp1_exp_par_oth_tax_inc, 
    rcrapp1.rcrapp1_exp_par_untax_inc, 
    rcrapp1.rcrapp1_exp_fath_inc_fr_wrk, 
    rcrapp1.rcrapp1_exp_moth_inc_fr_wrk, 
    rcrapp1.rcrapp1_fed_coll_choice_1, 
    rcrapp1.rcrapp1_fed_coll_choice_2, 
    rcrapp1.rcrapp1_fed_coll_choice_3, 
    rcrapp1.rcrapp1_coll_choice_no, 
    rcrapp1.rcrapp1_in_default, 
    rcrapp1.rcrapp1_owes_refund, 
    rcrapp1.rcrapp1_consider_for_staf, 
    rcrapp1.rcrapp1_bal_on_all_staf, 
    rcrapp1.rcrapp1_recent_staf_bal, 
    rcrapp1.rcrapp1_recent_staf_int_rate, 
    rcrapp1.rcrapp1_recent_staf_fr_mth_yr, 
    rcrapp1.rcrapp1_recent_staf_to_mth_yr, 
    rcrapp1.rcrapp1_recent_staf_class, 
    rcrapp1.rcrapp1_rqst_fa_pref, 
    rcrapp1.rcrapp1_rqst_fa_fr_mth_yr, 
    rcrapp1.rcrapp1_rqst_fa_to_mth_yr, 
    rcrapp1.rcrapp1_rqst_staf_amt, 
    rcrapp1.rcrapp1_rqst_staf_fa_fr_mth_yr, 
    rcrapp1.rcrapp1_rqst_staf_fa_to_mth_yr, 
    rcrapp1.rcrapp1_rqst_fa_summer_this_yr, 
    rcrapp1.rcrapp1_rqst_fa_fall_this_yr, 
    rcrapp1.rcrapp1_rqst_fa_winter_next_yr, 
    rcrapp1.rcrapp1_rqst_fa_spring_next_yr, 
    rcrapp1.rcrapp1_rqst_fa_summer_next_yr, 
    rcrapp1.rcrapp1_rqst_fa_other, 
    rcrapp1.rcrapp1_classification, 
    rcrapp1.rcrapp1_exp_wrk_hrs, 
    rcrapp1.rcrapp1_inst_hous_cde, 
    rcrapp1.rcrapp1_coll_cde_1, 
    rcrapp1.rcrapp1_hous_cde_1, 
    rcrapp1.rcrapp1_coll_cde_2, 
    rcrapp1.rcrapp1_hous_cde_2, 
    rcrapp1.rcrapp1_coll_cde_3, 
    rcrapp1.rcrapp1_hous_cde_3, 
    rcrapp1.rcrapp1_coll_cde_4, 
    rcrapp1.rcrapp1_hous_cde_4, 
    rcrapp1.rcrapp1_coll_cde_5, 
    rcrapp1.rcrapp1_hous_cde_5, 
    rcrapp1.rcrapp1_coll_cde_6, 
    rcrapp1.rcrapp1_hous_cde_6, 
    rcrapp1.rcrapp1_coll_cde_7, 
    rcrapp1.rcrapp1_hous_cde_7, 
    rcrapp1.rcrapp1_coll_cde_8, 
    rcrapp1.rcrapp1_hous_cde_8, 
    rcrapp1.rcrapp1_addl_coll_cde_1, 
    rcrapp1.rcrapp1_addl_coll_cde_2, 
    rcrapp1.rcrapp1_addl_coll_cde_3, 
    rcrapp1.rcrapp1_addl_coll_cde_4, 
    rcrapp1.rcrapp1_addl_coll_cde_5, 
    rcrapp1.rcrapp1_addl_coll_cde_6, 
    rcrapp1.rcrapp1_addl_coll_cde_7, 
    rcrapp1.rcrapp1_addl_coll_cde_8, 
    rcrapp1.rcrapp1_extra_request, 
    rcrapp1.rcrapp1_rel_to_state, 
    rcrapp1.rcrapp1_rel_to_us_ed, 
    rcrapp1.rcrapp1_rel_us_ed_to_state, 
    rcrapp1.rcrapp1_rel_us_ed_to_coll, 
    rcrapp1.rcrapp1_bus_farm_supp_filed, 
    rcrapp1.rcrapp1_signed, 
    rcrapp1.rcrapp1_signed_sps, 
    rcrapp1.rcrapp1_signed_fath, 
    rcrapp1.rcrapp1_signed_moth, 
    rcrapp1.rcrapp1_signed_par, 
    rcrapp1.rcrapp1_signed_mth_day, 
    rcrapp1.rcrapp1_signed_yr, 
    rcrapp1.rcrapp1_depend_age_0_5, 
    rcrapp1.rcrapp1_depend_age_6_12, 
    rcrapp1.rcrapp1_depend_age_13_plus, 
    rcrapp1.rcrapp1_cont_wrk_acyr, 
    rcrapp1.rcrapp1_permit_draft_reg, 
    rcrapp1.rcrapp1_pell_rel_msg, 
    rcrapp1.rcrapp1_st_agency_rel_msg, 
    rcrapp1.rcrapp1_verification_msg, 
    rcrapp1.rcrapp1_verification_prty, 
    rcrapp1.rcrapp1_ins, 
    rcrapp1.rcrapp1_selective_service, 
    rcrapp1.rcrapp1_spec_cond_ind, 
    rcrapp1.rcrapp1_title_iv_default, 
    rcrapp1.rcrapp1_inas_option, 
    rcrapp1.rcrapp1_ede_source, 
    rcrapp1.rcrapp1_ede_spec_cond_ind, 
    rcrapp1.rcrapp1_used_trans_no, 
    rcrapp1.rcrapp1_def_match_region, 
    rcrapp1.rcrapp1_prev_coll4, 
    rcrapp1.rcrapp1_1989_rsrc_4000_9192, 
    rcrapp1.rcrapp1_1990_rsrc_4000_9192, 
    rcrapp1.rcrapp1_in_def_owe_ref, 
    rcrapp1.rcrapp1_par_ss_bene_stu_only, 
    rcrapp1.rcrapp1_completion_date, 
    rcrapp1.rcrapp1_par_remarriage_date, 
    rcrapp1.rcrapp1_lowest_inc_filer, 
    rcrapp1.rcrapp1_means_test_ben, 
    rcrapp1.rcrapp1_par_recd_tanf, 
    rcrapp1.rcrapp1_par_recd_ssi, 
    rcrapp1.rcrapp1_par_means_test_ben, 
    rcrapp1.rcrapp1_par_hous_stat, 
    rcrapp1.rcrapp1_active_duty, 
    rcrapp1.rcrapp1_par_food_stamps, 
    rcrapp1.rcrapp1_par_school_lunch, 
    rcrapp1.rcrapp1_par_recd_wic, 
    rcrapp1.rcrapp1_recd_ssi, 
    rcrapp1.rcrapp1_food_stamps, 
    rcrapp1.rcrapp1_school_lunch, 
    rcrapp1.rcrapp1_recd_tanf, 
    rcrapp1.rcrapp1_recd_wic, 
    rcrapp1.rcrapp1_surname_prefix, 
    rcrapp1.rcrapp1_ctry_code_phone, 
    rcrapp1.rcrapp1_addr2, 
    rcrapp1.rcrapp1_addr3, 
    rcrapp1.rcrapp1_addr4, 
    rcrapp1.rcrapp1_house_number, 
    rcrapp1.rcrapp1_recv_vet_benefits, 
    rcrapp1.rcrapp1_type_vet_benefits, 
    rcrapp1.rcrapp1_ira_distributions, 
    rcrapp1.rcrapp1_untax_pensions, 
    rcrapp1.rcrapp1_vets_non_ed_ben, 
    rcrapp1.rcrapp1_oth_non_report_money, 
    rcrapp1.rcrapp1_emancipated_minor, 
    rcrapp1.rcrapp1_legal_guardian, 
    rcrapp1.rcrapp1_unaccomp_youth_school, 
    rcrapp1.rcrapp1_unaccomp_youth_hud, 
    rcrapp1.rcrapp1_at_risk_homeless, 
    rcrapp1.rcrapp1_par_ira_distributions, 
    rcrapp1.rcrapp1_par_untax_pensions, 
    rcrapp1.rcrapp1_par_vets_non_ed_ben, 
    rcrapp1.rcrapp1_par_cntry_code_res, 
    rcrapp1.rcrapp1_mail_addr, 
    rcrapp1.rcrapp1_mail_city, 
    rcrapp1.rcrapp1_mail_stat_code, 
    rcrapp1.rcrapp1_mail_postal_cde, 
    rcrapp1.rcrapp1_intl_phone_no, 
    rcrapp1.rcrapp1_mail_country_cde 
    from rcrapp1@aimsmgr201040 rcrapp1;

    commit;    
  end p_import_rcrapp1;

  procedure p_import_rcrapp3 as
  begin
    execute immediate('truncate table rcrapp3_ext');

    insert into rcrapp3_ext(
    rcrapp3_ext.rcrapp3_aidy_code, 
    rcrapp3_ext.rcrapp3_pidm, 
    rcrapp3_ext.rcrapp3_infc_code, 
    rcrapp3_ext.rcrapp3_seq_no, 
    rcrapp3_ext.rcrapp3_c_inst_1_unassess_va, 
    rcrapp3_ext.rcrapp3_c_inst_2_unassess_va, 
    rcrapp3_ext.rcrapp3_c_inst_3_unassess_va, 
    rcrapp3_ext.rcrapp3_c_1_alt_ctrb, 
    rcrapp3_ext.rcrapp3_c_2_alt_ctrb, 
    rcrapp3_ext.rcrapp3_pell_1_alt_ctrb, 
    rcrapp3_ext.rcrapp3_pell_2_alt_ctrb, 
    rcrapp3_ext.rcrapp3_c_par_1_alt_ctrb, 
    rcrapp3_ext.rcrapp3_c_par_2_alt_ctrb, 
    rcrapp3_ext.rcrapp3_pell_1_alt_pgi, 
    rcrapp3_ext.rcrapp3_pell_2_alt_pgi, 
    rcrapp3_ext.rcrapp3_par_1_alt_agi, 
    rcrapp3_ext.rcrapp3_par_2_alt_agi, 
    rcrapp3_ext.rcrapp3_par_alt_mrtl_status, 
    rcrapp3_ext.rcrapp3_par_alt_fam_size, 
    rcrapp3_ext.rcrapp3_par_alt_no_in_coll, 
    rcrapp3_ext.rcrapp3_alt_opt_loss_to_zero, 
    rcrapp3_ext.rcrapp3_alt_opt_hme_val, 
    rcrapp3_ext.rcrapp3_alt_opt_par_in_coll, 
    rcrapp3_ext.rcrapp3_msg_91, 
    rcrapp3_ext.rcrapp3_msg_91_budg_1, 
    rcrapp3_ext.rcrapp3_msg_91_budg_2, 
    rcrapp3_ext.rcrapp3_msg_91_budg_3, 
    rcrapp3_ext.rcrapp3_msg_73_tax, 
    rcrapp3_ext.rcrapp3_c_1_ctrb_adj_1_8_mth, 
    rcrapp3_ext.rcrapp3_c_2_ctrb_adj_1_8_mth, 
    rcrapp3_ext.rcrapp3_c_1_ctrb_adj_ovr_9_mth, 
    rcrapp3_ext.rcrapp3_c_2_ctrb_adj_ovr_9_mth, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_1, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_2, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_3, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_4, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_5, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_6, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_7, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_8, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_9, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_10, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_11, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_12, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_13, 
    rcrapp3_ext.rcrapp3_rej_reason_prty_14, 
    rcrapp3_ext.rcrapp3_rej_in_analysis_flag, 
    rcrapp3_ext.rcrapp3_msg_0, 
    rcrapp3_ext.rcrapp3_msg_1, 
    rcrapp3_ext.rcrapp3_msg_2, 
    rcrapp3_ext.rcrapp3_msg_3, 
    rcrapp3_ext.rcrapp3_msg_4, 
    rcrapp3_ext.rcrapp3_msg_5, 
    rcrapp3_ext.rcrapp3_msg_6, 
    rcrapp3_ext.rcrapp3_msg_11, 
    rcrapp3_ext.rcrapp3_msg_12, 
    rcrapp3_ext.rcrapp3_msg_13, 
    rcrapp3_ext.rcrapp3_msg_14, 
    rcrapp3_ext.rcrapp3_msg_15, 
    rcrapp3_ext.rcrapp3_msg_21, 
    rcrapp3_ext.rcrapp3_msg_22, 
    rcrapp3_ext.rcrapp3_msg_23, 
    rcrapp3_ext.rcrapp3_msg_24, 
    rcrapp3_ext.rcrapp3_msg_26, 
    rcrapp3_ext.rcrapp3_msg_27, 
    rcrapp3_ext.rcrapp3_msg_28, 
    rcrapp3_ext.rcrapp3_msg_29, 
    rcrapp3_ext.rcrapp3_msg_30, 
    rcrapp3_ext.rcrapp3_msg_32, 
    rcrapp3_ext.rcrapp3_msg_33, 
    rcrapp3_ext.rcrapp3_msg_34, 
    rcrapp3_ext.rcrapp3_msg_46, 
    rcrapp3_ext.rcrapp3_msg_47, 
    rcrapp3_ext.rcrapp3_msg_48, 
    rcrapp3_ext.rcrapp3_msg_49, 
    rcrapp3_ext.rcrapp3_msg_50, 
    rcrapp3_ext.rcrapp3_msg_61, 
    rcrapp3_ext.rcrapp3_msg_62, 
    rcrapp3_ext.rcrapp3_msg_63, 
    rcrapp3_ext.rcrapp3_msg_64, 
    rcrapp3_ext.rcrapp3_msg_65, 
    rcrapp3_ext.rcrapp3_msg_66, 
    rcrapp3_ext.rcrapp3_msg_67, 
    rcrapp3_ext.rcrapp3_msg_68, 
    rcrapp3_ext.rcrapp3_msg_71, 
    rcrapp3_ext.rcrapp3_msg_73, 
    rcrapp3_ext.rcrapp3_msg_74, 
    rcrapp3_ext.rcrapp3_msg_75, 
    rcrapp3_ext.rcrapp3_msg_76, 
    rcrapp3_ext.rcrapp3_msg_77, 
    rcrapp3_ext.rcrapp3_msg_78, 
    rcrapp3_ext.rcrapp3_msg_79, 
    rcrapp3_ext.rcrapp3_msg_80, 
    rcrapp3_ext.rcrapp3_msg_81, 
    rcrapp3_ext.rcrapp3_msg_82, 
    rcrapp3_ext.rcrapp3_msg_90, 
    rcrapp3_ext.rcrapp3_msg_92, 
    rcrapp3_ext.rcrapp3_msg_93, 
    rcrapp3_ext.rcrapp3_msg_94, 
    rcrapp3_ext.rcrapp3_msg_95, 
    rcrapp3_ext.rcrapp3_msg_96, 
    rcrapp3_ext.rcrapp3_msg_97, 
    rcrapp3_ext.rcrapp3_msg_98, 
    rcrapp3_ext.rcrapp3_msg_99, 
    rcrapp3_ext.rcrapp3_msg_11_par_fam_memb, 
    rcrapp3_ext.rcrapp3_msg_12_par_no_in_coll, 
    rcrapp3_ext.rcrapp3_msg_22_par_der_agi, 
    rcrapp3_ext.rcrapp3_msg_27_val, 
    rcrapp3_ext.rcrapp3_msg_47_par_hme_val, 
    rcrapp3_ext.rcrapp3_msg_48_par_hme_val_3, 
    rcrapp3_ext.rcrapp3_msg_48_par_hme_eqty_3, 
    rcrapp3_ext.rcrapp3_msg_49_par_hme_val, 
    rcrapp3_ext.rcrapp3_msg_49_par_hme_eqty, 
    rcrapp3_ext.rcrapp3_msg_63_fam_memb, 
    rcrapp3_ext.rcrapp3_msg_64_no_in_coll, 
    rcrapp3_ext.rcrapp3_msg_81_avail_inc, 
    rcrapp3_ext.rcrapp3_msg_90_inc, 
    rcrapp3_ext.rcrapp3_msg_90_sma, 
    rcrapp3_ext.rcrapp3_msg_90_diff, 
    rcrapp3_ext.rcrapp3_msg_92_budg_1, 
    rcrapp3_ext.rcrapp3_msg_92_budg_2, 
    rcrapp3_ext.rcrapp3_msg_92_budg_3, 
    rcrapp3_ext.rcrapp3_msg_93_calc, 
    rcrapp3_ext.rcrapp3_msg_93_resc, 
    rcrapp3_ext.rcrapp3_par_1_alt_fed_tax, 
    rcrapp3_ext.rcrapp3_par_2_alt_fed_tax, 
    rcrapp3_ext.rcrapp3_msg_0_fed_1_fam_ctrb, 
    rcrapp3_ext.rcrapp3_msg_0_fed_2_fam_ctrb, 
    rcrapp3_ext.rcrapp3_msg_0_fed_1_pgi, 
    rcrapp3_ext.rcrapp3_msg_0_fed_2_pgi, 
    rcrapp3_ext.rcrapp3_assumption_msg_1, 
    rcrapp3_ext.rcrapp3_assumption_msg_2, 
    rcrapp3_ext.rcrapp3_assumption_msg_3, 
    rcrapp3_ext.rcrapp3_assumption_msg_4, 
    rcrapp3_ext.rcrapp3_assumption_msg_5, 
    rcrapp3_ext.rcrapp3_assumption_msg_6, 
    rcrapp3_ext.rcrapp3_msg_51, 
    rcrapp3_ext.rcrapp3_msg_51_val, 
    rcrapp3_ext.rcrapp3_msg_83, 
    rcrapp3_ext.rcrapp3_msg_84, 
    rcrapp3_ext.rcrapp3_msg_85, 
    rcrapp3_ext.rcrapp3_msg_85_val, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_1, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_2, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_3, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_4, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_5, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_6, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_7, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_8, 
    rcrapp3_ext.rcrapp3_assumpt_ovrd_9, 
    rcrapp3_ext.rcrapp3_attend_same_coll, 
    rcrapp3_ext.rcrapp3_bus_dbt, 
    rcrapp3_ext.rcrapp3_bus_val, 
    rcrapp3_ext.rcrapp3_c_signed, 
    rcrapp3_ext.rcrapp3_c_signed_fath, 
    rcrapp3_ext.rcrapp3_c_signed_moth, 
    rcrapp3_ext.rcrapp3_c_signed_mth_day, 
    rcrapp3_ext.rcrapp3_c_signed_par, 
    rcrapp3_ext.rcrapp3_c_signed_sps, 
    rcrapp3_ext.rcrapp3_c_signed_yr, 
    rcrapp3_ext.rcrapp3_cash_chk, 
    rcrapp3_ext.rcrapp3_child_care, 
    rcrapp3_ext.rcrapp3_child_supp_paid, 
    rcrapp3_ext.rcrapp3_degree_type, 
    rcrapp3_ext.rcrapp3_efc_match, 
    rcrapp3_ext.rcrapp3_estimate_flags, 
    rcrapp3_ext.rcrapp3_faa_dep_override, 
    rcrapp3_ext.rcrapp3_faa_signature, 
    rcrapp3_ext.rcrapp3_faa_title_iv_code, 
    rcrapp3_ext.rcrapp3_faf_fafsa_data, 
    rcrapp3_ext.rcrapp3_faf_rcpt_date, 
    rcrapp3_ext.rcrapp3_farm_dbt, 
    rcrapp3_ext.rcrapp3_farm_val, 
    rcrapp3_ext.rcrapp3_father_age, 
    rcrapp3_ext.rcrapp3_father_hi_grade, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_4, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_5, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_6, 
    rcrapp3_ext.rcrapp3_ga_match_flag, 
    rcrapp3_ext.rcrapp3_ged_mth_yr, 
    rcrapp3_ext.rcrapp3_grad_or_prof, 
    rcrapp3_ext.rcrapp3_hs_grad_mth_yr, 
    rcrapp3_ext.rcrapp3_invest_dbt, 
    rcrapp3_ext.rcrapp3_invest_val, 
    rcrapp3_ext.rcrapp3_live_on_farm, 
    rcrapp3_ext.rcrapp3_married, 
    rcrapp3_ext.rcrapp3_mother_age, 
    rcrapp3_ext.rcrapp3_mother_hi_grade, 
    rcrapp3_ext.rcrapp3_msg_52, 
    rcrapp3_ext.rcrapp3_msg_91_sec_fm_efc, 
    rcrapp3_ext.rcrapp3_p_res_date, 
    rcrapp3_ext.rcrapp3_par_alt_hme_val, 
    rcrapp3_ext.rcrapp3_par_alt_hme_val_cap, 
    rcrapp3_ext.rcrapp3_par_bus_dbt, 
    rcrapp3_ext.rcrapp3_par_bus_val, 
    rcrapp3_ext.rcrapp3_par_cash_chk, 
    rcrapp3_ext.rcrapp3_par_child_supp_paid, 
    rcrapp3_ext.rcrapp3_par_eic, 
    rcrapp3_ext.rcrapp3_par_farm_dbt, 
    rcrapp3_ext.rcrapp3_par_farm_val, 
    rcrapp3_ext.rcrapp3_par_for_inc_excl, 
    rcrapp3_ext.rcrapp3_par_house_food_oth, 
    rcrapp3_ext.rcrapp3_par_inc_benefits, 
    rcrapp3_ext.rcrapp3_par_invest_dbt, 
    rcrapp3_ext.rcrapp3_par_invest_val, 
    rcrapp3_ext.rcrapp3_par_ira_keough, 
    rcrapp3_ext.rcrapp3_par_live_on_farm, 
    rcrapp3_ext.rcrapp3_par_mth_mort_rent_pay, 
    rcrapp3_ext.rcrapp3_par_portion_of_pens, 
    rcrapp3_ext.rcrapp3_par_re_dbt, 
    rcrapp3_ext.rcrapp3_par_re_val, 
    rcrapp3_ext.rcrapp3_par_spec_fuels, 
    rcrapp3_ext.rcrapp3_par_tax_def_pension, 
    rcrapp3_ext.rcrapp3_par_te_int_inc, 
    rcrapp3_ext.rcrapp3_pin, 
    rcrapp3_ext.rcrapp3_preparer_ein, 
    rcrapp3_ext.rcrapp3_preparer_sign, 
    rcrapp3_ext.rcrapp3_preparer_ssn, 
    rcrapp3_ext.rcrapp3_prev_coll5, 
    rcrapp3_ext.rcrapp3_re_dbt, 
    rcrapp3_ext.rcrapp3_re_val, 
    rcrapp3_ext.rcrapp3_reject_ovrd_1, 
    rcrapp3_ext.rcrapp3_reject_ovrd_2, 
    rcrapp3_ext.rcrapp3_reject_ovrd_3, 
    rcrapp3_ext.rcrapp3_reject_ovrd_4, 
    rcrapp3_ext.rcrapp3_reject_ovrd_5, 
    rcrapp3_ext.rcrapp3_reject_ovrd_6, 
    rcrapp3_ext.rcrapp3_reject_ovrd_7, 
    rcrapp3_ext.rcrapp3_s_mar_mth_yr, 
    rcrapp3_ext.rcrapp3_sar_c_flag, 
    rcrapp3_ext.rcrapp3_self_help_value, 
    rcrapp3_ext.rcrapp3_soc_sec, 
    rcrapp3_ext.rcrapp3_stu_alt_no_in_coll, 
    rcrapp3_ext.rcrapp3_yr_in_coll_2, 
    rcrapp3_ext.rcrapp3_adj_efc_calc_req, 
    rcrapp3_ext.rcrapp3_faa_adj_efc_corr, 
    rcrapp3_ext.rcrapp3_efc_recalc_corr_req, 
    rcrapp3_ext.rcrapp3_s_res_date, 
    rcrapp3_ext.rcrapp3_offl_unoffl_ind, 
    rcrapp3_ext.rcrapp3_assumption_msg_7, 
    rcrapp3_ext.rcrapp3_assumption_msg_8, 
    rcrapp3_ext.rcrapp3_assumption_msg_9, 
    rcrapp3_ext.rcrapp3_assumption_msg_10, 
    rcrapp3_ext.rcrapp3_pheaa_grant_amt_1, 
    rcrapp3_ext.rcrapp3_pheaa_grant_amt_2, 
    rcrapp3_ext.rcrapp3_pheaa_grant_amt_3, 
    rcrapp3_ext.rcrapp3_pheaa_grant_amt_4, 
    rcrapp3_ext.rcrapp3_pheaa_grant_cde_1, 
    rcrapp3_ext.rcrapp3_pheaa_grant_cde_2, 
    rcrapp3_ext.rcrapp3_pheaa_grant_cde_3, 
    rcrapp3_ext.rcrapp3_pheaa_grant_cde_4, 
    rcrapp3_ext.rcrapp3_reject_ovrd_a, 
    rcrapp3_ext.rcrapp3_reject_ovrd_c, 
    rcrapp3_ext.rcrapp3_par_mar_mth_yr, 
    rcrapp3_ext.rcrapp3_par_tui_fee_ded, 
    rcrapp3_ext.rcrapp3_fath_ssn_match, 
    rcrapp3_ext.rcrapp3_moth_ssn_match, 
    rcrapp3_ext.rcrapp3_reject_ovrd_g, 
    rcrapp3_ext.rcrapp3_user_id, 
    rcrapp3_ext.rcrapp3_activity_date, 
    rcrapp3_ext.rcrapp3_reject_ovrd_12, 
    rcrapp3_ext.rcrapp3_reject_ovrd_j, 
    rcrapp3_ext.rcrapp3_reject_ovrd_k, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_7, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_8, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_9, 
    rcrapp3_ext.rcrapp3_fed_coll_choice_10, 
    rcrapp3_ext.rcrapp3_reject_ovrd_b, 
    rcrapp3_ext.rcrapp3_reject_ovrd_n, 
    rcrapp3_ext.rcrapp3_reject_ovrd_w, 
    rcrapp3_ext.rcrapp3_reject_ovrd_20, 
    rcrapp3_ext.rcrapp3_teach_coursework, 
    rcrapp3_ext.rcrapp3_educ_credits, 
    rcrapp3_ext.rcrapp3_need_based_employ, 
    rcrapp3_ext.rcrapp3_grant_scholar_aid, 
    rcrapp3_ext.rcrapp3_par_educ_credits, 
    rcrapp3_ext.rcrapp3_par_need_based_employ, 
    rcrapp3_ext.rcrapp3_par_grant_scholar_aid, 
    rcrapp3_ext.rcrapp3_spec_circum_flg, 
    rcrapp3_ext.rcrapp3_tax_def_pension, 
    rcrapp3_ext.rcrapp3_te_int_inc, 
    rcrapp3_ext.rcrapp3_house_food_oth, 
    rcrapp3_ext.rcrapp3_co_op_earnings, 
    rcrapp3_ext.rcrapp3_par_co_op_earnings, 
    rcrapp3_ext.rcrapp3_irs_request_flag, 
    rcrapp3_ext.rcrapp3_par_irs_request_flag --, 
--    rcrapp3_ext.rcrapp3_reject_ovrd_21 -- gone in 201220
    )
    select
    rcrapp3.rcrapp3_aidy_code, 
    rcrapp3.rcrapp3_pidm, 
    rcrapp3.rcrapp3_infc_code, 
    rcrapp3.rcrapp3_seq_no, 
    rcrapp3.rcrapp3_c_inst_1_unassess_va, 
    rcrapp3.rcrapp3_c_inst_2_unassess_va, 
    rcrapp3.rcrapp3_c_inst_3_unassess_va, 
    rcrapp3.rcrapp3_c_1_alt_ctrb, 
    rcrapp3.rcrapp3_c_2_alt_ctrb, 
    rcrapp3.rcrapp3_pell_1_alt_ctrb, 
    rcrapp3.rcrapp3_pell_2_alt_ctrb, 
    rcrapp3.rcrapp3_c_par_1_alt_ctrb, 
    rcrapp3.rcrapp3_c_par_2_alt_ctrb, 
    rcrapp3.rcrapp3_pell_1_alt_pgi, 
    rcrapp3.rcrapp3_pell_2_alt_pgi, 
    rcrapp3.rcrapp3_par_1_alt_agi, 
    rcrapp3.rcrapp3_par_2_alt_agi, 
    rcrapp3.rcrapp3_par_alt_mrtl_status, 
    rcrapp3.rcrapp3_par_alt_fam_size, 
    rcrapp3.rcrapp3_par_alt_no_in_coll, 
    rcrapp3.rcrapp3_alt_opt_loss_to_zero, 
    rcrapp3.rcrapp3_alt_opt_hme_val, 
    rcrapp3.rcrapp3_alt_opt_par_in_coll, 
    rcrapp3.rcrapp3_msg_91, 
    rcrapp3.rcrapp3_msg_91_budg_1, 
    rcrapp3.rcrapp3_msg_91_budg_2, 
    rcrapp3.rcrapp3_msg_91_budg_3, 
    rcrapp3.rcrapp3_msg_73_tax, 
    rcrapp3.rcrapp3_c_1_ctrb_adj_1_8_mth, 
    rcrapp3.rcrapp3_c_2_ctrb_adj_1_8_mth, 
    rcrapp3.rcrapp3_c_1_ctrb_adj_ovr_9_mth, 
    rcrapp3.rcrapp3_c_2_ctrb_adj_ovr_9_mth, 
    rcrapp3.rcrapp3_rej_reason_prty_1, 
    rcrapp3.rcrapp3_rej_reason_prty_2, 
    rcrapp3.rcrapp3_rej_reason_prty_3, 
    rcrapp3.rcrapp3_rej_reason_prty_4, 
    rcrapp3.rcrapp3_rej_reason_prty_5, 
    rcrapp3.rcrapp3_rej_reason_prty_6, 
    rcrapp3.rcrapp3_rej_reason_prty_7, 
    rcrapp3.rcrapp3_rej_reason_prty_8, 
    rcrapp3.rcrapp3_rej_reason_prty_9, 
    rcrapp3.rcrapp3_rej_reason_prty_10, 
    rcrapp3.rcrapp3_rej_reason_prty_11, 
    rcrapp3.rcrapp3_rej_reason_prty_12, 
    rcrapp3.rcrapp3_rej_reason_prty_13, 
    rcrapp3.rcrapp3_rej_reason_prty_14, 
    rcrapp3.rcrapp3_rej_in_analysis_flag, 
    rcrapp3.rcrapp3_msg_0, 
    rcrapp3.rcrapp3_msg_1, 
    rcrapp3.rcrapp3_msg_2, 
    rcrapp3.rcrapp3_msg_3, 
    rcrapp3.rcrapp3_msg_4, 
    rcrapp3.rcrapp3_msg_5, 
    rcrapp3.rcrapp3_msg_6, 
    rcrapp3.rcrapp3_msg_11, 
    rcrapp3.rcrapp3_msg_12, 
    rcrapp3.rcrapp3_msg_13, 
    rcrapp3.rcrapp3_msg_14, 
    rcrapp3.rcrapp3_msg_15, 
    rcrapp3.rcrapp3_msg_21, 
    rcrapp3.rcrapp3_msg_22, 
    rcrapp3.rcrapp3_msg_23, 
    rcrapp3.rcrapp3_msg_24, 
    rcrapp3.rcrapp3_msg_26, 
    rcrapp3.rcrapp3_msg_27, 
    rcrapp3.rcrapp3_msg_28, 
    rcrapp3.rcrapp3_msg_29, 
    rcrapp3.rcrapp3_msg_30, 
    rcrapp3.rcrapp3_msg_32, 
    rcrapp3.rcrapp3_msg_33, 
    rcrapp3.rcrapp3_msg_34, 
    rcrapp3.rcrapp3_msg_46, 
    rcrapp3.rcrapp3_msg_47, 
    rcrapp3.rcrapp3_msg_48, 
    rcrapp3.rcrapp3_msg_49, 
    rcrapp3.rcrapp3_msg_50, 
    rcrapp3.rcrapp3_msg_61, 
    rcrapp3.rcrapp3_msg_62, 
    rcrapp3.rcrapp3_msg_63, 
    rcrapp3.rcrapp3_msg_64, 
    rcrapp3.rcrapp3_msg_65, 
    rcrapp3.rcrapp3_msg_66, 
    rcrapp3.rcrapp3_msg_67, 
    rcrapp3.rcrapp3_msg_68, 
    rcrapp3.rcrapp3_msg_71, 
    rcrapp3.rcrapp3_msg_73, 
    rcrapp3.rcrapp3_msg_74, 
    rcrapp3.rcrapp3_msg_75, 
    rcrapp3.rcrapp3_msg_76, 
    rcrapp3.rcrapp3_msg_77, 
    rcrapp3.rcrapp3_msg_78, 
    rcrapp3.rcrapp3_msg_79, 
    rcrapp3.rcrapp3_msg_80, 
    rcrapp3.rcrapp3_msg_81, 
    rcrapp3.rcrapp3_msg_82, 
    rcrapp3.rcrapp3_msg_90, 
    rcrapp3.rcrapp3_msg_92, 
    rcrapp3.rcrapp3_msg_93, 
    rcrapp3.rcrapp3_msg_94, 
    rcrapp3.rcrapp3_msg_95, 
    rcrapp3.rcrapp3_msg_96, 
    rcrapp3.rcrapp3_msg_97, 
    rcrapp3.rcrapp3_msg_98, 
    rcrapp3.rcrapp3_msg_99, 
    rcrapp3.rcrapp3_msg_11_par_fam_memb, 
    rcrapp3.rcrapp3_msg_12_par_no_in_coll, 
    rcrapp3.rcrapp3_msg_22_par_der_agi, 
    rcrapp3.rcrapp3_msg_27_val, 
    rcrapp3.rcrapp3_msg_47_par_hme_val, 
    rcrapp3.rcrapp3_msg_48_par_hme_val_3, 
    rcrapp3.rcrapp3_msg_48_par_hme_eqty_3, 
    rcrapp3.rcrapp3_msg_49_par_hme_val, 
    rcrapp3.rcrapp3_msg_49_par_hme_eqty, 
    rcrapp3.rcrapp3_msg_63_fam_memb, 
    rcrapp3.rcrapp3_msg_64_no_in_coll, 
    rcrapp3.rcrapp3_msg_81_avail_inc, 
    rcrapp3.rcrapp3_msg_90_inc, 
    rcrapp3.rcrapp3_msg_90_sma, 
    rcrapp3.rcrapp3_msg_90_diff, 
    rcrapp3.rcrapp3_msg_92_budg_1, 
    rcrapp3.rcrapp3_msg_92_budg_2, 
    rcrapp3.rcrapp3_msg_92_budg_3, 
    rcrapp3.rcrapp3_msg_93_calc, 
    rcrapp3.rcrapp3_msg_93_resc, 
    rcrapp3.rcrapp3_par_1_alt_fed_tax, 
    rcrapp3.rcrapp3_par_2_alt_fed_tax, 
    rcrapp3.rcrapp3_msg_0_fed_1_fam_ctrb, 
    rcrapp3.rcrapp3_msg_0_fed_2_fam_ctrb, 
    rcrapp3.rcrapp3_msg_0_fed_1_pgi, 
    rcrapp3.rcrapp3_msg_0_fed_2_pgi, 
    rcrapp3.rcrapp3_assumption_msg_1, 
    rcrapp3.rcrapp3_assumption_msg_2, 
    rcrapp3.rcrapp3_assumption_msg_3, 
    rcrapp3.rcrapp3_assumption_msg_4, 
    rcrapp3.rcrapp3_assumption_msg_5, 
    rcrapp3.rcrapp3_assumption_msg_6, 
    rcrapp3.rcrapp3_msg_51, 
    rcrapp3.rcrapp3_msg_51_val, 
    rcrapp3.rcrapp3_msg_83, 
    rcrapp3.rcrapp3_msg_84, 
    rcrapp3.rcrapp3_msg_85, 
    rcrapp3.rcrapp3_msg_85_val, 
    rcrapp3.rcrapp3_assumpt_ovrd_1, 
    rcrapp3.rcrapp3_assumpt_ovrd_2, 
    rcrapp3.rcrapp3_assumpt_ovrd_3, 
    rcrapp3.rcrapp3_assumpt_ovrd_4, 
    rcrapp3.rcrapp3_assumpt_ovrd_5, 
    rcrapp3.rcrapp3_assumpt_ovrd_6, 
    rcrapp3.rcrapp3_assumpt_ovrd_7, 
    rcrapp3.rcrapp3_assumpt_ovrd_8, 
    rcrapp3.rcrapp3_assumpt_ovrd_9, 
    rcrapp3.rcrapp3_attend_same_coll, 
    rcrapp3.rcrapp3_bus_dbt, 
    rcrapp3.rcrapp3_bus_val, 
    rcrapp3.rcrapp3_c_signed, 
    rcrapp3.rcrapp3_c_signed_fath, 
    rcrapp3.rcrapp3_c_signed_moth, 
    rcrapp3.rcrapp3_c_signed_mth_day, 
    rcrapp3.rcrapp3_c_signed_par, 
    rcrapp3.rcrapp3_c_signed_sps, 
    rcrapp3.rcrapp3_c_signed_yr, 
    rcrapp3.rcrapp3_cash_chk, 
    rcrapp3.rcrapp3_child_care, 
    rcrapp3.rcrapp3_child_supp_paid, 
    rcrapp3.rcrapp3_degree_type, 
    rcrapp3.rcrapp3_efc_match, 
    rcrapp3.rcrapp3_estimate_flags, 
    rcrapp3.rcrapp3_faa_dep_override, 
    rcrapp3.rcrapp3_faa_signature, 
    rcrapp3.rcrapp3_faa_title_iv_code, 
    rcrapp3.rcrapp3_faf_fafsa_data, 
    rcrapp3.rcrapp3_faf_rcpt_date, 
    rcrapp3.rcrapp3_farm_dbt, 
    rcrapp3.rcrapp3_farm_val, 
    rcrapp3.rcrapp3_father_age, 
    rcrapp3.rcrapp3_father_hi_grade, 
    rcrapp3.rcrapp3_fed_coll_choice_4, 
    rcrapp3.rcrapp3_fed_coll_choice_5, 
    rcrapp3.rcrapp3_fed_coll_choice_6, 
    rcrapp3.rcrapp3_ga_match_flag, 
    rcrapp3.rcrapp3_ged_mth_yr, 
    rcrapp3.rcrapp3_grad_or_prof, 
    rcrapp3.rcrapp3_hs_grad_mth_yr, 
    rcrapp3.rcrapp3_invest_dbt, 
    rcrapp3.rcrapp3_invest_val, 
    rcrapp3.rcrapp3_live_on_farm, 
    rcrapp3.rcrapp3_married, 
    rcrapp3.rcrapp3_mother_age, 
    rcrapp3.rcrapp3_mother_hi_grade, 
    rcrapp3.rcrapp3_msg_52, 
    rcrapp3.rcrapp3_msg_91_sec_fm_efc, 
    rcrapp3.rcrapp3_p_res_date, 
    rcrapp3.rcrapp3_par_alt_hme_val, 
    rcrapp3.rcrapp3_par_alt_hme_val_cap, 
    rcrapp3.rcrapp3_par_bus_dbt, 
    rcrapp3.rcrapp3_par_bus_val, 
    rcrapp3.rcrapp3_par_cash_chk, 
    rcrapp3.rcrapp3_par_child_supp_paid, 
    rcrapp3.rcrapp3_par_eic, 
    rcrapp3.rcrapp3_par_farm_dbt, 
    rcrapp3.rcrapp3_par_farm_val, 
    rcrapp3.rcrapp3_par_for_inc_excl, 
    rcrapp3.rcrapp3_par_house_food_oth, 
    rcrapp3.rcrapp3_par_inc_benefits, 
    rcrapp3.rcrapp3_par_invest_dbt, 
    rcrapp3.rcrapp3_par_invest_val, 
    rcrapp3.rcrapp3_par_ira_keough, 
    rcrapp3.rcrapp3_par_live_on_farm, 
    rcrapp3.rcrapp3_par_mth_mort_rent_pay, 
    rcrapp3.rcrapp3_par_portion_of_pens, 
    rcrapp3.rcrapp3_par_re_dbt, 
    rcrapp3.rcrapp3_par_re_val, 
    rcrapp3.rcrapp3_par_spec_fuels, 
    rcrapp3.rcrapp3_par_tax_def_pension, 
    rcrapp3.rcrapp3_par_te_int_inc, 
    rcrapp3.rcrapp3_pin, 
    rcrapp3.rcrapp3_preparer_ein, 
    rcrapp3.rcrapp3_preparer_sign, 
    rcrapp3.rcrapp3_preparer_ssn, 
    rcrapp3.rcrapp3_prev_coll5, 
    rcrapp3.rcrapp3_re_dbt, 
    rcrapp3.rcrapp3_re_val, 
    rcrapp3.rcrapp3_reject_ovrd_1, 
    rcrapp3.rcrapp3_reject_ovrd_2, 
    rcrapp3.rcrapp3_reject_ovrd_3, 
    rcrapp3.rcrapp3_reject_ovrd_4, 
    rcrapp3.rcrapp3_reject_ovrd_5, 
    rcrapp3.rcrapp3_reject_ovrd_6, 
    rcrapp3.rcrapp3_reject_ovrd_7, 
    rcrapp3.rcrapp3_s_mar_mth_yr, 
    rcrapp3.rcrapp3_sar_c_flag, 
    rcrapp3.rcrapp3_self_help_value, 
    rcrapp3.rcrapp3_soc_sec, 
    rcrapp3.rcrapp3_stu_alt_no_in_coll, 
    rcrapp3.rcrapp3_yr_in_coll_2, 
    rcrapp3.rcrapp3_adj_efc_calc_req, 
    rcrapp3.rcrapp3_faa_adj_efc_corr, 
    rcrapp3.rcrapp3_efc_recalc_corr_req, 
    rcrapp3.rcrapp3_s_res_date, 
    rcrapp3.rcrapp3_offl_unoffl_ind, 
    rcrapp3.rcrapp3_assumption_msg_7, 
    rcrapp3.rcrapp3_assumption_msg_8, 
    rcrapp3.rcrapp3_assumption_msg_9, 
    rcrapp3.rcrapp3_assumption_msg_10, 
    rcrapp3.rcrapp3_pheaa_grant_amt_1, 
    rcrapp3.rcrapp3_pheaa_grant_amt_2, 
    rcrapp3.rcrapp3_pheaa_grant_amt_3, 
    rcrapp3.rcrapp3_pheaa_grant_amt_4, 
    rcrapp3.rcrapp3_pheaa_grant_cde_1, 
    rcrapp3.rcrapp3_pheaa_grant_cde_2, 
    rcrapp3.rcrapp3_pheaa_grant_cde_3, 
    rcrapp3.rcrapp3_pheaa_grant_cde_4, 
    rcrapp3.rcrapp3_reject_ovrd_a, 
    rcrapp3.rcrapp3_reject_ovrd_c, 
    rcrapp3.rcrapp3_par_mar_mth_yr, 
    rcrapp3.rcrapp3_par_tui_fee_ded, 
    rcrapp3.rcrapp3_fath_ssn_match, 
    rcrapp3.rcrapp3_moth_ssn_match, 
    rcrapp3.rcrapp3_reject_ovrd_g, 
    rcrapp3.rcrapp3_user_id, 
    rcrapp3.rcrapp3_activity_date, 
    rcrapp3.rcrapp3_reject_ovrd_12, 
    rcrapp3.rcrapp3_reject_ovrd_j, 
    rcrapp3.rcrapp3_reject_ovrd_k, 
    rcrapp3.rcrapp3_fed_coll_choice_7, 
    rcrapp3.rcrapp3_fed_coll_choice_8, 
    rcrapp3.rcrapp3_fed_coll_choice_9, 
    rcrapp3.rcrapp3_fed_coll_choice_10, 
    rcrapp3.rcrapp3_reject_ovrd_b, 
    rcrapp3.rcrapp3_reject_ovrd_n, 
    rcrapp3.rcrapp3_reject_ovrd_w, 
    rcrapp3.rcrapp3_reject_ovrd_20, 
    rcrapp3.rcrapp3_teach_coursework, 
    rcrapp3.rcrapp3_educ_credits, 
    rcrapp3.rcrapp3_need_based_employ, 
    rcrapp3.rcrapp3_grant_scholar_aid, 
    rcrapp3.rcrapp3_par_educ_credits, 
    rcrapp3.rcrapp3_par_need_based_employ, 
    rcrapp3.rcrapp3_par_grant_scholar_aid, 
    rcrapp3.rcrapp3_spec_circum_flg, 
    rcrapp3.rcrapp3_tax_def_pension, 
    rcrapp3.rcrapp3_te_int_inc, 
    rcrapp3.rcrapp3_house_food_oth, 
    rcrapp3.rcrapp3_co_op_earnings, 
    rcrapp3.rcrapp3_par_co_op_earnings, 
    rcrapp3.rcrapp3_irs_request_flag, 
    rcrapp3.rcrapp3_par_irs_request_flag --, 
--    rcrapp3.rcrapp3_reject_ovrd_21
    from rcrapp3@aimsmgr201040 rcrapp3;

    commit;
  end p_import_rcrapp3;

  procedure p_import_rcrapp4 as
  begin
    execute immediate('truncate table rcrapp4_ext');

    insert into rcrapp4_ext(
    rcrapp4_ext.RCRAPP4_AIDY_CODE, 
    rcrapp4_ext.RCRAPP4_PIDM, 
    rcrapp4_ext.rcrapp4_infc_code, 
    rcrapp4_ext.RCRAPP4_SEQ_NO,
    rcrapp4_ext.RCRAPP4_REC_TYPE, 
    rcrapp4_ext.RCRAPP4_MDE_ID_NO, 
    rcrapp4_ext.RCRAPP4_CTRL_NO, 
    rcrapp4_ext.RCRAPP4_TAPE_RPT_EXTRCT_DATE, 
    rcrapp4_ext.RCRAPP4_INST_RPT_TYPE, 
    rcrapp4_ext.RCRAPP4_SECT_R_WRITTEN_EXPLAIN, 
    rcrapp4_ext.RCRAPP4_SECT_S_INST_ID, 
    rcrapp4_ext.RCRAPP4_ST_SPECIFIC_INFO, 
    rcrapp4_ext.RCRAPP4_C_INST_1_OUT_ST_TUIT, 
    rcrapp4_ext.RCRAPP4_C_INST_1_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_1_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_1_BOOKS, 
    rcrapp4_ext.RCRAPP4_C_INST_1_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_INST_1_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_1_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_1_EST_ST_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_2_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_2_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_2_BOOKS, 
    rcrapp4_ext.RCRAPP4_C_INST_2_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_INST_2_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_2_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_2_EST_ST_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_3_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_3_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_3_BOOKS, 
    rcrapp4_ext.RCRAPP4_C_INST_3_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_INST_3_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_3_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_3_EST_ST_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_1_DEPEND_CARE, 
    rcrapp4_ext.RCRAPP4_C_INST_2_DEPEND_CARE, 
    rcrapp4_ext.RCRAPP4_C_INST_3_DEPEND_CARE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_NA_1_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_NA_2_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_PELL_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_PELL_PGI, 
    rcrapp4_ext.RCRAPP4_C_NA_2_PELL_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_PELL_PGI, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_NA_FISAP_INC, 
    rcrapp4_ext.RCRAPP4_RECORD_MARK, 
    rcrapp4_ext.RCRAPP4_COLL_SPECIFIC_INFO, 
    rcrapp4_ext.RCRAPP4_ADJ_EFC, 
    rcrapp4_ext.RCRAPP4_EFC_TYPE, 
    rcrapp4_ext.RCRAPP4_ESAR_USER_DEF_FIELDS, 
    rcrapp4_ext.RCRAPP4_C_INST_1_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_2_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_3_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_4_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_4_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_4_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_4_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_4_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_4_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_4_UNASSESS_VA, 
    rcrapp4_ext.RCRAPP4_C_INST_5_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_5_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_5_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_5_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_5_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_5_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_5_UNASSESS_VA, 
    rcrapp4_ext.RCRAPP4_C2_INST_1_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_1_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_2_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_2_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_3_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_3_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_4_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_4_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_5_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_5_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_PELL_1_EFC, 
    rcrapp4_ext.RCRAPP4_PELL_2_EFC, 
    rcrapp4_ext.RCRAPP4_S_IV_INC, 
    rcrapp4_ext.RCRAPP4_P_IV_INC, 
    rcrapp4_ext.RCRAPP4_CURRENT_PLUS, 
    rcrapp4_ext.RCRAPP4_CURRENT_CONSOL, 
    rcrapp4_ext.RCRAPP4_WORK_STUDY, 
    rcrapp4_ext.RCRAPP4_LOAN_INT, 
    rcrapp4_ext.RCRAPP4_PLUS_INT, 
    rcrapp4_ext.RCRAPP4_S_DIV_INT_INC, 
    rcrapp4_ext.RCRAPP4_S_FARM_VAL_FAF, 
    rcrapp4_ext.RCRAPP4_S_FARM_DBT_FAF, 
    rcrapp4_ext.RCRAPP4_P_FARM_VAL_FAF, 
    rcrapp4_ext.RCRAPP4_P_FARM_DBT_FAF, 
    rcrapp4_ext.RCRAPP4_VERIF_NUM, 
    rcrapp4_ext.RCRAPP4_ALT_OPT_TUIT, 
    rcrapp4_ext.RCRAPP4_MSG_31, 
    rcrapp4_ext.RCRAPP4_MSG_72, 
    rcrapp4_ext.RCRAPP4_OLDEST_LOAN_DATE, 
    rcrapp4_ext.RCRAPP4_NSLDS_MATCH, 
    rcrapp4_ext.RCRAPP4_BAL_ON_SLS, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_PC_IND, 
    rcrapp4_ext.RCRAPP4_IRA_KEOGH_AMT, 
    rcrapp4_ext.RCRAPP4_EXP_PAR_TUIT_PD, 
    rcrapp4_ext.RCRAPP4_EXP_PAR_NO_CHILD_TUIT, 
    rcrapp4_ext.RCRAPP4_HME_YR_PURCH, 
    rcrapp4_ext.RCRAPP4_HME_PURCH_AMT, 
    rcrapp4_ext.RCRAPP4_VISA_CLASS, 
    rcrapp4_ext.RCRAPP4_C_INST_1_OTH_CTRB, 
    rcrapp4_ext.RCRAPP4_C_1_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_1_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_1_IRA_KEOGH, 
    rcrapp4_ext.RCRAPP4_C_1_TRUST_VAL, 
    rcrapp4_ext.RCRAPP4_C_1_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_1_OTH_ASSET_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_2_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_2_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_2_IRA_KEOGH, 
    rcrapp4_ext.RCRAPP4_C_2_TRUST_VAL, 
    rcrapp4_ext.RCRAPP4_C_2_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_2_OTH_ASSET_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_OTH_ASSET_ALLW, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_OTH_ASSET_ALLW, 
    rcrapp4_ext.RCRAPP4_TYPE_DATA_AVAIL, 
    rcrapp4_ext.RCRAPP4_IM_PAR_DATA_AVAIL, 
    rcrapp4_ext.RCRAPP4_FM_PAR_DATA_AVAIL, 
    rcrapp4_ext.RCRAPP4_IM_BUDG_DUR_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_STATE_FOR_TAXES, 
    rcrapp4_ext.RCRAPP4_PAR_STATE_FOR_TAXES, 
    rcrapp4_ext.RCRAPP4_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_TAX_FILER, 
    rcrapp4_ext.RCRAPP4_PAR_TAX_FILER, 
    rcrapp4_ext.RCRAPP4_TYPE_FM_CALC, 
    rcrapp4_ext.RCRAPP4_PAR_DEP_CARE_AND_MED, 
    rcrapp4_ext.RCRAPP4_S_EIC, 
    rcrapp4_ext.RCRAPP4_SSA_CITIZEN_IND, 
    rcrapp4_ext.RCRAPP4_SAR_EFC, 
    rcrapp4_ext.RCRAPP4_HS_GED_RCVD, 
    rcrapp4_ext.RCRAPP4_FISAP_INC, 
    rcrapp4_ext.RCRAPP4_EMAIL_ADDRESS, 
    rcrapp4_ext.RCRAPP4_NSLDS_TRAN_NO, 
    rcrapp4_ext.RCRAPP4_DRIVERS_LIC_IND, 
    rcrapp4_ext.RCRAPP4_LEGAL_RES_IND, 
    rcrapp4_ext.RCRAPP4_PAR_LEGAL_RES_IND, 
    rcrapp4_ext.RCRAPP4_TX_RET_FILED_IND, 
    rcrapp4_ext.RCRAPP4_PAR_TX_RET_FILED_IND, 
    rcrapp4_ext.RCRAPP4_1040A_ELIG_IND, 
    rcrapp4_ext.RCRAPP4_PAR_1040A_ELIG_IND, 
    rcrapp4_ext.RCRAPP4_INV_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_PAR_INV_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_BUS_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_PAR_BUS_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_FARM_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_PAR_FARM_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_DEATH_DATE, 
    rcrapp4_ext.RCRAPP4_VA_MATCH_FLAG, 
    rcrapp4_ext.RCRAPP4_FISAP_2_INC, 
    rcrapp4_ext.RCRAPP4_DRUG_OFFENSE_CONVIC, 
    rcrapp4_ext.RCRAPP4_FATH_LAST_NAME, 
    rcrapp4_ext.RCRAPP4_MOTH_LAST_NAME, 
    rcrapp4_ext.RCRAPP4_FATH_SSN, 
    rcrapp4_ext.RCRAPP4_MOTH_SSN, 
    rcrapp4_ext.RCRAPP4_SUB_VERIF_SELECTED, 
    rcrapp4_ext.RCRAPP4_P_RES_DATE_MO_YR, 
    rcrapp4_ext.RCRAPP4_S_RES_DATE_MO_YR, 
    rcrapp4_ext.RCRAPP4_PRISONER_MATCH_FLAG, 
    rcrapp4_ext.RCRAPP4_HAVE_CHILDREN, 
    rcrapp4_ext.RCRAPP4_PAR_WORKSHEET_C, 
    rcrapp4_ext.RCRAPP4_WORKSHEET_C, 
    rcrapp4_ext.RCRAPP4_SOURCE_CORRECTION, 
    rcrapp4_ext.RCRAPP4_EFC_CHANGE_IND, 
    rcrapp4_ext.RCRAPP4_DUP_SSN_IND, 
    rcrapp4_ext.RCRAPP4_EARLY_ADM_FLAG, 
    rcrapp4_ext.RCRAPP4_SEC_INS_MATCH_IND, 
    rcrapp4_ext.RCRAPP4_WORKSHEET_A, 
    rcrapp4_ext.RCRAPP4_PAR_WORKSHEET_A, 
    rcrapp4_ext.RCRAPP4_WORKSHEET_B, 
    rcrapp4_ext.RCRAPP4_PAR_WORKSHEET_B, 
    rcrapp4_ext.RCRAPP4_SS_MATCH, 
    rcrapp4_ext.RCRAPP4_SS_REG_FLAG, 
    rcrapp4_ext.RCRAPP4_FATH_FIRST_NAME_INI, 
    rcrapp4_ext.RCRAPP4_FATH_BIRTH_DATE, 
    rcrapp4_ext.RCRAPP4_MOTH_FIRST_NAME_INI, 
    rcrapp4_ext.RCRAPP4_MOTH_BIRTH_DATE, 
    rcrapp4_ext.RCRAPP4_P_EMAIL_ADDRESS, 
    rcrapp4_ext.RCRAPP4_ADDRESS_CHG_FLAG, 
    rcrapp4_ext.RCRAPP4_PUSH_ISIR_FLAG, 
    rcrapp4_ext.RCRAPP4_SAR_C_CHANGE_FLAG, 
    rcrapp4_ext.rcrapp4_custodial_parent, 
    rcrapp4_ext.RCRAPP4_CUST_PAR_BASE_PCT_INC,
    rcrapp4_ext.RCRAPP4_PAR_BASE_CTRB_INC, 
    rcrapp4_ext.RCRAPP4_PAR_BASE_CTRB_ASSETS, 
    rcrapp4_ext.RCRAPP4_PAR_BASE_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_USER_ID, 
    rcrapp4_ext.RCRAPP4_ACTIVITY_DATE, 
    rcrapp4_ext.RCRAPP4_FEE_WAIVER_IND, 
    rcrapp4_ext.RCRAPP4_WRK_STDY_LOANS_INT, 
    rcrapp4_ext.RCRAPP4_AP_FLAG, 
    rcrapp4_ext.RCRAPP4_PAR1_OCCUPATION, 
    rcrapp4_ext.RCRAPP4_PAR1_EMPLOYER, 
    rcrapp4_ext.RCRAPP4_PAR2_OCCUPATION, 
    rcrapp4_ext.RCRAPP4_PAR2_EMPLOYER, 
    rcrapp4_ext.RCRAPP4_ADOPTED_AFTER_13, 
    rcrapp4_ext.RCRAPP4_HOMELESS, 
    rcrapp4_ext.RCRAPP4_COMBAT_PAY, 
    rcrapp4_ext.RCRAPP4_PAR_NUM_BUSINESSES, 
    rcrapp4_ext.RCRAPP4_PAR_NUM_FARMS, 
    rcrapp4_ext.RCRAPP4_PAR_COMBAT_PAY, 
    rcrapp4_ext.RCRAPP4_IM_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_EFM_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_FM_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_PARENT_TYPE_1, 
    rcrapp4_ext.RCRAPP4_PARENT_TYPE_2, 
    rcrapp4_ext.rcrapp4_dod_match_flag, 
    rcrapp4_ext.rcrapp4_dod_par_death_date --, 
--    rcrapp4_ext.rcrapp4_high_school_name --, 
--    rcrapp4_ext.rcrapp4_high_school_city --, 
--    rcrapp4_ext.rcrapp4_stat_code_high_sch --, 
--    rcrapp4_ext.rcrapp4_high_school_cde --, 
--    rcrapp4_ext.rcrapp4_high_school_flg --, 
--    rcrapp4_ext.rcrapp4_p_asset_thresh_excd --, 
--    rcrapp4_ext.rcrapp4_s_asset_thresh_excd    
    )
    select
    rcrapp4_ext.RCRAPP4_AIDY_CODE, 
    rcrapp4_ext.RCRAPP4_PIDM, 
    rcrapp4_ext.rcrapp4_infc_code, 
    rcrapp4_ext.RCRAPP4_SEQ_NO,
    rcrapp4_ext.RCRAPP4_REC_TYPE, 
    rcrapp4_ext.RCRAPP4_MDE_ID_NO, 
    rcrapp4_ext.RCRAPP4_CTRL_NO, 
    rcrapp4_ext.RCRAPP4_TAPE_RPT_EXTRCT_DATE, 
    rcrapp4_ext.RCRAPP4_INST_RPT_TYPE, 
    rcrapp4_ext.RCRAPP4_SECT_R_WRITTEN_EXPLAIN, 
    rcrapp4_ext.RCRAPP4_SECT_S_INST_ID, 
    rcrapp4_ext.RCRAPP4_ST_SPECIFIC_INFO, 
    rcrapp4_ext.RCRAPP4_C_INST_1_OUT_ST_TUIT, 
    rcrapp4_ext.RCRAPP4_C_INST_1_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_1_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_1_BOOKS, 
    rcrapp4_ext.RCRAPP4_C_INST_1_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_1_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_INST_1_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_1_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_1_EST_ST_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_2_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_2_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_2_BOOKS, 
    rcrapp4_ext.RCRAPP4_C_INST_2_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_2_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_INST_2_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_2_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_2_EST_ST_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_3_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_3_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_3_BOOKS, 
    rcrapp4_ext.RCRAPP4_C_INST_3_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_3_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_INST_3_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_3_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_3_EST_ST_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_1_DEPEND_CARE, 
    rcrapp4_ext.RCRAPP4_C_INST_2_DEPEND_CARE, 
    rcrapp4_ext.RCRAPP4_C_INST_3_DEPEND_CARE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_CTRB_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_CTRB_FOR_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_NA_1_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_NA_2_TFC_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_PELL_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_1_PELL_PGI, 
    rcrapp4_ext.RCRAPP4_C_NA_2_PELL_OFFL_EST, 
    rcrapp4_ext.RCRAPP4_C_NA_2_PELL_PGI, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_1_P_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_IM_CTRB_STDT, 
    rcrapp4_ext.RCRAPP4_C_NA_2_P_IM_ANLY_TYPE, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_NA_FISAP_INC, 
    rcrapp4_ext.RCRAPP4_RECORD_MARK, 
    rcrapp4_ext.RCRAPP4_COLL_SPECIFIC_INFO, 
    rcrapp4_ext.RCRAPP4_ADJ_EFC, 
    rcrapp4_ext.RCRAPP4_EFC_TYPE, 
    rcrapp4_ext.RCRAPP4_ESAR_USER_DEF_FIELDS, 
    rcrapp4_ext.RCRAPP4_C_INST_1_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_2_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_3_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_4_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_4_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_4_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_4_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_4_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_4_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_4_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_4_UNASSESS_VA, 
    rcrapp4_ext.RCRAPP4_C_INST_5_ASSUM_HOUS, 
    rcrapp4_ext.RCRAPP4_C_INST_5_BUDG_DUR, 
    rcrapp4_ext.RCRAPP4_C_INST_5_BUDG_NAME, 
    rcrapp4_ext.RCRAPP4_C_INST_5_EST_FA_NEED, 
    rcrapp4_ext.RCRAPP4_C_INST_5_EST_PELL_AWRD, 
    rcrapp4_ext.RCRAPP4_C_INST_5_LIVING_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_EXP, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TOT_PAR_CTRB, 
    rcrapp4_ext.RCRAPP4_C_INST_5_TUIT_FEE, 
    rcrapp4_ext.RCRAPP4_C_INST_5_UNASSESS_VA, 
    rcrapp4_ext.RCRAPP4_C2_INST_1_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_1_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_2_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_2_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_3_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_3_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_4_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_4_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_C2_INST_5_EST_PELL_AWD, 
    rcrapp4_ext.RCRAPP4_C2_INST_5_TOT_FAM_CTRB, 
    rcrapp4_ext.RCRAPP4_PELL_1_EFC, 
    rcrapp4_ext.RCRAPP4_PELL_2_EFC, 
    rcrapp4_ext.RCRAPP4_S_IV_INC, 
    rcrapp4_ext.RCRAPP4_P_IV_INC, 
    rcrapp4_ext.RCRAPP4_CURRENT_PLUS, 
    rcrapp4_ext.RCRAPP4_CURRENT_CONSOL, 
    rcrapp4_ext.RCRAPP4_WORK_STUDY, 
    rcrapp4_ext.RCRAPP4_LOAN_INT, 
    rcrapp4_ext.RCRAPP4_PLUS_INT, 
    rcrapp4_ext.RCRAPP4_S_DIV_INT_INC, 
    rcrapp4_ext.RCRAPP4_S_FARM_VAL_FAF, 
    rcrapp4_ext.RCRAPP4_S_FARM_DBT_FAF, 
    rcrapp4_ext.RCRAPP4_P_FARM_VAL_FAF, 
    rcrapp4_ext.RCRAPP4_P_FARM_DBT_FAF, 
    rcrapp4_ext.RCRAPP4_VERIF_NUM, 
    rcrapp4_ext.RCRAPP4_ALT_OPT_TUIT, 
    rcrapp4_ext.RCRAPP4_MSG_31, 
    rcrapp4_ext.RCRAPP4_MSG_72, 
    rcrapp4_ext.RCRAPP4_OLDEST_LOAN_DATE, 
    rcrapp4_ext.RCRAPP4_NSLDS_MATCH, 
    rcrapp4_ext.RCRAPP4_BAL_ON_SLS, 
    rcrapp4_ext.RCRAPP4_C_NA_1_IM_PC_IND, 
    rcrapp4_ext.RCRAPP4_IRA_KEOGH_AMT, 
    rcrapp4_ext.RCRAPP4_EXP_PAR_TUIT_PD, 
    rcrapp4_ext.RCRAPP4_EXP_PAR_NO_CHILD_TUIT, 
    rcrapp4_ext.RCRAPP4_HME_YR_PURCH, 
    rcrapp4_ext.RCRAPP4_HME_PURCH_AMT, 
    rcrapp4_ext.RCRAPP4_VISA_CLASS, 
    rcrapp4_ext.RCRAPP4_C_INST_1_OTH_CTRB, 
    rcrapp4_ext.RCRAPP4_C_1_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_1_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_1_IRA_KEOGH, 
    rcrapp4_ext.RCRAPP4_C_1_TRUST_VAL, 
    rcrapp4_ext.RCRAPP4_C_1_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_1_OTH_ASSET_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_2_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_2_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_2_IRA_KEOGH, 
    rcrapp4_ext.RCRAPP4_C_2_TRUST_VAL, 
    rcrapp4_ext.RCRAPP4_C_2_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_2_OTH_ASSET_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_PAR_1_OTH_ASSET_ALLW, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_INC_ADJ, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_OTH_ALLOW, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_OTH_ASSET, 
    rcrapp4_ext.RCRAPP4_C_PAR_2_OTH_ASSET_ALLW, 
    rcrapp4_ext.RCRAPP4_TYPE_DATA_AVAIL, 
    rcrapp4_ext.RCRAPP4_IM_PAR_DATA_AVAIL, 
    rcrapp4_ext.RCRAPP4_FM_PAR_DATA_AVAIL, 
    rcrapp4_ext.RCRAPP4_IM_BUDG_DUR_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_STATE_FOR_TAXES, 
    rcrapp4_ext.RCRAPP4_PAR_STATE_FOR_TAXES, 
    rcrapp4_ext.RCRAPP4_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_TAX_FILER, 
    rcrapp4_ext.RCRAPP4_PAR_TAX_FILER, 
    rcrapp4_ext.RCRAPP4_TYPE_FM_CALC, 
    rcrapp4_ext.RCRAPP4_PAR_DEP_CARE_AND_MED, 
    rcrapp4_ext.RCRAPP4_S_EIC, 
    rcrapp4_ext.RCRAPP4_SSA_CITIZEN_IND, 
    rcrapp4_ext.RCRAPP4_SAR_EFC, 
    rcrapp4_ext.RCRAPP4_HS_GED_RCVD, 
    rcrapp4_ext.RCRAPP4_FISAP_INC, 
    rcrapp4_ext.RCRAPP4_EMAIL_ADDRESS, 
    rcrapp4_ext.RCRAPP4_NSLDS_TRAN_NO, 
    rcrapp4_ext.RCRAPP4_DRIVERS_LIC_IND, 
    rcrapp4_ext.RCRAPP4_LEGAL_RES_IND, 
    rcrapp4_ext.RCRAPP4_PAR_LEGAL_RES_IND, 
    rcrapp4_ext.RCRAPP4_TX_RET_FILED_IND, 
    rcrapp4_ext.RCRAPP4_PAR_TX_RET_FILED_IND, 
    rcrapp4_ext.RCRAPP4_1040A_ELIG_IND, 
    rcrapp4_ext.RCRAPP4_PAR_1040A_ELIG_IND, 
    rcrapp4_ext.RCRAPP4_INV_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_PAR_INV_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_BUS_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_PAR_BUS_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_FARM_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_PAR_FARM_NET_WORTH, 
    rcrapp4_ext.RCRAPP4_DEATH_DATE, 
    rcrapp4_ext.RCRAPP4_VA_MATCH_FLAG, 
    rcrapp4_ext.RCRAPP4_FISAP_2_INC, 
    rcrapp4_ext.RCRAPP4_DRUG_OFFENSE_CONVIC, 
    rcrapp4_ext.RCRAPP4_FATH_LAST_NAME, 
    rcrapp4_ext.RCRAPP4_MOTH_LAST_NAME, 
    rcrapp4_ext.RCRAPP4_FATH_SSN, 
    rcrapp4_ext.RCRAPP4_MOTH_SSN, 
    rcrapp4_ext.RCRAPP4_SUB_VERIF_SELECTED, 
    rcrapp4_ext.RCRAPP4_P_RES_DATE_MO_YR, 
    rcrapp4_ext.RCRAPP4_S_RES_DATE_MO_YR, 
    rcrapp4_ext.RCRAPP4_PRISONER_MATCH_FLAG, 
    rcrapp4_ext.RCRAPP4_HAVE_CHILDREN, 
    rcrapp4_ext.RCRAPP4_PAR_WORKSHEET_C, 
    rcrapp4_ext.RCRAPP4_WORKSHEET_C, 
    rcrapp4_ext.RCRAPP4_SOURCE_CORRECTION, 
    rcrapp4_ext.RCRAPP4_EFC_CHANGE_IND, 
    rcrapp4_ext.RCRAPP4_DUP_SSN_IND, 
    rcrapp4_ext.RCRAPP4_EARLY_ADM_FLAG, 
    rcrapp4_ext.RCRAPP4_SEC_INS_MATCH_IND, 
    rcrapp4_ext.RCRAPP4_WORKSHEET_A, 
    rcrapp4_ext.RCRAPP4_PAR_WORKSHEET_A, 
    rcrapp4_ext.RCRAPP4_WORKSHEET_B, 
    rcrapp4_ext.RCRAPP4_PAR_WORKSHEET_B, 
    rcrapp4_ext.RCRAPP4_SS_MATCH, 
    rcrapp4_ext.RCRAPP4_SS_REG_FLAG, 
    rcrapp4_ext.RCRAPP4_FATH_FIRST_NAME_INI, 
    rcrapp4_ext.RCRAPP4_FATH_BIRTH_DATE, 
    rcrapp4_ext.RCRAPP4_MOTH_FIRST_NAME_INI, 
    rcrapp4_ext.RCRAPP4_MOTH_BIRTH_DATE, 
    rcrapp4_ext.RCRAPP4_P_EMAIL_ADDRESS, 
    rcrapp4_ext.RCRAPP4_ADDRESS_CHG_FLAG, 
    rcrapp4_ext.RCRAPP4_PUSH_ISIR_FLAG, 
    rcrapp4_ext.RCRAPP4_SAR_C_CHANGE_FLAG, 
    rcrapp4_ext.rcrapp4_custodial_parent, 
    rcrapp4_ext.RCRAPP4_CUST_PAR_BASE_PCT_INC,
    rcrapp4_ext.RCRAPP4_PAR_BASE_CTRB_INC, 
    rcrapp4_ext.RCRAPP4_PAR_BASE_CTRB_ASSETS, 
    rcrapp4_ext.RCRAPP4_PAR_BASE_TOT_CTRB, 
    rcrapp4_ext.RCRAPP4_USER_ID, 
    rcrapp4_ext.RCRAPP4_ACTIVITY_DATE, 
    rcrapp4_ext.RCRAPP4_FEE_WAIVER_IND, 
    rcrapp4_ext.RCRAPP4_WRK_STDY_LOANS_INT, 
    rcrapp4_ext.RCRAPP4_AP_FLAG, 
    rcrapp4_ext.RCRAPP4_PAR1_OCCUPATION, 
    rcrapp4_ext.RCRAPP4_PAR1_EMPLOYER, 
    rcrapp4_ext.RCRAPP4_PAR2_OCCUPATION, 
    rcrapp4_ext.RCRAPP4_PAR2_EMPLOYER, 
    rcrapp4_ext.RCRAPP4_ADOPTED_AFTER_13, 
    rcrapp4_ext.RCRAPP4_HOMELESS, 
    rcrapp4_ext.RCRAPP4_COMBAT_PAY, 
    rcrapp4_ext.RCRAPP4_PAR_NUM_BUSINESSES, 
    rcrapp4_ext.RCRAPP4_PAR_NUM_FARMS, 
    rcrapp4_ext.RCRAPP4_PAR_COMBAT_PAY, 
    rcrapp4_ext.RCRAPP4_IM_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_EFM_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_FM_FORMULA_FOR_CALC, 
    rcrapp4_ext.RCRAPP4_PARENT_TYPE_1, 
    rcrapp4_ext.RCRAPP4_PARENT_TYPE_2, 
    rcrapp4_ext.rcrapp4_dod_match_flag, 
    rcrapp4_ext.rcrapp4_dod_par_death_date --, 
--    rcrapp4_ext.rcrapp4_high_school_name --, 
--    rcrapp4_ext.rcrapp4_high_school_city --, 
--    rcrapp4_ext.rcrapp4_stat_code_high_sch--, 
--    rcrapp4_ext.rcrapp4_high_school_cde --, 
--    rcrapp4_ext.rcrapp4_high_school_flg --, 
    --rcrapp4_ext.rcrapp4_p_asset_thresh_excd --, 
    --rcrapp4_ext.rcrapp4_s_asset_thresh_excd    
    from rcrapp4@aimsmgr201040 rcrapp4_ext;

    commit;
  end p_import_rcrapp4;

  procedure p_import_rcrlds4 as
  begin
    execute immediate('truncate table rcrlds4_ext');

    insert into rcrlds4_ext(
    rcrlds4_ext.RCRLDS4_AIDY_CODE, 
    rcrlds4_ext.RCRLDS4_PIDM, 
    rcrlds4_ext.RCRLDS4_INFC_CODE, 
    rcrlds4_ext.RCRLDS4_SEQ_NO, 
    rcrlds4_ext.RCRLDS4_CURR_REC_IND, 
    rcrlds4_ext.RCRLDS4_CREATOR_ID, 
    rcrlds4_ext.RCRLDS4_USER_ID, 
    rcrlds4_ext.RCRLDS4_CREATE_DATE, 
    rcrlds4_ext.RCRLDS4_ACTIVITY_DATE, 
    rcrlds4_ext.RCRLDS4_NSLDS_DB_RESULT_FLAG, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_OUT_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_UNSUB_OUT_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_OUT_PRIN_BAL_2, 
    rcrlds4_ext.RCRLDS4_PERK_CUMULATIVE_AMT, 
    rcrlds4_ext.RCRLDS4_PERK_CURRENT_YR_AMT, 
    rcrlds4_ext.RCRLDS4_PERK_1ST_DISB_FLAG_1, 
    rcrlds4_ext.RCRLDS4_PERK_1ST_DISB_FLAG_2, 
    rcrlds4_ext.RCRLDS4_PERK_EXP_LOAN_FLAG, 
    rcrlds4_ext.RCRLDS4_PELL_OVER_PAY_FLAG, 
    rcrlds4_ext.RCRLDS4_SEOG_OVER_PAY_FLAG, 
    rcrlds4_ext.RCRLDS4_PERK_DEFAULT_FLAG, 
    rcrlds4_ext.RCRLDS4_ACT_BANKRUPT_FLAG, 
    rcrlds4_ext.RCRLDS4_PROC_DATE, 
    rcrlds4_ext.RCRLDS4_MATCH_IND, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UNSUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_CONSOL_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UNSUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_CONSOL_TOTAL, 
    rcrlds4_ext.RCRLDS4_TRAN_NO, 
    rcrlds4_ext.RCRLDS4_PELL_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_SEOG_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_PERK_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_PERK_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_DEFAULTED_LOAN_FLG, 
    rcrlds4_ext.RCRLDS4_DISCHARG_LOAN_FLG, 
    rcrlds4_ext.RCRLDS4_LOAN_SAT_REPAY_FLG, 
    rcrlds4_ext.RCRLDS4_DEF_LOAN_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_DISCHARG_LOAN_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_LOAN_SAT_REPAY_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ACT_BANKRUPT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_OVER_PAY_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_PERK_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_PELL_PMT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_PELL_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_LOANS_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_COMB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_COMB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_COMB_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_POST_SCREEN_RSN_CDE, 
    rcrlds4_ext.RCRLDS4_DL_MPN_FLAG, 
    rcrlds4_ext.RCRLDS4_FFEL_MPN_FLAG, 
    rcrlds4_ext.RCRLDS4_FFEL_LEND_CODE, 
    rcrlds4_ext.RCRLDS4_LOAN_CHG_FLAG, 
    rcrlds4_ext.RCRLDS4_CURRENT_SSN, 
    rcrlds4_ext.RCRLDS4_CURRENT_FIRST_NAME, 
    rcrlds4_ext.RCRLDS4_CURRENT_LAST_NAME, 
    rcrlds4_ext.RCRLDS4_CURRENT_BIRTH_DATE, 
    rcrlds4_ext.RCRLDS4_DL_PLUS_MPN_FLAG, 
    rcrlds4_ext.RCRLDS4_POST_SCREEN_RSN_CDE_2, 
    rcrlds4_ext.RCRLDS4_POST_SCREEN_RSN_CDE_3, 
    rcrlds4_ext.RCRLDS4_AGT_OVER_LIMIT_SUB, 
    rcrlds4_ext.RCRLDS4_AGT_OVER_LIMIT_COMB, 
    rcrlds4_ext.RCRLDS4_AGT_FFEL_CONS_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_PLUS_OUT_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_FFEL_CONS_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_PLUS_TOTAL, 
    rcrlds4_ext.RCRLDS4_ACG_PMT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_SMART_PMT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_ACG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_SMART_FLG, 
    rcrlds4_ext.RCRLDS4_DL_GR_PLUS_MPN_FLG, 
    rcrlds4_ext.RCRLDS4_ACG_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_ACG_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_SMART_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_SMART_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_FRAUD_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_GR_PLUS_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_PLUS_TOTAL, 
    rcrlds4_ext.RCRLDS4_FRAUD_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_GR_AGT_OVER_LIMIT_SUB, 
    rcrlds4_ext.RCRLDS4_GR_AGT_OVER_LIMIT_COMB, 
    rcrlds4_ext.RCRLDS4_TEACH_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_TEACH_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_TEACH_LOAN_CONV_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_TEACH_LN_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_TEACH_LOAN_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_TEACH_UG_DISB_AMT, 
    rcrlds4_ext.RCRLDS4_AGT_TEACH_GR_DISB_AMT, 
    rcrlds4_ext.RCRLDS4_TEACH_LN_CONV_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_TEACH_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_TEACH_FLG, 
    rcrlds4_ext.RCRLDS4_UG_PERCENT_ELIG_USED, 
    rcrlds4_ext.RCRLDS4_UG_REMAINING_AMOUNT, 
    rcrlds4_ext.RCRLDS4_GR_PERCENT_ELIG_USED, 
    rcrlds4_ext.RCRLDS4_GR_REMAINING_AMOUNT, 
    rcrlds4_ext.RCRLDS4_AGT_TCH_GRANT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_TCH_LOAN_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_UG_SUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNSUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_COMB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNAL_CONSOL_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_SUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNSUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_COMB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_SUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNSUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UG_COMB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNAL_CONS_TOTAL, 
    rcrlds4_ext.RCRLDS4_UG_AWARD_YEAR, 
    rcrlds4_ext.RCRLDS4_UG_DEPEND_STATUS, 
    rcrlds4_ext.RCRLDS4_AGT_GR_SUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNSUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_COMB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNAL_CONSOL_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_SUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNSUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_COMB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_SUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNSUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_COMB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNAL_CONS_TOTAL, 
    rcrlds4_ext.RCRLDS4_GR_AWARD_YEAR, 
    rcrlds4_ext.rcrlds4_gr_depend_status, 
    rcrlds4_ext.rcrlds4_pell_leu --, 
--    rcrlds4_ext.rcrlds4_iasg_over_pay_flg --, 
--    rcrlds4_ext.rcrlds4_iasg_over_pay_contact
    )
    select
    rcrlds4_ext.RCRLDS4_AIDY_CODE, 
    rcrlds4_ext.RCRLDS4_PIDM, 
    rcrlds4_ext.RCRLDS4_INFC_CODE, 
    rcrlds4_ext.RCRLDS4_SEQ_NO, 
    rcrlds4_ext.RCRLDS4_CURR_REC_IND, 
    rcrlds4_ext.RCRLDS4_CREATOR_ID, 
    rcrlds4_ext.RCRLDS4_USER_ID, 
    rcrlds4_ext.RCRLDS4_CREATE_DATE, 
    rcrlds4_ext.RCRLDS4_ACTIVITY_DATE, 
    rcrlds4_ext.RCRLDS4_NSLDS_DB_RESULT_FLAG, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_OUT_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_UNSUB_OUT_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_OUT_PRIN_BAL_2, 
    rcrlds4_ext.RCRLDS4_PERK_CUMULATIVE_AMT, 
    rcrlds4_ext.RCRLDS4_PERK_CURRENT_YR_AMT, 
    rcrlds4_ext.RCRLDS4_PERK_1ST_DISB_FLAG_1, 
    rcrlds4_ext.RCRLDS4_PERK_1ST_DISB_FLAG_2, 
    rcrlds4_ext.RCRLDS4_PERK_EXP_LOAN_FLAG, 
    rcrlds4_ext.RCRLDS4_PELL_OVER_PAY_FLAG, 
    rcrlds4_ext.RCRLDS4_SEOG_OVER_PAY_FLAG, 
    rcrlds4_ext.RCRLDS4_PERK_DEFAULT_FLAG, 
    rcrlds4_ext.RCRLDS4_ACT_BANKRUPT_FLAG, 
    rcrlds4_ext.RCRLDS4_PROC_DATE, 
    rcrlds4_ext.RCRLDS4_MATCH_IND, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UNSUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_CONSOL_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_SUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UNSUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_CONSOL_TOTAL, 
    rcrlds4_ext.RCRLDS4_TRAN_NO, 
    rcrlds4_ext.RCRLDS4_PELL_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_SEOG_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_PERK_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_PERK_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_DEFAULTED_LOAN_FLG, 
    rcrlds4_ext.RCRLDS4_DISCHARG_LOAN_FLG, 
    rcrlds4_ext.RCRLDS4_LOAN_SAT_REPAY_FLG, 
    rcrlds4_ext.RCRLDS4_DEF_LOAN_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_DISCHARG_LOAN_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_LOAN_SAT_REPAY_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ACT_BANKRUPT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_OVER_PAY_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_PERK_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_PELL_PMT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_PELL_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_LOANS_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_COMB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_COMB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_COMB_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_POST_SCREEN_RSN_CDE, 
    rcrlds4_ext.RCRLDS4_DL_MPN_FLAG, 
    rcrlds4_ext.RCRLDS4_FFEL_MPN_FLAG, 
    rcrlds4_ext.RCRLDS4_FFEL_LEND_CODE, 
    rcrlds4_ext.RCRLDS4_LOAN_CHG_FLAG, 
    rcrlds4_ext.RCRLDS4_CURRENT_SSN, 
    rcrlds4_ext.RCRLDS4_CURRENT_FIRST_NAME, 
    rcrlds4_ext.RCRLDS4_CURRENT_LAST_NAME, 
    rcrlds4_ext.RCRLDS4_CURRENT_BIRTH_DATE, 
    rcrlds4_ext.RCRLDS4_DL_PLUS_MPN_FLAG, 
    rcrlds4_ext.RCRLDS4_POST_SCREEN_RSN_CDE_2, 
    rcrlds4_ext.RCRLDS4_POST_SCREEN_RSN_CDE_3, 
    rcrlds4_ext.RCRLDS4_AGT_OVER_LIMIT_SUB, 
    rcrlds4_ext.RCRLDS4_AGT_OVER_LIMIT_COMB, 
    rcrlds4_ext.RCRLDS4_AGT_FFEL_CONS_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_PLUS_OUT_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_FFEL_CONS_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_PLUS_TOTAL, 
    rcrlds4_ext.RCRLDS4_ACG_PMT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_SMART_PMT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_ACG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_SMART_FLG, 
    rcrlds4_ext.RCRLDS4_DL_GR_PLUS_MPN_FLG, 
    rcrlds4_ext.RCRLDS4_ACG_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_ACG_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_SMART_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_SMART_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_FRAUD_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_GR_PLUS_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_PLUS_TOTAL, 
    rcrlds4_ext.RCRLDS4_FRAUD_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_GR_AGT_OVER_LIMIT_SUB, 
    rcrlds4_ext.RCRLDS4_GR_AGT_OVER_LIMIT_COMB, 
    rcrlds4_ext.RCRLDS4_TEACH_OVER_PAY_FLG, 
    rcrlds4_ext.RCRLDS4_TEACH_OVER_PAY_CONTACT, 
    rcrlds4_ext.RCRLDS4_TEACH_LOAN_CONV_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_TEACH_LN_PRIN_BAL, 
    rcrlds4_ext.RCRLDS4_TEACH_LOAN_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_TEACH_UG_DISB_AMT, 
    rcrlds4_ext.RCRLDS4_AGT_TEACH_GR_DISB_AMT, 
    rcrlds4_ext.RCRLDS4_TEACH_LN_CONV_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_TEACH_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_ADDL_TEACH_FLG, 
    rcrlds4_ext.RCRLDS4_UG_PERCENT_ELIG_USED, 
    rcrlds4_ext.RCRLDS4_UG_REMAINING_AMOUNT, 
    rcrlds4_ext.RCRLDS4_GR_PERCENT_ELIG_USED, 
    rcrlds4_ext.RCRLDS4_GR_REMAINING_AMOUNT, 
    rcrlds4_ext.RCRLDS4_AGT_TCH_GRANT_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_TCH_LOAN_CHG_FLG, 
    rcrlds4_ext.RCRLDS4_AGT_UG_SUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNSUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_COMB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNAL_CONSOL_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_SUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNSUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_COMB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_UG_SUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNSUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UG_COMB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_UG_UNAL_CONS_TOTAL, 
    rcrlds4_ext.RCRLDS4_UG_AWARD_YEAR, 
    rcrlds4_ext.RCRLDS4_UG_DEPEND_STATUS, 
    rcrlds4_ext.RCRLDS4_AGT_GR_SUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNSUB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_COMB_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNAL_CONSOL_OPB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_SUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNSUB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_COMB_PEND_DISB, 
    rcrlds4_ext.RCRLDS4_AGT_GR_SUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNSUB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_COMB_TOTAL, 
    rcrlds4_ext.RCRLDS4_AGT_GR_UNAL_CONS_TOTAL, 
    rcrlds4_ext.RCRLDS4_GR_AWARD_YEAR, 
    rcrlds4_ext.rcrlds4_gr_depend_status, 
    rcrlds4_ext.rcrlds4_pell_leu --, 
--    rcrlds4_ext.rcrlds4_iasg_over_pay_flg --, 
--    rcrlds4_ext.rcrlds4_iasg_over_pay_contact
    from rcrlds4@aimsmgr201040 rcrlds4_ext;

    commit;
  end p_import_rcrlds4;

  procedure p_import_rorstat as
  begin
    execute immediate('truncate table rorstat_ext');

    insert into rorstat_ext(
    rorstat_ext.RORSTAT_AIDY_CODE, 
    rorstat_ext.RORSTAT_PIDM, 
    rorstat_ext.RORSTAT_LOCK_IND, 
    rorstat_ext.RORSTAT_PGRP_LOCK_IND, 
    rorstat_ext.RORSTAT_BGRP_PROC_IND, 
    rorstat_ext.RORSTAT_PGRP_PROC_IND, 
    rorstat_ext.RORSTAT_TGRP_PROC_IND, 
    rorstat_ext.RORSTAT_PCKG_FUND_PROC_IND, 
    rorstat_ext.RORSTAT_VER_COMPLETE, 
    rorstat_ext.RORSTAT_USER_ID, 
    rorstat_ext.RORSTAT_ACTIVITY_DATE, 
    rorstat_ext.RORSTAT_VER_PAY_IND, 
    rorstat_ext.RORSTAT_BGRP_CODE, 
    rorstat_ext.RORSTAT_APRD_CODE, 
    rorstat_ext.RORSTAT_PGRP_CODE, 
    rorstat_ext.RORSTAT_TGRP_CODE, 
    rorstat_ext.RORSTAT_RECALC_NA_IND, 
    rorstat_ext.RORSTAT_PC, 
    rorstat_ext.RORSTAT_SC, 
    rorstat_ext.RORSTAT_APPL_RCVD_DATE, 
    rorstat_ext.RORSTAT_PCKG_FUND_DATE, 
    rorstat_ext.RORSTAT_NEED_DATE, 
    rorstat_ext.RORSTAT_PCKG_COMP_DATE, 
    rorstat_ext.RORSTAT_ALL_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_MEMO_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_PCKG_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_DISB_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_PELL_SCHED_AWD, 
    rorstat_ext.RORSTAT_PRI_SAR_PGI, 
    rorstat_ext.RORSTAT_SEC_SAR_PGI, 
    rorstat_ext.RORSTAT_SAR_DATE, 
    rorstat_ext.RORSTAT_SAR_TRAN_NO, 
    rorstat_ext.RORSTAT_SAR_SSN, 
    rorstat_ext.RORSTAT_SAR_INIT, 
    rorstat_ext.RORSTAT_CM_SC_LOCK_IND, 
    rorstat_ext.RORSTAT_CM_PC_LOCK_IND, 
    rorstat_ext.RORSTAT_CM_TFC_LOCK_IND, 
    rorstat_ext.RORSTAT_PGI_LOCK_IND, 
    rorstat_ext.RORSTAT_INST_SC_LOCK_IND, 
    rorstat_ext.RORSTAT_INST_PC_LOCK_IND, 
    rorstat_ext.RORSTAT_GRS_NEED, 
    rorstat_ext.RORSTAT_UNMET_NEED, 
    rorstat_ext.RORSTAT_TFC, 
    rorstat_ext.RORSTAT_AWD_LTR_IND, 
    rorstat_ext.RORSTAT_RESOURCE_AMT, 
    rorstat_ext.RORSTAT_RESOURCE_ACT_DATE, 
    rorstat_ext.RORSTAT_TRK_LTR_IND, 
    rorstat_ext.RORSTAT_IM_GRS_NEED, 
    rorstat_ext.RORSTAT_IM_UNMET_NEED, 
    rorstat_ext.RORSTAT_IM_TFC, 
    rorstat_ext.RORSTAT_ADDL_STFD_ELIG_IND, 
    rorstat_ext.RORSTAT_IM_LOCK, 
    rorstat_ext.RORSTAT_FM_BATCH_LOCK, 
    rorstat_ext.RORSTAT_IM_BATCH_LOCK, 
    rorstat_ext.RORSTAT_NSLDS_OVRD_IND, 
    rorstat_ext.RORSTAT_FORMER_HEAL_IND, 
    rorstat_ext.RORSTAT_PELL_LT_HALF_COA, 
    rorstat_ext.RORSTAT_INFO_ACCESS_IND, 
    rorstat_ext.RORSTAT_BORROWER_BASED_CDE, 
    rorstat_ext.RORSTAT_PELL_ORIG_IND, 
    rorstat_ext.RORSTAT_PELL_DISB_LOCK_IND, 
    rorstat_ext.RORSTAT_POST_BA_PELL_OVRD, 
    rorstat_ext.RORSTAT_INFC_CODE, 
    rorstat_ext.RORSTAT_ALTERNATE_PELL_IND, 
    rorstat_ext.RORSTAT_PELL_ATTEND_COST, 
    rorstat_ext.RORSTAT_LOW_TUITION_COST, 
    rorstat_ext.RORSTAT_SUB_LOAN_EXCL_AMT, 
    rorstat_ext.RORSTAT_TGRP_CODE_LOCK_IND, 
    rorstat_ext.RORSTAT_BGRP_CODE_LOCK_IND, 
    rorstat_ext.RORSTAT_PGRP_CODE_LOCK_IND, 
    rorstat_ext.RORSTAT_TRK_LTR_IND_DATE, 
    rorstat_ext.RORSTAT_TURN_OFF_PELL_IND, 
    rorstat_ext.RORSTAT_VER_USER_ID, 
    rorstat_ext.RORSTAT_VER_DATE, 
    rorstat_ext.RORSTAT_DATA_ORIGIN, 
    rorstat_ext.RORSTAT_PREP_OR_TEACH_IND, 
    rorstat_ext.RORSTAT_ACG_DISB_LOCK_IND, 
    rorstat_ext.RORSTAT_SMART_DISB_LOCK_IND, 
    rorstat_ext.RORSTAT_AUTO_ZERO_EFC_IND, 
    rorstat_ext.RORSTAT_ADDL_PELL_ELIG_IND, 
    rorstat_ext.rorstat_dep_no_par_data, 
    rorstat_ext.rorstat_aprd_code_pell --, 
--    rorstat_ext.rorstat_post_911_pell_elig --, 
--    rorstat_ext.rorstat_pbud_info_access_ind
    )
    select
    rorstat_ext.RORSTAT_AIDY_CODE, 
    rorstat_ext.RORSTAT_PIDM, 
    rorstat_ext.RORSTAT_LOCK_IND, 
    rorstat_ext.RORSTAT_PGRP_LOCK_IND, 
    rorstat_ext.RORSTAT_BGRP_PROC_IND, 
    rorstat_ext.RORSTAT_PGRP_PROC_IND, 
    rorstat_ext.RORSTAT_TGRP_PROC_IND, 
    rorstat_ext.RORSTAT_PCKG_FUND_PROC_IND, 
    rorstat_ext.RORSTAT_VER_COMPLETE, 
    rorstat_ext.RORSTAT_USER_ID, 
    rorstat_ext.RORSTAT_ACTIVITY_DATE, 
    rorstat_ext.RORSTAT_VER_PAY_IND, 
    rorstat_ext.RORSTAT_BGRP_CODE, 
    rorstat_ext.RORSTAT_APRD_CODE, 
    rorstat_ext.RORSTAT_PGRP_CODE, 
    rorstat_ext.RORSTAT_TGRP_CODE, 
    rorstat_ext.RORSTAT_RECALC_NA_IND, 
    rorstat_ext.RORSTAT_PC, 
    rorstat_ext.RORSTAT_SC, 
    rorstat_ext.RORSTAT_APPL_RCVD_DATE, 
    rorstat_ext.RORSTAT_PCKG_FUND_DATE, 
    rorstat_ext.RORSTAT_NEED_DATE, 
    rorstat_ext.RORSTAT_PCKG_COMP_DATE, 
    rorstat_ext.RORSTAT_ALL_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_MEMO_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_PCKG_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_DISB_REQ_COMP_DATE, 
    rorstat_ext.RORSTAT_PELL_SCHED_AWD, 
    rorstat_ext.RORSTAT_PRI_SAR_PGI, 
    rorstat_ext.RORSTAT_SEC_SAR_PGI, 
    rorstat_ext.RORSTAT_SAR_DATE, 
    rorstat_ext.RORSTAT_SAR_TRAN_NO, 
    rorstat_ext.RORSTAT_SAR_SSN, 
    rorstat_ext.RORSTAT_SAR_INIT, 
    rorstat_ext.RORSTAT_CM_SC_LOCK_IND, 
    rorstat_ext.RORSTAT_CM_PC_LOCK_IND, 
    rorstat_ext.RORSTAT_CM_TFC_LOCK_IND, 
    rorstat_ext.RORSTAT_PGI_LOCK_IND, 
    rorstat_ext.RORSTAT_INST_SC_LOCK_IND, 
    rorstat_ext.RORSTAT_INST_PC_LOCK_IND, 
    rorstat_ext.RORSTAT_GRS_NEED, 
    rorstat_ext.RORSTAT_UNMET_NEED, 
    rorstat_ext.RORSTAT_TFC, 
    rorstat_ext.RORSTAT_AWD_LTR_IND, 
    rorstat_ext.RORSTAT_RESOURCE_AMT, 
    rorstat_ext.RORSTAT_RESOURCE_ACT_DATE, 
    rorstat_ext.RORSTAT_TRK_LTR_IND, 
    rorstat_ext.RORSTAT_IM_GRS_NEED, 
    rorstat_ext.RORSTAT_IM_UNMET_NEED, 
    rorstat_ext.RORSTAT_IM_TFC, 
    rorstat_ext.RORSTAT_ADDL_STFD_ELIG_IND, 
    rorstat_ext.RORSTAT_IM_LOCK, 
    rorstat_ext.RORSTAT_FM_BATCH_LOCK, 
    rorstat_ext.RORSTAT_IM_BATCH_LOCK, 
    rorstat_ext.RORSTAT_NSLDS_OVRD_IND, 
    rorstat_ext.RORSTAT_FORMER_HEAL_IND, 
    rorstat_ext.RORSTAT_PELL_LT_HALF_COA, 
    rorstat_ext.RORSTAT_INFO_ACCESS_IND, 
    rorstat_ext.RORSTAT_BORROWER_BASED_CDE, 
    rorstat_ext.RORSTAT_PELL_ORIG_IND, 
    rorstat_ext.RORSTAT_PELL_DISB_LOCK_IND, 
    rorstat_ext.RORSTAT_POST_BA_PELL_OVRD, 
    rorstat_ext.RORSTAT_INFC_CODE, 
    rorstat_ext.RORSTAT_ALTERNATE_PELL_IND, 
    rorstat_ext.RORSTAT_PELL_ATTEND_COST, 
    rorstat_ext.RORSTAT_LOW_TUITION_COST, 
    rorstat_ext.RORSTAT_SUB_LOAN_EXCL_AMT, 
    rorstat_ext.RORSTAT_TGRP_CODE_LOCK_IND, 
    rorstat_ext.RORSTAT_BGRP_CODE_LOCK_IND, 
    rorstat_ext.RORSTAT_PGRP_CODE_LOCK_IND, 
    rorstat_ext.RORSTAT_TRK_LTR_IND_DATE, 
    rorstat_ext.RORSTAT_TURN_OFF_PELL_IND, 
    rorstat_ext.RORSTAT_VER_USER_ID, 
    rorstat_ext.RORSTAT_VER_DATE, 
    rorstat_ext.RORSTAT_DATA_ORIGIN, 
    rorstat_ext.RORSTAT_PREP_OR_TEACH_IND, 
    rorstat_ext.RORSTAT_ACG_DISB_LOCK_IND, 
    rorstat_ext.RORSTAT_SMART_DISB_LOCK_IND, 
    rorstat_ext.RORSTAT_AUTO_ZERO_EFC_IND, 
    rorstat_ext.RORSTAT_ADDL_PELL_ELIG_IND, 
    rorstat_ext.rorstat_dep_no_par_data, 
    rorstat_ext.rorstat_aprd_code_pell --, 
--    rorstat_ext.rorstat_post_911_pell_elig --, 
--    rorstat_ext.rorstat_pbud_info_access_ind
    from rorstat@aimsmgr201040 rorstat_ext;

    commit;
  end p_import_rorstat;

  procedure p_import_gtvsdax as
  begin
    execute immediate('truncate table gtvsdax_ext');

    insert into gtvsdax_ext(
    gtvsdax_ext.GTVSDAX_EXTERNAL_CODE,
    gtvsdax_ext.GTVSDAX_INTERNAL_CODE,
    gtvsdax_ext.GTVSDAX_REPORTING_DATE,
    gtvsdax_ext.GTVSDAX_TRANSLATION_CODE,
    gtvsdax_ext.GTVSDAX_INTERNAL_CODE_SEQNO,
    gtvsdax_ext.GTVSDAX_INTERNAL_CODE_GROUP,
    gtvsdax_ext.GTVSDAX_DESC,
    gtvsdax_ext.GTVSDAX_SYSREQ_IND,
    gtvsdax_ext.gtvsdax_activity_date
    )
    select
    gtvsdax_ext.GTVSDAX_EXTERNAL_CODE,
    gtvsdax_ext.GTVSDAX_INTERNAL_CODE,
    gtvsdax_ext.GTVSDAX_REPORTING_DATE,
    gtvsdax_ext.GTVSDAX_TRANSLATION_CODE,
    gtvsdax_ext.GTVSDAX_INTERNAL_CODE_SEQNO,
    gtvsdax_ext.GTVSDAX_INTERNAL_CODE_GROUP,
    gtvsdax_ext.GTVSDAX_DESC,
    gtvsdax_ext.GTVSDAX_SYSREQ_IND,
    gtvsdax_ext.gtvsdax_activity_date
    from gtvsdax@aimsmgr201040 gtvsdax_ext;

    commit;
  end p_import_gtvsdax;

  procedure p_import_spbpers_add
  as

  begin
    execute immediate('truncate table spbpers_ext');

    insert into spbpers_ext(
    spbpers_ext.SPBPERS_PIDM,
    spbpers_ext.SPBPERS_SSN,
    spbpers_ext.SPBPERS_BIRTH_DATE,
    spbpers_ext.SPBPERS_LGCY_CODE,
    spbpers_ext.SPBPERS_ETHN_CODE,
    spbpers_ext.SPBPERS_MRTL_CODE,
    spbpers_ext.SPBPERS_RELG_CODE,
    spbpers_ext.SPBPERS_SEX,
    spbpers_ext.SPBPERS_CONFID_IND,
    spbpers_ext.SPBPERS_DEAD_IND,
    spbpers_ext.SPBPERS_VETC_FILE_NUMBER,
    spbpers_ext.SPBPERS_LEGAL_NAME,
    spbpers_ext.spbpers_pref_first_name,
    spbpers_ext.SPBPERS_NAME_PREFIX,
    spbpers_ext.SPBPERS_NAME_SUFFIX,
    spbpers_ext.SPBPERS_ACTIVITY_DATE,
    spbpers_ext.SPBPERS_VERA_IND,
    spbpers_ext.SPBPERS_CITZ_IND,
    spbpers_ext.SPBPERS_DEAD_DATE,
    spbpers_ext.SPBPERS_PIN,
    spbpers_ext.SPBPERS_CITZ_CODE,
    spbpers_ext.SPBPERS_HAIR_CODE,
    spbpers_ext.SPBPERS_EYES_CODE,
    spbpers_ext.SPBPERS_CITY_BIRTH,
    spbpers_ext.SPBPERS_STAT_CODE_BIRTH,
    spbpers_ext.SPBPERS_DRIVER_LICENSE,
    spbpers_ext.SPBPERS_STAT_CODE_DRIVER,
    spbpers_ext.SPBPERS_NATN_CODE_DRIVER,
    spbpers_ext.SPBPERS_UOMS_CODE_HEIGHT,
    spbpers_ext.SPBPERS_HEIGHT,
    spbpers_ext.SPBPERS_UOMS_CODE_WEIGHT,
    spbpers_ext.SPBPERS_WEIGHT,
    spbpers_ext.SPBPERS_SDVET_IND,
    spbpers_ext.SPBPERS_LICENSE_ISSUED_DATE,
    spbpers_ext.SPBPERS_LICENSE_EXPIRES_DATE,
    spbpers_ext.SPBPERS_INCAR_IND,
    spbpers_ext.SPBPERS_WEBID,
    spbpers_ext.SPBPERS_WEB_LAST_ACCESS,
    spbpers_ext.SPBPERS_PIN_DISABLED_IND,
    spbpers_ext.SPBPERS_ITIN,
    spbpers_ext.SPBPERS_ACTIVE_DUTY_SEPR_DATE,
    spbpers_ext.SPBPERS_DATA_ORIGIN,
    spbpers_ext.SPBPERS_USER_ID,
    spbpers_ext.SPBPERS_ETHN_CDE,
    spbpers_ext.SPBPERS_CONFIRMED_RE_CDE,
    spbpers_ext.SPBPERS_CONFIRMED_RE_DATE,
    spbpers_ext.veteran_1,
    spbpers_ext.veteran_benefits_elegible_1)
    select
    spbpers_add.SPBPERS_PIDM,
    spbpers_add.SPBPERS_SSN,
    spbpers_add.SPBPERS_BIRTH_DATE,
    spbpers_add.SPBPERS_LGCY_CODE,
    spbpers_add.SPBPERS_ETHN_CODE,
    spbpers_add.SPBPERS_MRTL_CODE,
    spbpers_add.SPBPERS_RELG_CODE,
    spbpers_add.SPBPERS_SEX,
    spbpers_add.SPBPERS_CONFID_IND,
    spbpers_add.SPBPERS_DEAD_IND,
    spbpers_add.SPBPERS_VETC_FILE_NUMBER,
    spbpers_add.SPBPERS_LEGAL_NAME,
    spbpers_add.spbpers_pref_first_name,
    spbpers_add.SPBPERS_NAME_PREFIX,
    spbpers_add.SPBPERS_NAME_SUFFIX,
    spbpers_add.SPBPERS_ACTIVITY_DATE,
    spbpers_add.SPBPERS_VERA_IND,
    spbpers_add.SPBPERS_CITZ_IND,
    spbpers_add.SPBPERS_DEAD_DATE,
    spbpers_add.SPBPERS_PIN,
    spbpers_add.SPBPERS_CITZ_CODE,
    spbpers_add.SPBPERS_HAIR_CODE,
    spbpers_add.SPBPERS_EYES_CODE,
    spbpers_add.SPBPERS_CITY_BIRTH,
    spbpers_add.SPBPERS_STAT_CODE_BIRTH,
    spbpers_add.SPBPERS_DRIVER_LICENSE,
    spbpers_add.SPBPERS_STAT_CODE_DRIVER,
    spbpers_add.SPBPERS_NATN_CODE_DRIVER,
    spbpers_add.SPBPERS_UOMS_CODE_HEIGHT,
    spbpers_add.SPBPERS_HEIGHT,
    spbpers_add.SPBPERS_UOMS_CODE_WEIGHT,
    spbpers_add.SPBPERS_WEIGHT,
    spbpers_add.SPBPERS_SDVET_IND,
    spbpers_add.SPBPERS_LICENSE_ISSUED_DATE,
    spbpers_add.SPBPERS_LICENSE_EXPIRES_DATE,
    spbpers_add.SPBPERS_INCAR_IND,
    spbpers_add.SPBPERS_WEBID,
    spbpers_add.SPBPERS_WEB_LAST_ACCESS,
    spbpers_add.SPBPERS_PIN_DISABLED_IND,
    spbpers_add.SPBPERS_ITIN,
    spbpers_add.SPBPERS_ACTIVE_DUTY_SEPR_DATE,
    spbpers_add.SPBPERS_DATA_ORIGIN,
    spbpers_add.SPBPERS_USER_ID,
    spbpers_add.SPBPERS_ETHN_CDE,
    spbpers_add.SPBPERS_CONFIRMED_RE_CDE,
    spbpers_add.SPBPERS_CONFIRMED_RE_DATE,
    spbpers_add.veteran_1,
    spbpers_add.veteran_benefits_elegible_1
    from spbpers_add@aimsmgr201040;

    commit;
  end;

  procedure p_import_sibinst
  as
  begin
    execute immediate('truncate table sibinst_ext');

    insert into sibinst_ext(
    sibinst_ext.sibinst_pidm,
    sibinst_ext.sibinst_term_code_eff,
    sibinst_ext.sibinst_fcst_code,
    sibinst_ext.sibinst_fctg_code,
    sibinst_ext.sibinst_fstp_code,
    sibinst_ext.sibinst_fcnt_code,
    sibinst_ext.sibinst_schd_ind,
    sibinst_ext.sibinst_advr_ind,
    sibinst_ext.sibinst_fcst_date,
    sibinst_ext.sibinst_wkld_code,
    sibinst_ext.sibinst_cntr_code,
    sibinst_ext.sibinst_appoint_date,
    sibinst_ext.sibinst_activity_date,
    sibinst_ext.sibinst_data_origin,
    sibinst_ext.sibinst_user_id)
    select
    sibinst.sibinst_pidm,
    sibinst.sibinst_term_code_eff,
    sibinst.sibinst_fcst_code,
    sibinst.sibinst_fctg_code,
    sibinst.sibinst_fstp_code,
    sibinst.sibinst_fcnt_code,
    sibinst.sibinst_schd_ind,
    sibinst.sibinst_advr_ind,
    sibinst.sibinst_fcst_date,
    sibinst.sibinst_wkld_code,
    sibinst.sibinst_cntr_code,
    sibinst.sibinst_appoint_date,
    sibinst.sibinst_activity_date,
    sibinst.sibinst_data_origin,
    sibinst.sibinst_user_id
    from sibinst@aimsmgr201040;

    commit;

  end;


  procedure p_fix_majors(term_code_in varchar2)as

  type ref_type is ref cursor;
  ref_cur ref_type;

  sd_pidm_rec td_student_data.sd_pidm%type;
  sd_term_code_rec varchar2(60);
  sgbstdn_majr_code_1_rec varchar2(20);
  majr_desc varchar2(60);

  stmt_string varchar2(2048) := null;

  begin
    stmt_string := 
    'select ' ||
    'td_student_data.sd_pidm, ' ||
    'td_student_data.sd_term_code, ' ||
  --  'primary_major_1, ' ||
    'sgbstdn_join.sgbstdn_majr_code_1, ' ||
    '(select stvmajr.stvmajr_desc from stvmajr_ext stvmajr where stvmajr.stvmajr_code = sgbstdn_join.sgbstdn_majr_code_1) majr_desc ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_1_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_2_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_1, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_121, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_122, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_1_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_1_3, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_221, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_222, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_223, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_2_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_2_3, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_minr_1, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_minr_1_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_2, ' ||
  --  'sgbstdn_join.sgbstdn_majr_code_conc_2_2 ' ||
    'from td_student_data ' ||
    'inner join sgbstdn@aimsmgr' || term_code_in || ' sgbstdn_join  ' ||
    '      on sgbstdn_join.sgbstdn_pidm = sd_pidm ' ||
    '      and sgbstdn_join.sgbstdn_term_code_eff = (select max(s2.sgbstdn_term_code_eff) ' ||
    '                                                from sgbstdn@aimsmgr' || term_code_in || ' s2 ' ||
    '                                                where s2.sgbstdn_pidm = sgbstdn_join.sgbstdn_pidm ' ||
    '                                                and s2.sgbstdn_term_code_eff <= ''' || term_code_in || ''') ' ||
    'where td_student_data.sd_term_code = ''' || term_code_in || ''' ' ||
    'and primary_major_1 is null' ;

    open ref_cur for stmt_string; 
    loop
--      fetch ref_cur into row_rec;
      fetch ref_cur into sd_pidm_rec, sd_term_code_rec, sgbstdn_majr_code_1_rec, majr_desc;
      exit when ref_cur%notfound;

      update td_student_data set
      td_student_data.primary_major_1 = sgbstdn_majr_code_1_rec,
      td_student_data.primary_major_1_desc = majr_desc
      where td_student_data.sd_pidm = sd_pidm_rec
      and td_student_data.sd_term_code = sd_term_code_rec;

    end loop;

    commit;
  end p_fix_majors;



end imprpts;
/
