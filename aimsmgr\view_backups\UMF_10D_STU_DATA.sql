--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View UMF_10D_STU_DATA
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "AIMSMGR"."UMF_10D_STU_DATA" ("PIDM_KEY", "TERM_CODE_KEY", "TERM_DESC", "ENROLLED_IND", "REGISTERED_IND", "TERM_CODE_LAST_ATTENDED", "GRADUATED_IND", "ENROLLMENT_ADD_DATE", "BIRTH_DATE", "AGE", "GENDER", "ETHN_CODE", "DECEASED_IND", "DECEASED_DATE", "CONFIDENTIALITY_IND", "FULL_PART_TIME_IND", "CLAS_CODE", "STST_CODE", "LEVL_CODE", "STYP_CODE", "ORIG_STYP_CODE", "TERM_CODE_ADMIT", "CAMP_CODE", "RESD_CODE", "RATE_CODE", "ADMT_CODE", "COLL_CODE", "DEGC_CODE", "MAJR_CODE", "MAJR_CODE_1_2", "MAJR_CODE_MINOR_1", "MAJR_CODE_MINOR_1_2", "MAJR_CODE_CONCENTRATION_1", "MAJR_CODE_CONCENTRATION_1_2", "LEVL_CODE_2", "COLL_CODE_2", "DEGC_CODE_2", "MAJR_CODE_2", "MAJR_CODE_2_2", "MAJR_CODE_MINOR_2", "MAJR_CODE_MINOR_2_2", "MAJR_CODE_CONCENTRATION_2", "MAJR_CODE_CONCENTRATION_2_2", "MAJR_CODE_CONCENTRATION_121", "MAJR_CODE_CONCENTRATION_122", "MAJR_CODE_CONCENTRATION_221", "MAJR_CODE_CONCENTRATION_222", "PROGRAM_1", "PROGRAM_2", "TERM_CODE_INTENDED_GRADUATION", "INTENDED_GRADUATION_DATE", "ADVISOR_LAST_NAME1", "ADVISOR_FIRST_NAME1", "HOLD_CODE1", "HOLD_REASON1", "HOLD_CODE2", "HOLD_REASON2", "HOLD_CODE3", "HOLD_REASON3", "HOLD_CODE4", "HOLD_REASON4", "HOLD_CODE5", "HOLD_REASON5", "ADDITIONAL_HOLDS_IND", "AR_OLDEST_EFFECTIVE_DATE", "EGOL_CODE", "RACE_CODE1", "RACE_CODE2", "RACE_CODE3", "RACE_CODE4", "RACE_CODE5", "RACE_CODE_COUNT", "ETHN_CDE", "ETHN_CDE_DESC", "CITZ_CODE", "ONLINE_COURSES_ONLY", "SITE_CODE", "FULL_PART_TIME_IND_UMF", "REPORT_ETHNICITY") AS 
  select "PIDM_KEY","TERM_CODE_KEY","TERM_DESC","ENROLLED_IND","REGISTERED_IND","TERM_CODE_LAST_ATTENDED","GRADUATED_IND","ENROLLMENT_ADD_DATE","BIRTH_DATE","AGE","GENDER","ETHN_CODE","DECEASED_IND","DECEASED_DATE","CONFIDENTIALITY_IND","FULL_PART_TIME_IND","CLAS_CODE","STST_CODE","LEVL_CODE","STYP_CODE","ORIG_STYP_CODE","TERM_CODE_ADMIT","CAMP_CODE","RESD_CODE","RATE_CODE","ADMT_CODE","COLL_CODE","DEGC_CODE","MAJR_CODE","MAJR_CODE_1_2","MAJR_CODE_MINOR_1","MAJR_CODE_MINOR_1_2","MAJR_CODE_CONCENTRATION_1","MAJR_CODE_CONCENTRATION_1_2","LEVL_CODE_2","COLL_CODE_2","DEGC_CODE_2","MAJR_CODE_2","MAJR_CODE_2_2","MAJR_CODE_MINOR_2","MAJR_CODE_MINOR_2_2","MAJR_CODE_CONCENTRATION_2","MAJR_CODE_CONCENTRATION_2_2","MAJR_CODE_CONCENTRATION_121","MAJR_CODE_CONCENTRATION_122","MAJR_CODE_CONCENTRATION_221","MAJR_CODE_CONCENTRATION_222","PROGRAM_1","PROGRAM_2","TERM_CODE_INTENDED_GRADUATION","INTENDED_GRADUATION_DATE","ADVISOR_LAST_NAME1","ADVISOR_FIRST_NAME1","HOLD_CODE1","HOLD_REASON1","HOLD_CODE2","HOLD_REASON2","HOLD_CODE3","HOLD_REASON3","HOLD_CODE4","HOLD_REASON4","HOLD_CODE5","HOLD_REASON5","ADDITIONAL_HOLDS_IND","AR_OLDEST_EFFECTIVE_DATE","EGOL_CODE","RACE_CODE1","RACE_CODE2","RACE_CODE3","RACE_CODE4","RACE_CODE5","RACE_CODE_COUNT","ETHN_CDE","ETHN_CDE_DESC","CITZ_CODE","ONLINE_COURSES_ONLY","SITE_CODE","FULL_PART_TIME_IND_UMF","REPORT_ETHNICITY"
from <EMAIL>
;
