with pop_sel as(
  select
  zrbrday_run_date run_date,
  zrbrday_term_code term_code,
  count(*) head_count
  from zrbrday
  group by 
  zrbrday_run_date,
  zrbrday_term_code
  order by 1 desc
)
select
pop_sel.run_date,
pop_sel.term_code,
pop_sel.head_count,
pop_last_year.run_date run_date_ly,
pop_last_year.term_code term_code_ly,
pop_last_year.head_count head_count_ly,
pop_two_year.run_date run_date_2y,
pop_two_year.term_code term_code_2y,
pop_two_year.head_count head_count_2y
from pop_sel
left outer join pop_sel pop_last_year on pop_last_year.run_date = (pop_sel.run_date - 366)
                                      and pop_last_year.term_code = to_char(to_number(pop_sel.term_code) - 100)
left outer join pop_sel pop_two_year on pop_two_year.run_date = (pop_sel.run_date - (366 * 2))
                                     and pop_two_year.term_code = to_char(to_number(pop_sel.term_code) - 200)
where pop_sel.term_code = '201720'
and pop_sel.run_date = trunc(sysdate);
