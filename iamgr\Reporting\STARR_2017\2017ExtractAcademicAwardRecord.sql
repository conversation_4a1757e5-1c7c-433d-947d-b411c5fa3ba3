select
spriden.spriden_id as "LocalStudentId",

case
  when shrdgmr_join.stvdegc_acat_code = '22' then '2'
  when shrdgmr_join.stvdegc_acat_code = '24' then '5'
  when shrdgmr_join.stvdegc_acat_code = '42' then '7'
  when shrdgmr_join.stvdegc_acat_code = '43' then '8'
  when shrdgmr_join.stvdegc_acat_code = '44' then '18'
  else ''
end as "AcademicAwardLevel",

to_char(shrdgmr_join.shrdgmr_grad_date, 'YYYY-MM-DD') as "AcademicAwardDate",

shrdgmr_join.stvdegc_desc as "AcademicAwardTitle",

substr(shrdgmr_join.stvmajr_cipc_code, 1, 2) || '.' ||
substr(shrdgmr_join.stvmajr_cipc_code, 3, 6) as "DegreeCIPCode"

from spriden
-- start term
inner join(
  select 
  max(stvterm.stvterm_code) as start_term_code
  from stvterm
  where stvterm.stvterm_code = '201630'
) start_term on start_term.start_term_code <> '999999'
-- end term
inner join(
  select max(stvterm.stvterm_code) as end_term_code
  from stvterm
  where stvterm.stvterm_code = '201740'
) end_term on end_term.end_term_code <> '999999'
-- has to have a UID
inner join(
  select 
  goradid.goradid_pidm,
  goradid.goradid_additional_id
  from goradid
  where goradid.goradid_adid_code = 'UIC'
  and goradid.goradid_additional_id is not null
  and goradid.goradid_additional_id != '9175170611' -- this one has two uics in banner
) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
-- spbpers on not dead
inner join(
  select
  spbpers.spbpers_pidm
  from spbpers
  where spbpers.spbpers_dead_ind is null
)spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
-- has an awarded degree
inner join(
  select
  shrdgmr.shrdgmr_pidm,
  shrdgmr.shrdgmr_grad_date,
  stvdegc_join.stvdegc_desc,
  stvdegc_join.stvdegc_acat_code,
  stvmajr_join.stvmajr_cipc_code,
  shrlgpa_join.shrlgpa_gpa
  from shrdgmr
  inner join(
    select
    shrlgpa.shrlgpa_pidm,
    shrlgpa.shrlgpa_levl_code,
    shrlgpa.shrlgpa_gpa
    from shrlgpa
    where shrlgpa.shrlgpa_gpa_type_ind = 'O'
  ) shrlgpa_join on shrlgpa_join.shrlgpa_pidm = shrdgmr.shrdgmr_pidm
                 and shrlgpa_join.shrlgpa_levl_code = shrdgmr.shrdgmr_levl_code
  left outer join(
    select
    stvdegc.stvdegc_code,
    stvdegc.stvdegc_desc,
    stvdegc.stvdegc_acat_code
    from stvdegc
  ) stvdegc_join on stvdegc_join.stvdegc_code = shrdgmr.shrdgmr_degc_code
  left outer join(
    select
    stvmajr.stvmajr_code,
    stvmajr.stvmajr_cipc_code
    from stvmajr
  ) stvmajr_join on stvmajr_join.stvmajr_code = shrdgmr.shrdgmr_majr_code_1
  where shrdgmr.shrdgmr_degs_code = 'AW'
) shrdgmr_join on shrdgmr_join.shrdgmr_pidm = spriden.spriden_pidm

where spriden.spriden_change_ind is null
and exists (select
            'has course work in their primary level in these terms'
            from shrtgpa
            inner join(
              select
              sgbstdn.sgbstdn_pidm,
              sgbstdn.sgbstdn_term_code_eff,
              sgbstdn.sgbstdn_levl_code
              from sgbstdn
            ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                           and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                     from sgbstdn sgbstdn_1
                                                                     where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                     and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                           and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
            where shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
            and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
            and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
--and spriden.spriden_pidm in (75786)
;

--select *
--from shrdgmr
--where shrdgmr_pidm = 75786

