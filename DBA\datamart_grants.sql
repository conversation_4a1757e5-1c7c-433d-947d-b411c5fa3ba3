/*******************************************************************************
Schema and Datamart Grants: admmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER admmgr IDENTIFIED BY l337advising';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO admmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO admmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO admmgr';
--EXECUTE IMMEDIATE 'ALTER USER admmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO admmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO admmgr';
----EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO admmgr';
----EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO admmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO admmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO admmgr';
----EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO admmgr';
--EXECUTE IMMEDIATE 'alter user admmgr profile topgun';
--p_dm_dba_grant_select_table ('admmgr');
----P_DM_DBA_GRANT_SELECT_TABLE_EXT ('admmgr');
----P_DM_DBA_GRANT_SELECT_VIEW ('admmgr');
--END;
--
--grant select on goremal_ext to admmgr;
--grant select on sabsupl_ext to admmgr;
--grant select on saradap_ext to admmgr;
--grant select on sarappd_ext to admmgr;
--grant select on sarchkl_ext to admmgr;
--grant select on sfrstcr_ext to admmgr;
--grant select on sgradvr_ext to admmgr;
--grant select on sgrsatt_ext to admmgr;
--grant select on shrtgpa_ext to admmgr;
--grant select on shrlgpa_ext to admmgr;
--grant select on shrtrit_ext to admmgr;
--grant select on shrtram to admmgr;
--grant select on shrtrcr_ext to admmgr;
--grant select on shrtatt_ext to admmgr;
--grant select on sorhsch_ext to admmgr;
--grant select on sorlcur_ext to admmgr;
--grant select on sorlfos_ext to admmgr;
--grant select on sprhold_ext to admmgr;
--grant select on spriden_ext to admmgr;
--grant select on sprtele_ext to admmgr;


/*******************************************************************************
Schema and Datamart Grants: abetmgr
--Create user  and grant create table, and tablespace
*******************************************************************************/

--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER abetmgr IDENTIFIED BY L337Acr3d1da710n';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO abetmgr';
--EXECUTE IMMEDIATE 'ALTER USER abetmgr DEFAULT ROLE';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO abetmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO abetmgr';
--EXECUTE IMMEDIATE 'alter user abetmgr profile topgun';
--EXECUTE IMMEDIATE 'grant select on um_student_data to abetmgr';
--p_dm_dba_grant_select_table ('abetmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('abetmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('abetmgr');
--END;
--alter USER abetmgr IDENTIFIED BY lms4eva;

/*******************************************************************************
Schema and Datamart Grants: canvasmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER canvasmgr IDENTIFIED BY lms4eva';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO canvasmgr';
--EXECUTE IMMEDIATE 'ALTER USER canvasmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO canvasmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO canvasmgr';
--EXECUTE IMMEDIATE 'alter user canvasmgr profile topgun';
--p_dm_dba_grant_select_table ('canvasmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('canvasmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('canvasmgr');
--END;
--alter USER canvasmgr IDENTIFIED BY lms4eva;

/*******************************************************************************
Datamart Grants for AA Business Objects Schema: CHSMGR 
*******************************************************************************/
--CREATE USER CHSMGR IDENTIFIED BY GoBlue050923;
--GRANT CREATE TABLE TO CHSMGR;
--GRANT CREATE VIEW TO CHSMGR;
--ALTER USER CHSMGR QUOTA 100M ON development;
--GRANT UNLIMITED TABLESPACE TO CHSMGR;
--ALTER USER CHSMGR DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO CHSMGR;
--GRANT CREATE PROCEDURE TO CHSMGR;
--GRANT CREATE ANY DIRECTORY TO CHSMGR;
--ALTER USER CHSMGR PROFILE topgun;
--
--BEGIN
--P_DM_DBA_GRANT_SELECT_TABLE ('CHSMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('CHSMGR');
--END;

/*******************************************************************************
Datamart Grants for AA Business Objects Schema: MJRRETMGR 
*******************************************************************************/
--CREATE USER MJRRETMGR IDENTIFIED BY GoBlue020525;
--GRANT CREATE TABLE TO MJRRETMGR;
--GRANT CREATE VIEW TO MJRRETMGR;
--ALTER USER MJRRETMGR QUOTA 100M ON development;
--GRANT UNLIMITED TABLESPACE TO MJRRETMGR;
--ALTER USER MJRRETMGR DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO MJRRETMGR;
--GRANT CREATE PROCEDURE TO MJRRETMGR;
--GRANT CREATE ANY DIRECTORY TO MJRRETMGR;
--ALTER USER MJRRETMGR PROFILE topgun;
--
--BEGIN
--P_DM_DBA_GRANT_SELECT_TABLE ('MJRRETMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('MJRRETMGR');
--END;

/*******************************************************************************
Datamart Grants for AA Business Objects Schema: bo_flint_student 
*******************************************************************************/
--CREATE USER bo_flint_student IDENTIFIED BY GoBlue042321;
--GRANT
-- CREATE TABLE
--TO bo_flint_student;
--GRANT
-- CREATE VIEW
--TO bo_flint_student;
--ALTER USER bo_flint_student
-- QUOTA 100M ON development;
--GRANT
-- UNLIMITED TABLESPACE
--TO bo_flint_student;
--ALTER USER bo_flint_student DEFAULT ROLE ALL;
--GRANT
-- CREATE SESSION
--TO bo_flint_student;
--GRANT
-- CREATE PROCEDURE
--TO bo_flint_student;
--GRANT
-- CREATE ANY DIRECTORY
--TO bo_flint_student;
--ALTER USER bo_flint_student
-- PROFILE topgun;
--GRANT SELECT ON td_admissions_applicant TO bo_flint_student;
--GRANT SELECT ON td_catalog_schedule TO bo_flint_student;
--GRANT SELECT ON td_course_credits TO bo_flint_student;
--GRANT SELECT ON td_demographic TO bo_flint_student;
--GRANT SELECT ON td_registration_detail TO bo_flint_student;
--GRANT SELECT ON td_student_data TO bo_flint_student;
--GRANT SELECT ON td_term_summary TO bo_flint_student;
--GRANT SELECT ON td_test_score TO bo_flint_student;
--GRANT SELECT ON um_admissions_applicant TO bo_flint_student;
--GRANT SELECT ON um_admissions_daily TO bo_flint_student;
--GRANT SELECT ON um_catalog TO bo_flint_student;
--GRANT SELECT ON um_catalog_schedule TO bo_flint_student;
--GRANT SELECT ON um_current_term TO bo_flint_student; --
--GRANT SELECT ON um_degree TO bo_flint_student;
--GRANT SELECT ON um_demographic TO bo_flint_student;
--revoke select ON um_finaid_by_term to bo_flint_student;
--grant select on um_reg_course_daily to bo_flint_student;
--grant select on um_reg_course_section_daily to bo_flint_student;
--grant select on um_reg_student_daily to bo_flint_student;
--grant select on um_reg_student_daily_ia to bo_flint_student;
--grant select on um_registration_detail to bo_flint_student;
--grant select on um_student_data to bo_flint_student;
--grant select on um_student_transcript to bo_flint_student;
--grant select on um_test_score to bo_flint_student;
--grant select on check_admissions_applicant to bo_flint_student;
--grant select on check_demographic to bo_flint_student;
--grant select on check_student_data to bo_flint_student;
--grant select on DM_LOAD to bo_flint_student;
--grant select on DM_EXTR to bo_flint_student;
--grant select on DM_TRAN to bo_flint_student;
--grant select on DM_EXTRACT_TABLE_NAMES to bo_flint_student;
--GRANT EXECUTE ON aimsmgr.ZDMCLEN TO bo_flint_student;
--BEGIN
--P_DM_DBA_GRANT_SELECT_TABLE ('bo_flint_student');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('bo_flint_student');
--P_DM_DBA_GRANT_SELECT_VIEW ('bo_flint_student');
--END;
--GRANT SELECT ON AIMSMGR.UMV_CEREMONY TO BO_FLINT_STUDENT;
--DESC UMV_CEREMONY;
--grant select on UGMGR.FTIAC_DOMESTIC_MI to bo_flint_student;
--grant select on UGMGR.FTIAC_DOMESTIC_OUT_STATE to bo_flint_student;
--grant select on UGMGR.FTIAC_INTERNATIONAL to bo_flint_student;
--grant select on UGMGR.FTIAC_PROMISE_SCHOLAR to bo_flint_student;
--grant select on UGMGR.DOMESTIC_TRANSFER to bo_flint_student;
--grant select on UGMGR.INTERNATIONAL_GRADUATE to bo_flint_student;
--grant select on UGMGR.DOMESTIC_GRADUATE to bo_flint_student;
--grant select on aimsmgr.shrtrat to bo_flint_student;

--grant select on famgr.rprawrd_ext to bo_flint_student;
--grant select on um_test_score to bo_flint_student ;
--grant select on um_admissions_applicant to bo_flint_student;

--grant select on rorstat to bo_flint_student;
--grant select on rcrapp1 to bo_flint_student;
--grant select on rcrapp2 to bo_flint_student;
--grant select on rprawrd to bo_flint_student;
--grant select on glbextr to bo_flint_student;
--grant select on rrrareq to bo_flint_student;
--grant select on szvcstu to bo_flint_student;
--grant select on stvterm to bo_flint_student;


/*******************************************************************************
Datamart Grants for AA Business Objects Schema: HEERFMGR 
*******************************************************************************/
--CREATE USER HEERFMGR IDENTIFIED BY GoBlue041423;
--GRANT CREATE TABLE TO HEERFMGR;
--GRANT CREATE VIEW TO HEERFMGR;
--ALTER USER HEERFMGR QUOTA 100M ON development;
--GRANT UNLIMITED TABLESPACE TO HEERFMGR;
--ALTER USER HEERFMGR DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO HEERFMGR;
--GRANT CREATE PROCEDURE TO HEERFMGR;
--GRANT CREATE ANY DIRECTORY TO HEERFMGR;
--ALTER USER HEERFMGR PROFILE topgun;
-- 
--BEGIN
--P_DM_DBA_GRANT_SELECT_TABLE ('HEERFMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('HEERFMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('HEERFMGR');
--
--END;

/*******************************************************************************
Schema and Datamart Grants: targetxmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--p_dm_dba_grant_select_table ('webfocus');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('webfocus');
--P_DM_DBA_GRANT_SELECT_VIEW ('webfocus');
--grant DEBUG on ZDMSTDD TO TARGETXMGR;
--grant DEBUG on ZDMDEMO TO TARGETXMGR;
--grant DEBUG on ZDMADMA TO TARGETXMGR;
--grant DEBUG on ZDMDEGR TO TARGETXMGR;
--grant DEBUG on ZDMHOUS TO TARGETXMGR;
--grant DEBUG on ZDMREGD TO TARGETXMGR;
--grant DEBUG on ZDMSTDT TO TARGETXMGR;
--END;

/*******************************************************************************
Datamart Grants for AA Business Objects Schema: bo_flint_fa 
*******************************************************************************/
--CREATE USER bo_flint_fa IDENTIFIED BY GoBlue072821;
--GRANT CREATE TABLE TO bo_flint_fa;
--GRANT CREATE VIEW TO bo_flint_fa;
--ALTER USER bo_flint_fa QUOTA 100M ON DEVELOPMENT ;
--GRANT UNLIMITED TABLESPACE TO bo_flint_fa;
--ALTER USER bo_flint_fa DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO bo_flint_fa;
--GRANT CREATE PROCEDURE TO bo_flint_fa;
--GRANT CREATE ANY DIRECTORY TO bo_flint_fa;
--alter user bo_flint_fa profile topgun;

--grant select on td_admissions_applicant to bo_flint_student;
--grant select on td_catalog_schedule to bo_flint_student;
--grant select on td_course_credits to bo_flint_student;
--grant select on td_demographic to bo_flint_student;
--grant select on td_registration_detail to bo_flint_student;
--
--grant select on td_student_data to bo_flint_student;
--grant select on td_term_summary to bo_flint_student;
--grant select on td_test_score to bo_flint_student;
--
--grant select on um_admissions_applicant to bo_flint_student;
--grant select on um_admissions_daily to bo_flint_student;
--grant select on um_catalog to bo_flint_student;
--grant select on um_catalog_schedule to bo_flint_student;
--grant select on um_current_term to bo_flint_student; --
--grant select on um_degree to bo_flint_student;
--grant select on um_demographic to bo_flint_student;
--revoke select on um_finaid_by_term to bo_flint_student;
--grant select on um_reg_course_daily to bo_flint_student;
--grant select on um_reg_course_section_daily to bo_flint_student;
--grant select on um_reg_student_daily to bo_flint_student;
--grant select on um_reg_student_daily_ia to bo_flint_student;
--grant select on um_registration_detail to bo_flint_student;
--grant select on um_student_data to bo_flint_student;
--grant select on um_student_transcript to bo_flint_student;
--grant select on um_test_score to bo_flint_student;
--
--grant select on check_admissions_applicant to bo_flint_student;
--grant select on check_demographic to bo_flint_student;
--grant select on check_student_data to bo_flint_student;
--
--grant select on DM_LOAD to bo_flint_student;
--grant select on DM_EXTR to bo_flint_student;
--grant select on DM_TRAN to bo_flint_student;
--grant select on DM_EXTRACT_TABLE_NAMES to bo_flint_student;
--GRANT EXECUTE ON aimsmgr.ZDMCLEN TO bo_flint_student;

/*******************************************************************************
Schema and Datamart Grants: CEPMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--CREATE USER cepmgr IDENTIFIED BY GoBlue070621;
--GRANT CREATE TABLE TO cepmgr;
--GRANT CREATE VIEW TO cepmgr;
--ALTER USER cepmgr QUOTA 100M ON DEVELOPMENT;
--GRANT UNLIMITED TABLESPACE TO cepmgr;
--ALTER USER cepmgr DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO cepmgr;
--GRANT CREATE PROCEDURE TO cepmgr;
--GRANT CREATE ANY DIRECTORY TO cepmgr;
--GRANT CREATE MATERIALIZED VIEW TO cepmgr;
--alter user cepmgr profile topgun;
--
--grant select on td_admissions_applicant to cepmgr;
--grant select on td_catalog_schedule to cepmgr;
--grant select on td_course_credits to cepmgr;
--grant select on td_demographic to cepmgr;
--grant select on td_registration_detail to cepmgr;
--grant select on td_student_transcript to cepmgr;
--grant select on td_student_data to cepmgr;
--grant select on td_term_summary to cepmgr;
--grant select on td_test_score to cepmgr;
--grant select on um_admissions_applicant to cepmgr;
--grant select on um_admissions_daily to cepmgr;
--grant select on um_catalog to cepmgr;
--grant select on um_catalog_schedule to cepmgr;
--grant select on um_current_term to cepmgr; --
--grant select on um_degree to cepmgr;
--grant select on um_demographic to cepmgr;
--revoke select on um_finaid_by_term to cepmgr;
--grant select on um_reg_course_daily to cepmgr;
--grant select on um_reg_course_section_daily to cepmgr;
--grant select on um_reg_student_daily to cepmgr;
--grant select on um_reg_student_daily_ia to cepmgr;
--grant select on um_registration_detail to cepmgr;
--grant select on um_student_data to cepmgr;
--grant select on um_student_transcript to cepmgr;
--grant select on um_test_score to cepmgr;
--grant select on stvterm_ext to cepmgr;

/*******************************************************************************
Datamart Grants for AA Business Objects Schema: rbessacmgr 
*******************************************************************************/
--CREATE USER rbessacmgr IDENTIFIED BY GoBlue072821;
--GRANT CREATE TABLE TO rbessacmgr;
--GRANT CREATE VIEW TO rbessacmgr;
--ALTER USER rbessacmgr QUOTA 100M ON DEVELOPMENT ;
--GRANT UNLIMITED TABLESPACE TO rbessacmgr;
--ALTER USER rbessacmgr DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO rbessacmgr;
--GRANT CREATE PROCEDURE TO rbessacmgr;
--REVOKE CREATE ANY DIRECTORY FROM rbessacmgr;
--alter user rbessacmgr profile topgun;
--grant DEBUG CONNECT SESSION to rbessacmgr;
----grant debug any procedure to rbessacmgr;
--grant select on check_admissions_applicant to rbessacmgr;
--grant select on check_demographic to rbessacmgr;
--grant select on check_student_data to rbessacmgr;

/*******************************************************************************
Schema and Datamart Grants: CONTMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--CREATE USER contmgr IDENTIFIED BY GoBlue091621;
--GRANT CREATE TABLE TO contmgr;
--GRANT CREATE VIEW TO contmgr;
--ALTER USER contmgr QUOTA 100M ON DEVELOPMENT;
--GRANT UNLIMITED TABLESPACE TO contmgr;
--ALTER USER contmgr DEFAULT ROLE ALL;
--GRANT CREATE SESSION TO contmgr;
--GRANT CREATE PROCEDURE TO contmgr;
--GRANT CREATE ANY DIRECTORY TO contmgr;
--GRANT CREATE MATERIALIZED VIEW TO contmgr;
--GRANT CREATE JOB TO contmgr;
--GRANT MANAGE SCHEDULER TO contmgr;
--alter user contmgr profile topgun;
--grant select on td_admissions_applicant to contmgr;
--grant select on td_catalog_schedule to contmgr;
--grant select on td_course_credits to contmgr;
--grant select on td_demographic to contmgr with grant option;
--grant select on td_registration_detail to contmgr;
--grant select on td_student_data to contmgr with grant option;
--grant select on td_term_summary to contmgr;
--grant select on td_test_score to contmgr;
--grant select on um_admissions_applicant to contmgr;
--grant select on um_admissions_daily to contmgr;
--grant select on um_catalog to contmgr;
--grant select on um_catalog_schedule to contmgr;
--grant select on um_current_term to contmgr; --
--grant select on um_degree to contmgr;
--grant select on um_demographic to contmgr with grant option;
--grant select on um_reg_course_daily to contmgr;
--grant select on um_reg_course_section_daily to contmgr;
--grant select on um_reg_student_daily to contmgr;
--grant select on um_reg_student_daily_ia to contmgr;
--grant select on um_registration_detail to contmgr;
--grant select on um_student_data to contmgr;
--grant select on um_student_transcript to contmgr;
--grant select on um_test_score to contmgr;
--grant select on stvterm_ext to contmgr with grant option;
--grant select on co_pop_undup to ugmgr;

/*******************************************************************************
Schema and Datamart Grants: FAMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER famgr IDENTIFIED BY password';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO famgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO famgr';
--EXECUTE IMMEDIATE 'ALTER USER famgr QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO famgr';
--EXECUTE IMMEDIATE 'ALTER USER famgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO famgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO famgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO famgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO famgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO famgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO famgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO famgr';
--EXECUTE IMMEDIATE 'alter user famgr profile topgun';
--p_dm_dba_grant_select_table ('famgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('famgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('famgr');
--END;
--BEGIN
--EXECUTE IMMEDIATE 'GRANT SELECT ON UM_CASHIER_BALANCE TO FAMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ON UM_CASHIER_BALANCE TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ON UM_CASHIER_BALANCE TO IAMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ON UM_CASHIER_TRANSACTION TO FAMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ON UM_CASHIER_TRANSACTION TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ON UM_CASHIER_TRANSACTION TO IAMGR';
--END;

--grant select on um_demographic to famgr with grant option;
--grant select on um_student_data to famgr with grant option;
--grant select on aimsmgr.gerattd to famgr with grant option;
--grant select on aimsmgr.UM_SCHL_APPLICANTS to famgr with grant option;
--grant select on aimsmgr.SGRSCMT to famgr with grant option;
--grant select on aimsmgr.UM_FINAID_EFC to famgr with grant option;
--grant select on aimsmgr.UM_SCHL_APPLICANTS to famgr with grant option;
--grant select on aimsmgr.UM_SCHL_AWARD_ELIGIBILTY to famgr with grant option;
--grant select on aimsmgr.UM_ADMISSIONS_APPLICANT to famgr with grant option;
--grant select on um_test_score to famgr with grant option;
--grant select on um_admissions_applicant to famgr with grant option;
--grant select on um_current_term to famgr with grant option;
--grant select on um_student_data to famgr with grant option;
--grant select on td_student_data to famgr with grant option;



/*******************************************************************************
Schema and Datamart Grants: targetxmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER targetxmgr IDENTIFIED BY password';
--EXECUTE IMMEDIATE 'GRANT CONNECT TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT SELECT_CATALOG_ROLE TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO targetxmgr';
--EXECUTE IMMEDIATE 'ALTER USER targetxmgr QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO targetxmgr';
--EXECUTE IMMEDIATE 'ALTER USER targetxmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO targetxmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO targetxmgr';
--EXECUTE IMMEDIATE 'alter user targetxmgr profile topgun';
--p_dm_dba_grant_select_table ('targetxmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('targetxmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('targetxmgr');
--END;


/*******************************************************************************
Schema and Datamart Grants: CAMPUSLABSMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER CAMPUSLABSMGR IDENTIFIED BY password';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'ALTER USER CAMPUSLABSMGR QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'ALTER USER CAMPUSLABSMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO CAMPUSLABSMGR';
--EXECUTE IMMEDIATE 'alter user CAMPUSLABSMGR profile topgun';
--p_dm_dba_grant_select_table ('CAMPUSLABSMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('CAMPUSLABSMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('CAMPUSLABSMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: accomidatemgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER accomidatemgr IDENTIFIED BY password';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO accomidatemgr';
--EXECUTE IMMEDIATE 'ALTER USER accomidatemgr QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO accomidatemgr';
--EXECUTE IMMEDIATE 'ALTER USER accomidatemgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO accomidatemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO accomidatemgr';
--EXECUTE IMMEDIATE 'alter user accomidatemgr profile topgun';
--p_dm_dba_grant_select_table ('accomidatemgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('accomidatemgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('accomidatemgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: ADIRONMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER ADIRONMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO ADIRONMGR';
--EXECUTE IMMEDIATE 'ALTER USER ADIRONMGR QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO ADIRONMGR';
--EXECUTE IMMEDIATE 'ALTER USER ADIRONMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO ADIRONMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO ADIRONMGR';
--EXECUTE IMMEDIATE 'alter user ADIRONMGR profile topgun';
--p_dm_dba_grant_select_table ('ADIRONMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('ADIRONMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('ADIRONMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: HEDAMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER HEDAMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO HEDAMGR';
--EXECUTE IMMEDIATE 'ALTER USER HEDAMGR QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO HEDAMGR';
--EXECUTE IMMEDIATE 'ALTER USER HEDAMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO HEDAMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO HEDAMGR';
--EXECUTE IMMEDIATE 'alter user HEDAMGR profile topgun';
--p_dm_dba_grant_select_table ('HEDAMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('HEDAMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('HEDAMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: HRLMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER HRLMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO HRLMGR';
--EXECUTE IMMEDIATE 'ALTER USER HRLMGR QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO HRLMGR';
--EXECUTE IMMEDIATE 'ALTER USER HRLMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO HRLMGR';
--EXECUTE IMMEDIATE 'alter user HRLMGR profile topgun';
--p_dm_dba_grant_select_table ('HRLMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('HRLMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('HRLMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: HRLMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER HRLMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO HRLMGR';
----EXECUTE IMMEDIATE 'ALTER USER HRLMGR QUOTA 100M ON DEVELOPMENT';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO HRLMGR';
--EXECUTE IMMEDIATE 'ALTER USER HRLMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO HRLMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO HRLMGR';
--EXECUTE IMMEDIATE 'alter user HRLMGR profile topgun';
--p_dm_dba_grant_select_table ('HRLMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('HRLMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('HRLMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: HRMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER HRMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO HRMGR';
--EXECUTE IMMEDIATE 'ALTER USER HRMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO HRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO HRMGR';
--EXECUTE IMMEDIATE 'alter user HRMGR profile topgun';
--p_dm_dba_grant_select_table ('HRMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('HRMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('HRMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: NPSASMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER NPSASMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO NPSASMGR';
--EXECUTE IMMEDIATE 'ALTER USER NPSASMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO NPSASMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO NPSASMGR';
--EXECUTE IMMEDIATE 'alter user NPSASMGR profile topgun';
--p_dm_dba_grant_select_table ('NPSASMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('NPSASMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('NPSASMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: RECMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER RECMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO RECMGR';
--EXECUTE IMMEDIATE 'ALTER USER RECMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO RECMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO RECMGR';
--EXECUTE IMMEDIATE 'alter user RECMGR profile topgun';
--p_dm_dba_grant_select_table ('RECMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('RECMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('RECMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: SILMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER SILMGR IDENTIFIED BY GoBlue022522';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO SILMGR';
--EXECUTE IMMEDIATE 'ALTER USER SILMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO SILMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO SILMGR';
--EXECUTE IMMEDIATE 'alter user SILMGR profile topgun';
--p_dm_dba_grant_select_table ('SILMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('SILMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('SILMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: capsmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER capsmgr IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO capsmgr';
--EXECUTE IMMEDIATE 'ALTER USER capsmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO capsmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO capsmgr';
--EXECUTE IMMEDIATE 'alter user capsmgr profile topgun';
--p_dm_dba_grant_select_table ('capsmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('capsmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('capsmgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: ORIENTMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER ORIENTMGR IDENTIFIED BY orient2017';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO ORIENTMGR';
--EXECUTE IMMEDIATE 'ALTER USER ORIENTMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO ORIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO ORIENTMGR';
--EXECUTE IMMEDIATE 'alter user ORIENTMGR profile topgun';
--p_dm_dba_grant_select_table ('ORIENTMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('ORIENTMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('ORIENTMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: fsbmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER fsbmgr IDENTIFIED BY "Tota!!y_t^p_$eCret"';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO fsbmgr';
--EXECUTE IMMEDIATE 'ALTER USER fsbmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO fsbmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO fsbmgr';
--EXECUTE IMMEDIATE 'alter user fsbmgr profile topgun';
--p_dm_dba_grant_select_table ('fsbmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('fsbmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('fsbmgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: INOWMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER INOWMGR IDENTIFIED BY password';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO INOWMGR';
--EXECUTE IMMEDIATE 'ALTER USER INOWMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO INOWMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO INOWMGR';
--EXECUTE IMMEDIATE 'alter user INOWMGR profile topgun';
--p_dm_dba_grant_select_table ('INOWMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('INOWMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('INOWMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: MAXIENTMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER MAXIENTMGR IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'ALTER USER MAXIENTMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO MAXIENTMGR';
--EXECUTE IMMEDIATE 'alter user MAXIENTMGR profile topgun';
--p_dm_dba_grant_select_table ('MAXIENTMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('MAXIENTMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('MAXIENTMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: umfmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER umfmgr IDENTIFIED BY dim1337';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO umfmgr';
--EXECUTE IMMEDIATE 'ALTER USER umfmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO umfmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO umfmgr';
--EXECUTE IMMEDIATE 'alter user umfmgr profile topgun';
--p_dm_dba_grant_select_table ('umfmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('umfmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('umfmgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: cashmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER cashmgr IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO cashmgr';
--EXECUTE IMMEDIATE 'ALTER USER cashmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO cashmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO cashmgr';
--EXECUTE IMMEDIATE 'alter user cashmgr profile topgun';
--p_dm_dba_grant_select_table ('cashmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('cashmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('cashmgr');
--END;
--
--grant select on um_holds to cashmgr;
--grant select on aimsmgr.at_ar_balance_by_entity to cashmgr;
--grant select on tbraccd_ext to cashmgr;

/*******************************************************************************
Schema and Datamart Grants: tabmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
----EXECUTE IMMEDIATE 'CREATE USER tabmgr IDENTIFIED BY ia4eva';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO tabmgr';
--EXECUTE IMMEDIATE 'ALTER USER tabmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO tabmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO tabmgr';
--EXECUTE IMMEDIATE 'alter user tabmgr profile topgun';
--p_dm_dba_grant_select_table ('tabmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('tabmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('tabmgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: tripmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER tripmgr IDENTIFIED BY umf4eva';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO tripmgr';
--EXECUTE IMMEDIATE 'ALTER USER tripmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO tripmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO tripmgr';
--EXECUTE IMMEDIATE 'alter user tripmgr profile topgun';
--p_dm_dba_grant_select_table ('tripmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('tripmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('tripmgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: talendmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER talendmgr IDENTIFIED BY MaQ1WoTT6GBf';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO talendmgr';
--EXECUTE IMMEDIATE 'ALTER USER talendmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO talendmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO talendmgr';
--EXECUTE IMMEDIATE 'alter user talendmgr profile topgun';
--p_dm_dba_grant_select_table ('talendmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('talendmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('talendmgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: rcaster
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER rcaster IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO rcaster';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO rcaster';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO rcaster';
--EXECUTE IMMEDIATE 'ALTER USER rcaster DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO rcaster';
--EXECUTE IMMEDIATE 'alter user rcaster profile topgun';
--p_dm_dba_grant_select_table ('rcaster');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('rcaster');
--P_DM_DBA_GRANT_SELECT_VIEW ('rcaster');
--END;

/*******************************************************************************
Schema and Datamart Grants: nscmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER nscmgr IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO nscmgr';
--EXECUTE IMMEDIATE 'ALTER USER nscmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO nscmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO nscmgr';
--EXECUTE IMMEDIATE 'alter user nscmgr profile topgun';
--EXECUTE IMMEDIATE 'GRANT CREATE PUBLIC SYNONYM TO nscmgr';
--p_dm_dba_grant_select_table ('nscmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('nscmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('nscmgr');
--END;
--
--GRANT SELECT ON NSC_CONTACT_LIST TO IAMGR WITH GRANT OPTION;
--GRANT SELECT ON NSC_DEGREE TO IAMGR WITH GRANT OPTION;
--GRANT SELECT ON NSC_ENROLLMENT TO IAMGR WITH GRANT OPTION;
--GRANT SELECT ON NSC_STUDENT_HISTORY TO IAMGR WITH GRANT OPTION;
--GRANT SELECT ON NSC_STUDENT_SELECTION TO IAMGR WITH GRANT OPTION;
--GRANT SELECT ON UM_ADMISSIONS_APPLICANT TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON UM_STUDENT_DATA TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON UM_DEGREE TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON UM_DEMOGRAPHIC TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON UM_CURRENT_TERM TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVLEVL TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVSTYP TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVAPST TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVTERM_EXT TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVAPST TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVAPDC TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVDEGC TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVSTST TO NSCMGR WITH GRANT OPTION;
--GRANT SELECT ON STVTERM TO NSCMGR WITH GRANT OPTION;
--
--CREATE PUBLIC SYNONYM NSC_CONTACT_LIST FOR NSCMGR.NSC_CONTACT_LIST;
--CREATE PUBLIC SYNONYM NSC_DEGREE FOR NSCMGR.NSC_DEGREE;
--CREATE PUBLIC SYNONYM NSC_ENROLLMENT FOR NSCMGR.NSC_ENROLLMENT;
--CREATE PUBLIC SYNONYM NSC_STUDENT_HISTORY FOR NSCMGR.NSC_STUDENT_HISTORY;
--CREATE PUBLIC SYNONYM NSC_STUDENT_SELECTION FOR NSCMGR.NSC_STUDENT_SELECTION;

/*******************************************************************************
Schema and Datamart Grants: salesforcemgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER salesforcemgr IDENTIFIED BY GoBlue092921';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO salesforcemgr';
--EXECUTE IMMEDIATE 'ALTER USER salesforcemgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO salesforcemgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO salesforcemgr';
--EXECUTE IMMEDIATE 'alter user salesforcemgr profile topgun';
--p_dm_dba_grant_select_table ('salesforcemgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('salesforcemgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('salesforcemgr');
--END;

/*******************************************************************************
Schema and Datamart Grants: inowmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER inowmgr IDENTIFIED BY "inow4eva!"';
--EXECUTE IMMEDIATE 'GRANT CONNECT TO inowmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO inowmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO inowmgr';
--EXECUTE IMMEDIATE 'ALTER USER inowmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO inowmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PUBLIC SYNONYM TO inowmgr';
--EXECUTE IMMEDIATE 'alter user inowmgr profile topgun';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GERATTD_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GORADID TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GOREMAL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GORPRAC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.INOW_SARRQST TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RCRAPP1 TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RCRAPP2 TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RORSTAT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RRRAREQ TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RTVTREQ TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SABNSTU TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARAATT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARADAP TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARADAP_ADD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARADDR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARAPPD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARCHKL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARCTRL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SAREFOS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARETRY TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARHEAD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARHSCH TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPCOL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPERS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPHON TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPRAC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPRFN TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPSES TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARQUAN TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARRFNO TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARRQST TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCBCRSE TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCBCRSE_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRCORQ TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRCORQ_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRCLS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRCLS_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRCOL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRCOL_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRDEG TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRDEG_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRLVL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRLVL_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRMAJ TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRMAJ_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRPRG TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRPRG_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRTST TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRRTST_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRTEXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SCRTEXT_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SFRSTCR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SGBSTDN TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRDGMR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRTGPA TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRTTRM TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SMRPRLE TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SOBSBGI TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORCCON TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORCMJR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORDEGR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORHSCH TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORPCOL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORTEST TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORTSPC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPBPERS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPBPERS_ADD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRADDR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRHOLD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRHOLD_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRIDEN TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRIDEN_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRTELE TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SSRMEET_EXT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVADMR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVADMT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVAPDC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVAPST TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVATTS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCITZ TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCNTY TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCOLL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHLDD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLANG TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLEVL TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVMAJR TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVNATN TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRESD TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSBGI TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTAT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTYP TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTERM TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTESC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVVTYP TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ADMISSIONS_APPLICANT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CURRENT_TERM TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEMOGRAPHIC TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_HOLDS TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_DATA TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_TRANSCRIPT TO INOWMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_TEST_SCORE TO INOWMGR';
--END;

/*******************************************************************************
Schema and Datamart Grants: UGMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER UGMGR IDENTIFIED BY umug4eva';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO UGMGR';
--EXECUTE IMMEDIATE 'ALTER USER UGMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CONNECT TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ANY DICTIONARY TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE SYNONYM TO UGMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PUBLIC SYNONYM TO UGMGR';
--EXECUTE IMMEDIATE 'ALTER USER UGMGR PROFILE TOPGUN';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.AR_AWARD_DETAIL_BY_YEAR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.EMAS_UNPUSHED_APPS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GERATTD_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GERATTD_ORNT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARAATT_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARADAP_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARADDR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARCTRL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SAREFOS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARETRY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARHEAD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPERS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARPHON TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARQUAN_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SGRSATT_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHBTATC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRICMT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRTATC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRTCMT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRTRAT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SOBPTRM TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SOBPTRM_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SOBSBGI TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SOBSBGI_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORBTAG TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORCCON TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORCMJR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORTEST_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPBPERS_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRHOLD_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRIDEN TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRIDEN_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SSRMEET_ORNT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVADMT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVAPDC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVAPST TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVASCD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVASTD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVATTS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVATTS_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVBLDG TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCALD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCAMP TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCHRT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCITZ TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCLAS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCNTY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCNTY_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCOLL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCSTA TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEPT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVEGOL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVESTS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVGMOD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVGRST TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHAPS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHLDD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHLDD_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVINTS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLANG TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLANG_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLEVL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLEVL_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVMAJR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVMRCD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVNATN TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVPTRM TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRATE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRECR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRELT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRESD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRRCD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRSTS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSAPR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSBGI TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSBGI_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSITE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSSTS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTAT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTST TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTYP TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTYP_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSUBJ TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTERM TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTERM_EXT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTESC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVVETC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVVTYP TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVWRSN TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_ADMISSIONS_APPLICANT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_ADMISSIONS_DAY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_CATALOG_SCHEDULE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_COURSE_CREDITS TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_DEMOGRAPHIC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_REGISTRATION_DETAIL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_STUDENT_DATA TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_TERM_SUMMARY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_TEST_SCORE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UMF_10D_ADM_APP TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UMF_10D_STU_ATTR TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UMF_10D_STU_DATA TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UMF_10D_STU_DATA_NUM TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UMF_10D_STU_IDENTIFICATION TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UMF_10D_STU_REG_DETAIL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ADMISSIONS_APPLICANT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ADMISSIONS_DAILY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CATALOG TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CATALOG_SCHEDULE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CURRENT_TERM TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEGREE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEGREE_OLD TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEMOGRAPHIC TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_FINAID_BY_TERM TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ORIENTATION_DAILY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REGISTRATION_DETAIL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REG_COURSE_SECTION_DAILY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REG_STUDENT_DAILY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_SEM_FUNNEL TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_DATA TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_TRANSCRIPT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_TEST_SCORE TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT DEBUG ON AIMSMGR.ZRBADMS_V2 TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT DEBUG ON AIMSMGR.ZRBEMAS_TODAY TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT DEBUG ON AIMSMGR.ZRBORNT TO UGMGR';
--EXECUTE IMMEDIATE  'GRANT DEBUG ON AIMSMGR.ZRBRDAY TO UGMGR';
--END;

--grant alter database link to ugmgr;
--grant select on emasmgr.um_emas to ugmgr;
--grant select on famgr.fa_award_admissions to ugmgr;
--grant select on famgr.FA_AWARD_ADMISSIONS to ugmgr;
--grant select on aimsmgr.srbadms_v2 to ugmgr;
--grant select on aimsmgr.srbadms_v2 to ugmgr;
--grant select on aimsmgr.sprhold to ugmgr;
--grant select on aimsmgr.goradid to ugmgr;
--grant select on co_pop_undup to ugmgr;
--grant select on aimsmgr.zrbadms_v2 to ugmgr with grant option;
--grant select on aimsmgr.TD_ADMISSIONS_APPLICANT to ugmgr with grant option;
--grant select on aimsmgr.UM_SEM_FUNNEL to ugmgr with grant option;
--grant select on aimsmgr.shrtrat to ugmgr with grant option;
--grant select on aimsmgr.shbtatc to ugmgr with grant option;
--grant select on aimsmgr.shrtcmt to ugmgr with grant option;
--grant select on aimsmgr.sobsbgi to ugmgr with grant option;
--grant select on aimsmgr.sorbtag to ugmgr with grant option;
--grant select on aimsmgr.stvcald to ugmgr with grant option;
--grant select on aimsmgr.shrtatc to ugmgr with grant option;
--grant select on aimsmgr.stvsbgi to ugmgr with grant option;
--
--grant select on aimsmgr.shrlgpa_ext to ugmgr with grant option;
--grant select on aimsmgr.um_admissions_applicant to ugmgr with grant option;
--grant select on aimsmgr.um_demographic to ugmgr with grant option;
--grant select on aimsmgr.sarchkl_ext to ugmgr with grant option;

--
/*******************************************************************************
Schema and Datamart Grants: GRMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER GRMGR IDENTIFIED BY "gr!2014"';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO GRMGR';
--EXECUTE IMMEDIATE 'ALTER USER GRMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CONNECT TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT SELECT ANY DICTIONARY TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE SYNONYM TO GRMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PUBLIC SYNONYM TO GRMGR';
--EXECUTE IMMEDIATE 'alter user GRMGR profile topgun';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GTVFUNC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RCRAPP1_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.RCRAPP2_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SFRSTCR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHRDGMR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORDEGR_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRIDEN_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVADMR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVADMT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVAPDC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVAPST TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVASCD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVASTD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVATTR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVATTS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVATYP TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVBLDG TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCALD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCAMP TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCERT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCHRT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCITZ TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCLAS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCMTT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCNTY TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCOLL TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCOMF TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCOMS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCOMT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCSTA TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCTYP TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVCUDA TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGC_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEGS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVDEPT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVEGOL TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVESTS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVETCT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVETHN TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVFTYP TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVGAST TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVGMOD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVGRST TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHAPS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHLDD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVHONR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVINTS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLANG TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVLEVL TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVMAJR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVMRCD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVNATN TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVORIG TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVPTRM TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRATE TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRECR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRELT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRESD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRESD_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRRCD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVRSTS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSAPR TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSBGI TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSCHD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSITE TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSSTS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTAT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTST TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTYP TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSTYP_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVSUBJ TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTERM TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTERM_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVTESC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVVETC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVVTYP TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVWRSN TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_ADMISSIONS_APPLICANT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_ADMISSIONS_DAY TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_CATALOG_SCHEDULE TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_COURSE_CREDITS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_DEMOGRAPHIC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_REGISTRATION_DETAIL TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_STUDENT_DATA TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_TERM_SUMMARY TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_TEST_SCORE TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ADMISSIONS_APPLICANT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ADM_APPLICATION_DAILY TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CATALOG TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CATALOG_SCHEDULE TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CURRENT_TERM TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEGREE_OLD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEMOGRAPHIC TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_HOLDS TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ORIENTATION_DAILY TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REGISTRATION_DETAIL TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_SEM_FUNNEL TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_DATA TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_TRANSCRIPT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_TEST_SCORE TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.VIEWUMFSUNAPSISADMISSIONS_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.VIEWUMFSUNAPSIS_EXT TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ZRBADMS_TD TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ZRBADMS_V2 TO GRMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ZRBORNT TO GRMGR';
--END;

--BEGIN
--P_DM_DBA_GRANT_SELECT_TABLE ('grmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('grmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('grmgr');
--END;



/*******************************************************************************
Schema and Datamart Grants: SSCMGR
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER SSCMGR IDENTIFIED BY "ssc@dm1n"';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO SSCMGR';
--EXECUTE IMMEDIATE 'ALTER USER SSCMGR DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO SSCMGR';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO SSCMGR';
--EXECUTE IMMEDIATE 'alter user SSCMGR profile topgun';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ADVSWIPE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ADV_EMP TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ADV_EMP_POSITION TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ADV_STUDY_CONN_TRANS TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ADV_TUTOR_LOCATIONS TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.GERATTD TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARAPPD TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARCTRL TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARHEAD TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SARQUAN TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SFRSTCR TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SGRADVR TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SHTTRAN TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SIBINST TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SIRDPCL TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORCONT TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORINTS TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SORTEST TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRCMNT TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.SPRIDEN TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.STVINTS TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TBBDETC TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TBRACCD TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_ADMISSIONS_APPLICANT TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_CATALOG_SCHEDULE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_COURSE_CREDITS TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_DEMOGRAPHIC TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_REGISTRATION_DETAIL TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_STUDENT_DATA TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_TERM_SUMMARY TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.TD_TEST_SCORE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ADMISSIONS_APPLICANT TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CATALOG TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CATALOG_SCHEDULE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_CURRENT_TERM TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEGREE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEGREE_OLD TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_DEMOGRAPHIC TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_HOLDS TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_HOLDS_MV TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_HOLDS_W_BALANCE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_HOUSING TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_ORIENTATION_DAILY TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REGISTRATION_DETAIL TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REG_COURSE_SECTION_DAILY TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_REG_STUDENT_DAILY TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_SEM_FUNNEL TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_DATA TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_STUDENT_TRANSCRIPT TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_TEST_SCORE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.UM_TUITION_REVENUE TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.VETINTENT TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ZRBADMS_V2 TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ZRBRDAY TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON AIMSMGR.ZRBRDTL TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON FAMGR.FA_AWARD_BY_AIDY TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON FAMGR.FA_AWARD_BY_TERM TO SSCMGR';
--EXECUTE IMMEDIATE  'GRANT SELECT ON FAMGR.FA_APPLICANT TO SSCMGR';
--p_dm_dba_grant_select_table ('SSCMGR');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('SSCMGR');
--P_DM_DBA_GRANT_SELECT_VIEW ('SSCMGR');
--END;

/*******************************************************************************
Schema and Datamart Grants: regmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
----EXECUTE IMMEDIATE 'CREATE USER regmgr IDENTIFIED BY reg201510';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO regmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO regmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO regmgr';
--EXECUTE IMMEDIATE 'ALTER USER regmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO regmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO regmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE ANY DIRECTORY TO regmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE MATERIALIZED VIEW TO regmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE JOB TO regmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO regmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO regmgr';
--EXECUTE IMMEDIATE 'alter user regmgr profile topgun';
--p_dm_dba_grant_select_table ('regmgr');
--P_DM_DBA_GRANT_SELECT_TABLE_EXT ('regmgr');
--P_DM_DBA_GRANT_SELECT_VIEW ('regmgr');
--END;
--grant select on aimsmgr.ssrmeet to regmgr;
--grant select on aimsmgr.ssrmeet_ext to regmgr;
--grant select on aimsmgr.stvterm_ext to regmgr;
--grant select on aimsmgr.stvschd_ext to regmgr; -- does not exist on iaprod
--grant select on aimsmgr.ssbsect_ext to regmgr;
--grant select on aimsmgr.stvbldg_ext to regmgr;

--desc aimsmgr.SSRRLVL;
--grant select on aimsmgr.SSRRLVL to regmgr with grant option;
--grant select on aimsmgr.SSRXLST to regmgr with grant option;
--grant select on aimsmgr.SSRFEES to regmgr with grant option;
--grant select on aimsmgr.SSBSECT to regmgr with grant option;
--grant select on aimsmgr.SSBXLST to regmgr with grant option;
--grant select on aimsmgr.SSRMEET to regmgr with grant option;
--grant select on aimsmgr.SIRASGN to regmgr with grant option;
--grant select on aimsmgr.SIRASGN to regmgr with grant option;
--grant select on aimsmgr.stvterm to regmgr with grant option;
--grant select on aimsmgr.spriden to regmgr with grant option;
--grant select on aimsmgr.SSBSECT to regmgr with grant option;
--grant select on aimsmgr.SHRTTRM to regmgr with grant option;
--grant select on aimsmgr.SHRTTCM to regmgr with grant option;

--grant select on aimsmgr.shbtatc to regmgr with grant option;
--grant select on aimsmgr.stvsbgi to regmgr with grant option;
--grant select on aimsmgr.stvsbgi_ext to regmgr with grant option;
--grant select on aimsmgr.shrtatc to regmgr with grant option;


/*******************************************************************************
Schema and Datamart Grants: webfocus
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'ALTER USER webfocus IDENTIFIED BY umf4eva';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO webfocus';
--EXECUTE IMMEDIATE 'ALTER USER WEBFOCUS PROFILE topgun';
--END;
--create table ADV_APPT_IN as
--select * from <EMAIL>;
--create table ADV_STUDENT_IN as
--select * from <EMAIL>;
--create table BOTCDATE as
--select * from <EMAIL>;
--create table BOTSBDS as
--select * from <EMAIL>;
--create table BOTSCIT as
--select * from <EMAIL>;
--create table BOTSTATE as
--select * from <EMAIL>;
--create table BOTWATCH as
--select * from <EMAIL>;
--create table RZBSTAT as
--select * from <EMAIL>;
--create table SCH_DATE_SEL as
--select * from <EMAIL>;
--create table SCH_DATE_SEL2 as
--select * from <EMAIL>;
--create table SCH_DATE_SEL_2_BACK as
--select * from <EMAIL>;
--create table SCH_WINDOW_SEL as
--select * from <EMAIL>;
--create table TRAN_SD_POP_TERM_SEL as
--select * from <EMAIL>;
--create table UM_MANAGED_REPORTING as
--select * from <EMAIL>;

--grant select on talendmgr.WM_VIA_CW_COURSE_TO_DEPT to iamgr;

/*******************************************************************************
Schema and Datamart Grants: oelmgr
*******************************************************************************/
--Create user and grant create table, and tablespace
--BEGIN
--EXECUTE IMMEDIATE 'CREATE USER oelmgr IDENTIFIED BY toefltest201510';
--EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO oelmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO oelmgr';
--EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO oelmgr';
--EXECUTE IMMEDIATE 'ALTER USER oelmgr DEFAULT ROLE ALL';
--EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO oelmgr';
--EXECUTE IMMEDIATE 'GRANT MANAGE SCHEDULER TO oelmgr';
--EXECUTE IMMEDIATE 'GRANT CREATE DATABASE LINK TO oelmgr';
--EXECUTE IMMEDIATE 'alter user oelmgr profile topgun';
--p_dm_dba_grant_select_table ('oelmgr');
--END;
