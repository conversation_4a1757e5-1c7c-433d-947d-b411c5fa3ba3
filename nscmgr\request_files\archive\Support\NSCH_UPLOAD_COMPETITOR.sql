--------------------------------------------------------------------------------------------------------
-- Author:		<PERSON>, <PERSON>, University of Michigan-Flint
-- Create date: 02-18-16
-- Description:	Queries to gather Banner data for upload to National Student Clearinhouse Student Tracker
-- Notes:       EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
-- Known issue(s):	
--------------------------------------------------------------------------------------------------------

--set basic selection criteria here
with base_sel as (
  select distinct 
  first_name, middle_name, last_name, name_suffix, birthdate, min(term_code_entry) as term_code_entry, dm_pidm
  --*
  from td_admissions_applicant 
  join td_demographic 
    on ad_pidm = dm_pidm  
    and term_code_entry = td_demographic.td_term_code
  where term_code_entry like '%10' --'201610'
  and styp_code in ('F','T')
  and inst_accepted_app_any_date_ind = 'Y'
  and registered_ind = 'N'
  group by first_name, middle_name, last_name, name_suffix, birthdate, dm_pidm
  ),

--select * from base_sel;

---------------------

pop_sel as (
  --This section creates a header file.
  select
  1 order_num,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  to_char(sysdate, 'YYYYMMDD') "E",
  'DA' "F",
  'I' "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from dual
  
  union
  --This section pulls the student records for the payload.
  select
  2 order_num,
  'D1' "A",
  null "B",
  substr(first_name,1,20) "C",
  substr(regexp_replace(trim(middle_name), '[[:punct:] ]',null), 1 ,1) "D",
  substr(last_name,1,20) "E",
  substr(regexp_replace(trim(name_suffix), '[[:punct:] ]',null),1,5) "F",
  to_char(birthdate, 'YYYYMMDD') "G",
  to_char(to_number(SUBSTR(term_code_entry,1,4))-1 ||'0901') "H", 
  null "I",
  '002327' "J",
  '00' "K",
  to_char(dm_pidm) "L"
  from base_sel
  
    
  union
    --This is to count the number of records and append a trailer record
  select
  3 order_num,
  'T1' "A",
  to_char(count(base_sel.dm_pidm)+2) "B",
  null "C",
  null "D",
  null "E",
  null "F",
  null "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from base_sel
  
  order by order_num
)

select 
pop_sel.A,
pop_sel.B,
pop_sel.C,
pop_sel.D,
pop_sel.E,
pop_sel.F,
pop_sel.G,
pop_sel.H,
pop_sel.I,
pop_sel.J,
pop_sel.K,
pop_sel.L
from pop_sel
;
