/*This script is designed to build a cohort table. A cohort is defined as the students that enter the University
in the Fall or prior summer as a FTIAC or Transfer and is denoted in the CO_IA_STUDENT_TYPE_CODE field.
In 2010 the registrars office began coding FTIAC students that started in summer and continued to fall as
FTIAC in the banner system for fall, while the Transfers who started in summer were never rolled in the system.
AIMS created a field called IA_STUDENT_TYPE_CODE for Institutional Analysis use that would roll the summer transfers
to fall starting in Fall 2010.
Student Ethnicity is another field that has changed over time and needed to be normalized to current standards.
Prior to 2010 the codes were in a format that did not include multi-racial and Non-resident Alien and were manually
calculated with an algorithm using race_codes and residency_codes. In 2010 the banner system began using the current
federal and state definitions for race and ethnicity and stored them in a new filed called report_ethnicity.
With all of the changes in definitions to students over the course of time there was no way to analyse cohort data in
a consistent fashion.  The ia_cohort_pop table was developed to rectify this issue.  The "2011 to present" portion
of the query will pull the student data from the AIMSPROD data mart that has the correct Ethnicity and has the summer
students that continue to fall roll to Fall as either a FTIAC or Transfer.
*/
CREATE OR REPLACE VIEW IA_COHORT_POP_TBL_2
AS
  -------2011 to present
  SELECT IA_TD_STUDENT_DATA.sd_pidm AS CO_PIDM,
    IA_TD_STUDENT_DATA.sd_term_code CO_TERM_CODE_KEY, -- fall term code
    IA_TD_STUDENT_DATA.ia_student_type_code CO_IA_STUDENT_TYPE_CODE,
    IA_TD_STUDENT_DATA.full_part_time_ind_umf CO_FULL_PART_IND_UMF,
    IA_TD_STUDENT_DATA.report_ethnicity AS CO_ETHNICITY,
    IA_TD_STUDENT_DATA.ia_gender        AS CO_GENDER
  FROM IA_TD_STUDENT_DATA
  WHERE primary_level_code  = 'UG'
  AND registered_ind        = 'Y'
  AND (ia_student_type_code = 'F'
  OR ia_student_type_code   = 'T')
  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '201210'
  /*****************************************************************************
  filters the term code to pull only fall terms greater than or equal to
  fall 2011 Cohort table row size as of 20160 is 13688
  *****************************************************************************/
  UNION
  SELECT CO_PIDM,
    CO_TERM_CODE_KEY,
    CO_IA_STUDENT_TYPE_CODE,
    CO_FULL_PART_IND_UMF,
    CO_ETHNICITY,
    CO_GENDER
  FROM ia_co_all_2008_2010
  INNER JOIN IA_TD_STUDENT_DATA
  ON ia_co_all_2008_2010.co_pidm           = IA_TD_STUDENT_DATA.sd_pidm
  AND ia_co_all_2008_2010.CO_TERM_CODE_KEY = IA_TD_STUDENT_DATA.sd_term_code
  WHERE primary_level_code                 = 'UG'
  OR CO_PIDM                              IN (76842)
  UNION
  SELECT CO_PIDM,
    CO_TERM_CODE_KEY,
    CO_IA_STUDENT_TYPE_CODE,
    CO_FULL_PART_IND_UMF,
    CO_ETHNICITY,
    CO_GENDER
  FROM ia_co_all_2007
  INNER JOIN IA_TD_STUDENT_DATA
  ON ia_co_all_2007.co_pidm           = IA_TD_STUDENT_DATA.sd_pidm
  AND ia_co_all_2007.CO_TERM_CODE_KEY = IA_TD_STUDENT_DATA.sd_term_code
  WHERE primary_level_code            = 'UG';