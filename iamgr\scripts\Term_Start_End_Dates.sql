
select 
sd.sd_term_code,
sd_term_desc term,
to_char(st.stvterm_start_date, 'MM/DD/YYYY') start_date,
to_char(st.stvterm_end_date, 'MM/DD/YYYY') end_date,
count (*) headcount
from
um_student_data sd
inner join aimsmgr.stvterm st on sd.sd_term_code = st.stvterm_code
where
sd.REGISTERED_IND = 'Y'
group by
sd.SD_TERM_code,
sd_term_desc,
st.stvterm_start_date,
st.stvterm_end_date
order by
sd.SD_TERM_code,
sd_term_desc,
st.stvterm_start_date,
st.stvterm_end_date
;