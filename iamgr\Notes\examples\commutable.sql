select distinct COUNTY_CODE_ADMIT,
COUNTY_DESC_ADMIT,
STATE_CODE_ADMIT,
STATE_DESC_ADMIT
from UM_ADMISSIONS_APPLICANT
where TERM_CODE_ENTRY = '201710'
and STATE_CODE_ADMIT = 'MI'
and COUNTY_CODE_ADMIT in (
'MI049', --<PERSON><PERSON> (MI)
'MI087', --<PERSON><PERSON><PERSON>
'MI093', --<PERSON> (MI)
'MI125', --Oakland
'MI145', --Saginaw
'MI155', --<PERSON><PERSON>see
'MI157') --<PERSON><PERSON><PERSON>
;
