--------------------------------------------------------
--  DDL for View BO_TTD_ALL_COMPLETERS
--------------------------------------------------------

CREATE OR REPLACE VIEW "BO_FLINT_STUDENT"."BO_TTD_ALL_COMPLETERS" AS
WITH dp1 AS (
  SELECT
    ROW_NUMBER()
    OVER(PARTITION BY sd.sd_pidm
         ORDER BY
           sd_term_code DESC
    )                          max_term,
    sd.sd_pidm,
    dc.umid,
    sd.orig_student_type_code,
    sd.ia_student_type_code,
    sd.primary_level_code,
    sd.primary_college_code,
    sd.report_level_code,
    sd.sd_term_code,
    sd.inst_hours_earned,
    stvterm.stvterm_start_date,
    stvterm.stvterm_fa_proc_yr start_aidy_code,
--    sd.primary_major_1         start_major,
    dc.grad_date,
    dc.fiscal_year             grad_year,
    dc.grad_term_code,
    dc.degree_major_code,
    dc.degree_code,
    dc.nces_code,
    dc.completion_type
  FROM
         td_student_data sd
    INNER JOIN (--1038
      SELECT
        *
      FROM
        iamgr.ia_degree_completions
    )                   dc ON dc.pidm = sd.sd_pidm
    LEFT JOIN aimsmgr.stvterm_ext stvterm ON sd.sd_term_code = stvterm.stvterm_code
  WHERE
      sd.registered_ind = 'Y'
    AND sd.primary_level_code = dc.primary_level_code
    AND sd.sd_term_code <= dc.grad_term_code
), start_date AS (
  SELECT DISTINCT
    dp1.sd_pidm,
    dp1.sd_term_code,
    dp1.primary_level_code,
    dp1.stvterm_start_date start_date
  FROM
    dp1
  WHERE
    dp1.ia_student_type_code IN ( 'F', 'T', 'N' )
), max_term_sel AS (
  SELECT DISTINCT
    dp1.sd_pidm,
    dp1.inst_hours_earned,
    dp1.primary_college_code col_code
  FROM
    dp1
  WHERE
    dp1.max_term = 1
), dp2 AS (
  SELECT
    dp1.*,
    ROW_NUMBER()
    OVER(PARTITION BY dp1.sd_pidm, dp1.primary_level_code, degree_major_code, degree_code
         ORDER BY
           dp1.sd_term_code
    )                                                       dup_ind,
    max_term_sel.col_code,
    start_date.start_date,
    round((dp1.grad_date - start_date.start_date) / 365, 2) AS ttd_years,
    max_term_sel.inst_hours_earned                          hours_earned
  FROM
         dp1
    INNER JOIN start_date ON dp1.sd_pidm = start_date.sd_pidm
                             AND dp1.sd_term_code = start_date.sd_term_code
                             AND dp1.primary_level_code = start_date.primary_level_code
    INNER JOIN max_term_sel ON max_term_sel.sd_pidm = dp1.sd_pidm
)
SELECT
  grad_term_code,
  grad_year,
  sd_term_code start_term_code,
  start_aidy_code,
  sd_pidm,
  umid,
  orig_student_type_code,
  ia_student_type_code,
  primary_level_code,
  col_code,
  degree_major_code,
  degree_code,
  completion_type,
  start_date,
  grad_date,
  ttd_years,
  hours_earned
FROM
  dp2
WHERE
  dup_ind = 1;
  
  
  
  select count (*)
  from IAMGR.ia_degree_completions;