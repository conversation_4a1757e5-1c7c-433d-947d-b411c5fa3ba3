--CDS C9
select 
IA_TD_TEST_SCORE.SD_PIDM,
IA_TD_TEST_SCORE.SD_TERM_CODE,
IA_TD_TEST_SCORE.ia_student_type_code,
IA_TD_TEST_SCORE.HSCH_RANK,
ROUND (TO_NUMBER(IA_TD_TEST_SCORE.HSCH_GPA),2)HSCH_GPA,
ROUND (TO_NUMBER(IA_TD_TEST_SCORE.SAT_READ_WRI_S11),2)SAT_READ_WRI_S11,
ROUND (TO_NUMBER(IA_TD_TEST_SCORE.SAT_MATH_S12),2)SAT_MATH_S12,
case
when IA_TD_TEST_SCORE.HSCH_GPA >= 3.750 Then '3.75 and higher'
when IA_TD_TEST_SCORE.HSCH_GPA >= 3.500 and IA_TD_TEST_SCORE.HSCH_GPA <= 3.749 Then '3.50 - 3.74'
when IA_TD_TEST_SCORE.HSCH_GPA >= 3.250 and IA_TD_TEST_SCORE.HSCH_GPA <= 3.499 Then '3.25 - 3.49'
when IA_TD_TEST_SCORE.HSCH_GPA >= 3.000 and IA_TD_TEST_SCORE.HSCH_GPA <= 3.249 Then '3.00 - 3.24'
when IA_TD_TEST_SCORE.HSCH_GPA >= 2.500 and IA_TD_TEST_SCORE.HSCH_GPA <= 2.999 Then '2.50 - 2.99'
when IA_TD_TEST_SCORE.HSCH_GPA >= 2.500 and IA_TD_TEST_SCORE.HSCH_GPA <= 2.999 Then '2.50 - 2.99'
when IA_TD_TEST_SCORE.HSCH_GPA >= 2.000 and IA_TD_TEST_SCORE.HSCH_GPA <= 2.499 Then '2.00 - 2.49'
when IA_TD_TEST_SCORE.HSCH_GPA >= 1.000 and IA_TD_TEST_SCORE.HSCH_GPA <= 1.999 Then '1.00 - 1.99'
when IA_TD_TEST_SCORE.HSCH_GPA <= .999 Then 'Less than 1.0'
else 'NULL or out of range'
end HSCH_GPA_BIN,
case
when IA_TD_TEST_SCORE.SAT_READ_WRI_S11 >= 700 and IA_TD_TEST_SCORE.SAT_READ_WRI_S11 <= 800 then '700-800'
when IA_TD_TEST_SCORE.SAT_READ_WRI_S11 >= 600 and IA_TD_TEST_SCORE.SAT_READ_WRI_S11 <= 699 then '600-699'
when IA_TD_TEST_SCORE.SAT_READ_WRI_S11 >= 500 and IA_TD_TEST_SCORE.SAT_READ_WRI_S11 <= 599 then '500-599'
when IA_TD_TEST_SCORE.SAT_READ_WRI_S11 >= 400 and IA_TD_TEST_SCORE.SAT_READ_WRI_S11 <= 499 then '400-499'
when IA_TD_TEST_SCORE.SAT_READ_WRI_S11 >= 300 and IA_TD_TEST_SCORE.SAT_READ_WRI_S11 <= 399 then '300-399'
when IA_TD_TEST_SCORE.SAT_READ_WRI_S11 >= 200 and IA_TD_TEST_SCORE.SAT_READ_WRI_S11 <= 299 then '200-299'
else 'NULL or out of range'
end SAT_READ_WRI_BAND,
TO_NUMBER(IA_TD_TEST_SCORE.SAT_READ_WRI_S11)SAT_READ_WRI_S11,
case
when IA_TD_TEST_SCORE.SAT_MATH_S12 >= 700 and IA_TD_TEST_SCORE.SAT_MATH_S12 <= 800 then '700-800'
when IA_TD_TEST_SCORE.SAT_MATH_S12 >= 600 and IA_TD_TEST_SCORE.SAT_MATH_S12 <= 699 then '600-699'
when IA_TD_TEST_SCORE.SAT_MATH_S12 >= 500 and IA_TD_TEST_SCORE.SAT_MATH_S12 <= 599 then '500-599'
when IA_TD_TEST_SCORE.SAT_MATH_S12 >= 400 and IA_TD_TEST_SCORE.SAT_MATH_S12 <= 499 then '400-499'
when IA_TD_TEST_SCORE.SAT_MATH_S12 >= 300 and IA_TD_TEST_SCORE.SAT_MATH_S12 <= 399 then '300-399'
when IA_TD_TEST_SCORE.SAT_MATH_S12 >= 200 and IA_TD_TEST_SCORE.SAT_MATH_S12 <= 299 then '200-299'
else 'NULL or out of range'
end SAT_MATH_S12_BAND,
TO_NUMBER(IA_TD_TEST_SCORE.SAT_MATH_S12)SAT_MATH_S12,
case
when IA_TD_TEST_SCORE.ACT_COMPOSITE >= 30 and IA_TD_TEST_SCORE.ACT_COMPOSITE <= 36 then '30-36'
when IA_TD_TEST_SCORE.ACT_COMPOSITE >= 24 and IA_TD_TEST_SCORE.ACT_COMPOSITE <= 29 then '24-29'
when IA_TD_TEST_SCORE.ACT_COMPOSITE >= 18 and IA_TD_TEST_SCORE.ACT_COMPOSITE <= 23 then '18-23'
when IA_TD_TEST_SCORE.ACT_COMPOSITE >= 12 and IA_TD_TEST_SCORE.ACT_COMPOSITE <= 17 then '12-17'
when IA_TD_TEST_SCORE.ACT_COMPOSITE >= 6 and IA_TD_TEST_SCORE.ACT_COMPOSITE <= 11 then '6-11'
when IA_TD_TEST_SCORE.ACT_COMPOSITE <= 5 then 'Below 6'
else 'NULL or out of range'
end ACT_COMPOSITE_BAND,
TO_NUMBER(IA_TD_TEST_SCORE.ACT_COMPOSITE)ACT_COMPOSITE,
case
when IA_TD_TEST_SCORE.ACT_MATH >= 30 and IA_TD_TEST_SCORE.ACT_MATH <= 36 then '30-36'
when IA_TD_TEST_SCORE.ACT_MATH >= 24 and IA_TD_TEST_SCORE.ACT_MATH <= 29 then '24-29'
when IA_TD_TEST_SCORE.ACT_MATH >= 18 and IA_TD_TEST_SCORE.ACT_MATH <= 23 then '18-23'
when IA_TD_TEST_SCORE.ACT_MATH >= 12 and IA_TD_TEST_SCORE.ACT_MATH <= 17 then '12-17'
when IA_TD_TEST_SCORE.ACT_MATH >= 6 and IA_TD_TEST_SCORE.ACT_MATH <= 11 then '6-11'
when IA_TD_TEST_SCORE.ACT_MATH <= 5 then 'Below 6'
else 'NULL or out of range'
end ACT_MATH_BAND,
TO_NUMBER(IA_TD_TEST_SCORE.ACT_MATH)ACT_MATH,
case
when IA_TD_TEST_SCORE.ACT_ENGLISH >= 30 and IA_TD_TEST_SCORE.ACT_ENGLISH <= 36 then '30-36'
when IA_TD_TEST_SCORE.ACT_ENGLISH >= 24 and IA_TD_TEST_SCORE.ACT_ENGLISH <= 29 then '24-29'
when IA_TD_TEST_SCORE.ACT_ENGLISH >= 18 and IA_TD_TEST_SCORE.ACT_ENGLISH <= 23 then '18-23'
when IA_TD_TEST_SCORE.ACT_ENGLISH >= 12 and IA_TD_TEST_SCORE.ACT_ENGLISH <= 17 then '12-17'
when IA_TD_TEST_SCORE.ACT_ENGLISH >= 6 and IA_TD_TEST_SCORE.ACT_ENGLISH <= 11 then '6-11'
when IA_TD_TEST_SCORE.ACT_ENGLISH <= 5 then 'Below 6'
else 'NULL or out of range'
end ACT_ENGLISH_BAND,
TO_NUMBER(IA_TD_TEST_SCORE.ACT_ENGLISH)ACT_ENGLISH,
TO_NUMBER(IA_TD_TEST_SCORE.ACT_COMBINED_ENG_WRI)ACT_WRITING

from ia_td_test_score
where ia_td_test_score.sd_term_code = '202110' --update
and ia_td_test_score.registered_ind = 'Y'
and ia_td_test_score.ia_student_type_code = 'F'
;
--CDS C10
with data_pull as (
select
sd.SD_PIDM,
--sd.hsch_rank,
--sd.hsch_size,
sd.hsch_percentile

from
ia_td_student_data sd
where sd.sd_term_code = '202110'
and sd.registered_ind = 'Y'
and sd.ia_student_type_code = 'F'
)
select
(select count (sd_pidm) from data_pull) total_count,

TO_CHAR(
(select count (sd_pidm) from data_pull where hsch_percentile > 90
) / (select count (sd_pidm) from data_pull where hsch_percentile is not null)*100, '900')||'%' top_10_prcnt,

TO_CHAR(
(select count (sd_pidm) from data_pull where hsch_percentile > 75
) / (select count (sd_pidm) from data_pull where hsch_percentile is not null)*100, '900')||'%' top_25_prcnt,

TO_CHAR(
(select count (sd_pidm) from data_pull where hsch_percentile > 50
) / (select count (sd_pidm) from data_pull where hsch_percentile is not null)*100, '900')||'%' top_50_prcnt,

TO_CHAR(
(select count (sd_pidm) from data_pull where hsch_percentile <= 50
) / (select count (sd_pidm) from data_pull where hsch_percentile is not null)*100, '900')||'%' bottom_50_prcnt,

TO_CHAR(
(select count (sd_pidm) from data_pull where hsch_percentile <= 25
) / (select count (sd_pidm) from data_pull where hsch_percentile is not null)*100, '900')||'%' bottom_25_prcnt,

TO_CHAR(
(select count (sd_pidm) from data_pull where hsch_percentile is not null
) / (select count (sd_pidm) from data_pull )*100, '900')||'%' rank_submt_prcnt
from dual
;

--SEC I-3
with DP1 as (
select
popsel.*
from (
select
distinct 
RD.COURSE,
RD.SECTION_NUMBER,
RD.MEETING_SCHD_CODE_1,
count (RD.pidm) as course_enrollment
from
IA_TD_REGISTRATION_DETAIL RD
WHERE
RD.TD_TERM_CODE in (select current_term from um_current_term)    
and RD.CRSE_NUMBER < 500  
and RD.MEETING_SCHD_CODE_1 in ('L/L','L/D','MM','LAB')
and RD.section_number not like ('W%')
and RD.section_number not like ('9%')
and RD.section_number != 'IS'
group by 
COURSE,
SECTION_NUMBER,
MEETING_SCHD_CODE_1
)popsel
where popsel.course_enrollment >2
),
section_count as (
select 
case
when course_enrollment >2 and course_enrollment <10 then '1'
when course_enrollment >=10 and course_enrollment <20 then '2'
when course_enrollment >=20 and course_enrollment <30 then '3'
when course_enrollment >=30 and course_enrollment <40 then '4'
when course_enrollment >=40 and course_enrollment <50 then '5'
when course_enrollment >=50 and course_enrollment <100 then '6'
Else '7'
end order_num,
case
when course_enrollment >2 and course_enrollment <10 then '2-9'
when course_enrollment >=10 and course_enrollment <20 then '10-19'
when course_enrollment >=20 and course_enrollment <30 then '20-29'
when course_enrollment >=30 and course_enrollment <40 then '30-39'
when course_enrollment >=40 and course_enrollment <50 then '40-49'
when course_enrollment >=50 and course_enrollment <100 then '50-99'
Else '100+'
end class_section,
DP1.*
from
DP1
)
select
'Top' as report,
order_num,
class_section,
count (*) course_count
from
section_count
where 
MEETING_SCHD_CODE_1 in ('L/L','L/D','MM')
group by 
'Top',
order_num,
class_section

union all

select
'Bottom' as report,
order_num,
class_section,
count (*) course_count
from
section_count
where 
MEETING_SCHD_CODE_1 in ('L/L','LAB')
group by 
'Bottom',
order_num,
class_section
order by report desc, order_num
;





