SELECT
  M_HRDW1.JOB.JOB_EFFDT,
  M_HRDW1.JOB.JOB_END_DT,
  M_HRDW1.PERSONAL_DATA.LAST_NAME,
  M_HRDW1.PERSONAL_DATA.FIRST_NAME,
  M_HRDW1.PERSONAL_DATA.EMPLID,
  M_HRDW1.PERSONAL_DEMOGRAPHICS.SEX,
  M_HRDW1.PERSONAL_DEMOGRAPHICS.ETHNIC_GROUP_DESCR,
  M_HRDW1.PERSONAL_DATA.HIGHEST_EDUC_LVL_DESCRSHORT,
  M_HRDW1.PERSONAL_DEMOGRAPHICS.CITIZENSHIP_STATUS_DESCR,
  M_HRDW1.JOB.EMPL_RCD,
  M_HRDW1.JOB.APPT_DEPT_DESCR,
  M_HRDW1.JOB.JOBCODE_DESCR,
  M_HRDW1.JOB.ANNUAL_FTR,
  M_HRDW1.JO<PERSON>.FTE,
  M_HRDW1.JOB.COMPRATE,
  M_HRDW1.JOB.CLASS_INDC_DESCRSHORT,
  M_HRDW1.DEPT_BUDGET_ERN.FUNDING_DEPT_DESCR,
  M_HRDW1.DEPT_BUDGET_ERN.DIST_PCT,
  M_HRDW1.DEPT_BUDGET_ERN.PERCENT_EFFORT,
  M_HRDW1.DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_EFFDT,
  M_HRDW1.DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_END_DT,
  M_HRDW1.DEPT_BUDGET_ERN.SHORTCODE,
  M_HRDW1.JOB.APPT_PERIOD_DESCR,
  M_HRDW1.JOB.TENURE_STATUS,
  M_HRDW1.JOB.APPT_START_DATE,
  M_HRDW1.JOB.FTR_RATE,
  M_HRDW1.JOB.STD_HOURS,
  M_HRDW1.DEPT_BUDGET_ERN.SHORTCODE_DESCR,
  M_HRDW1.JOBCODE_TBL.IPEDSSCODE_DESCR,
  M_HRDW1.WORK_ADDRESS.CAMPUS_ID,
  M_HRDW1.JOB.EMPL_STATUS,
  M_HRDW1.PERSONAL_DATA.HR_MAJ_DEGREE1_TXT,
  M_HRDW1.PERSONAL_DATA.HR_MAJ_DEGREE2_TXT,
  M_HRDW1.PERSONAL_DATA.HR_STUDY_FIELD_DESCR,
  M_HRDW1.PERSONAL_DATA.HR_STUDY_FIELD,
  M_HRDW1.JOB.JOB_EFFSEQ
FROM
  M_HRDW1.JOB,
  M_HRDW1.PERSONAL_DATA,
  M_HRDW1.PERSONAL_DEMOGRAPHICS,
  M_HRDW1.DEPT_BUDGET_ERN,
  M_HRDW1.JOBCODE_TBL,
  M_HRDW1.WORK_ADDRESS
WHERE
  ( M_HRDW1.PERSONAL_DATA.EMPLID=M_HRDW1.WORK_ADDRESS.EMPLID(+)  )
  AND  ( M_HRDW1.PERSONAL_DATA.EMPLID=M_HRDW1.PERSONAL_DEMOGRAPHICS.EMPLID  )
  AND  ( M_HRDW1.PERSONAL_DATA.EMPLID=M_HRDW1.JOB.EMPLID  )
  AND  ( M_HRDW1.JOB.JOBCODE=M_HRDW1.JOBCODE_TBL.JOBCODE  )
  AND  ( M_HRDW1.JOB.EMPLID=M_HRDW1.DEPT_BUDGET_ERN.EMPLID(+)  )
  AND  ( M_HRDW1.JOB.EMPL_RCD=M_HRDW1.DEPT_BUDGET_ERN.EMPL_RCD(+)  )
  AND  ( M_HRDW1.JOB.APPT_DEPTID=M_HRDW1.DEPT_BUDGET_ERN.APPT_DEPTID(+)  )
  AND  ( (
 @prompt ( '! Job Effective Sequence Options:'  , 'a' , {'Max Sequence','All Sequences'},,CONSTRAINED)
  = 'All Sequences'
)

  or

(
  M_HRDW1.JOB.JOB_EFFSEQ = 
    (
     select max(a_JOB.JOB_EFFSEQ) 
       from M_HRDW1.JOB a_JOB where 
         a_JOB.EMPLID=M_HRDW1.JOB.EMPLID and
         a_JOB.EMPL_RCD=M_HRDW1.JOB.EMPL_RCD and
         a_JOB.JOB_EFFDT=M_HRDW1.JOB.JOB_EFFDT and
    @prompt ( '! Job Effective Sequence Options:'  , 'a' , {'Max Sequence','All Sequences'},,CONSTRAINED)
    = 'Max Sequence'
    ) 
) 
 
or 
(
	M_HRDW1.JOB.EMPLID IS NULL and

	M_HRDW1.JOB.EMPL_RCD is NULL
)  )
  AND  ( M_HRDW1.JOB.JOB_EFFDT<=M_HRDW1.DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_END_DT  )
  AND  ( M_HRDW1.DEPT_BUDGET_ERN.DEPT_BUDGET_ERN_EFFDT <= M_HRDW1.JOB.JOB_END_DT  )
  AND  
  (
   M_HRDW1.JOB.JOB_LOCATION  =  @prompt('Enter Job Location:','A','Job\Job Location',Mono,Free,Persistent,,User:0)
   AND
   M_HRDW1.JOB.REG_TEMP  IN  @prompt('Enter value(s) for Reg Temp:','A','Job\Reg Temp',Multi,Free,Persistent,,User:1)
   AND
   M_HRDW1.JOB.JOB_EFFDT  >=  @prompt('Enter Job Effdt:','D',,Mono,Free,Persistent,,User:2)
   AND
   M_HRDW1.JOB.EMPL_STATUS  IN  @prompt('Enter value(s) for Empl Status:','A','Job\Empl Status',Multi,Free,Persistent,,User:2)
  )
