--DROP TABLE IA_HLC_STUDY_BAC;
TRUNCATE TABLE IA_HLC_STUDY_BAC;

--CREATE TABLE IA_HLC_STUDY_BAC AS
INSERT INTO IA_HLC_STUDY_BAC
SELECT * FROM IA_HLC_STUDY_TBL;

COMMIT;

select count(*) 
from user_tab_columns
where table_name='IA_HLC_STUDY_BAC';

select count(*) 
from user_tab_columns
where table_name='IA_HLC_STUDY_TBL';

SELECT COUNT (*) FROM IA_HLC_STUDY_BAC;
SELECT COUNT (*) FROM IA_HLC_STUDY_TBL;

--DROP TABLE IA_HLC_STUDY_TBL;
TRUNCATE TABLE IA_HLC_STUDY_TBL;

--CREATE TABLE IA_HLC_STUDY_TBL AS 
INSERT INTO IA_HLC_STUDY_TBL
(
SELECT
  /***************************************
  Cohort Un-duped
  ***************************************/
  IA_COHORT_POP_UNDUP_TBL.CO_PIDM,
  most_recent.umid,
  IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY,
  SD.sd_term_desc CO_TERM_DESC,
  IA_COHORT_POP_UNDUP_TBL.CO_IA_STUDENT_TYPE_CODE,
  IA_COHORT_POP_UNDUP_TBL.CO_FULL_PART_IND_UMF,
  IA_COHORT_POP_UNDUP_TBL.CO_ETHNICITY,
  IA_COHORT_POP_UNDUP_TBL.CO_GENDER,
  /***************************************
  Cohort Demographic
  ***************************************/
 
  SD.age,
  SD.hsch_rank,
  SD.hsch_size,
  SD.hsch_gpa,
  SD.primary_college_code,
  SD.PRIMARY_DEGREE_CODE,
  SD.ORIG_STUDENT_TYPE_DESC,
  SD.HONORS_PROGRAM,
  SD.FIRST_GENERATION_STUDENT_IND,
  SD.RESIDENCY_DESC,
  
  CASE
    WHEN  most_recent.DECEASED_DATE IS NOT NULL THEN 'YES'
    ELSE 'NO'
  END DECEASED_IND,
  CASE
    WHEN SD.hsch_gpa < 2.000
    THEN 'Below 2.0'
    WHEN SD.hsch_gpa >= 2.000
    AND SD.hsch_gpa   < 3.000
    THEN '2.00-2.99'
    WHEN SD.hsch_gpa >= 3.000
    AND SD.hsch_gpa   < 3.500
    THEN '3.00-3.49'
    WHEN SD.hsch_gpa >= 3.500
    THEN '3.5+'
    ELSE 'No Earned GPA'
  END HS_gpa_bin_desc,
  first_fall_term_gpa.term_gpa,
  CASE
    WHEN first_fall_term_gpa.term_gpa < 2.000
    THEN 'Below 2.0'
    WHEN first_fall_term_gpa.term_gpa >= 2.000
    AND first_fall_term_gpa.term_gpa   < 3.000
    THEN '2.00-2.99'
    WHEN first_fall_term_gpa.term_gpa >= 3.000
    AND first_fall_term_gpa.term_gpa   < 3.500
    THEN '3.00-3.49'
    WHEN first_fall_term_gpa.term_gpa >= 3.500
    THEN '3.5+'
    ELSE 'No Earned GPA'
  END frst_term_gpa_bin_desc,
  most_recent.overall_gpa most_recent_GPA,
  CASE
    WHEN most_recent.overall_gpa < 2.000
    THEN 'Below 2.0'
    WHEN most_recent.overall_gpa >= 2.000
    AND most_recent.overall_gpa   < 3.000
    THEN '2.00-2.99'
    WHEN most_recent.overall_gpa >= 3.000
    AND most_recent.overall_gpa   < 3.500
    THEN '3.00-3.49'
    WHEN most_recent.overall_gpa >= 3.500
    THEN '3.5+'
    ELSE 'No Earned GPA'
  END most_recent_gpa_bin_desc,
  most_recent.OVERALL_HOURS_EARNED most_recent_credit_hours,
  CASE
    WHEN most_recent.OVERALL_HOURS_EARNED >=0
    AND most_recent.OVERALL_HOURS_EARNED  <= 15
    THEN '0-15 CH'
    WHEN most_recent.OVERALL_HOURS_EARNED >=16
    AND most_recent.OVERALL_HOURS_EARNED  <= 30
    THEN '16-30 CH'
    WHEN most_recent.OVERALL_HOURS_EARNED >=31
    AND most_recent.OVERALL_HOURS_EARNED  <=60
    THEN '31-60 CH'
    WHEN most_recent.OVERALL_HOURS_EARNED >=61
    AND most_recent.OVERALL_HOURS_EARNED  <=90
    THEN '61-90 CH'
    WHEN most_recent.OVERALL_HOURS_EARNED >=91
    AND most_recent.OVERALL_HOURS_EARNED  <=120
    THEN '91-120 CH'
    WHEN most_recent.OVERALL_HOURS_EARNED >=121
    THEN '121+ CH'
    ELSE 'No Earned Credits'
  END most_recent_CH_bin_desc,
  most_recent.MOST_RECENT_CLASS_CODE,
  most_recent.MOST_RECENT_MAJOR,
  SD.HSCH_DESC,
  SD.HSCH_CITY,
  SD.HSCH_STATE,
  SD.HSCH_ZIP,
  SD.PCOL_DESC_1              AS pcol_desc,
  SD.PCOL_GPA_TRANSFERRED_1   AS PCOL_GPA,
  SD.PCOL_DEGC_CODE_1         AS PCOL_DEGC_CODE,
  SD.PCOL_HOURS_TRANSFERRED_1 AS PCOL_HOURS,
  NVL( SD.VETERAN_IND,'N') Veteran_ind,
  --FINAID.PELL_EFC_AMOUNT,
  CASE
    WHEN FINAID.PELL_EFC_AMOUNT >=0
    AND FINAID.PELL_EFC_AMOUNT  <= 1000
    THEN 'Low EFC $0-$1,000'
    WHEN FINAID.PELL_EFC_AMOUNT >1000
    AND FINAID.PELL_EFC_AMOUNT  < 10000
    THEN 'Medium EFC $1,001-$9,999'
    WHEN FINAID.PELL_EFC_AMOUNT >=10000
    THEN 'Large EFC $10,000+'
    WHEN FINAID.PELL_EFC_AMOUNT IS NULL
    THEN 'NO FinAid Record'
  END EFC_LEVEL,
  SD.CLASS_CODE,
  SD.primary_admit_code,
  SD.residency_code,
  SD.primary_major_1,
  NVL(SD.HOUSING_IND,'N') housing_ind,
  SD.TOTAL_CREDIT_HOURS_UMF,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT T1.sd_PIDM
    FROM ia_td_student_data T1
    WHERE T1.a1_county_code IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    )
  ), 'No') Commutable_ind_A1,
  NVL(
  (SELECT 'Yes'
  FROM dual
  WHERE EXISTS
    (SELECT TD_ADMISSIONS_APPLICANT.AD_pidm,
      MAX(TD_ADMISSIONS_APPLICANT.APST_DATE)
    FROM TD_ADMISSIONS_APPLICANT
    WHERE INST_ACCEPTED_APP_ANY_DATE_IND        = 'Y' --was admitted
    AND registered_ind                          = 'Y' --enrolled
    AND TD_ADMISSIONS_APPLICANT.ad_pidm         = IA_COHORT_POP_UNDUP_TBL.co_pidm
    AND TD_ADMISSIONS_APPLICANT.term_code_entry = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
    AND TD_ADMISSIONS_APPLICANT.COUNTY_CODE_ADMIT IN ('MI049','MI157','MI099','MI093','MI155','MI145','MI125','MI087')
    GROUP BY AD_pidm
    )
  ), 'No') Commutable_ind_origin,
  /***************************************
  Student Success Variables
  join to IA_COHORT_SIX_YR for projected
  ***************************************/
  CASE
    WHEN first_fall_term_gpa.term_gpa >= 2.0
    THEN 'Yes'
    ELSE 'No'
  END success_after_1st_term,
  most_recent.MOST_RECENT_ASTD_CODE,
  most_recent.STUDENT_STATUS_CODE,
  test_scores.act_composite,
  test_scores.act_math,
  test_scores.act_english,
  test_scores.SAT_VERBAL,
  test_scores.SAT_MATHEMATICS,
  test_scores.SAT_READING,
  test_scores.SAT_VOCABULARY,
  test_scores.SAT_WRITING,
  test_scores.SAT_ESSAY,
  test_scores.SAT_MC,
  test_scores.MATH_PLACEMENT_CODE,
  test_scores.ENG_READ_PLACEMENT_CODE,
  test_scores.ENG_WRIT_PLACEMENT_CODE,
  ia_um_test_scores.ILSC_LEVEL,
  ia_um_test_scores.VGC_LEVEL,
  /***************************************
  CSI Qualitative Variables - cohort term
  ***************************************/
  ia_nl_csi.WORK_DURING_SCHOOL,
  ia_nl_csi.DESIRE_TO_TRANSFER,
  ia_nl_csi.MOTHERS_EDU,
  ia_nl_csi.FATHERS_EDU,
  -- calculate First Generation student
  CASE
    WHEN (ia_nl_csi.MOTHERS_EDU IS NULL
    AND ia_nl_csi.FATHERS_EDU   IS NULL)
    THEN NULL
    WHEN (ia_nl_csi.MOTHERS_EDU = 'High School Diploma'
    OR ia_nl_csi.MOTHERS_EDU    ='Some High School'
    OR ia_nl_csi.MOTHERS_EDU    = 'Elementary')
    AND (ia_nl_csi.FATHERS_EDU IS NULL)
    THEN 'Yes'
    WHEN (ia_nl_csi.MOTHERS_EDU IS NULL)
    AND (ia_nl_csi.FATHERS_EDU   = 'High School Diploma'
    OR ia_nl_csi.FATHERS_EDU     ='Some High School'
    OR ia_nl_csi.FATHERS_EDU     = 'Elementary')
    THEN 'Yes'
    WHEN (ia_nl_csi.MOTHERS_EDU = 'High School Diploma'
    OR ia_nl_csi.MOTHERS_EDU    ='Some High School'
    OR ia_nl_csi.MOTHERS_EDU    = 'Elementary')
    AND (ia_nl_csi.FATHERS_EDU  = 'High School Diploma'
    OR ia_nl_csi.FATHERS_EDU    ='Some High School'
    OR ia_nl_csi.FATHERS_EDU    = 'Elementary')
    THEN 'Yes'
    ELSE 'No'
  END Frst_Gen_Stud,
  -- end first gen student calculation
  /************************************************
  CSI Stanine Variables - cohort term
  Summary scores are expressed on a stanine scale:
  1 = very low, 5 = average, 9 = very high
  Calculating Stanines
  Result Ranking 4% 7% 12% 17% 20% 17% 12% 7% 4%
  Stanine        1   2   3   4   5   6   7   8   9
  **************************************************/
  ia_nl_csi.DROPOUT_PRONENESS_STANINE,
  ia_nl_csi.EDUCATIONAL_STRESS_STANINE,
  ia_nl_csi.RECEPTIVITY_TO_HELP_STANINE,
  ia_nl_csi.PREDICTED_ACAD_DIFF_STANINE
  /***************************************
  From and Joins
  ***************************************/
FROM IA_COHORT_POP_UNDUP_TBL
LEFT JOIN ia_td_student_data SD
ON SD.sd_pidm       = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND SD.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
LEFT JOIN
  (SELECT sd_pidm, sd_term_code, TERM_GPA FROM um_student_data
  ) first_fall_term_gpa
ON first_fall_term_gpa.sd_pidm       = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND first_fall_term_gpa.sd_term_code = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY
LEFT JOIN
  (SELECT sd_pidm,
    TERM_GPA,
    sd_term_code,
    OVERALL_GPA,
    MOST_RECENT_ASTD_CODE,
    STUDENT_STATUS_CODE,
    OVERALL_HOURS_EARNED,
    UMID,
    DECEASED_DATE,
    CLASS_CODE MOST_RECENT_CLASS_CODE,
    PRIMARY_MAJOR_1 MOST_RECENT_MAJOR
    
  FROM um_student_data t
  WHERE sd_term_code =
    (SELECT MAX(sd_term_code)
    FROM um_student_data
    WHERE sd_pidm          = t.sd_pidm
    AND primary_level_code = 'UG'
    )
  ) most_recent
ON most_recent.sd_pidm = IA_COHORT_POP_UNDUP_TBL.co_pidm
  --CSI Data
LEFT JOIN ia_nl_csi
ON ia_nl_csi.student_ID = most_recent.umid
  -- Test Scores
LEFT JOIN
  (SELECT pidm,
    act_composite,
    act_math,
    act_english,
    SAT_VERBAL,
    SAT_MATHEMATICS,
    SAT_READING,
    SAT_VOCABULARY,
    SAT_WRITING,
    SAT_ESSAY,
    SAT_MC,
    MATH_PLACEMENT_CODE,
    ENG_READ_PLACEMENT_CODE,
    ENG_WRIT_PLACEMENT_CODE
  FROM um_test_score
  ) test_scores
ON test_scores.pidm          = IA_COHORT_POP_UNDUP_TBL.co_pidm
LEFT JOIN
  (SELECT pidm,
    ILSC_LEVEL,
    VGC_LEVEL
  FROM ia_um_test_score
  ) ia_um_test_scores
ON ia_um_test_scores.pidm          = IA_COHORT_POP_UNDUP_TBL.co_pidm
LEFT JOIN
  (SELECT FA_PIDM, TERM_CODE,PELL_EFC_AMOUNT FROM UM_FINAID_BY_TERM
  )FINAID
ON FINAID.FA_PIDM    = IA_COHORT_POP_UNDUP_TBL.co_pidm
AND FINAID.TERM_CODE = IA_COHORT_POP_UNDUP_TBL.CO_TERM_CODE_KEY

--where IA_COHORT_POP_UNDUP_TBL.co_pidm = '152270'
) 
;
COMMIT;
