--select to_number(nvl(hsch_gpa, '0')), PCOL_GPA_TRANSFERRED_1 from td_demographic


(--High School GPA
  select hsch_gpa, td_demographic.*
  from td_demographic 
  where REGEXP_REPLACE(td_demographic.hsch_gpa, '[^0-9.]+', '') != hsch_gpa
)
union
(
  select hsch_gpa, td_demographic.*
  from td_demographic 
  where length(REGEXP_REPLACE(td_demographic.hsch_gpa, '[.]+', '')) < length(hsch_gpa) - 1
);

(--ACT Math
  select td_test_score.act_math, td_test_score.*
  from td_test_score 
  where REGEXP_REPLACE(td_test_score.act_math, '[^0-9.]+', '') != td_test_score.act_math
)
union
(
  select td_test_score.act_math, td_test_score.*
  from td_test_score 
  where length(REGEXP_REPLACE(td_test_score.act_math, '[.]+', '')) < length(td_test_score.act_math) - 1
);

(--Comp Sc Placement
  select td_test_score.COMP_SC_PLACEMENT, td_test_score.*
  from td_test_score 
  where REGEXP_REPLACE(td_test_score.COMP_SC_PLACEMENT, '[^0-9.]+', '') != td_test_score.COMP_SC_PLACEMENT
)
union
(
  select td_test_score.COMP_SC_PLACEMENT, td_test_score.*
  from td_test_score 
  where length(REGEXP_REPLACE(td_test_score.COMP_SC_PLACEMENT, '[.]+', '')) < length(td_test_score.COMP_SC_PLACEMENT) - 1
);

(--Math placement
  select td_test_score.MATH_PLACEMENT, td_test_score.*
  from td_test_score 
  where REGEXP_REPLACE(td_test_score.MATH_PLACEMENT, '[^0-9.]+', '') != td_test_score.MATH_PLACEMENT
)
union
(
  select td_test_score.MATH_PLACEMENT, td_test_score.*
  from td_test_score 
  where length(REGEXP_REPLACE(td_test_score.MATH_PLACEMENT, '[.]+', '')) < length(td_test_score.MATH_PLACEMENT) - 1
);
