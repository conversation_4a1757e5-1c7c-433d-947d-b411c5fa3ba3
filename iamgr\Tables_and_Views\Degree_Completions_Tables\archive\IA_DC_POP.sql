/*******************************************************************************
primary degree from primary_major_1
*******************************************************************************/
create or replace view IA_DEGREE_COMPLETIONS as( 

  select
  popsel.PIDM,
  popsel.GRAD_TERM_CODE,
  popsel.HP_STEM_ORDER_DESC,
  popsel.HP_STEM_ORDER_NUM,
  popsel.DEGREE_MAJOR_CODE,
  popsel.DEGREE_SOURCE_DESC,
  popsel.DEGREE_SOURCE,
  popsel.DEGREE_SEQNO,
  popsel.DEGREE_CODE,
  decode ( row_number() over(partition by PIDM, degree_code order by <PERSON><PERSON><PERSON>_SOURCE, HP_STEM_ORDER_NUM ), 1, 'first major','second major') COMPLETION_TYPE --) popsel2
   from (
    select 
    pidm,
    GRAD_TERM_CODE,
    case
    when ia_cw_hp.degree_code is not null then 'Health'
    when ia_cw_stem.majr_code is not null then 'STEM'
    else 'Standard'
    end hp_stem_order_desc,
    case
    when ia_cw_hp.degree_code is not null then 1 
    when ia_cw_stem.majr_code is not null then 2
    else 3
    end hp_stem_order_num, 
    ia_um_degree.primary_major_1 degree_major_code,  
    'primary degree' degree_source_desc,
    1 degree_source,
    DEGREE_SEQNO,
    ia_um_degree.degree_code
    from ia_um_degree
    left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
    left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
    where  DEGREE_STATUS = 'AW'
    union all
    /*******************************************************************************
    second degree from primary_major_2
    *******************************************************************************/
    select 
    pidm,
    GRAD_TERM_CODE,
    'Standard' hp_stem_order_desc,
    3 hp_stem_order_num,
    ia_um_degree.primary_major_2 degree_major_code,
    'second major' degree_source_desc,
    2 degree_source,
    DEGREE_SEQNO,
    ia_um_degree.degree_code
    from ia_um_degree
    left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
    left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
    where 
    ia_um_degree.primary_major_2 is not null and
    DEGREE_STATUS = 'AW' ) popsel
    
);
