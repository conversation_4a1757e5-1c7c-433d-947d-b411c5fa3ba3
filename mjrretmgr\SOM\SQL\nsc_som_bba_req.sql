grant select on IA_CW_FY_TERM to mjrretmgr;
desc son_nurr_bsn_cohorts;

with base_sel as (
    select
    *
    from SOM_BBA_COHORTS som
    inner join iamgr.IA_CW_FY_TERM fy 
      on fy.fy_term_code = som.SD_TERM_CODE
    WHERE fy.fy = '22-23'                            --UPDATE THIS

),
POP_SEL AS (
    SELECT
        '1' ORDER_NUM,
        'H1' A,
        '002327' B,
        '00' C,
        'UNIVERSITY OF MICHIGAN FLINT' D,
        TO_CHAR(SYSDATE, 'YYYYMMDD') E,
        'CO' F,
        'I' G,
        cast(NULL as varchar2(256)) H,
        cast(NULL as varchar2(256)) I,
        cast(NULL as varchar2(256)) J,
        cast(NULL as varchar2(256)) K,
        cast(NULL as varchar2(256)) L
    FROM DUAL
    UNION ALL
    SELECT
        '2' ORDER_NUM,
        'D1' A,
        cast(NULL as varchar2(256)) B,
        CASE
            WHEN (FIRST_NAME IS NULL OR FIRST_NAME = '.') THEN 'Jon'
            ELSE SUBSTR(replace(FIRST_NAME, chr(39)),1,20)
        END C,
        SUBSTR(REGEXP_REPLACE(TRIM(MIDDLE_INITIAL), '[[:punct:] ]',NULL), 1 ,1) D,
       
        CASE
            WHEN (LAST_NAME IS NULL OR LAST_NAME = '.') THEN 'Doe'
            ELSE SUBSTR(replace(LAST_NAME, chr(39)),1,20)
        END E,
        SUBSTR(REGEXP_REPLACE(TRIM(NAME_SUFFIX), '[[:punct:] ]',NULL),1,5) F,
        TO_CHAR(BIRTHDATE, 'YYYYMMDD') G,
--        TO_CHAR(TO_NUMBER(SUBSTR('201910',1,4))-1 ||'0915') H,
        TO_CHAR(TO_NUMBER(SUBSTR((select min(sd_term_code) from base_sel),1,4)) ||'0915') H,
        cast(NULL as varchar2(256)) I,
        '002327' J,
        '00' K,
        TO_CHAR(SD_PIDM) L
    FROM BASE_SEL
    UNION ALL
    SELECT
        '3' ORDER_NUM,
        'T1' A,
        TO_CHAR(COUNT(SD_PIDM)+2) B,
        cast(NULL as varchar2(256)) C,
        cast(NULL as varchar2(256)) D,
        cast(NULL as varchar2(256)) E,
        cast(NULL as varchar2(256)) F,
        cast(NULL as varchar2(256)) G,
        cast(NULL as varchar2(256)) H,
        cast(NULL as varchar2(256)) I,
        cast(NULL as varchar2(256)) J,
        cast(NULL as varchar2(256)) K,
        cast(NULL as varchar2(256)) L
    FROM BASE_SEL
)
SELECT
    cast(POP_SEL.A as varchar2(256)) a,
    cast(POP_SEL.B as varchar2(256)) b,
    cast(POP_SEL.C as varchar2(256)) c,
    cast(POP_SEL.D as varchar2(256)) d,
    cast(POP_SEL.E as varchar2(256)) e,
    cast(POP_SEL.F as varchar2(256)) f,
    cast(POP_SEL.G as varchar2(256)) g,
    cast(POP_SEL.H as varchar2(256)) h,
    cast(POP_SEL.I as varchar2(256)) i,
    cast(POP_SEL.J as varchar2(256)) j,
    cast(POP_SEL.K as varchar2(256)) k,
    cast(POP_SEL.L as varchar2(256)) l
FROM POP_SEL
order by decode(a, 'H1', 1, 'D1', 2, '3')
;


    select
    fy.fy,
--    som.*
    count(*) headcount
    from SOM_BBA_COHORTS som
    inner join iamgr.IA_CW_FY_TERM fy 
      on fy.fy_term_code = som.SD_TERM_CODE
    where fy.fy is not null  
    group by
    fy.fy
    order by 
    1
    ;  