WITH DP1 AS (
select
COURSE,
SECTION_NUMBER,
XLST_GROUP,
case
when RD.CRSE_NUMBER > 500 then 'GR'
else 'UG'
end Level_code,
COUNT(*)HEADCOUNT
from
IA_TD_REGISTRATION_DETAIL RD
WHERE
RD.TD_TERM_CODE in ('202110')
AND RD.section_number not in ('90','91','92','93',
'94','95','96','97','98','99','IS','AT','TT','S1') 
--'W1','W2','W3','W4','W5','W6','W7','M1','M2','M3','M4')
AND RD.MEETING_SCHD_CODE_1 in ('L/L','L/D','MM','LAB')
group by
COURSE,
SECTION_NUMBER,
XLST_GROUP,
case
when RD.CRSE_NUMBER > 500 then 'GR'
else 'UG'
end
),DP2 AS(
SELECT DP1.*,
CASE WHEN XLST_GROUP IS NULL THEN 1
ELSE ROW_NUMBER() OVER (PARTITION BY XLST_GROUP ORDER BY SECTION_NUMBER )
END
ROW_COUNT
FROM DP1
),MAX_COUNT AS(
SELECT 
XLST_GROUP,
ROUND(1/MAX(ROW_COUNT),3)PERCENT_OF_XLIST
FROM DP2
GROUP BY
XLST_GROUP
),DP3 AS(
SELECT DP2.*,
NVL(MAX_COUNT.PERCENT_OF_XLIST,1)PERCENT_OF_XLIST
FROM DP2
LEFT JOIN MAX_COUNT ON MAX_COUNT.XLST_GROUP = DP2.XLST_GROUP
ORDER BY DP2.XLST_GROUP
)
SELECT * FROM DP3


;