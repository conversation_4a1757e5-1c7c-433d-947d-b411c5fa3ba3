CREATE OR R<PERSON>LACE VIEW 
  IA_FA_FUND_BASE AS
SELECT RT.RPRATRM_PIDM FA_PIDM,
dm.umid,
  dm.first_name || ' ' || dm.last_name name,
  dm.last_name "Last Name",
  dm.age,
  dm.ca_email email,
  sd.primary_major_1_desc "First Major",
  sd.primary_major_2_desc "Second Major",
  sd.PRIMARY_MINOR_1_DESC "Minor",
  sd.PRIMARY_DEGREE_DESC "Degree",
  sd.primary_college_desc "College",
  RT.RPRATRM_PERIOD FA_TERM_CODE,
  SUBSTR(RT.RPRATRM_AIDY_CODE,1,2)||'-'||SUBSTR(RT.RPRATRM_AIDY_CODE,3,4)FA_FY,
  RT.RPRATRM_AIDY_CODE FA_AID_YEAR_CODE,
  RT.RPRATRM_FUND_CODE FA_FUND_CODE,
  RB.RFRBASE_FSRC_CODE FA_FUND_SOURCE,
  RB.RFRBASE_FTYP_CODE FA_FUND_TYPE_CODE,
  RB.RFRBASE_DETAIL_CODE FA_FUND_DETAIL_CODE,
  RB.RFRBASE_FUND_TITLE FA_FUND_TITLE,
  RT.RPRATRM_OFFER_AMT FA_OFFER_AMT,
  RT.RPRATRM_PAID_AMT FA_PAID_AMT,
  RT.RPRATRM_ACCEPT_AMT FA_ACCEPT_AMT,
  RT.RPRATRM_ORIG_OFFER_AMT FA_ORIG_OFFER_AMT,
  RT.RPRATRM_ORIG_OFFER_DATE FA_ORIG_OFFER_DATE,
  RC2.RCRAPP2_PELL_PGI EFC,
  RC4.RCRAPP4_FISAP_INC FISAP_INC

FROM AIMSMGR.RPRATRM_EXT RT
INNER JOIN AIMSMGR.RFRBASE_EXT RB
    ON RB.RFRBASE_FUND_CODE = RT.RPRATRM_FUND_CODE
    LEFT JOIN
      (SELECT *
          FROM AIMSMGR.RCRAPP1_EXT RC1
          INNER JOIN AIMSMGR.RCRAPP2_EXT RC2
          ON RC2.RCRAPP2_PIDM            = RC1.RCRAPP1_PIDM
          AND RC2.RCRAPP2_AIDY_CODE      = RC1.RCRAPP1_AIDY_CODE
          AND RC2.RCRAPP2_SEQ_NO         = RC1.RCRAPP1_SEQ_NO
          AND RC2.RCRAPP2_INFC_CODE      = RC1.RCRAPP1_INFC_CODE
          WHERE RC1.RCRAPP1_CURR_REC_IND = 'Y'
      ) RC2 ON RC2.RCRAPP1_PIDM          = RT.RPRATRM_PIDM
            AND RC2.RCRAPP1_AIDY_CODE    = RT.RPRATRM_AIDY_CODE
     LEFT JOIN
      (SELECT *
          FROM AIMSMGR.RCRAPP1_EXT RC1
          INNER JOIN AIMSMGR.RCRAPP4_EXT RC4
          ON RC4.RCRAPP4_PIDM            = RC1.RCRAPP1_PIDM
          AND RC4.RCRAPP4_AIDY_CODE      = RC1.RCRAPP1_AIDY_CODE
          AND RC4.RCRAPP4_SEQ_NO         = RC1.RCRAPP1_SEQ_NO
          AND RC4.RCRAPP4_INFC_CODE      = RC1.RCRAPP1_INFC_CODE
          WHERE RC1.RCRAPP1_CURR_REC_IND = 'Y'
      ) RC4 ON RC4.RCRAPP1_PIDM          = RT.RPRATRM_PIDM
            AND RC4.RCRAPP1_AIDY_CODE    = RT.RPRATRM_AIDY_CODE
left join um_demographic dm on RT.RPRATRM_PIDM = dm.dm_pidm
left join um_student_data sd on RT.RPRATRM_PIDM = sd.sd_pidm
                             and RT.RPRATRM_PERIOD = sd.sd_term_code
;

select 
fa_fy,
count (distinct fa_pidm) headcount
from
IA_FA_FUND_BASE
where
fa_paid_amt > 0 
group by fa_fy
;

truncate table ia_fa_fund_base_tbl_bac;

insert into ia_fa_fund_base_tbl_bac
select * from ia_fa_fund_base_tbl;

select count (*) from ia_fa_fund_base_tbl;
select count (*) from ia_fa_fund_base_tbl_bac;


select 
fa_term_code,
count (*) 
from ia_fa_fund_base_tbl
group by
fa_term_code
;

DROP table ia_fa_fund_base_tbl;

create table ia_fa_fund_base_tbl as
select * from ia_fa_fund_base
--where fa_term_code = 202240
;
--(select current_term from um_current_term);