describe ia_td_admissions_applicant;

TRUNCATE TABLE TEMP_GSS;
insert into TEMP_GSS 
(
SELECT 
POPSEL.MAJOR,
POPSEL.CIP_CODE,
POPSEL.CIP_6_CODE,
POPSEL.GSS_FIELD_NAME,
POPSEL.GSS_CODE,
POPSEL.CGS_GRE_CROSSWALK_CODE

FROM (
WITH DP1 AS (
select 
distinct 
--PRIMARY_MAJOR_1_DESC MAJOR, 
PRIMARY_MAJOR_1_CIPC_CODE CIP_6_CODE
--from ia_td_student_data
from ia_td_admissions_applicant
where TERM_CODE_ENTRY >= '201110'
AND PRIMARY_LEVEL_CODE in ('GR','G2','G3','DR')
AND PRIMARY_MAJOR_1_CIPC_CODE IS NOT NULL

MINUS

SELECT 
DISTINCT
CIP_6_CODE
FROM
IA_CW_MJR_GSS_GRE
)
SELECT 
DISTINCT
PRIMARY_MAJOR_1_DESC MAJOR,
SUBSTR(PRIMARY_MAJOR_1_CIPC_CODE,1,2)||'.'||SUBSTR(PRIMARY_MAJOR_1_CIPC_CODE,3,6) CIP_CODE,
PRIMARY_MAJOR_1_CIPC_CODE CIP_6_CODE,
' ' GSS_FIELD_NAME,
' ' GSS_CODE,
' ' CGS_GRE_CROSSWALK_CODE

FROM DP1
LEFT OUTER JOIN
ia_td_admissions_applicant ON DP1.CIP_6_CODE = ia_td_admissions_applicant.PRIMARY_MAJOR_1_CIPC_CODE
WHERE 
TERM_CODE_ENTRY >= '201110'
AND PRIMARY_LEVEL_CODE in ('GR','G2','G3','DR')
AND PRIMARY_MAJOR_1_CIPC_CODE IS NOT NULL
AND PRIMARY_MAJOR_1_CIPC_CODE NOT IN ('000000')
AND PRIMARY_MAJOR_1_DESC NOT IN ('Major Not Declared')

ORDER BY CIP_CODE
)POPSEL
)
;
SELECT * FROM TEMP_GSS;
INSERT INTO IA_CW_MJR_GSS_GRE
SELECT * FROM TEMP_GSS;
commit;
