/*
This query is designed to create the IPEDS degreee Completions Upload. The table
being queried (IA_DEGREE_COMPLETIONS)will requre to be updated after census day
of the spring semester.  

Export data to comma dilimeted file and edit the file to remove trailing commas.
Upload to IPEDS as Key Value Pair.
Section D must be manually entered.  Use the Tableau Completions Dashboard
*/
with PS0 AS(
select 
PIDM,
AGE,
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
DECODE(NCES_CODE,5,4,7,5,17,6,18,6,6,7,8,7) COMPLETER_LEVEL,
IPEDS_RACE_CODE,
REPORT_ETHNICITY,
IPEDS_GENDER_CODE,
CASE
WHEN AGE <18 THEN 'AGE1'
WHEN AGE >=18 AND AGE <=24 THEN 'AGE2'
WHEN AGE >=25 AND AGE <=39 THEN 'AGE3'
WHEN AGE >=40 THEN 'AGE4'
ELSE 'AGE5'
END PARTD_AGE
FROM
IA_DEGREE_COMPLETIONS
where FISCAL_YEAR = '19-20'                --  Update before running
),
PS1 AS (
SELECT
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE,
COUNT(PIDM) AS DEG_COUNT
FROM
PS0
GROUP BY
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE
),
PS2 AS (
SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=A' C,
'MAJORNUM='||PS1.COMPLETION_TYPE_CODE D,
'CIPCODE='||SUBSTR(PS1.CIP_CODE, 1, 2) ||'.'||SUBSTR(PS1.CIP_CODE, 3, 6) E,
'AWLEVEL='||PS1.NCES_CODE F,
'RACE='||PS1.IPEDS_RACE_CODE  G,
'SEX='||PS1.IPEDS_GENDER_CODE H,
'COUNT='||PS1.DEG_COUNT I
FROM PS1
),
PS3 AS(
SELECT
PS2.*,
CASE
WHEN 
(E = 'CIPCODE=30.9999' AND F = 'AWLEVEL=5') --UG BAS APLS
OR (E = 'CIPCODE=52.0201' AND F = 'AWLEVEL=5') --UG BBA BUS
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=5') --UG BSN NURR, NURG
OR (E = 'CIPCODE=51.0908' AND F = 'AWLEVEL=5') --UG BS BSRT
OR (E = 'CIPCODE=44.0701' AND F = 'AWLEVEL=5') --UG BSW SWK,SUTI (Sub Abuse)
OR (E = 'CIPCODE=51.0701' AND F = 'AWLEVEL=5') --UG BS RSP
OR (E = 'CIPCODE=51.0908' AND F = 'AWLEVEL=5') --UG BS RSP
OR (E = 'CIPCODE=09.0100' AND F = 'AWLEVEL=7') --GR MA ACOM
OR (E = 'CIPCODE=13.0501' AND F = 'AWLEVEL=7') --GR MA ETEC
OR (E = 'CIPCODE=24.0199' AND F = 'AWLEVEL=7') --GR MA LBS
OR (E = 'CIPCODE=11.0101' AND F = 'AWLEVEL=7') --GR MS CAIS
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=7') --GR MSN NURN
OR (E = 'CIPCODE=13.1003' AND F = 'AWLEVEL=7') --GR EDU 
OR (E = 'CIPCODE=13.1001' AND F = 'AWLEVEL=7') --GR EDU INED
OR (E = 'CIPCODE=13.1206' AND F = 'AWLEVEL=7') --GR EDU LITE
OR (E = 'CIPCODE=13.1502' AND F = 'AWLEVEL=7') --GR EDU
OR (E = 'CIPCODE=51.2308' AND F = 'AWLEVEL=18') --DR PTPP
OR (E = 'CIPCODE=51.3804' AND F = 'AWLEVEL=18') --DR DRAP
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=18') --DR DNP
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=6') --DR NUR POST BACH CERT
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=8') --DR NUR POST GRAD CERT
OR (E = 'CIPCODE=51.2308' AND F = 'AWLEVEL=8') --PT POST GRAD CERT
THEN '1'
ELSE '2'
END DIS_ED
FROM PS2
),
PS4 AS(
SELECT DISTINCT PIDM,IPEDS_GENDER_CODE,IPEDS_RACE_CODE,COMPLETER_LEVEL, 
MAX(AGE) AGE
FROM PS0
GROUP BY PIDM,IPEDS_GENDER_CODE,IPEDS_RACE_CODE,COMPLETER_LEVEL
),
PART_C_A AS(
SELECT 
DISTINCT
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE, 
PIDM
FROM PS0
WHERE COMPLETION_TYPE_CODE = 1
),
PART_C AS(
SELECT 
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE, 
COUNT(*) DEMO_COUNT 
FROM PART_C_A
GROUP BY 
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE
)
SELECT A,B,C,D,E,F,G,H,I,
'' j,'' K,'' L,'' M,'' N,'' O,'' P,'' Q,'' R,'' S,'' T
FROM PS3

UNION ALL

SELECT DISTINCT A,B,'PART=B' C,D,E,F,'DistanceED='||DIS_ED G,'' H,'' I,
'' j,'' K,'' L,'' M,'' N,'' O,'' P,'' Q,'' R,'' S,'' T
FROM PS3 --WHERE J = '1'

UNION ALL

SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=C' C,
'RACE='||PART_C.IPEDS_RACE_CODE  D,
'SEX='||PART_C.IPEDS_GENDER_CODE E,
'COUNT='||PART_C.DEMO_COUNT F,'' G,'' H,'' I,
'' j,'' K,'' L,'' M,'' N,'' O,'' P,'' Q,'' R,'' S,'' T
FROM PART_C

UNION ALL

SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=D' C,
'CTLEVEL='||COMPLETER_LEVEL D,
'CRACE15='||DECODE(IPEDS_GENDER_CODE,1,1,0) E,
'CRACE16='||DECODE(IPEDS_GENDER_CODE,2,1,0) F,
'CRACE17='||DECODE(IPEDS_RACE_CODE,1,1,0) G,
'CRACE41='||DECODE(IPEDS_RACE_CODE,2,1,0) H,
'CRACE42='||DECODE(IPEDS_RACE_CODE,3,1,0) I,
'CRACE43='||DECODE(IPEDS_RACE_CODE,4,1,0) J,
'CRACE44='||DECODE(IPEDS_RACE_CODE,5,1,0) K,
'CRACE45='||DECODE(IPEDS_RACE_CODE,6,1,0) L,
'CRACE46='||DECODE(IPEDS_RACE_CODE,7,1,0) M,
'CRACE47='||DECODE(IPEDS_RACE_CODE,8,1,0) N,
'CRACE23='||DECODE(IPEDS_RACE_CODE,9,1,0) O,
'AGE1='||CASE WHEN AGE<18 THEN 1 ELSE 0 END P,
'AGE2='||CASE WHEN AGE >= 18 AND AGE <= 24 THEN 1 ELSE 0 END Q,
'AGE3='||CASE WHEN AGE >= 25 AND AGE <= 39 THEN 1 ELSE 0 END R,
'AGE4='||CASE WHEN AGE >= 40 THEN 1 ELSE 0 END S,
'AGE5='||CASE WHEN AGE IS NULL THEN 1 ELSE 0 END T
from PS4
--select * from ps4
;

