select 
case 
when popsel.CRSE_NUMBER <300 then 'LL-UG'
when popsel.CRSE_NUMBER <500 and popsel.CRSE_NUMBER >=300 then 'UL-UG'
else 'GR'
end crse_level,
--count (distinct popsel.td_pidm)as TD_SECTION_ENROLLMENT,
sum (popsel.TD_credit_hours) as TD_CREDIT_HOURS

from (
--primary instructor
select
IA_TD_REGISTRATION_DETAIL.pidm td_pidm,
IA_TD_REGISTRATION_DETAIL.TD_TERM_CODE,
IA_TD_REGISTRATION_DETAIL.CRSE_NUMBER,
IA_TD_REGISTRATION_DETAIL.CREDIT_HOURS TD_credit_hours

from
IA_TD_REGISTRATION_DETAIL

)popsel

WHERE
--popsel.TD_TERM_CODE in ('201610')
popsel.TD_TERM_CODE in ('201440','201510','201520','201530')

group by
case 
  when popsel.CRSE_NUMBER <300 then 'LL-UG'
  when popsel.CRSE_NUMBER <500 and popsel.CRSE_NUMBER >=300 then 'UL-UG'
  else 'GR'
end

;


select 
DEGREE_MAJOR_CODE,
--nces_desc,
--NCES_CODE,
count (*)
from IA_DEGREE_COMPLETIONS
where fiscal_year = 'FY 14-15' and
COMPLETION_TYPE_CODE = 1
and NCES_CODE in (7,18,6)
and CIP_CODE like '51%'
group by DEGREE_MAJOR_CODE--,
--nces_desc,NCES_CODE

select distinct primary_major_1, primary_major_1_desc
from IA_TD_STUDENT_DATA
where sd_term_code >= '201110'
order by 1