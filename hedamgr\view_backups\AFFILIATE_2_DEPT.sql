--------------------------------------------------------
--  File created - Friday-May-20-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View AFFILIATE_2_DEPT
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "HEDAMGR"."AFFILIATE_2_DEPT" ("ID", "HED__ACCOUNT__C", "HED__CONTACT__C", "HED__PRIMARY__C", "ROLE__C") AS 
  with pop_sel as(
  select 
  sibinst.sibinst_pidm pop_sel_pidm,
  sibinst.sibinst_fcst_code,
  sirdpcl.sirdpcl_term_code_eff,
  sirdpcl.sirdpcl_dept_code
  from aimsmgr.sibinst
  inner join aimsmgr.sirdpcl on sirdpcl.sirdpcl_pidm = sibinst.sibinst_pidm
                             and sirdpcl.sirdpcl_term_code_eff = (select max(s1.sirdpcl_term_code_eff)
                                                                  from aimsmgr.sirdpcl s1
                                                                  where s1.sirdpcl_pidm = sirdpcl.sirdpcl_pidm)
  where sibinst.sibinst_term_code_eff = (select max(s1.sibinst_term_code_eff)
                                         from aimsmgr.sibinst s1
                                         where s1.sibinst_pidm = sibinst.sibinst_pidm)
  and not exists (select 'is a dup'
                  from aimsmgr.sprhold sprhold
                  where sprhold.sprhold_pidm = sibinst.sibinst_pidm
                  and sprhold.sprhold_hldd_code = 'DP')
  and sibinst.sibinst_fcst_code = 'AC'
  order by 1
)
select
aff.id,
account.id hed__account__c,
contact.id hed__contact__c,
'true' hed__primary__c,
'Faculty/Staff' role__c
from pop_sel
inner join account on account.um_external_key__c = pop_sel.sirdpcl_dept_code || ' (Dept)'
inner join contact on contact.pidm__c = pop_sel.pop_sel_pidm
inner join um_demographic on um_demographic.dm_pidm = pop_sel.pop_sel_pidm
                          and um_demographic.umid not like ('ADV-%')
left outer join hed__affiliation__c aff on aff.hed__account__c = account.id
                                        and aff.hed__contact__c = contact.id

--where um_demographic.dm_pidm in (55342, 10475)
--where ca_email = 'false'

order by 1
;
