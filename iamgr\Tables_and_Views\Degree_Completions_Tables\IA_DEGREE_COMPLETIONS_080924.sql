TRUNCATE TABLE IA_DEGREE_COMPLETIONS_BAC; 

INSERT INTO IA_DEGREE_COMPLETIONS_BAC
SELECT * FROM IA_DEGREE_COMPLETIONS;
commit;

select count (*) from IA_DEGREE_COMPLETIONS;
select count (*) from IA_DEGREE_COMPLETIONS_BAC;

/*******************************************************************************
author: Dan Getty Institutional Analysis
Purpose: This Query is to build a degree completions table for reporting
that will move duplicate degrees (ie 1 of 2BA's or 2BS's) from 1st degree to 2nd
degree.  The query will also need to identify if a degree is a STEM or Health
field degree and give these degrees priority when deciding which degree to
keep as the first major completion and which to push to the second degree
following State reporting methodologies.

Table Name: IA_DEGREE_COMP_TBL
Grain: 1 row/FY/Student
*******************************************************************************/
insert into IA_DEGREE_COMPLETIONS 
/*******************************************************************************
  first and second major completions from primary_major_1
*******************************************************************************/

with popsel as 
    (SELECT ia_um_degree.pidm,
      ia_um_degree.UMID,
      ia_um_student_data.age,
      ia_um_degree.GRAD_TERM_CODE,
      ia_um_degree.GRAD_DATE,
      ia_um_degree.REPORT_ETHNICITY,
      ia_um_degree.gender,
      ia_um_degree.LEVEL_CODE PRIMARY_LEVEL_CODE,
--      ia_um_student_data.PRIMARY_LEVEL_CODE,
      CASE
        WHEN ia_cw_hp.degree_code IS NOT NULL
        THEN 'Health'
        WHEN ia_cw_stem.majr_code IS NOT NULL
        THEN 'STEM'
        ELSE 'Standard'
      END hp_stem_order_desc,
      CASE
        WHEN ia_cw_hp.degree_code IS NOT NULL
        THEN 1
        WHEN ia_cw_stem.majr_code IS NOT NULL
        THEN 2
        ELSE 3
      END hp_stem_order_num,
      ia_um_degree.primary_major_1 degree_major_code,
      ia_um_degree.PRIMARY_MAJOR_1_DESC degree_major_desc,
      ia_um_degree.primary_major_1_cipc_code cip_code,
      ia_um_degree.PRIMARY_PROGRAM,
      ia_um_degree.PRIMARY_PROGRAM_DESC,
      ia_um_student_data.ONLINE_COURSES_ONLY_IND online_only,
      ia_cw_nces_code.IA_NCES_CODE nces_code,
      ia_cw_nces_code.IA_NCES_DESC nces_desc,
      'primary degree' degree_source_desc,
      1 degree_source,
      ia_um_degree.degree_code,
      ia_um_degree.DEGREE_DESC
    FROM ia_um_degree
    LEFT JOIN ia_um_student_data
    ON ia_um_student_data.sd_pidm       = ia_um_degree.pidm
    AND ia_um_student_data.sd_term_code = ia_um_degree.grad_term_code
    LEFT JOIN ia_cw_hp
    ON ia_cw_hp.degree_code      = ia_um_degree.degree_code
    AND ia_cw_hp.primary_program = ia_um_degree.primary_program
    LEFT JOIN ia_cw_stem
    ON ia_cw_stem.majr_code = ia_um_degree.primary_major_1
    LEFT JOIN ia_cw_nces_code
    ON IA_CW_NCES_CODE.degree_code      = ia_um_degree.degree_code
    AND IA_CW_NCES_CODE.MAJR_CODE = ia_um_degree.primary_major_1
    WHERE DEGREE_STATUS                 = 'AW'
  --    and ia_um_degree.pidm = '109335'
    
    UNION ALL
/*******************************************************************************
    second majors from primary_major_2
*******************************************************************************/
    SELECT ia_um_degree.pidm,
      ia_um_degree.UMID,
      ia_um_student_data.age,
      ia_um_degree.GRAD_TERM_CODE,
      ia_um_degree.GRAD_DATE,
      ia_um_degree.REPORT_ETHNICITY,
      ia_um_degree.gender,
      ia_um_degree.LEVEL_CODE PRIMARY_LEVEL_CODE,
--      ia_um_student_data.PRIMARY_LEVEL_CODE,
      'Standard' hp_stem_order_desc,
      3 hp_stem_order_num,
      ia_um_degree.primary_major_2 degree_major_code,
      ia_um_degree.PRIMARY_MAJOR_2_DESC degree_major_desc,
      ia_um_degree.primary_major_2_cipc_code cip_code,
      ia_um_degree.PRIMARY_PROGRAM,
      ia_um_degree.PRIMARY_PROGRAM_DESC,
      ia_um_student_data.ONLINE_COURSES_ONLY_IND online_only,
      ia_cw_nces_code.IA_NCES_CODE nces_code,
      ia_cw_nces_code.IA_NCES_DESC nces_desc,
      'second major' degree_source_desc,
      2 degree_source,
      ia_um_degree.degree_code,
      ia_um_degree.DEGREE_DESC
    FROM ia_um_degree
    LEFT JOIN ia_um_student_data
    ON ia_um_student_data.sd_pidm       = ia_um_degree.pidm
    AND ia_um_student_data.sd_term_code = ia_um_degree.grad_term_code
    LEFT JOIN ia_cw_hp
    ON ia_cw_hp.degree_code      = ia_um_degree.degree_code
    AND ia_cw_hp.primary_program = ia_um_degree.primary_program
    LEFT JOIN ia_cw_stem
    ON ia_cw_stem.majr_code = ia_um_degree.primary_major_1
    LEFT JOIN ia_cw_nces_code
    ON IA_CW_NCES_CODE.degree_code      = ia_um_degree.degree_code
    AND IA_CW_NCES_CODE.MAJR_CODE = ia_um_degree.primary_major_2
    WHERE ia_um_degree.primary_major_2 IS NOT NULL
    AND DEGREE_STATUS                   = 'AW'
         ), 
    
    
    popsel2 as ( 
      SELECT 
    '23-24' Fiscal_Year,                                    -- UPDATE THIS FIELD
    popsel.PIDM,
    popsel.UMID,
    popsel.age,
    popsel.GRAD_TERM_CODE,
    popsel.GRAD_DATE,
    DECODE(popsel.REPORT_ETHNICITY,
      'Nonresident Alien',1,
      'Hispanic or Latino',2,
      'American Indian or Alaska Native',3,
      'Asian',4,
      'Black or African American',5,
      'Native Hawaiian and Other Pacific Islander',6,
      'White',7,
      'Two or more races',8,9)IPEDS_RACE_CODE,
    popsel.REPORT_ETHNICITY,
    DECODE(popsel.gender,'F',2,1)IPEDS_GENDER_CODE,
    popsel.gender,
    popsel.PRIMARY_LEVEL_CODE,
    popsel.DEGREE_CODE,
    popsel.DEGREE_DESC,
    popsel.DEGREE_MAJOR_CODE,
    popsel.degree_major_desc,
    popsel.PRIMARY_PROGRAM,
    popsel.PRIMARY_PROGRAM_DESC,
    popsel.online_only,
    popsel.HP_STEM_ORDER_DESC,
    popsel.cip_code,
    popsel.NCES_CODE,
    popsel.nces_desc,
    popsel.DEGREE_SOURCE_DESC,
    
--    CASE
--    WHEN (DEGREE_CODE = 'CERG' or DEGREE_CODE = 'CER' or DEGREE_CODE = 'CERT') THEN 1
--    ELSE DECODE ( ROW_NUMBER() OVER(PARTITION BY PIDM, DEGREE_CODE ORDER BY 
--         DEGREE_SOURCE, HP_STEM_ORDER_NUM ), 1, 1, 2) 
--    END
--    COMPLETION_TYPE_CODE,
--
    DECODE (ROW_NUMBER() OVER(PARTITION BY PIDM, DEGREE_CODE ORDER BY 
        DEGREE_SOURCE, HP_STEM_ORDER_NUM ), 1, 1, 2) COMPLETION_TYPE_CODE,

--     CASE
--    WHEN (DEGREE_CODE = 'CERG' or DEGREE_CODE = 'CER' or DEGREE_CODE = 'CERT') THEN 'first major'
--    ELSE DECODE ( ROW_NUMBER() OVER(PARTITION BY PIDM, DEGREE_CODE ORDER BY 
--    DEGREE_SOURCE, HP_STEM_ORDER_NUM ), 1, 'first major','second major') 
--    END
--    COMPLETION_TYPE 
    
    DECODE ( ROW_NUMBER() OVER(PARTITION BY PIDM, DEGREE_CODE ORDER BY 
        DEGREE_SOURCE, HP_STEM_ORDER_NUM ), 1, 'first major','second major') 
        COMPLETION_TYPE 
    
    
    from popsel
  where popsel.GRAD_DATE >= '01-JUL-23'                     -- UPDATE THIS FIELD
  and Grad_date <= '30-JUN-24'                              -- UPDATE THIS FIELD
  
--  AND popsel.pidm = ''
--  and popsel.pidm = '109335'  --*****************
 -- )
 )
 select * from popsel2
-- where pidm = 71333
;

 select * from um_degree
 where pidm = 71333  
and GRAD_DATE >= '01-JUL-23'                     -- UPDATE THIS FIELD
  and Grad_date <= '30-JUN-24'  
;
  
commit;

select * from IA_DEGREE_COMPLETIONS 
where Fiscal_Year = '23-24'                                 -- UPDATE THIS FIELD
;



delete from IA_DEGREE_COMPLETIONS 
where Fiscal_Year = '23-24'                                 -- UPDATE THIS FIELD
;

commit;