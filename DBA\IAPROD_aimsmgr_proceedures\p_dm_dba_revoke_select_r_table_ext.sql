--------------------------------------------------------
--  DDL for Procedure P_DM_DBA_REVOKE_SELECT_R_TABLE_EXT
--------------------------------------------------------
set define off;

  CREATE OR REPLACE EDITIONABLE PROCEDURE "AIMSMGR"."P_DM_DBA_REVOKE_SELECT_R_TABLE_EXT" (id_in varchar2) as

 obj_name   VARCHAR2(4000);
 CURSOR table_cur IS
SELECT
  table_name
 FROM
  all_tables
 WHERE
   owner = 'AIMSMGR'
  AND ( table_name LIKE 'R%\_EXT' escape '\');
 table_rec  table_cur%rowtype;
BEGIN
 dbms_output.enable(NULL);
 OPEN table_cur;
 LOOP
  FETCH table_cur INTO table_rec;
  EXIT WHEN table_cur%notfound;
  obj_name := 'REVOKE SELECT ON aimsmgr.'
              || table_rec.table_name
              || ' FROM '||id_in;
  dbms_output.put_line(obj_name);
  EXECUTE IMMEDIATE obj_name;
 END LOOP;

 CLOSE table_cur;
END p_dm_dba_revoke_select_r_table_ext;


/
