create or replace view IA_FIL AS

WITH popsel AS (
      --primary instructor
       SELECT
  ia_td_registration_detail.pidm                           td_pidm,
  ia_td_registration_detail.td_term_code,
  ia_td_registration_detail.fy,
  um_catalog_schedule.coll_code,
  um_catalog_schedule.coll_desc,
  um_catalog_schedule.dept_code,
  um_catalog_schedule.dept_desc,
  ia_td_registration_detail.crn_key,
  ia_td_registration_detail.subj_code,
  ia_td_registration_detail.crse_number,
  ia_td_registration_detail.section_number,
  ia_td_registration_detail.meeting_schd_code_1,
  ia_td_registration_detail.xlst_group,
  ia_td_registration_detail.credit_hours                   td_credit_hours,
  um_catalog_schedule.primary_instructor_last_name
  || ', '
  || um_catalog_schedule.primary_instructor_first_name     instructor_name,
  '1'                                                      AS instructor_number
 FROM
  ia_td_registration_detail
  FULL OUTER JOIN um_catalog_schedule ON um_catalog_schedule.term_code_key = ia_td_registration_detail.td_term_code
                                         AND um_catalog_schedule.crn_key = ia_td_registration_detail.crn_key
 UNION ALL
      --secondary instructor
       SELECT
  ia_td_registration_detail.pidm                    td_pidm,
  ia_td_registration_detail.td_term_code,
  ia_td_registration_detail.fy,
  um_catalog_schedule.coll_code,
  um_catalog_schedule.coll_desc,
  um_catalog_schedule.dept_code,
  um_catalog_schedule.dept_desc,
  ia_td_registration_detail.crn_key,
  ia_td_registration_detail.subj_code,
  ia_td_registration_detail.crse_number,
  ia_td_registration_detail.section_number,
  ia_td_registration_detail.meeting_schd_code_1,
  ia_td_registration_detail.xlst_group,
  ia_td_registration_detail.credit_hours            td_credit_hours,
  um_catalog_schedule.instructor_last_name2
  || ', '
  || um_catalog_schedule.instructor_first_name2     instructor_name,
  '2'                                               AS instructor_number
 FROM
  ia_td_registration_detail
  FULL OUTER JOIN um_catalog_schedule ON um_catalog_schedule.term_code_key = ia_td_registration_detail.td_term_code
                                         AND um_catalog_schedule.crn_key = ia_td_registration_detail.crn_key
 UNION ALL
      --Third instructor
       SELECT
  ia_td_registration_detail.pidm                    td_pidm,
  ia_td_registration_detail.td_term_code,
  ia_td_registration_detail.fy,
  um_catalog_schedule.coll_code,
  um_catalog_schedule.coll_desc,
  um_catalog_schedule.dept_code,
  um_catalog_schedule.dept_desc,
  ia_td_registration_detail.crn_key,
  ia_td_registration_detail.subj_code,
  ia_td_registration_detail.crse_number,
  ia_td_registration_detail.section_number,
  ia_td_registration_detail.meeting_schd_code_1,
  ia_td_registration_detail.xlst_group,
  ia_td_registration_detail.credit_hours            td_credit_hours,
  um_catalog_schedule.instructor_last_name3
  || ', '
  || um_catalog_schedule.instructor_first_name3     instructor_name,
  '3'                                               AS instructor_number
 FROM
  ia_td_registration_detail
  FULL OUTER JOIN um_catalog_schedule ON um_catalog_schedule.term_code_key = ia_td_registration_detail.td_term_code
                                         AND um_catalog_schedule.crn_key = ia_td_registration_detail.crn_key
), popsel2 AS (
 SELECT
  popsel.td_term_code,
  popsel.fy,
  CASE
   WHEN popsel.instructor_name = ', '
        AND popsel.instructor_number = 1 THEN
    'keep'
   WHEN popsel.instructor_name = ', '
        AND popsel.instructor_number <> 1 THEN
    'remove'
   ELSE
    'keep'
  END                               ins_clean,
  CASE
   WHEN popsel.coll_code = '00'
        AND ( popsel.dept_code = 'HON'
              OR popsel.dept_code = 'UNV' ) THEN
    'AS'
   WHEN popsel.coll_code = '00'
        AND popsel.dept_code = 'SEC' THEN
    'EH'
   ELSE
    popsel.coll_code
  END                               coll_code,
  popsel.crn_key,
  popsel.subj_code,
  popsel.crse_number,
  popsel.section_number,
  popsel.meeting_schd_code_1,
  popsel.xlst_group,
  popsel.instructor_name,
  popsel.instructor_number,
  CASE
   WHEN popsel.crse_number < 300 THEN
    'LL-UG'
   WHEN popsel.crse_number < 500
        AND popsel.crse_number >= 300 THEN
    'UL-UG'
   ELSE
    'GR'
  END                               crse_level,
  COUNT(popsel.td_pidm)            AS td_section_enrollment,
  SUM(popsel.td_credit_hours)      AS td_credit_hours
 FROM
  popsel
 WHERE
  popsel.fy = (
   SELECT
    substr(current_aidy_code, 1, 2)
    || '-'
    || substr(current_aidy_code, 3, 4) fy
   FROM
    um_current_term
  )             --update for FY
      --and
--      popsel.TD_TERM_CODE >= 201240
     GROUP BY
  fy,
  popsel.td_term_code,
  popsel.fy,
  CASE
    WHEN popsel.coll_code = '00'
         AND ( popsel.dept_code = 'HON'
               OR popsel.dept_code = 'UNV' ) THEN
     'AS'
    WHEN popsel.coll_code = '00'
         AND popsel.dept_code = 'SEC' THEN
     'EH'
    ELSE
     popsel.coll_code
  END,
  CASE
    WHEN popsel.instructor_name = ', '
         AND popsel.instructor_number = 1 THEN
     'keep'
    WHEN popsel.instructor_name = ', '
         AND popsel.instructor_number <> 1 THEN
     'remove'
    ELSE
     'keep'
  END,
  popsel.crn_key,
  popsel.subj_code,
  popsel.crse_number,
  popsel.section_number,
  popsel.meeting_schd_code_1,
  popsel.xlst_group,
  popsel.instructor_name,
  popsel.instructor_number,
  CASE
    WHEN popsel.crse_number < 300 THEN
     'LL-UG'
    WHEN popsel.crse_number < 500
         AND popsel.crse_number >= 300 THEN
     'UL-UG'
    ELSE
     'GR'
  END
),popsel3 as (
SELECT
 fy,
 td_term_code,
 coll_code,
 crn_key,
 subj_code,
 crse_number,
 section_number,
 meeting_schd_code_1,
 xlst_group,
 instructor_name,
 instructor_number,
 crse_level,
 td_section_enrollment,
 td_credit_hours
FROM
 popsel2
WHERE
 ins_clean = 'keep'
ORDER BY
 td_term_code,
 subj_code,
 crse_number,
 section_number,
 instructor_number)
 
,DP2 AS(
SELECT popsel3.*,
CASE WHEN XLST_GROUP IS NULL THEN 1
ELSE ROW_NUMBER() OVER (PARTITION BY td_term_code, XLST_GROUP ORDER BY SECTION_NUMBER )
END
ROW_COUNT
FROM popsel3
)

,MAX_COUNT AS(
SELECT 
td_term_code,
XLST_GROUP,
ROUND(1/MAX(ROW_COUNT),3)PERCENT_OF_XLIST
FROM DP2
GROUP BY
td_term_code,
XLST_GROUP
)

,DP3 AS(
SELECT 
--distinct
--DP2.*,
 DP2.fy,
 DP2.td_term_code,
 DP2.coll_code,
 DP2.crn_key,
 DP2.subj_code,
 DP2.crse_number,
 DP2.section_number,
 DP2.meeting_schd_code_1,
 DP2.xlst_group,
 DP2.instructor_name,
 dp2.instructor_number,
 DP2.crse_level,
 DP2.td_section_enrollment,
 DP2.td_credit_hours,
NVL(MAX_COUNT.PERCENT_OF_XLIST,1)PERCENT_OF_XLIST
FROM DP2
LEFT JOIN MAX_COUNT ON MAX_COUNT.td_term_code = DP2.td_term_code
and MAX_COUNT.XLST_GROUP = DP2.XLST_GROUP
ORDER BY DP2.XLST_GROUP
)
SELECT distinct dp3.* FROM dp3 
order by 9,2
--where td_term_code = '202110'

 
 
 
 ;