with pop_sel as(
  select
  td_term_code,
  pcol_gpa_transferred_1,
  case
    when pcol_gpa_transferred_1 >= 3.5 then '4.00-3.50'
    when pcol_gpa_transferred_1 < 3.5 and pcol_gpa_transferred_1 >= 3.0 then '3.49-3.00'
    when pcol_gpa_transferred_1 < 3.0 and pcol_gpa_transferred_1 >= 2.7 then '2.99-2.70'
    when pcol_gpa_transferred_1 < 2.7 and pcol_gpa_transferred_1 >= 2.5 then '2.69-2.50'
    when pcol_gpa_transferred_1 < 2.5 and pcol_gpa_transferred_1 >= 2.0 then '2.49-2.00'
    else '0.00-1.99'
  end cut_score
  from td_demographic
  inner join( 
    select *
    from td_student_data
  )sd_join on sd_join.sd_pidm = td_demographic.dm_pidm
           and sd_join.sd_term_code = td_demographic.td_term_code
  where sd_join.student_type_code = 'T'
  and (td_demographic.pcol_gpa_transferred_1 is not null or
       td_demographic.pcol_gpa_transferred_1 > 0)
)
,
total_count as (
 select
  td_term_code,
  count(*) NUMBER_PER_CUT_SCORE
  from td_demographic
  inner join( 
    select *
    from td_student_data
  )sd_join on sd_join.sd_pidm = td_demographic.dm_pidm
           and sd_join.sd_term_code = td_demographic.td_term_code
  where sd_join.student_type_code = 'T'
  and (td_demographic.pcol_gpa_transferred_1 is not null or
       td_demographic.pcol_gpa_transferred_1 > 0)
  group by td_term_code, sd_join.student_type_code
)
select
pop_sel.td_term_code,
pop_sel.cut_score,
count(pop_sel.pcol_gpa_transferred_1) number_per_cut_score,
to_char(((count(pop_sel.pcol_gpa_transferred_1) / tc_join.number_per_cut_score) * 100), '99.00') || '%' gpa_dist
from pop_sel
inner join(
  select * 
  from total_count
)tc_join on tc_join.td_term_code = pop_sel.td_term_code
group by pop_sel.td_term_code, pop_sel.cut_score, tc_join.number_per_cut_score
order by pop_sel.td_term_code, pop_sel.cut_score



 