with pop_sel as(
  select
  spriden.spriden_pidm,
  spriden.spriden_id,
  shrtgpa_join.shrtgpa_term_code,
  shrtgpa_join.sgbstdn_levl_code,
  shrtgpa_join.stvdegc_acat_code,
  shrtgpa_join.sgbstdn_styp_code,
  shrtgpa_join.sgbstdn_resd_code,
  shrtgpa_join.shrtgpa_gpa,
  (select sum(shrtgpa.shrtgpa_gpa_hours) from shrtgpa where shrtgpa.shrtgpa_pidm = spriden.spriden_pidm
                                                      and shrtgpa.shrtgpa_levl_code = shrtgpa_join.sgbstdn_levl_code
                                                      and shrtgpa.shrtgpa_term_code <= shrtgpa_join.shrtgpa_term_code) overall_gpa_hours,
  (select sum(shrtgpa.shrtgpa_quality_points) from shrtgpa where shrtgpa.shrtgpa_pidm = spriden.spriden_pidm
                                                           and shrtgpa.shrtgpa_levl_code = shrtgpa_join.sgbstdn_levl_code
                                                           and shrtgpa.shrtgpa_term_code <= shrtgpa_join.shrtgpa_term_code) overall_quality_points

  from spriden
  -- start term of our student popsel
  inner join(
    select 
    max(stvterm.stvterm_code) as start_term_code
    from stvterm
    where stvterm.stvterm_code = '201630'
  ) start_term on start_term.start_term_code <> '999999'
  -- end term of our student pop sel
  inner join(
    select max(stvterm.stvterm_code) as end_term_code
    from stvterm
    where stvterm.stvterm_code = '201740'
  ) end_term on end_term.end_term_code <> '999999'
  -- our begining of time (first banner term)
  inner join(
    select 
    stvterm.stvterm_code as bobt_term_code
    from stvterm
    where stvterm.stvterm_code = '199910'
  ) begining_of_banner_term on begining_of_banner_term.bobt_term_code <> '999999'
  inner join(
    select
    shrtgpa.shrtgpa_pidm,
    shrtgpa.shrtgpa_term_code,
    shrtgpa.shrtgpa_gpa,
    shrtgpa.shrtgpa_gpa_type_ind,
    sgbstdn_join.sgbstdn_levl_code,
    sgbstdn_join.sgbstdn_degc_code_1,
    sgbstdn_join.stvdegc_acat_code,
    sgbstdn_join.sgbstdn_styp_code,
    sgbstdn_join.sgbstdn_resd_code
    from shrtgpa
    inner join(
      select
      sgbstdn.sgbstdn_pidm,
      sgbstdn.sgbstdn_term_code_eff,
      sgbstdn.sgbstdn_levl_code,
      sgbstdn.sgbstdn_degc_code_1,
      sgbstdn.sgbstdn_resd_code,
      (select stvdegc.stvdegc_acat_code from stvdegc where stvdegc.stvdegc_code = sgbstdn.sgbstdn_degc_code_1) stvdegc_acat_code,
      sgbstdn.sgbstdn_styp_code
      from sgbstdn
    ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                   and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
                   and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                             from sgbstdn sgbstdn_1
                                                             where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                             and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
  )shrtgpa_join on shrtgpa_join.shrtgpa_pidm = spriden.spriden_pidm
                and shrtgpa_join.shrtgpa_term_code >= begining_of_banner_term.bobt_term_code
                and shrtgpa_join.shrtgpa_term_code <= end_term.end_term_code
                and shrtgpa_join.shrtgpa_gpa_type_ind = 'I'
  
  -- has to have a UID
  inner join(
    select 
    goradid.goradid_pidm,
    goradid.goradid_additional_id
    from goradid
    where goradid.goradid_adid_code = 'UIC'
    and goradid.goradid_additional_id is not null
    and goradid.goradid_additional_id != '9175170611' -- this one has two uics in banner
  ) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
  -- spbpers on not dead
  inner join(
    select
    spbpers.spbpers_pidm
    from spbpers
    where spbpers.spbpers_dead_ind is null
  )spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
  where spriden.spriden_change_ind is null
  and exists (select
              'has course work in their primary level in these terms'
              from shrtgpa
              inner join(
                select
                sgbstdn.sgbstdn_pidm,
                sgbstdn.sgbstdn_term_code_eff,
                sgbstdn.sgbstdn_levl_code
                from sgbstdn
              ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                             and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                       from sgbstdn sgbstdn_1
                                                                       where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                       and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                             and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
              where shrtgpa.shrtgpa_gpa_type_ind = 'I'
              and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
              and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
              and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
)
select
pop_sel.spriden_id as "LocalStudentId",

--to_char(to_number(substr(pop_sel.shrtgpa_term_code, 1, 4)) - 1) || '-' || substr(pop_sel.shrtgpa_term_code, 1, 4)
--as "AcademicYearDesignator",

--(select to_char(stvterm.stvterm_start_date, 'YYYY-MM') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code) 
--as "SessionDesignator",

-- 2017
(select to_char(stvterm.stvterm_start_date, 'YYYY-MM-DD') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code) 
as "SessionBeginDate",
(select to_char(stvterm.stvterm_end_date, 'YYYY-MM-DD') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code) 
as "SessionEndDate",

case substr(pop_sel.shrtgpa_term_code, 5, 2)
  when '10' then 'Fall'
  when '20' then 'Winter'
  when '30' then 'Spring'
  when '40' then 'Summer'
  else 'Other'
end as "SessionName",

'Semester' as "SessionType",

case pop_sel.sgbstdn_resd_code
  when 'N' then 'OutOfState'
  when 'R' then 'InState'
  else 'NotReported'
end as "ResidencyStatusCode",

-- 2017
case
  when (pop_sel.sgbstdn_levl_code = 'GR' or 
        pop_sel.sgbstdn_levl_code = 'G2' or 
        pop_sel.sgbstdn_levl_code = 'G3' or 
        pop_sel.sgbstdn_levl_code = 'DR' or 
        pop_sel.sgbstdn_levl_code = 'D2' or 
        pop_sel.sgbstdn_levl_code = 'D3') and pop_sel.sgbstdn_styp_code in ('S', 'G') then 'No' --'21'
  when pop_sel.stvdegc_acat_code = '00' then 'No' -- '20'
  when (pop_sel.sgbstdn_levl_code like ('U%') or pop_sel.sgbstdn_levl_code = 'NA') and pop_sel.sgbstdn_styp_code in ('G', 'D', 'E', 'S', 'X') then 'No' --'20'
  else 'Yes'
end "DegreeOrCertificateSeekingStud",

'Yes' as "PrimaryAcademicLevel",

case
--  when (pop_sel.sgbstdn_levl_code = 'GR' or 
--        pop_sel.sgbstdn_levl_code = 'G2' or 
--        pop_sel.sgbstdn_levl_code = 'G3' or 
--        pop_sel.sgbstdn_levl_code = 'DR' or 
--        pop_sel.sgbstdn_levl_code = 'D2' or 
--        pop_sel.sgbstdn_levl_code = 'D3') and
--       pop_sel.stvdegc_acat_code = '00' then '21'
  when (pop_sel.sgbstdn_levl_code = 'GR' or 
        pop_sel.sgbstdn_levl_code = 'G2' or 
        pop_sel.sgbstdn_levl_code = 'G3' or 
        pop_sel.sgbstdn_levl_code = 'DR' or 
        pop_sel.sgbstdn_levl_code = 'D2' or 
        pop_sel.sgbstdn_levl_code = 'D3') and
       pop_sel.sgbstdn_styp_code in ('S', 'G') then '21'

  when pop_sel.stvdegc_acat_code = '00' then '20'
  when pop_sel.sgbstdn_styp_code = 'G' then '20'

  when pop_sel.stvdegc_acat_code = '22' then '2'
  when pop_sel.stvdegc_acat_code = '24' then '5'
  when pop_sel.stvdegc_acat_code = '42' then '7'
  when pop_sel.stvdegc_acat_code = '43' then '8'
  when pop_sel.stvdegc_acat_code = '44' then '18'
  else ''
end as "StudentLevelCode",

case 
  when pop_sel.sgbstdn_styp_code = 'C' then 'Continuing'
 
  when pop_sel.sgbstdn_styp_code = 'D' then
    case 
      when (select count(*)
            from shrtgpa
            where shrtgpa.shrtgpa_pidm = pop_sel.spriden_pidm
            and shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code < pop_sel.shrtgpa_term_code) > 0 then 'Continuing'
      else 'FirstTime'
    end  

  when pop_sel.sgbstdn_styp_code = 'E' then
    case 
      when (select count(*)
            from shrtgpa
            where shrtgpa.shrtgpa_pidm = pop_sel.spriden_pidm
            and shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code < pop_sel.shrtgpa_term_code) > 0 then 'Continuing'
      else 'FirstTime'
    end  
 
  when pop_sel.sgbstdn_styp_code = 'F' then 'FirstTime'
 
  when pop_sel.sgbstdn_styp_code = 'G' then
    case 
      when (select count(*)
            from shrtgpa
            where shrtgpa.shrtgpa_pidm = pop_sel.spriden_pidm
            and shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code < pop_sel.shrtgpa_term_code) > 0 then 'Continuing'
      else 'FirstTime'
    end  


  when pop_sel.sgbstdn_styp_code = 'N' then
    case 
      when (select count(*)
            from shrtgpa
            where shrtgpa.shrtgpa_pidm = pop_sel.spriden_pidm
            and shrtgpa.shrtgpa_levl_code in ('GR', 'DR')
            and shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code < pop_sel.shrtgpa_term_code) > 0 then 'Continuing'
      else 'FirstTime'
    end  

  when pop_sel.sgbstdn_styp_code = 'R' then 'Re-admit'
 
  when pop_sel.sgbstdn_styp_code = 'S' then     
    case 
      when (select count(*)
            from shrtgpa
            where shrtgpa.shrtgpa_pidm = pop_sel.spriden_pidm
            and shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code < pop_sel.shrtgpa_term_code) > 0 then 'Continuing'
      else 'FirstTime'
    end  

  when pop_sel.sgbstdn_styp_code = 'T' then 'TransferIn'
 
  when pop_sel.sgbstdn_styp_code = 'X' then 
    case 
      when (select count(*)
            from shrtgpa
            where shrtgpa.shrtgpa_pidm = pop_sel.spriden_pidm
            and shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code < pop_sel.shrtgpa_term_code) > 0 then 'Continuing'
      else 'FirstTime'
    end  
  
  else ''
end as "PostsecondaryEnrollmentType",

case 
  when pop_sel.sgbstdn_styp_code = 'D' then 'Dual Enrolled'
  when pop_sel.sgbstdn_styp_code = 'E' then 'Early Middle College'
  else 'Not High School Student'
end as "HighSchoolStudent",


to_char(trunc(pop_sel.shrtgpa_gpa, 2), '0.99') as "AcademicSessionGPA",

to_char(
  trunc(
    case
      when pop_sel.overall_gpa_hours > 0 then (pop_sel.overall_quality_points/pop_sel.overall_gpa_hours)
      else 0
    end
  , 2)
, '99.99')
as "CumulativeGradePointAverage"

from pop_sel
--where pop_sel.stvdegc_acat_code = '00'
--where pop_sel.spriden_pidm in (164288) --(158804) --135054)
--where pop_sel.spriden_pidm in (105896) --38739) --99646)
order by 1 --(select to_char(stvterm.stvterm_start_date, 'YYYY-MM') from stvterm where stvterm.stvterm_code = pop_sel.shrtgpa_term_code)
;

--select *
--from spriden
--where spriden_id = '78480374'