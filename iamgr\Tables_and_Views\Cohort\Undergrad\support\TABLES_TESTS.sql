create table IA_COHORT_NSC_RAW_BAC as
select * from IA_COHORT_NSC_RAW
;
create table IA_NSC_DEGREES_BAC as
select * from IA_NSC_DEGREES
;
create table IA_NSC_STUDENT_DATA_BAC as
select * from IA_NSC_STUDENT_DATA
;
create table IA_COHORT_TEN_YR_TBL_NSC_BAC as
select * from IA_COHORT_TEN_YR_TBL_NSC
;
create table IA_COHORT_PERSIST_TBL_NSC_BAC as
select * from IA_COHORT_PERSIST_TBL_NSC
;
create table IA_HLC_STUDY_TBL_BAC as
select * from IA_HLC_STUDY_TBL
;
select count (*)
from IA_COHORT_NSC_RAW_bac
;
select count (*)
from IA_COHORT_NSC_RAW
;
select count (*)
from ia_nsc_degrees
;
select count (*)
from ia_nsc_degrees_bac
;
select count (*)
from IA_NSC_STUDENT_DATA
;
select count (*)
from IA_NSC_STUDENT_DATA_bac
;
select count (*)
from IA_COHORT_TEN_YR_TBL_NSC
;
select count (*)
from IA_COHORT_TEN_YR_TBL_NSC_BAC
;
select count (*)
from IA_COHORT_PERSIST_TBL_NSC
;
select count (*)
from IA_COHORT_PERSIST_TBL_NSC_BAC
;
select count (*)
from IA_HLC_STUDY_TBL
;
select count (*)
from IA_HLC_STUDY_TBL_BAC
;