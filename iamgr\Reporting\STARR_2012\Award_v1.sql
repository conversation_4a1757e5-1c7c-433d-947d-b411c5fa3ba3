select
spriden.spriden_id as "SchoolAssignedID",

case
  when (current_sgbstdn_join.sgbstdn_levl_code = 'GR' or
        current_sgbstdn_join.sgbstdn_levl_code = 'PT') then 
    case
      when current_sgbstdn_join.sgbstdn_degc_code_1 = '000000' then 'GraduateNonDegree'
      when current_sgbstdn_join.sgbstdn_degc_code_1 like 'D%' then 'Doctoral'
      else 'Masters'
    end
  else
    case
    when (current_sgbstdn_join.sgbstdn_styp_code = 'S' or
          current_sgbstdn_join.sgbstdn_styp_code = 'X') then 'NonDegree'
    else
      case 
      when (current_sgbstdn_join.class_standing = 'FR' and
            current_sgbstdn_join.sgbstdn_styp_code = 'F') then 'CollegeFirstYear'
      when current_sgbstdn_join.class_standing = 'FR' then 'CollegeFirstYearAttendedBefore'
      when current_sgbstdn_join.class_standing = 'SO' then 'CollegeSophomore'
      when current_sgbstdn_join.class_standing = 'JR' then 'CollegeJunior'
      when current_sgbstdn_join.class_standing = 'SR' then 'CollegeSenior'
      end
    end
end as "StudentLevel",

(select to_char(term2.stvterm_start_date, 'YYYY-MM') 
 from stvterm term2
 where term2.stvterm_code = (select min(shrttrm.shrttrm_term_code)
                             from shrttrm
                             where shrttrm.shrttrm_pidm = spriden.spriden_pidm)
) as "EntryDate",

substr(shrdgmr_join.stvdegc_acat_code, 1, 1) || '.' ||
substr(shrdgmr_join.stvdegc_acat_code, 2, 1)as "AcademicAwardLevel",

to_char(shrdgmr_join.shrdgmr_grad_date, 'YYYY-MM') as "AcademicAwardDate",

shrdgmr_join.stvdegc_desc as "AcademicAwardTitle",

shrdgmr_join.stvmajr_cipc_code as "ProgramCIPCode",

trunc(shrdgmr_join.shrlgpa_gpa, 2) as "GradePointAverage"

from spriden
-- start term
inner join(
  select 
  max(stvterm.STVTERM_CODE) as start_term_code
  from stvterm
  where stvterm.stvterm_code = '201130'
) start_term on start_term.start_term_code <> '999999'
-- end term
inner join(
  select max(stvterm.STVTERM_CODE) as end_term_code
  from stvterm
  where stvterm.STVTERM_CODE = '201220'
) end_term on end_term.end_term_code <> '999999'
-- has to have a UID
inner join(
  select 
  goradid.GORADID_PIDM,
  goradid.GORADID_ADDITIONAL_ID
  from goradid
  where goradid.GORADID_ADID_CODE = 'UIC'
  and goradid.GORADID_ADDITIONAL_ID is not null
) goradid_join on goradid_join.goradid_pidm = spriden.spriden_pidm 
-- current term sgbstdn
inner join(
  select
  sgbstdn.sgbstdn_pidm,
  sgbstdn.sgbstdn_term_code_eff,
  sgbstdn.sgbstdn_levl_code,
  sgbstdn.sgbstdn_degc_code_1,
  sgbstdn.sgbstdn_styp_code,
  f_class_calc_fnc(sgbstdn.sgbstdn_pidm, sgbstdn.sgbstdn_levl_code, sgbstdn.sgbstdn_term_code_eff) as class_standing
  from 
  sgbstdn
  left outer join stvmajr on stvmajr.stvmajr_code = sgbstdn.sgbstdn_majr_code_1
)current_sgbstdn_join on current_sgbstdn_join.sgbstdn_pidm = spriden.spriden_pidm
                      and current_sgbstdn_join.SGBSTDN_TERM_CODE_EFF = (select max(s2.sgbstdn_term_code_eff)
                                                                        from sgbstdn s2
                                                                        where s2.sgbstdn_pidm = current_sgbstdn_join.sgbstdn_pidm
                                                                        and s2.sgbstdn_term_code_eff <= end_term.end_term_code)
-- spbpers on not dead
inner join(
  select
  spbpers.spbpers_pidm
  from spbpers
  where spbpers.SPBPERS_DEAD_IND is null
)spbpers_join on spbpers_join.spbpers_pidm = spriden.spriden_pidm
-- has an awarded degree
inner join(
  select
  shrdgmr.shrdgmr_pidm,
  shrdgmr.shrdgmr_grad_date,
  stvdegc_join.stvdegc_desc,
  stvdegc_join.stvdegc_acat_code,
  stvmajr_join.stvmajr_cipc_code,
  shrlgpa_join.shrlgpa_gpa
  from shrdgmr
  inner join(
    select
    shrlgpa.shrlgpa_pidm,
    shrlgpa.shrlgpa_levl_code,
    shrlgpa.shrlgpa_gpa
    from shrlgpa
    where shrlgpa.shrlgpa_gpa_type_ind = 'O'
  ) shrlgpa_join on shrlgpa_join.shrlgpa_pidm = shrdgmr.shrdgmr_pidm
                 and shrlgpa_join.shrlgpa_levl_code = shrdgmr.shrdgmr_levl_code
  left outer join(
    select
    stvdegc.stvdegc_code,
    stvdegc.stvdegc_desc,
    stvdegc.stvdegc_acat_code
    from stvdegc
  ) stvdegc_join on stvdegc_join.stvdegc_code = shrdgmr.shrdgmr_degc_code
  left outer join(
    select
    stvmajr.stvmajr_code,
    stvmajr.stvmajr_cipc_code
    from stvmajr
  ) stvmajr_join on stvmajr_join.stvmajr_code = shrdgmr.shrdgmr_majr_code_1
  where shrdgmr.shrdgmr_degs_code = 'AW'
) shrdgmr_join on shrdgmr_join.shrdgmr_pidm = spriden.spriden_pidm

where spriden.spriden_change_ind is null
and exists (select
            'has course work in their primary level in these terms'
            from shrtgpa
            inner join(
              select
              sgbstdn.sgbstdn_pidm,
              sgbstdn.sgbstdn_term_code_eff,
              sgbstdn.sgbstdn_levl_code
              from sgbstdn
            ) sgbstdn_join on sgbstdn_join.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                           and sgbstdn_join.sgbstdn_term_code_eff = (select max(sgbstdn_1.sgbstdn_term_code_eff)
                                                                     from sgbstdn sgbstdn_1
                                                                     where sgbstdn_1.sgbstdn_pidm = shrtgpa.shrtgpa_pidm
                                                                     and sgbstdn_1.sgbstdn_term_code_eff <= shrtgpa.shrtgpa_term_code)
                           and sgbstdn_join.sgbstdn_levl_code = shrtgpa.shrtgpa_levl_code
            where shrtgpa.shrtgpa_gpa_type_ind = 'I'
            and shrtgpa.shrtgpa_term_code >= start_term.start_term_code
            and shrtgpa.shrtgpa_term_code <= end_term.end_term_code
            and shrtgpa.shrtgpa_pidm = spriden.spriden_pidm)
--and spriden.SPRIDEN_PIDM in (65753)
