/*******************************************************************************
IPEDS 
Fall Collection: 12-Month Enrollment for July 1 � June 30 of FY
Applies to: 4-year Institutions
File Type: Key Value Pair (*.TXT)
Part A: Unduplicated Count
Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
Be sure to update the term codes each year before running
*******************************************************************************/
--Part A: Unduplicated Count
SELECT
'UNITID=171146' A,
'SURVSECT=E1D' B,
'PART=A' C,
'LINE='||
case 
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code = 'F' then '1'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code = 'T' then '2'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code in ('C','R') then '3'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code in ('D','E','G','S','X') then '7'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code = 'F' then '15'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code = 'T' then '16'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code in ('C','R') then '17'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code in ('D','E','G','S','X') then '21'
when popsel.report_level_code = 'GR' then '99'
else popsel.report_level_code||'-'||popsel.full_part_time_ind_umf||'-'||popsel.ia_student_type_code
end D,
--decode(popsel.REPORT_LEVEL_CODE,'UG',1,3) D,
'RACE='||popsel.IPEDS_RACE_CODE E,
'SEX='||popsel.IPEDS_GENDER_CODE F,
'COUNT='||count(popsel.sd_pidm) G
FROM(
with 
raw_data as(
select
sd_pidm,
sd_term_code,
full_part_time_ind_umf,
ia_student_type_code,
ia_gender as gender,
IPEDS_GENDER_CODE,
REPORT_ETHNICITY,
IPEDS_RACE_CODE,
REPORT_LEVEL_CODE
from
IA_TD_STUDENT_DATA
where
registered_ind = 'Y' and 
fy = '19-20'
              
),
pidm_count as(
select sd_pidm,
count (sd_pidm) as dup_count
from raw_data
group by
sd_pidm
),
min_term as(
select 
sd_pidm,
min(sd_term_code) as min_term
from raw_data
group by
raw_data.sd_pidm
)
select 
raw_data.sd_pidm,
raw_data.sd_term_code,
raw_data.full_part_time_ind_umf,
raw_data.ia_student_type_code,
raw_data.gender,
raw_data.IPEDS_GENDER_CODE,
raw_data.REPORT_ETHNICITY,
raw_data.IPEDS_RACE_CODE,
raw_data.REPORT_LEVEL_CODE,
pidm_count.dup_count
from min_term
left outer join raw_data on raw_data.sd_pidm = min_term.sd_pidm and
                            raw_data.sd_term_code = min_term.min_term
left outer join pidm_count on pidm_count.sd_pidm = min_term.sd_pidm
--order by raw_data.sd_pidm
)POPSEL

group by
'UNITID=171146',
'SURVSECT=E1D',
'PART=A',
case 
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code = 'F' then '1'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code = 'T' then '2'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code in ('C','R') then '3'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'F' and popsel.ia_student_type_code in ('D','E','G','S','X') then '7'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code = 'F' then '15'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code = 'T' then '16'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code in ('C','R') then '17'
when popsel.report_level_code = 'UG' and popsel.full_part_time_ind_umf = 'P' and popsel.ia_student_type_code in ('D','E','G','S','X') then '21'
when popsel.report_level_code = 'GR' then '99'
else popsel.report_level_code||'-'||popsel.full_part_time_ind_umf||'-'||popsel.ia_student_type_code
end ,
'RACE='||popsel.IPEDS_RACE_CODE,
'SEX='||popsel.IPEDS_GENDER_CODE 

union all

--Part B: Instructional Activity and Full-Time Equivalent (FTE) Enrollment
SELECT
'UNITID=171146' A,
'SURVSECT=E1D' B,
'PART=B' C,
--CREDHRSU Credit hour instructional activity at the undergraduate level	
'CREDHRSU='||(select sum (TOTAL_CREDIT_HOURS_UMF)
from ia_td_student_data where registered_ind = 'Y' and
fy = '19-20' and --UPDATE THIS
ipeds_class_code = 'UG') D,
--CONTHRS Contact hour instructional activity (undergraduate level only)	              
'CONTHRS='  E,          
--CREDHRSG Credit hour instructional activity at the graduate level	               
'CREDHRSG='||(select sum (TOTAL_CREDIT_HOURS_UMF)
from ia_td_student_data where registered_ind = 'Y' and
fy = '19-20' and --UPDATE THIS
(ipeds_class_code = 'GR' or ipeds_class_code = 'Drs-Acad')) F,
--RDOCFTE Reported Doctor's degree-professional practice student FTE	             
'RDOCFTE='|| (select round(sum(TOTAL_CREDIT_HOURS_UMF/16),0) FYES
from ia_td_student_data where registered_ind = 'Y' and
fy = '19-20' and --UPDATE THIS
ipeds_class_code = 'Drs-Prof') G

FROM
Dual;

--select distinct ipeds_class_code
--from ia_td_student_data where registered_ind = 'Y' and
--fy = '19-20' 
--and --UPDATE THIS
--ipeds_class_code = 'Drs-Prof' 
;
