/*
This query is designed to pull the FTIAC cohort and format the file for upload to
the NSCH.

The rules to be in the cohort are:
  Student is a full time, registered, FTIAC, UG in the FAll term
  or the student is a full time or part time, registered, FTIAC, UG in the prior Summer term 
  and the student returned in the Fall term as a full time, registered, continuing, UG, in Fall term
  
This must be used between 2007 and 2009 becaue FTIAC students starting in summer during these
3 years were not codded as FTIAC in the Fall, whereas students starting in Fall 2010 were 
rolled to the Fall as a student_type_code = 'F' and you will use the other script for 
2010-Present.
*/

with pop_sel as(
  --This section creates a header file.
  select
  1 order_num,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  to_char(sysdate, 'YYYYMMDD') "E",
  'CO' "F",
  'I' "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from dual
  
  union
  --This section pulls the student records for the payload.
  select
  2 order_num,
  'D1' "A",
  demo_join.ssn "B",
  demo_join.first_name "C",
  substr(demo_join.middle_name, 1 ,1) "D",
  demo_join.last_name "E",
  demo_join.name_suffix "F",
  to_char(demo_join.birthdate, 'YYYYMMDD') "G",
  '20040901' "H",  --Instert Cohort Term YYYYMMDD
  '' "I",
  '002327' "J",
  '00' "K",
  demo_join.dm_pidm "L"
  
  from td_student_data td_started
  inner join um_demographic demo_join on demo_join.dm_pidm = td_started.sd_pidm
  where primary_level_code = 'UG'
  and registered_ind = 'Y'
  and student_type_code = 'F'
  and (
    (
      sd_term_code = '200510'-- fall term code
      and full_part_time_ind_umf = 'P'
    ) or (
      sd_term_code = '200440'--summer term code
      and exists (
        select 'continued in fall'
        from td_student_data td_continued
        where td_started.sd_pidm = td_continued.sd_pidm
        and td_started.sd_term_code + 70 = td_continued.sd_term_code
        and td_continued.registered_ind = 'Y'
        --and td_continued.full_part_time_ind_umf = 'F'
        and td_continued.primary_level_code = 'UG'
      )
    )  
  )  

  union
    --This is to count the number of records and append a trailer record
  select
  3 order_num,
  'T1' "A",
  to_char(count(demo_join.dm_pidm)-2) "B",
  null "C",
  null "D",
  null "E",
  null "F",
  null "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from td_student_data td_started
  inner join um_demographic demo_join on demo_join.dm_pidm = td_started.sd_pidm
  where primary_level_code = 'UG'
  and registered_ind = 'Y'
  and student_type_code = 'F'
  and (
    (
      sd_term_code = '200510'-- fall term code
      and full_part_time_ind_umf = 'P'
    ) or (
      sd_term_code = '200440'--summer term code
      and exists (
        select 'continued in fall'
        from td_student_data td_continued
        where td_started.sd_pidm = td_continued.sd_pidm
        and td_started.sd_term_code + 70 = td_continued.sd_term_code
        and td_continued.registered_ind = 'Y'
        --and td_continued.full_part_time_ind_umf = 'F'
        and td_continued.primary_level_code = 'UG'
      )
    )  
  )  
  order by order_num
)
select 
pop_sel.A,
pop_sel.B,
pop_sel.C,
pop_sel.D,
pop_sel.E,
pop_sel.F,
pop_sel.G,
pop_sel.H,
pop_sel.I,
pop_sel.J,
pop_sel.K,
pop_sel.L
from pop_sel