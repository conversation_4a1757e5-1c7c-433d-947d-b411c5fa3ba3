/*******************************************************************************
Table grain: 1 row / student
Purpose: pull student data projected out 6 years on a student and create view
Primary keys: CO.CO_GR_PIDM
Foreign keys: sd.SD_PIDM
using report_level_code because the primary_level_code was changed in the
system for many of the programs.  Tried to link on Primary_Major_1 and
primary_program but many of these had been altered in the system as well.
*******************************************************************************/
--CREATE TABLE CO_GR_TEN_YR_NSC_TBL AS
INSERT INTO CO_GR_TEN_YR_NSC_TBL
SELECT DISTINCT
  co.co_gr_pidm,
  co.co_gr_term_code_key,
  co.start_date,
    co.CO_GR_IA_STUDENT_TYPE_CODE, 
  co.CO_GR_FULL_PART_IND_UMF, 
  co.CO_GR_ETHNICITY, 
  co.CO_GR_GENDER, 
  co.REPORT_LEVEL_CODE, 
  co.PRIMARY_LEVEL_CODE, 
  co.PRIMARY_DEGREE_CODE,
  co.PRIMARY_MAJOR_1_CIPC_CODE, 
  co.CITIZENSHIP_CODE, 
  co.CITIZENSHIP_DESC, 
  co.TRANS_GPA, PCOL_DESC_1, 
  co.PRIMARY_MAJOR_1, 
  co.PRIMARY_PROGRAM,
  co.PRIMARY_CONC_1, 
  co.UMID,
--First Fall********************************************************************
--    (SELECT td1.sd_term_code
--    FROM sd td1
--    WHERE td1.sd_term_code      = CO.CO_GR_TERM_CODE_KEY
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    ) frst_fall_term_code,
--    (SELECT td1.registered_ind
--    FROM sd td1
--    WHERE td1.sd_term_code      = CO.CO_GR_TERM_CODE_KEY
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    ) frst_fall_reg_ind,
--------------------------Second----------------------------------------------
--Second Fall Set *************************************************************
--    (SELECT td1.sd_term_code
--    FROM sd td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 100))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) Scnd_fall_term_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 100))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   scnd_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 100))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END scnd_fall_grad_ind,
----------------------------Third-----------------------------------------------
-- Third Fall Set **************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 200))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) thrd_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 200))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   thrd_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 200))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END thrd_fall_grad_ind,
----------------------------fourth---------------------------------------------------
-- Fourth Fall Set ******************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 300))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )frth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 300))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   frth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 300))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END frth_fall_grad_ind,
-----------------------------Fifth--------------------------------------------------
-- Fifth Fall Set ******************************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 400))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) ffth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 400))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   ffth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 400))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END ffth_fall_grad_ind,
-----------------------------Sixth--------------------------------------------
-- Sixth Fall Set **********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 500))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    ) sxth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 500))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   sxth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 500))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END sxth_fall_grad_ind,
---------------------------Seventh-------------------------------------------
--Seventh Fall Set ***********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 600))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )svnth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 600))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   svnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 600))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END svnth_fall_grad_ind,
    ---------------------------Eighth-------------------------------------------
--Eigth Fall Set ***********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 700))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )eigth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 700))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   eigth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 700))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END eigth_fall_grad_ind,
    ---------------------------Ninth-------------------------------------------
--Ninth Fall Set ***********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 800))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )nnth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 800))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   nnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 800))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END nnth_fall_grad_ind,
      ---------------------------Tenth-------------------------------------------
--Tenth Fall Set ***********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 900))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )tnth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 900))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   tnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 900))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END tnth_fall_grad_ind,
        ---------------------------Eleventh-------------------------------------------
--Eleventh Fall Set ***********************************************************
--    (SELECT td1.sd_term_code
--    FROM td_student_data td1
--    WHERE td1.sd_term_code      = TO_CHAR((to_number(CO.CO_GR_TERM_CODE_KEY) + 1000))
--    AND td1.report_level_code   = CO.report_level_code
--    AND td1.PRIMARY_DEGREE_CODE = CO.PRIMARY_DEGREE_CODE
--    AND td1.sd_pidm             = CO.CO_GR_PIDM
--    AND td1.student_type_code   = 'C'
--    )elvnth_fall_code,
  (
    SELECT
      td1.registered_ind
    FROM
      td_student_data td1
    WHERE
        td1.sd_term_code = to_char((TO_NUMBER(CO.co_gr_term_code_key) + 1000))
      AND td1.report_level_code = CO.report_level_code
      AND td1.primary_degree_code = CO.primary_degree_code
      AND td1.sd_pidm = CO.co_gr_pidm
      AND td1.student_type_code = 'C'
  )   elvnth_fall_term_reg_ind,
  CASE
    WHEN (
      SELECT
        COUNT(*)
      FROM
        um_degree umd
      WHERE
          umd.pidm = CO.co_gr_pidm
        AND umd.degree_status = 'AW'
        AND umd.degree_code = CO.primary_degree_code
--        AND umd.report_level_code = CO.report_level_code
        AND umd.grad_date > CO.start_date
        AND umd.grad_term_code < to_char((TO_NUMBER(CO.co_gr_term_code_key) + 1000))
    ) > 0 THEN
      'Y'
    ELSE
      NULL
  END elvnth_fall_grad_ind,
 ---------------------------Grad Date---------------------------------------
--Grad Date***********************************************************  
  (
    SELECT
      MAX(grad_date)
    FROM
      um_degree umd
    WHERE
        umd.pidm = CO.co_gr_pidm
      AND umd.degree_status = 'AW'
--      AND umd.report_level_code = CO.report_level_code
      AND umd.degree_code = CO.primary_degree_code
      AND umd.grad_date > CO.start_date
  )   grad_date 
-- Cohort Demographic Table joins and Where Clause ************************
FROM
       td_student_data sd
  INNER JOIN CO_GR_POP_TBL CO ON CO.co_gr_pidm = sd.sd_pidm
                                 AND CO.co_gr_term_code_key = sd.sd_term_code
                                 AND CO.primary_degree_code = sd.primary_degree_code
where co.co_gr_term_code_key like '%10'
;
--graduated multiple degrees with the same degree code but different grad dates
--    where CO.CO_GR_PIDM not in (187157,
--74927,
--97866,
--74840,
--89021,
--105140,
--197713);