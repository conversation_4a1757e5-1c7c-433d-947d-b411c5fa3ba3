SELECT
 m_hrdw1.job.job_effdt,
 m_hrdw1.job.job_end_dt,
 m_hrdw1.personal_data.last_name,
 m_hrdw1.personal_data.first_name,
 m_hrdw1.personal_data.emplid,
 m_hrdw1.personal_demographics.sex,
 m_hrdw1.personal_demographics.ethnic_group_descr,
 m_hrdw1.personal_data.highest_educ_lvl_descrshort,
 m_hrdw1.personal_demographics.citizenship_status_descr,
 m_hrdw1.job.empl_rcd,
 m_hrdw1.job.appt_dept_descr,
 m_hrdw1.job.jobcode_descr,
 m_hrdw1.job.annual_ftr,
 m_hrdw1.job.fte,
 m_hrdw1.job.comprate,
 m_hrdw1.job.class_indc_descrshort,
 m_hrdw1.dept_budget_ern.funding_dept_descr,
 m_hrdw1.dept_budget_ern.dist_pct,
 m_hrdw1.dept_budget_ern.percent_effort,
 m_hrdw1.dept_budget_ern.dept_budget_ern_effdt,
 m_hrdw1.dept_budget_ern.dept_budget_ern_end_dt,
 m_hrdw1.dept_budget_ern.shortcode,
 m_hrdw1.job.appt_period_descr,
 m_hrdw1.job.tenure_status,
 m_hrdw1.job.appt_start_date,
 m_hrdw1.job.ftr_rate,
 m_hrdw1.job.std_hours,
 m_hrdw1.dept_budget_ern.shortcode_descr,
 m_hrdw1.jobcode_tbl.ipedsscode_descr,
 m_hrdw1.work_address.campus_id,
 m_hrdw1.job.empl_status,
 m_hrdw1.personal_data.hr_maj_degree1_txt,
 m_hrdw1.personal_data.hr_maj_degree2_txt,
 m_hrdw1.personal_data.hr_study_field_descr,
 m_hrdw1.personal_data.hr_study_field,
 m_hrdw1.job.job_effseq
FROM
 m_hrdw1.job,
 m_hrdw1.personal_data,
 m_hrdw1.personal_demographics,
 m_hrdw1.dept_budget_ern,
 m_hrdw1.jobcode_tbl,
 m_hrdw1.work_address
WHERE
 ( m_hrdw1.personal_data.emplid = m_hrdw1.work_address.emplid (+) )
 AND ( m_hrdw1.personal_data.emplid = m_hrdw1.personal_demographics.emplid )
 AND ( m_hrdw1.personal_data.emplid = m_hrdw1.job.emplid )
 AND ( m_hrdw1.job.jobcode = m_hrdw1.jobcode_tbl.jobcode )
 AND ( m_hrdw1.job.emplid = m_hrdw1.dept_budget_ern.emplid (+) )
 AND ( m_hrdw1.job.empl_rcd = m_hrdw1.dept_budget_ern.empl_rcd (+) )
 AND ( m_hrdw1.job.appt_deptid = m_hrdw1.dept_budget_ern.appt_deptid (+) )
 AND ( ( 'All Sequences' = 'All Sequences' )
       OR ( m_hrdw1.job.job_effseq = (
  SELECT
   MAX(a_job.job_effseq)
  FROM
   m_hrdw1.job a_job
  WHERE
    a_job.emplid = m_hrdw1.job.emplid
   AND a_job.empl_rcd = m_hrdw1.job.empl_rcd
   AND a_job.job_effdt = m_hrdw1.job.job_effdt
   AND 'All Sequences' = 'Max Sequence'
 ) )
       OR ( m_hrdw1.job.emplid IS NULL
            AND m_hrdw1.job.empl_rcd IS NULL ) )
 AND ( m_hrdw1.job.job_effdt <= m_hrdw1.dept_budget_ern.dept_budget_ern_end_dt )
 AND ( m_hrdw1.dept_budget_ern.dept_budget_ern_effdt <= m_hrdw1.job.job_end_dt )
 AND ( m_hrdw1.job.job_location = 'FLNT'
       AND m_hrdw1.job.reg_temp IN ( 'R' )
       AND m_hrdw1.job.job_effdt >= '01-07-2020 00:00:00'
       AND m_hrdw1.job.empl_status IN ( 'A', 'P' ) )