--------------------------------------------------------
--  File created - Wednesday-September-08-2021   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View LOAD_COURSE_ROSTER
--------------------------------------------------------

  CREATE OR REPLACE FORCE VIEW "REGMGR"."LOAD_COURSE_ROSTER" ("TERM_CODE", "PTRM_CODE", "PTRM_DESC", "TERM_DESC", "CRN", "SUBJ_CODE", "CRSE_NUMB", "SECTION_NUMB", "XLST_GROUP", "TOTAL_GROUP_ENRL_COUNT", "SELECTED_CRN", "SELECTED_SUBJ_CODE", "SELECTED_CRSE_NUMB", "SELECTED_SECTION_NUMB", "SELECTED_CRSE_TITLE", "MEETING_INFO", "PRIM_INSTR_UMID", "PRIM_INSTR_EMAIL", "PRIM_INSTR_NAME", "ENRL_LIST_SEQ_NO", "CAT_CIVIC_ENGAGE_IND", "SEC_CIVIC_ENGAGE_IND", "STUDENT_PIDM", "UMID", "STUD_NAME", "CA_EMAIL", "UNIQNAME", "PHONE_NUMBER", "PRIMARY_MAJOR_1", "PRIMARY_MAJOR_1_DESC", "CONFIDENTIALITY_IND", "REGISTERED_HOURS", "OVERALL_HOURS_EARNED", "SFRSTCR_RSTS_DATE", "XLISTED_COURSES") AS 
  SELECT

--sfrstcr_join.umid,

  all_courses.term_code,
  all_courses.ptrm_code,
  all_courses.ptrm_desc,
  xlst_group_xselect.term_desc,
  all_courses.crn,
  all_courses.subj_code,
  all_courses.crse_numb,
  all_courses.section_numb,
  CASE
   WHEN all_courses.crn = all_courses.xlst_group THEN
    NULL
   ELSE
    substr(all_courses.xlst_group, 1, 2)
  END                                                       xlst_group,
  xlst_group_wait_total.total_enrl_count                    total_group_enrl_count,
  xlst_group_xselect.crn                                    selected_crn,
  xlst_group_xselect.subj_code                              selected_subj_code,
  xlst_group_xselect.crse_numb                              selected_crse_numb,
  xlst_group_xselect.section_numb                           selected_section_numb,
  xlst_group_xselect.crse_title                             selected_crse_title,
  xlst_group_xselect.meeting_info,
  xlst_group_xselect.primary_instructor_id                  prim_instr_umid,
  xlst_group_xselect.primary_instructor_email               prim_instr_email,
  CASE
   WHEN xlst_group_xselect.primary_instructor_last_name IS NULL THEN
    'Staff'
   ELSE
    xlst_group_xselect.primary_instructor_last_name
    || ', '
    || xlst_group_xselect.primary_instructor_first_name
    || ' '
    || xlst_group_xselect.primary_instructor_middle_init
  END                                                       prim_instr_name,
  ROW_NUMBER()
  OVER(PARTITION BY all_courses.term_code, xlst_group_xselect.subj_code, xlst_group_xselect.crse_numb,
                    xlst_group_xselect.section_numb
       ORDER BY sfrstcr_join.last_name,
                sfrstcr_join.first_name,
                sfrstcr_join.umid
  )                                                         enrl_list_seq_no,
  xlst_group_xselect.cat_civic_engage_ind,
  xlst_group_xselect.sec_civic_engage_ind,
  sfrstcr_join.sfrstcr_pidm                                 student_pidm,
  sfrstcr_join.umid,
  sfrstcr_join.stud_name,
--case
--	when sfrstcr_join.pprn_desc is not null then sfrstcr_join.last_name || ', ' || sfrstcr_join.first_name || ' ' || sfrstcr_join.middle_initial || '(' || sfrstcr_join.pprn_desc || ')'
--	else sfrstcr_join.last_name || ', ' || sfrstcr_join.first_name || ' ' || sfrstcr_join.middle_initial
--end student_name,
  sfrstcr_join.ca_email,
  sfrstcr_join.uniqname,
  sfrstcr_join.area_code || sfrstcr_join.phone_number       phone_number,
  sfrstcr_join.primary_major_1,
  sfrstcr_join.primary_major_1_desc,
  sfrstcr_join.confidentiality_ind,
  sfrstcr_join.sfrstcr_credit_hr                            registered_hours,
  nvl(sfrstcr_join.overall_hours_earned, 0)                 overall_hours_earned,
  sfrstcr_join.sfrstcr_rsts_date,
  substr((
   SELECT
    LISTAGG(ssbsect.ssbsect_subj_code
            || '-'
            || ssbsect.ssbsect_crse_numb
            || '-'
            || ssbsect.ssbsect_seq_numb
            || ' ('
            || ssbsect.ssbsect_crn
            || ')',
            ' / ') WITHIN GROUP(
     ORDER BY
      ssbsect.ssbsect_subj_code,
      ssbsect.ssbsect_crse_numb,
      ssbsect.ssbsect_seq_numb,
      ssbsect.ssbsect_crn
    )
   FROM
         aimsmgr.ssrxlst_ext ssrxlst
    INNER JOIN ssbsect ON ssbsect.ssbsect_term_code = ssrxlst.ssrxlst_term_code
                          AND ssbsect.ssbsect_crn = ssrxlst.ssrxlst_crn
   WHERE
     ssrxlst.ssrxlst_xlst_group = xlst_group_xselect.xlst_group
    AND ssrxlst.ssrxlst_term_code = xlst_group_xselect.term_code
    AND ssrxlst.ssrxlst_crn != xlst_group_xselect.crn
  ),
         1,
         180)                                               xlisted_courses
 FROM
       (
   SELECT
    ssbsect.ssbsect_term_code       term_code,
    ssbsect.ssbsect_crn             crn,
    ssbsect.ssbsect_subj_code       subj_code,
    ssbsect.ssbsect_crse_numb       crse_numb,
    ssbsect.ssbsect_seq_numb        section_numb,
    ssbsect.ssbsect_ptrm_code       ptrm_code,
    (
     SELECT
      sobptrm.sobptrm_desc
     FROM
      aimsmgr.sobptrm_ext sobptrm
     WHERE
       sobptrm.sobptrm_ptrm_code = ssbsect.ssbsect_ptrm_code
      AND sobptrm.sobptrm_term_code = ssbsect.ssbsect_term_code
    )                               ptrm_desc,
    xlst_join.ssrxlst_xlst_group    xlst_group,
    CASE
     WHEN xlst_join.ssrxlst_xlst_group IS NOT NULL THEN
      xlst_join.ssrxlst_xlst_group || 'ZZZ'
     ELSE
      ssbsect.ssbsect_crn
    END                             xlst_group_key,
    ssbsect.ssbsect_enrl            enrl_count
   FROM
    aimsmgr.ssbsect_ext    ssbsect
    LEFT OUTER JOIN (
     SELECT
      ssrxlst.ssrxlst_term_code,
      ssrxlst.ssrxlst_crn,
      ssrxlst.ssrxlst_xlst_group
     FROM
      aimsmgr.ssrxlst_ext ssrxlst
     ORDER BY
      ssrxlst.ssrxlst_term_code,
      ssrxlst.ssrxlst_crn
    )                      xlst_join ON xlst_join.ssrxlst_term_code = ssbsect.ssbsect_term_code
                   AND xlst_join.ssrxlst_crn = ssbsect.ssbsect_crn
   WHERE
     ssbsect.ssbsect_ssts_code = 'A'
--  and ssbsect.ssbsect_term_code in () -- '202110'
      AND ssbsect.ssbsect_term_code >= (
     SELECT
      current_term
     FROM
      um_current_term
    )
    AND ssbsect.ssbsect_term_code <= (
     SELECT
      MAX(sfrstcr_term_code)
     FROM
      aimsmgr.sfrstcr_ext
    )
   ORDER BY
    term_code,
    xlst_group_key
  ) all_courses
  INNER JOIN (
   SELECT
    ssbsect.ssbsect_term_code       term_code,
    catalog_join.term_desc,
    ssbsect.ssbsect_crn             crn,
    ssbsect.ssbsect_subj_code       subj_code,
    ssbsect.ssbsect_crse_numb       crse_numb,
    ssbsect.ssbsect_seq_numb        section_numb,
    catalog_join.sec_title          crse_title,
    ssbsect.ssbsect_ssts_code,
    xlst_join.ssrxlst_xlst_group    xlst_group,
    CASE
     WHEN xlst_join.ssrxlst_xlst_group IS NOT NULL THEN
      xlst_join.ssrxlst_xlst_group || 'ZZZ'
     ELSE
      ssbsect.ssbsect_crn
    END                             xlst_group_key,
    ssbsect.ssbsect_enrl            enrl_count,
    catalog_join.primary_instructor_id,
    catalog_join.primary_instructor_email,
    catalog_join.primary_instructor_last_name,
    catalog_join.primary_instructor_middle_init,
    catalog_join.primary_instructor_first_name,
    catalog_join.meeting_info,
    catalog_join.cat_civic_engage_ind,
    catalog_join.sec_civic_engage_ind
   FROM
    aimsmgr.ssbsect_ext    ssbsect
    LEFT OUTER JOIN (
     SELECT
      ssrxlst.ssrxlst_term_code,
      ssrxlst.ssrxlst_crn,
      ssrxlst.ssrxlst_xlst_group
     FROM
      aimsmgr.ssrxlst_ext ssrxlst
    )                      xlst_join ON xlst_join.ssrxlst_term_code = ssbsect.ssbsect_term_code
                   AND xlst_join.ssrxlst_crn = ssbsect.ssbsect_crn
    INNER JOIN (
     SELECT
      cs.term_code_key,
      cs.crn_key,
      cs.term_desc,
      cs.sec_title,
      cs.primary_instructor_id,
      cs.primary_instructor_email,
      cs.primary_instructor_last_name,
      cs.primary_instructor_middle_init,
      cs.primary_instructor_first_name,
      cs.meeting_info,
      cs.cat_civic_engage_ind,
      cs.sec_civic_engage_ind
     FROM
      um_catalog_schedule cs
    )                      catalog_join ON catalog_join.term_code_key = ssbsect.ssbsect_term_code
                      AND catalog_join.crn_key = ssbsect.ssbsect_crn
   WHERE
    ssbsect.ssbsect_ssts_code = 'A'
   ORDER BY
    term_code,
    xlst_group_key
  )  xlst_group_xselect ON xlst_group_xselect.xlst_group_key = all_courses.xlst_group_key
                          AND xlst_group_xselect.term_code = all_courses.term_code
  INNER JOIN (
   SELECT
    sub_1.term_code,
    sub_1.xlst_group_key,
    SUM(sub_1.enrl_count) total_enrl_count
   FROM
    (
     SELECT
      ssbsect.ssbsect_term_code    term_code,
      ssbsect.ssbsect_crn          crn,
      ssbsect.ssbsect_subj_code    subj_code,
      ssbsect.ssbsect_crse_numb    crse_numb,
      ssbsect.ssbsect_seq_numb     section_numb,
      CASE
       WHEN xlst_join.ssrxlst_xlst_group IS NOT NULL THEN
        xlst_join.ssrxlst_xlst_group || 'ZZZ'
       ELSE
        ssbsect.ssbsect_crn
      END                          xlst_group_key,
      ssbsect.ssbsect_enrl         enrl_count
     FROM
      aimsmgr.ssbsect_ext    ssbsect
      LEFT OUTER JOIN (
       SELECT
        ssrxlst.ssrxlst_term_code,
        ssrxlst.ssrxlst_crn,
        ssrxlst.ssrxlst_xlst_group
       FROM
        aimsmgr.ssrxlst_ext ssrxlst
       ORDER BY
        ssrxlst.ssrxlst_term_code,
        ssrxlst.ssrxlst_crn
      )                      xlst_join ON xlst_join.ssrxlst_term_code = ssbsect.ssbsect_term_code
                     AND xlst_join.ssrxlst_crn = ssbsect.ssbsect_crn
     WHERE
      ssbsect.ssbsect_ssts_code = 'A'
     ORDER BY
      term_code,
      xlst_group_key
    ) sub_1
   GROUP BY
    sub_1.term_code,
    sub_1.xlst_group_key
  )  xlst_group_wait_total ON xlst_group_wait_total.xlst_group_key = all_courses.xlst_group_key
                             AND xlst_group_wait_total.term_code = all_courses.term_code
  INNER JOIN (
   SELECT
    sfrstcr.sfrstcr_pidm,
    sfrstcr.sfrstcr_term_code,
    sfrstcr.sfrstcr_crn,
    demographic_join.umid,
    demographic_join.stud_name,
    demographic_join.last_name,
    demographic_join.middle_initial,
    demographic_join.first_name,
    demographic_join.area_code,
    demographic_join.phone_number,
    demographic_join.ca_email,
    demographic_join.uniqname,
--	demographic_join.pprn_desc,
        student_data_join.primary_major_1,
    student_data_join.primary_major_1_desc,
    student_data_join.confidentiality_ind,
    student_data_join.overall_hours_earned,
    sfrstcr.sfrstcr_credit_hr,
    sfrstcr.sfrstcr_rsts_code,
    sfrstcr.sfrstcr_rsts_date
   FROM
         aimsmgr.sfrstcr_ext sfrstcr
    INNER JOIN (
     SELECT
      stvrsts.stvrsts_code,
      stvrsts.stvrsts_incl_sect_enrl
     FROM
      aimsmgr.stvrsts_ext stvrsts
    )  rsts_join ON ( rsts_join.stvrsts_code = sfrstcr.sfrstcr_rsts_code
                     AND rsts_join.stvrsts_incl_sect_enrl = 'Y' )
                   OR ( rsts_join.stvrsts_code = sfrstcr.sfrstcr_rsts_code
                        AND rsts_join.stvrsts_code = 'RN' )
    INNER JOIN (
     SELECT
      dm.dm_pidm,
      dm.umid,
      dm.first_name,
      dm.middle_initial,
      dm.last_name,
      dm.a1_area_code       area_code,
      dm.a1_phone_number    phone_number,
      dm.ca_email,
      dm.uniqname,
      CASE
       WHEN pprn_desc IS NULL THEN
        dm.last_name
        || ','
        || dm.first_name
       ELSE
        dm.last_name
        || ','
        || dm.first_name
        || ','
        || dm.middle_initial
        || ','
        || dm.pprn_desc
      END                   stud_name
     FROM
      um_demographic dm
    )  demographic_join ON demographic_join.dm_pidm = sfrstcr.sfrstcr_pidm
    INNER JOIN (
     SELECT
      um_student_data.sd_pidm,
      um_student_data.sd_term_code,
      um_student_data.primary_major_1,
      um_student_data.primary_major_1_desc,
      um_student_data.confidentiality_ind,
      um_student_data.overall_hours_earned
     FROM
      um_student_data
    )  student_data_join ON student_data_join.sd_pidm = sfrstcr.sfrstcr_pidm
                           AND student_data_join.sd_term_code = sfrstcr.sfrstcr_term_code
  )  sfrstcr_join ON sfrstcr_join.sfrstcr_term_code = all_courses.term_code
                    AND sfrstcr_join.sfrstcr_crn = all_courses.crn
 ORDER BY
  xlst_group_xselect.xlst_group_key,
  xlst_group_xselect.subj_code,
  xlst_group_xselect.crse_numb,
  xlst_group_xselect.section_numb,
  enrl_list_seq_no
;
