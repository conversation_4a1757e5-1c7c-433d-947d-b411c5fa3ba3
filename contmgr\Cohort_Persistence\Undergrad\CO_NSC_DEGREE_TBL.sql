--------------------------------------------------------
-- CO_NSC_DEGREE_TBL
--------------------------------------------------------
--CREATE TABLE CO_NSC_DEGREE_TBL_BAC AS
--SELECT * FROM CO_NSC_DEGREE_TBL;
--
--DROP TABLE CO_NSC_DEGREE_TBL;
TRUNCATE TABLE CO_NSC_DEGREE_TBL;

--CREATE TABLE CO_NSC_DEGREE_TBL AS
INSERT INTO CO_NSC_DEGREE_TBL 

select 
distinct
    ROW_NUMBER()
 OVER(PARTITION BY co.co_pidm, nscd.mapped_grad_term_code
      ORDER BY
--      nscd.mapped_grad_term_code
       co.co_term_code_key 
 )                                    min_term_ind, 
co.co_pidm,
co.co_term_code_key,
nscd.MAPPED_GRAD_TERM_CODE TERM_CODE_GRADUATED,
nscd."TWOYEAR_FOURYEAR"

from co_pop_undup_tbl co
INNER join nsc_degree nscd on co.co_pidm = nscd.pidm 
and co.co_term_code_key < nscd.mapped_grad_term_code
and min_term_ind= 1
--order by 2,1
;

desc nsc_degree;