--------------------------------------------------------
--  File created - Thursday-May-19-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View DAP_PLANNER_DTL
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DWRKSMGR"."DAP_PLANNER_DTL" ("DAP_STU_ID", "DW_PIDM", "DW_UMID", "DAP_SCHOOL", "DAP_DEGREE", "DAP_CATALOG_YR", "DAP_PLAN_ID", "DAP_ACTIVE_FLAG", "DAP_DESCRIPTION", "DAP_PLAN_NUM", "DAP_STU_APPROVAL", "DAP_STUAPPR_DATE", "DAP_ADV_APPROVAL", "DAP_ADVAPPR_DATE", "DAP_APPR_STATUS", "DAP_LOCKED", "DAP_MOD_ID", "DAP_MOD_DATE", "UNIQUE_ID", "UNIQUE_KEY") AS 
  select "DAP_STU_ID",
  spriden.spriden_pidm dw_pidm,
  spriden.spriden_id dw_umid,
  "DAP_SCHOOL","DAP_DEGREE","DAP_CATALOG_YR","DAP_PLAN_ID","DAP_ACTIVE_FLAG","DAP_DESCRIPTION","DAP_PLAN_NUM","DAP_STU_APPROVAL","DAP_STUAPPR_DATE","DAP_ADV_APPROVAL","DAP_ADVAPPR_DATE","DAP_APPR_STATUS","DAP_LOCKED","DAP_MOD_ID","DAP_MOD_DATE","UNIQUE_ID","UNIQUE_KEY"
from <EMAIL>
inner join aimsmgr.spriden on spriden.spriden_id = cast(trim(dap_stu_id) as varchar2(9))
                           and spriden.spriden_change_ind is null
;
