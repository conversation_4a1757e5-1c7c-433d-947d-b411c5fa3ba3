create table ia_cohort_retention as (
    select
    td_student_data.sd_pidm,
    td_student_data.sd_term_code,
    td_student_data.IA_student_type_code,
    td_student_data.FULL_PART_TIME_IND_UMF,
    td_student_data.PRIMARY_ADMIT_CODE,
    nvl(td_demo_join.intl_ind, 'N') intl_ind ,
    nvl(td_demo_join.veteran_ind, 'N') veteran_ind ,
    nvl(td_student_data.housing_ind, 'N') housing_ind,
    
    case
      when current_join.sd_pidm > 0 then 'Y'
      when (select count(*) counter
            from um_degree
            where um_degree.pidm = td_student_data.sd_pidm
            and um_degree.grad_term_code >= td_student_data.sd_term_code
            and um_degree.grad_status = 'GR') > 0 then 'D'
      else 'N'
    end current_enrolled_ind
    from td_student_data
    inner join(
      select *
      from td_demographic
    )td_demo_join on td_demo_join.dm_pidm = td_student_data.sd_pidm
                  and td_demo_join.td_term_code = td_student_data.sd_term_code
    inner join(
      select *
      from td_test_score
    )td_test_join on td_test_join.pidm = td_student_data.sd_pidm
                  and td_test_join.td_term_code = td_student_data.sd_term_code
    left outer join(
      select *
      from um_student_data
    )current_join on current_join.sd_pidm = td_student_data.sd_pidm
                  and current_join.sd_term_code = to_char(to_number(td_student_data.sd_term_code) + 100)
                  and current_join.registered_ind = 'Y'
    where td_student_data.sd_term_code in (select distinct td_2.sd_term_code
                                           from td_student_data td_2
                                           where substr(td_2.sd_term_code, 5, 2) = '10')
    and (td_student_data.IA_student_type_code = 'F' or 
         td_student_data.IA_student_type_code = 'T')
        and td_student_data.student_status_code = 'AS'
    and td_student_data.registered_ind = 'Y');
    
    
    
    
DROP TABLE ia_cohort_retention
select sd_term_code, IA_student_type_code,CURRENT_ENROLLED_IND,FULL_PART_TIME_IND_UMF,VETERAN_IND, INTL_IND , housing_ind, count(*)AS COHORT
from ia_cohort_retention 
where sd_term_code = 201510 
group by sd_term_code, IA_student_type_code,CURRENT_ENROLLED_IND,FULL_PART_TIME_IND_UMF,VETERAN_IND, INTL_IND , housing_ind;





CREATE TABLE IA_DW_RETENION AS
Select 

  trunc(sysdate) as run_date,
  201510 AS term_in,
  sd_term_code ,
  IA_student_type_code,
  CURRENT_ENROLLED_IND,
  FULL_PART_TIME_IND_UMF,
  (case PRIMARY_ADMIT_CODE
  when 'CH' Then 'Y'
  else 'N'
  End) PROMISE_IND,
  VETERAN_IND,
  INTL_IND ,
  housing_ind,
  10 AS day_of_term,
  201510 AS projected_term_in,
  count(*)AS CO_COUNT
               
from
IA_COHORT_RETENTION

where
sd_term_code = (select
  min(stvterm.stvterm_code) current_term
  from  aimsmgr.stvterm
  where stvterm.stvterm_end_date >= sysdate and (stvterm_code like '%10')
  and stvterm.stvterm_acyr_code != '9999')
--sd_term_code like '%10' and sd_term_code > '201510'

group by
  trunc(sysdate),
  201510,
  sd_term_code ,
  IA_student_type_code,
  CURRENT_ENROLLED_IND,
  FULL_PART_TIME_IND_UMF,
  (case PRIMARY_ADMIT_CODE
  when 'CH' Then 'Y'
  else 'N'
  End),
  VETERAN_IND,
  INTL_IND ,
  housing_ind,
  10,
  201510;
  
  SELECT *  FROM IA_DW_RETENION
