CREATE OR REPLACE PACKAGE BODY zdmcodm_undo AS

  -- Undo for Step 2: Cohort population and FA tables
  PROCEDURE undo_p1_update_co_pop AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_pop_undup_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_pop_undup_tbl');
    INSERT INTO co_pop_undup_tbl SELECT * FROM co_pop_undup_tbl_bac;

    dbms_output.put_line('Restoring co_gr_pop_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_pop_tbl');
    INSERT INTO co_gr_pop_tbl SELECT * FROM co_gr_pop_tbl_bac;

    dbms_output.put_line('Restoring co_fa_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_fa_tbl');
    INSERT INTO co_fa_tbl SELECT * FROM co_fa_tbl_bac;

    COMMIT;
  END undo_p1_update_co_pop;

  -- Undo for Step 3: NSC student and degree tables
  PROCEDURE undo_p2_update_co_nsc AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_nsc_student_data_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_nsc_student_data_tbl');
    INSERT INTO co_nsc_student_data_tbl SELECT * FROM co_nsc_student_data_tbl_bac;

    dbms_output.put_line('Restoring co_nsc_degree_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_nsc_degree_tbl');
    INSERT INTO co_nsc_degree_tbl SELECT * FROM co_nsc_degree_tbl_bac;

    COMMIT;
  END undo_p2_update_co_nsc;

  -- Undo for Step 4: 10 year NSC tables
  PROCEDURE undo_p3_update_co_ten_yr_nsc AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_ten_yr_nsc_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_ten_yr_nsc_tbl');
    INSERT INTO co_ten_yr_nsc_tbl SELECT * FROM co_ten_yr_nsc_tbl_bac;

    dbms_output.put_line('Restoring co_gr_ten_yr_nsc_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_ten_yr_nsc_tbl');
    INSERT INTO co_gr_ten_yr_nsc_tbl SELECT * FROM co_gr_ten_yr_nsc_tbl_bac;

    COMMIT;
  END undo_p3_update_co_ten_yr_nsc;

  -- Undo for Step 5: Persistence tables
  PROCEDURE undo_p4_update_co_persist AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_persist_nsc_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_persist_nsc_tbl');
    INSERT INTO co_persist_nsc_tbl SELECT * FROM co_persist_nsc_tbl_bac;

    dbms_output.put_line('Restoring co_gr_persist_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_persist_tbl');
    INSERT INTO co_gr_persist_tbl SELECT * FROM co_gr_persist_tbl_bac;

    COMMIT;
  END undo_p4_update_co_persist;

  -- Undo for Step 6: Most recent data tables
  PROCEDURE undo_p5_update_most_recent AS
  BEGIN
    dbms_output.enable(NULL);
    dbms_output.put_line('Restoring co_most_recent_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_most_recent_tbl');
    INSERT INTO co_most_recent_tbl SELECT * FROM co_most_recent_tbl_bac;

    dbms_output.put_line('Restoring co_gr_most_recent_tbl from backup');
    EXECUTE IMMEDIATE('TRUNCATE TABLE co_gr_most_recent_tbl');
    INSERT INTO co_gr_most_recent_tbl SELECT * FROM co_gr_most_recent_tbl_bac;

    COMMIT;
  END undo_p5_update_most_recent;

END zdmcodm_undo;