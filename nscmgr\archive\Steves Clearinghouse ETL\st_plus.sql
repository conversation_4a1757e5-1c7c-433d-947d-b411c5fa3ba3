declare

cursor pop_sel_cur is
select
*
from sem201520.sem_aims_hold_plus_fa
--where sem_aims_hold_plus.last_term = '201110'
order by sem_aims_hold_plus_fa.pidm;

type pop_sel_table is table of pop_sel_cur%rowtype index by binary_integer;
pop_sel_rec pop_sel_table;

cursor um_major_cur(pidm_in number, term_code_in varchar2) is
select
*
from td_student_data
where td_student_data.sd_term_code = term_code_in
and td_student_data.sd_pidm = pidm_in;

type um_major_table is table of um_major_cur%rowtype index by binary_integer;
um_major_rec um_major_table;

cursor sapr_cur(pidm_in number, term_code_in varchar2) is
select a.rorsapr_pidm       rzvsapr_pidm,
       stvterm_code    rzvsapr_term_code,
       a.rorsapr_sapr_code  rzvsapr_sapr_code,
       rtvsapr_disb_ind     rzvsapr_unsat_code,
       rtvsapr_desc         rzvsapr_desc
from 
       aimsmgr.rorsapr a,
       aimsmgr.rtvsapr,
       aimsmgr.stvterm
where
       a.rorsapr_sapr_code = rtvsapr_code
and
       a.rorsapr_term_code =
       (select max(b.rorsapr_term_code)
        from aimsmgr.rorsapr b
        where b.rorsapr_term_code <= stvterm_code
        and b.rorsapr_pidm = a.rorsapr_pidm)
and a.rorsapr_pidm = pidm_in
and stvterm.stvterm_code = term_code_in;

--select 
--* --rorsapr.rorsapr_pidm
--from <EMAIL> rorsapr
--where rorsapr.rorsapr_pidm = 2132 -- pidm_in
--and rorsapr.rorsapr_term_code = (select max(r1.rorsapr_term_code)
--                                 from rorsapr_ext r1
--                                 where r1.rorsapr_pidm = rorsapr.rorsapr_pidm
--                                 and r1.rorsapr_term_code < (select min(stvterm.stvterm_code) next_term
--                                                             from stvterm_ext stvterm
--                                                             where stvterm.stvterm_code > '201110'))
--and rorsapr.rorsapr_sapr_code != 'N'
--and rorsapr.rorsapr_sapr_code != 'SAP180' 
--and rorsapr.rorsapr_sapr_code != 'AD75'
--and rorsapr.rorsapr_sapr_code != 'SAP75'
--and rorsapr.rorsapr_sapr_code != 'AG1801'
--and rorsapr.rorsapr_sapr_code != 'AD180'
--and rorsapr.rorsapr_sapr_code != 'PEXTND'
--and rorsapr.rorsapr_sapr_code != 'SAPUR'
--and rorsapr.rorsapr_sapr_code != 'GRAD60'
--and rorsapr.rorsapr_sapr_code != 'SAPAD'
--and rorsapr.rorsapr_sapr_code != 'ACPL'
--and rorsapr.rorsapr_sapr_code != 'ACPLRV';
--      left outer join(
--        select
--        rorsapr.rorsapr_pidm,
--        rorsapr.rorsapr_sapr_code,
--        rorsapr.rorsapr_term_code
--        from rorsapr_ext rorsapr
--      )sap_issues_180_59_ind_join on sap_issues_180_59_ind_join.rorsapr_pidm = pop_sel.spriden_pidm
--                                  and sap_issues_180_59_ind_join.rorsapr_term_code = pop_sel.max_rorsapr_term_code
--                                  and (sap_issues_180_59_ind_join.rorsapr_sapr_code = 'N' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'SAP180' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'AD75' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'SAP75' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'AG1801' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'AD180' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'PEXTND' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'SAPUR' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'GRAD60' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'SAPAD' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'ACPL' or
--                                       sap_issues_180_59_ind_join.rorsapr_sapr_code = 'ACPLRV')

type sapr_table is table of sapr_cur%rowtype index by binary_integer;
sapr_rec sapr_table;

cursor aidy_cur(term_code_in varchar2) is
select
stvterm.stvterm_fa_proc_yr
from stvterm
where stvterm.stvterm_code = term_code_in;

type aidy_table is table of aidy_cur%rowtype index by binary_integer;
aidy_rec aidy_table;

cursor fam_memb_cur(pidm_in number, aidy_in varchar2) is
select rcrapp2.rcrapp2_pidm, 
       rcrapp2.rcrapp2_aidy_code, 
       rcrapp2.rcrapp2_pell_pgi,
       rcrapp2.rcrapp2_c_depend_status,
       rcrapp2.rcrapp2_model_cde,
       rcrapp1.rcrapp1_par_fam_memb,
       rcrapp1.rcrapp1_fam_memb
from aimsmgr.rcrapp1
inner join aimsmgr.rcrapp2 on rcrapp2.rcrapp2_pidm = rcrapp1.rcrapp1_pidm
                           and rcrapp2.rcrapp2_aidy_code = rcrapp1.rcrapp1_aidy_code
                           and rcrapp2.rcrapp2_seq_no = rcrapp1.rcrapp1_seq_no
                           and rcrapp2.rcrapp2_infc_code = rcrapp1.rcrapp1_infc_code
where rcrapp1.rcrapp1_pidm = pidm_in
and rcrapp1.rcrapp1_aidy_code = aidy_in
and rcrapp1.rcrapp1_curr_rec_ind = 'Y';

type fam_memb_table is table of fam_memb_cur%rowtype index by binary_integer;
fam_memb_rec fam_memb_table;

cursor efc_cur(pidm_in number, aidy_in varchar2) is
select rcrapp2.rcrapp2_pidm, 
       rcrapp2.rcrapp2_aidy_code, 
       rcrapp2.rcrapp2_pell_pgi,
       rcrapp2.rcrapp2_c_depend_status
from aimsmgr.rcrapp1
inner join aimsmgr.rcrapp2 on rcrapp2.rcrapp2_pidm = rcrapp1.rcrapp1_pidm
                           and rcrapp2.rcrapp2_aidy_code = rcrapp1.rcrapp1_aidy_code
                           and rcrapp2.rcrapp2_seq_no = rcrapp1.rcrapp1_seq_no
                           and rcrapp2.rcrapp2_infc_code = rcrapp1.rcrapp1_infc_code
where rcrapp1.rcrapp1_pidm = pidm_in
and rcrapp1.rcrapp1_aidy_code = aidy_in
and rcrapp1.rcrapp1_curr_rec_ind = 'Y';

type efc_table is table of efc_cur%rowtype index by binary_integer;
efc_rec efc_table;

cursor family_cur(pidm_in number, aidy_in varchar2) is
select 
rcrapp1.rcrapp1_pidm, 
rcrapp1.rcrapp1_aidy_code, 
rcrapp4.rcrapp4_fisap_inc --ome
from aimsmgr.rcrapp1, aimsmgr.rcrapp4
where rcrapp1.rcrapp1_curr_rec_ind  = 'Y'
and rcrapp1.rcrapp1_pidm = rcrapp4.rcrapp4_pidm
and rcrapp1.rcrapp1_aidy_code = rcrapp4.rcrapp4_aidy_code
and rcrapp1.rcrapp1_seq_no = rcrapp4.rcrapp4_seq_no
and rcrapp1.rcrapp1_infc_code = rcrapp4.rcrapp4_infc_code
and rcrapp1.rcrapp1_pidm = pidm_in
and rcrapp1.rcrapp1_aidy_code = aidy_in;

type family_table is table of family_cur%rowtype index by binary_integer;
family_rec family_table;

cursor pell_leu_cur(pidm_in number, aidy_in varchar2) is
select 
rcrlds4.rcrlds4_aidy_code,
rcrlds4.rcrlds4_pidm,
rcrlds4.rcrlds4_curr_rec_ind,
rcrlds4.rcrlds4_pell_leu,
rcrlds4.rcrlds4_agt_comb_total
from aimsmgr.rcrlds4
where rcrlds4.rcrlds4_curr_rec_ind = 'Y'
--and rcrlds4.rcrlds4_agt_comb_total is not null
and rcrlds4.rcrlds4_pidm = pidm_in
and rcrlds4.rcrlds4_aidy_code = aidy_in;

type pell_leu_table is table of pell_leu_cur%rowtype index by binary_integer;
pell_leu_rec pell_leu_table;

cursor pckg_cur(pidm_in number, aidy_in varchar2) is
select 
rorstat.rorstat_aidy_code,
rorstat.rorstat_pidm,
rorstat.rorstat_pckg_comp_date
from aimsmgr.rorstat
where rorstat.rorstat_pidm = pidm_in
and rorstat.rorstat_aidy_code = aidy_in;

type pckg_table is table of pckg_cur%rowtype index by binary_integer;
pckg_rec pckg_table;


cursor pell_cur(pidm_in number, aidy_in varchar2) is
Select 'Y' status
 from rcrapp1,rcrapp2
 where rcrapp1_curr_rec_ind = 'Y'
 and rcrapp1_aidy_code = aidy_in
 and rcrapp1_pidm = pidm_in
 and rcrapp1_pidm = rcrapp2_pidm
 and rcrapp2_infc_code = rcrapp1_infc_code
 and rcrapp2_aidy_code = rcrapp1_aidy_code
 and rcrapp2_seq_no = rcrapp1_seq_no
 and rcrapp2_pell_pgi <= 
   (select max(rorpell_max_pgi)
    from aimsmgr.rorpell
    where rorpell_award_amt > 0
    and rorpell_aidy_code = rcrapp1_aidy_code)
 and nvl(rcrapp2_eligibility_msg,'2') <> '1'
 and rcrapp1_degree_by_july = '2'
 and rcrapp1_citz_ind in ('1','2')
 and exists
      (select 1 from sgbstdn
       where sgbstdn_pidm = rcrapp1_pidm
        and sgbstdn_levl_code like 'U%'
        and sgbstdn_styp_code not in ('E','G','X','D','S')
        and sgbstdn_term_code_eff =
           (select max(sgbstdn_term_code_eff)
            from sgbstdn
            where sgbstdn_pidm = rcrapp1_pidm
            and sgbstdn_term_code_eff <=
              (select min(stvterm_code)
               from stvterm
               where stvterm_fa_proc_yr = rcrapp1_aidy_code)))
 and not exists
     (select 1 from rcrlds4
      where rcrlds4_pidm = rcrapp1_pidm
      and rcrlds4_pell_leu_limit_flag in ('E','C'))
 and not exists
     (select 1 from rrrareq
      where rrrareq_pidm = rcrapp1_pidm
      and rrrareq_aidy_code = rcrapp1_aidy_code
      and rrrareq_treq_code in ('DEF1','NSLDS','COISIR')
      and rrrareq_sat_ind = 'N';
      
type pell_table is table pell_cur%rowtype index by binary_integer;
pell_rec pell_table;

begin
  open pop_sel_cur;
  fetch pop_sel_cur bulk collect into pop_sel_rec;
  close pop_sel_cur;

  for i in 1..pop_sel_rec.count
  loop
    null;
    -- um major
--    open um_major_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).last_term);
--    fetch um_major_cur bulk collect into um_major_rec limit 1;
--    close um_major_cur;
    
--    open aidy_cur(pop_sel_rec(i).last_term);
--    fetch aidy_cur bulk collect into aidy_rec;
--    close aidy_cur;
    
--    open efc_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
--    fetch efc_cur bulk collect into efc_rec;
--    close efc_cur;
    
--    open family_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
--    fetch family_cur bulk collect into family_rec;
--    close family_cur;

--    open pell_leu_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
--    fetch pell_leu_cur bulk collect into pell_leu_rec;
--    close pell_leu_cur;

--    open sapr_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).last_term);
--    fetch sapr_cur bulk collect into sapr_rec;
--    close sapr_cur;

--    open fam_memb_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
--    fetch fam_memb_cur bulk collect into fam_memb_rec;
--    close fam_memb_cur;
--  
--
--    if fam_memb_rec.count > 0 then
--      if fam_memb_rec(1).rcrapp2_model_cde = 'D' then
--        update sem_aims_hold_plus_fa set
--        sem_aims_hold_plus_fa.family_size = fam_memb_rec(1).rcrapp1_par_fam_memb
--        where sem_aims_hold_plus_fa.pidm = pop_sel_rec(i).pidm
--        and sem_aims_hold_plus_fa.last_term = pop_sel_rec(i).last_term;
--      end if;
--
--      if fam_memb_rec(1).rcrapp2_model_cde = 'I' then
--        update sem_aims_hold_plus_fa set
--        sem_aims_hold_plus_fa.family_size = fam_memb_rec(1).rcrapp1_fam_memb
--        where sem_aims_hold_plus_fa.pidm = pop_sel_rec(i).pidm
--        and sem_aims_hold_plus_fa.last_term = pop_sel_rec(i).last_term;
--      end if;
--    end if;

    open pell_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
    fetch pell_cur bulk collect into pell_rec;
    close pell_cur;
  
    if pell_rec.count > 0 then
      update sem_aims_hold_plus_fa set
      sem_aims_hold_plus_fa.package_complete_date = pell_rec(1).status
      where sem_aims_hold_plus_fa.pidm = pop_sel_rec(i).pidm
      and sem_aims_hold_plus_fa.last_term = pop_sel_rec(i).last_term;
    end if;

--    open pckg_cur(pop_sel_rec(i).pidm, pop_sel_rec(i).aidy_code);
--    fetch pckg_cur bulk collect into pckg_rec;
--    close pckg_cur;
--  
--    if pckg_rec.count > 0 then
--      update sem_aims_hold_plus_fa set
--      sem_aims_hold_plus_fa.package_complete_date = pckg_rec(1).rorstat_pckg_comp_date
--      where sem_aims_hold_plus_fa.pidm = pop_sel_rec(i).pidm
--      and sem_aims_hold_plus_fa.last_term = pop_sel_rec(i).last_term;
--    end if;

--      update sem_aims_hold_plus set
----      fasfa_ind = 'Y'
--  --    aidy_code = aidy_rec(1).stvterm_fa_proc_yr,
--  --    um_major_code = um_major_rec(1).primary_major_1,
--  --    um_major_desc = um_major_rec(1).primary_major_1_desc
--  --    sem_aims_hold_plus.efc = efc_rec(1).rcrapp2_pell_pgi
--  --    sem_aims_hold_plus.family_income = family_rec(1).rcrapp4_fisap_inc --ome
----      sem_aims_hold_plus.total_pell_usage_lds4_pell_leu= pell_leu_rec(1).rcrlds4_pell_leu
----      sem_aims_hold_plus.sapr_code = sapr_rec(1).rzvsapr_sapr_code,
----      sem_aims_hold_plus.sapr_status = sapr_rec(1).rzvsapr_desc
--      sem_aims_hold_plus.rcrlds4_agt_comb_total = fam_memb_rec(1).rcrlds4_agt_comb_total
----      sem_aims_hold_plus.rcrapp2_c_depend_status = efc_rec(1).rcrapp2_c_depend_status
--
--      where sem_aims_hold_plus.pidm = pop_sel_rec(i).pidm
--      and sem_aims_hold_plus.last_term = pop_sel_rec(i).last_term;
--    else
--      update sem_aims_hold_plus set
--      sem_aims_hold_plus.rcrlds4_agt_comb_total = null
--      where sem_aims_hold_plus.pidm = pop_sel_rec(i).pidm
--      and sem_aims_hold_plus.last_term = pop_sel_rec(i).last_term;
--    end if;
    
  end loop;
  
  commit;
end;


--select 
--to_char((sem_aims_hold_plus.total_pell_usage_loan_debt * 100), '9990.000')
--from sem_aims_hold_plus
--where sem_aims_hold_plus.total_pell_usage_loan_debt is not null
--
--update sem_aims_hold_plus set
--sem_aims_hold_plus.total_pell_usage = to_char((sem_aims_hold_plus.total_pell_usage_loan_debt * 100), '9990.000')
--where sem_aims_hold_plus.total_pell_usage_loan_debt is not null

--update sem_aims_hold_plus set
--sem_aims_hold_plus.total_loan_debt = to_char(sem_aims_hold_plus.rcrlds4_agt_comb_total, '999990')
--where sem_aims_hold_plus.rcrlds4_agt_comb_total is not null


--update sem_aims_hold_plus set
--sem_aims_hold_plus.fasfa_ind = 'N'
--where sem_aims_hold_plus.fasfa_ind is null

update sem_aims_hold_plus set
dependent_status = case when rcrapp2_c_depend_status = '1' then 'independent'
                        when rcrapp2_c_depend_status = '2' then 'dependent'
                        else 'no_status' end

update sem_aims_hold_plus set 
loan_max_ind = case when rcrapp2_c_depend_status = '1' and total_loan_debt > 57500 then 'over'
                    when rcrapp2_c_depend_status = '1' and total_loan_debt <= 57500 then 'under'
                    when rcrapp2_c_depend_status = '2' and total_loan_debt <= 31000 then 'under'
                    when rcrapp2_c_depend_status = '2' and total_loan_debt <= 31000 then 'under'
                    else 'no_ind' end

update sem_aims_hold_plus set
sapr_pckg_ind = (select rtvsapr.rtvsapr_pckg_ind from aimsmgr.rtvsapr_ext rtvsapr where rtvsapr.rtvsapr_code = sapr_code and sapr_code is not null)


--select 
--order_num,
--pidm,
--umid,
--last_term,
--academic_year,
--overall_hours_earned,
--overall_gpa,
--commutable,
--attended_elsewhere_after_um,
--graduated_elsewhere_after_um,
--attended_2_yr_after_um,
--attended_4_yr_after_um,
--college_code_after_um_1,
--college_name_after_um_1,
--college_state_after_um_1,
--primary_level_code,
--primary_level_desc,
--county_code,
--county_desc,
--um_major_code,
--um_major_desc,
--efc,
--fasfa_ind,
--family_income,
--total_pell_usage,
--total_loan_debt,
--sapr_code,
--sapr_status
--from sem_aims_hold_plus
--order by order_num



update sem_aims_hold_plus set
class_code = (select td_student_data.class_code
               from td_student_data
               where td_student_data.sd_pidm = sem_aims_hold_plus.pidm
               and td_student_data.sd_term_code = sem_aims_hold_plus.last_term)


update sem_aims_hold_plus set
class_desc = (select stvclas.stvclas_desc
              from aimsmgr.stvclas_ext stvclas
              where stvclas.stvclas_code = sem_aims_hold_plus.class_code)



