--------------------------------------------------------
--  File created - Sunday-February-25-2024   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View V1_STUDENT_CURRENT_TERM_COURSE
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CANVASMGR"."V1_STUDENT_CURRENT_TERM_COURSE" ("ACADEMIC_TERM_ID", "COURSE_SECTION_ID", "COURSE_OFFERING_ID", "LE_CURRENT_COURSE_OFFERING_ID", "TERM_NAME", "TERM_CODE", "PERSON_ID", "PIDM", "UMID", "FIRST_NAME", "LAST_NAME", "UNIQNAME", "COURSE_TITLE", "TITLE", "CLASS_NUMBER", "SECTION_NUMBER", "STATUS", "TYPE", "MAX_ENROLLMENT", "CURRENT_GRADE", "FINAL_GRADE", "AVG_COURSE_GRADE", "STUDENT_TYPE_CODE", "STUDENT_TYPE_DESC", "CLASS_CODE", "CLASS_DESC", "PRIMARY_COLLEGE_CODE", "PRIMARY_COLLEGE_DESC", "PRIMARY_DEGREE_CODE", "PRIMARY_DEGREE_DESC", "PRIMARY_MAJOR_1", "PRIMARY_MAJOR_1_DESC", "PRIMARY_CONC_1", "PRIMARY_CONC_1_DESC", "PRIMARY_LEVEL_CODE", "REPORT_LEVEL_CODE", "TERM_GPA", "OVERALL_GPA", "GENDER", "AGE", "HSCH_GPA", "PCOL_GPA_TRANSFERRED_1", "REPORT_ETHNICITY", "HSCH_CODE", "HSCH_DESC") AS 
  WITH term_info AS (--8784
    SELECT
        utk.*
    FROM
        UDP_UMF_TERM_KEY utk
    WHERE
            sysdate < utk.le_term_end_date
        AND sysdate > utk.le_term_begin_date

), courses_sections_of_current_term AS (
    SELECT
        coe.le_code AS course_title,
        coe.title,
        ti.term_name,
        ti.term_code,
        cse.*,
        coe.academic_term_id
    FROM
             udp_course_offering_ext coe
        JOIN udp_course_section_ext cse ON coe.course_offering_id = cse.course_offering_id
        JOIN term_info              ti ON coe.academic_term_id = ti.academic_term_id
), courses_enrollment AS (
    SELECT
        csct.*,
        csee.person_id,
        usk.pidm,
        usk.umid,
        CASE
            WHEN p.first_name IS NULL THEN
                p.name
            ELSE
                p.first_name
        END         first_name,
        CASE
            WHEN p.last_name IS NULL THEN
                p.name
            ELSE
                p.last_name
        END         last_name,
          usk.uniqname
    FROM
             courses_sections_of_current_term csct
        JOIN udp_course_section_enrollment_ext csee ON csee.course_section_id = csct.course_section_id
        JOIN udp_person_ext                    p ON csee.person_id = p.person_id
--        JOIN udp_person_email_ext              pe ON pe.person_id = p.person_id
        JOIN udp_umf_stud_key usk on csee.person_id = usk.person_id
--    WHERE
--            csee.role = 'Student'
--        AND csee.role_status = 'Enrolled'
--        AND csee.enrollment_status = 'Active'

), course_grades AS (
    SELECT
        ce.*,
        cge.le_current_score current_grade,
        cge.le_final_score   final_grade
--   ,
--   cge.gpa_cumulative_excluding_course_grade AS cumulative_gpa
    FROM
             udp_course_grade_ext cge
        JOIN courses_enrollment ce ON ce.course_section_id = cge.course_section_id
                                      AND ce.person_id = cge.person_id
)

, average_course_grade as (
  select 
    course_section_id, 
    round(AVG(current_grade),2) as avg_course_grade 
  from (
    select 
      distinct uniqname, 
      course_section_id,
      current_grade 
    from course_grades
    ) course_unique_student_avg_grade 
    group by course_section_id
    )

, courses_grade_average as (
  select 
    cg.*, 
    acg.avg_course_grade  
  from 
    average_course_grade acg 
  join 
    course_grades cg 
  on 
    cg.course_section_id  = acg.course_section_id)

, umf_join AS ( 
 SELECT
   a.*,
   sd.student_type_code,
   sd.student_type_desc,
   sd.class_code,
   sd.class_desc,
   sd.primary_college_code,
   sd.primary_college_desc,
   sd.primary_degree_code,
   sd.primary_degree_desc,
   sd.primary_major_1,
   sd.primary_major_1_desc,
   sd.primary_conc_1,
   sd.primary_conc_1_desc,
   sd.primary_level_code,
   sd.report_level_code,
   sd.term_gpa,
   sd.overall_gpa,
   dm.gender,
   dm.age,
   dm.hsch_gpa,
   dm.PCOL_GPA_TRANSFERRED_1,
   dm.report_ethnicity,
   dm.hsch_code,
   dm.hsch_desc

 FROM
   courses_grade_average a
   left join um_student_data sd
   on a.pidm = sd.sd_pidm
   and a.term_code = sd.sd_term_code
   left join um_demographic dm
   on a.pidm = dm.dm_pidm
)
 SELECT 
ACADEMIC_TERM_ID,
COURSE_SECTION_ID, 
COURSE_OFFERING_ID, 
LE_CURRENT_COURSE_OFFERING_ID, 
TERM_NAME, 
TERM_CODE, 
PERSON_ID, 
PIDM, 
UMID, 
FIRST_NAME, 
LAST_NAME, 
UNIQNAME, 
COURSE_TITLE, 
TITLE, 
CLASS_NUMBER, 
SECTION_NUMBER, 
STATUS, 
TYPE, 
MAX_ENROLLMENT, 
CURRENT_GRADE, 
FINAL_GRADE, 
AVG_COURSE_GRADE,
student_type_code,
student_type_desc,
class_code,
class_desc,
primary_college_code,
primary_college_desc,
primary_degree_code,
primary_degree_desc,
primary_major_1,
primary_major_1_desc,
primary_conc_1,
primary_conc_1_desc,
primary_level_code,
report_level_code,
term_gpa,
overall_gpa,
 gender,
 age,
 hsch_gpa,
 PCOL_GPA_TRANSFERRED_1,
 report_ethnicity,
 hsch_code,
 hsch_desc
FROM
    umf_join
--where
--academic_term_id = 4
ORDER BY
    1,2,3,
    uniqname
;
