SELECT
ROUND(AVG(TOEFL_IBT_TOTAL),2),
ROUND(AVG(IELTS_OVERALL),2),
ROUND(AVG(TD_TEST_SCORE.SAT_VERBAL),2),
ROUND(AVG(TD_TEST_SCORE.SAT_MATHEMATICS),2),
ROUND(AVG(TD_TEST_SCORE.SAT_WRITING),2),
ROUND(AVG(TD_TEST_SCORE.ACT_COMPOSITE),2)

FROM
IA_TD_STUDENT_DATA
inner join TD_TEST_SCORE on TD_TEST_SCORE.TD_TERM_CODE = IA_TD_STUDENT_DATA.SD_TERM_CODE and
                            TD_TEST_SCORE.PIDM = IA_TD_STUDENT_DATA.SD_PIDM
WHERE
IA_TD_STUDENT_DATA.SD_TERM_CODE = '201510' and
IA_TD_STUDENT_DATA.REGISTERED_IND = 'Y' and
IA_TD_STUDENT_DATA.IA_STUDENT_TYPE_CODE = 'F' and
IA_TD_STUDENT_DATA.INTL_IND = 'Y'

