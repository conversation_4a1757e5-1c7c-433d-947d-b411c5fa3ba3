--------------------------------------------------------
--  File created - Thursday-May-19-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View DWRKS_PLANS_ONLY
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DWRKSMGR"."DWRKS_PLANS_ONLY" ("SD_PIDM", "UMID", "LAST_NAME", "FIRST_NAME", "MIDDLE_INITIAL", "TERM_CODE", "TERM_DESC", "STUDENT_STATUS_CODE", "STUDENT_STATUS_DESC", "PLAN_ID", "PLAN_TERM", "PLAN_TERM_DESC", "LEVEL_CODE", "LEVEL_DESC", "DEGREE_CODE", "DEGREE_DESC", "PLAN_NAME", "ACTIVE", "LOCKED", "ACTIVE_LOCKED_IND", "PLAN_OFFICIAL_TRACKING_STATUS", "PLAN_UNOFFICIAL_TRCKING_STATUS", "PLAN_TRACKING_STATUS_CURRENT", "PLAN_CREATED_BY", "PLAN_CREATED_DATE", "PLAN_MODIFIED_BY", "PLAN_MODIFIED_DATE", "PLAN_OVERRIDE_BY", "PLAN_OVERRIDE_DATE", "GROUP_ID") AS 
  select
distinct dwrks_plans.sd_pidm,
dwrks_plans.umid,
dwrks_plans.last_name,
dwrks_plans.first_name,
dwrks_plans.middle_initial,
dwrks_plans.term_code,
dwrks_plans.term_desc,
dwrks_plans.student_status_code,
dwrks_plans.student_status_desc,
dwrks_plans.plan_id,
dwrks_plans.plan_term,
dwrks_plans.plan_term_desc,
dwrks_plans.level_code,
dwrks_plans.level_desc,
dwrks_plans.degree_code,
dwrks_plans.degree_desc,
dwrks_plans.plan_name,
dwrks_plans.active,
dwrks_plans.locked,
dwrks_plans.active_locked_ind,
dwrks_plans.plan_official_tracking_status,
dwrks_plans.plan_unofficial_trcking_status,
dwrks_plans.plan_tracking_status_current,
dwrks_plans.plan_created_by,
dwrks_plans.plan_created_date,
dwrks_plans.plan_modified_by,
dwrks_plans.plan_modified_date,
dwrks_plans.plan_override_by,
dwrks_plans.plan_override_date,
dwrks_plans.group_id
from dwrks_plans
--where plan_term ='202010'
;
