WITH --22590
 dp1 AS (
 SELECT
  '171146'                                          unitid,
  'EF1'                                             survsect,
  sd_pidm,
  sd_term_code,
  ipeds_gender_code,
  report_ethnicity,
  ipeds_race_code,
  report_level_code,
  nvl(deceased_ind, 'N')                            deceased_ind,
  primary_major_1_cipc_code,
  td.a1_state_code,
  nvl(st.ipeds_state_code, '57')                    ipeds_state_code,
--case 
--when td.a1_nation_desc is null and td.a1_state_code = 'MI' then 'INUS_PPS'
--when td.a1_nation_desc in ('United States','United States of America') and td.a1_state_code = 'MI' then 'INUS_PPS'
--when td.a1_nation_desc in ('United States','United States of America') and td.a1_state_code != 'MI' then 'INUS_NOTPPS'
--when td.a1_nation_desc not in ('United States','United States of America') and td.a1_state_code != 'MI' then 'OUTSIDEUS'
--end ipeds_state_code_1,
  online_courses_only_ind,
  rd.section_number,
  ia_student_type_code,
  full_part_time_ind_umf,
  CASE
   WHEN report_level_code = 'UG'
        AND ia_student_type_code IN ( 'G', 'E', 'X', 'D', 'S' )
        AND primary_admit_term = sd_term_code THEN
    '1'
   WHEN report_level_code = 'GR'
        AND ia_student_type_code IN ( 'G', 'E', 'X', 'D', 'S' )
        AND primary_admit_term = sd_term_code THEN
    '2'
   ELSE
    '3'
  END                                               slevel_part_d,
--  round((trunc(sysdate) - hsch_grad_date) / 365, 2) AS tte_years,
round((enr_status_date - hsch_grad_date) / 365, 2) AS tte_years,
  
    CASE
   WHEN ia_student_type_code = 'F'
        AND (enr_status_date - hsch_grad_date) / 365 <= 1 THEN
    '2'
   ELSE
    NULL
  END hs,
 CASE
   WHEN ia_student_type_code = 'F'
        AND primary_level_code = 'UG'
        AND full_part_time_ind_umf = 'F' THEN
    1
   WHEN ia_student_type_code = 'T'
        AND primary_level_code = 'UG'
        AND full_part_time_ind_umf = 'F' THEN
    2
   WHEN (ia_student_type_code IN ('T','C', 'R' )
        AND primary_level_code in ('U2','U3')
        AND full_part_time_ind_umf = 'F') or
        (ia_student_type_code IN ('C', 'R' )
        AND primary_level_code = 'UG'
        AND full_part_time_ind_umf = 'F') 
        THEN
    3
   WHEN ia_student_type_code IN ( 'G', 'E', 'X', 'D', 'S' )
        AND primary_level_code like 'U%'
        AND full_part_time_ind_umf = 'F' THEN
    7
   WHEN (primary_level_code like 'G%' or primary_level_code like 'D%')
        AND full_part_time_ind_umf = 'F' THEN
    11
   WHEN ia_student_type_code = 'F'
        AND primary_level_code = 'UG'
        AND full_part_time_ind_umf = 'P' THEN
    15
   WHEN ia_student_type_code = 'T'
        AND primary_level_code = 'UG'
        AND full_part_time_ind_umf = 'P' THEN
    16
   WHEN (ia_student_type_code IN ('T','C', 'R' )
        AND primary_level_code in ('U2','U3')
        AND full_part_time_ind_umf = 'P') or
        (ia_student_type_code IN ('C', 'R' )
        AND primary_level_code = 'UG'
        AND full_part_time_ind_umf = 'P') 
        THEN
    17
   WHEN ia_student_type_code IN ( 'G', 'E', 'X', 'D', 'S' )
        AND primary_level_code like 'U%'
        AND full_part_time_ind_umf = 'P' THEN
    21
   WHEN (primary_level_code like 'G%' or primary_level_code like 'D%')
        AND full_part_time_ind_umf = 'P' THEN
    25
  END                                               slevel_part_a,
  CASE
   WHEN report_level_code = 'UG'
        AND ia_student_type_code IN ( 'F', 'T', 'C', 'R' ) THEN
    1
   WHEN report_level_code = 'UG'
        AND ia_student_type_code IN ( 'G', 'E', 'X', 'D', 'S' ) THEN
    2
   WHEN report_level_code = 'GR' THEN
    3
   ELSE
    NULL
  END                                               slevel_part_g,
  decode(report_level_code, 'UG', 1, 3)             slevel_part_b,
  age,
  CASE
   WHEN full_part_time_ind_umf = 'F'
        AND age < 18 THEN
    1
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 18 AND 19 THEN
    2
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 20 AND 21 THEN
    3
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 22 AND 24 THEN
    4
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 25 AND 29 THEN
    5
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 30 AND 34 THEN
    6
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 35 AND 39 THEN
    7
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 40 AND 49 THEN
    8
   WHEN full_part_time_ind_umf = 'F'
        AND age BETWEEN 50 AND 64 THEN
    9
   WHEN full_part_time_ind_umf = 'F'
        AND age >= 65 THEN
    10
   WHEN full_part_time_ind_umf = 'P'
        AND age < 18 THEN
    13
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 18 AND 19 THEN
    14
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 20 AND 21 THEN
    15
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 22 AND 24 THEN
    16
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 25 AND 29 THEN
    17
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 30 AND 34 THEN
    18
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 35 AND 39 THEN
    19
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 40 AND 49 THEN
    20
   WHEN full_part_time_ind_umf = 'P'
        AND age BETWEEN 50 AND 64 THEN
    21
   WHEN full_part_time_ind_umf = 'P'
        AND age >= 65 THEN
    22
  END                                               age_cat
 FROM
  ia_td_student_data     td
  LEFT JOIN td_registration_detail rd ON td.sd_pidm = rd.pidm
                                         AND td.sd_term_code = rd.td_term_code
  LEFT JOIN ia_cw_nces_state       st ON td.a1_state_code = st.a1_state_code
 WHERE
   registered_ind = 'Y'
  AND sd_term_code IN ( '202210' )            --Change this to current Fall term
), part_a AS (
--PART A-----------------------------------
 SELECT
  CAST('UNITID=' || unitid AS VARCHAR2(128))                     a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))                 b,
  CAST('PART=A' AS VARCHAR2(128))                                c,
  CAST('CIPCODE=99.0000' AS VARCHAR2(128))                       d,
  CAST('LINE=' || dp1.slevel_part_a AS VARCHAR2(128))            e,
  CAST('RACE=' || dp1.ipeds_race_code AS VARCHAR2(128))          f,
  CAST('SEX=' || dp1.ipeds_gender_code AS VARCHAR2(128))         g,
  CAST('count=' || COUNT(DISTINCT dp1.sd_pidm) AS VARCHAR2(128)) h,
  CAST(NULL AS VARCHAR2(128))                                    i,
  CAST(NULL AS VARCHAR2(128))                                    j,
  CAST(NULL AS VARCHAR2(128))                                    k
 FROM
  dp1
 GROUP BY
  CAST('UNITID=' || unitid AS VARCHAR2(128)),
  CAST('SURVSECT=' || survsect AS VARCHAR2(128)),
  CAST('PART=A' AS VARCHAR2(128)),
  CAST('CIPCODE=99.0000' AS VARCHAR2(128)),
  CAST('LINE=' || dp1.slevel_part_a AS VARCHAR2(128)),
  CAST('RACE=' || dp1.ipeds_race_code AS VARCHAR2(128)),
  CAST('SEX=' || dp1.ipeds_gender_code AS VARCHAR2(128))
), part_b AS (
--PART B-----------------------------------
 SELECT
  CAST('UNITID=' || unitid AS VARCHAR2(128))                 a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))             b,
  CAST('PART=B' AS VARCHAR2(128))                            c,
  CAST('LINE=' || age_cat AS VARCHAR2(128))                  d,
  CAST('SLEVEL=' || slevel_part_b AS VARCHAR2(128))          e,
  CAST('SEX=' || ipeds_gender_code AS VARCHAR2(128))         f,
  CAST('COUNT=' || COUNT(DISTINCT sd_pidm) AS VARCHAR2(128)) g,
  CAST(NULL AS VARCHAR2(128))                                h,
  CAST(NULL AS VARCHAR2(128))                                i,
  CAST(NULL AS VARCHAR2(128))                                j,
  CAST(NULL AS VARCHAR2(128))                                k
 FROM
  dp1
 GROUP BY
  CAST('UNITID=' || unitid AS VARCHAR2(128))                 ,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))             ,
  CAST('PART=B' AS VARCHAR2(128))                            ,
  CAST('LINE=' || age_cat AS VARCHAR2(128))                  ,
  CAST('SLEVEL=' || slevel_part_b AS VARCHAR2(128))          ,
  CAST('SEX=' || ipeds_gender_code AS VARCHAR2(128))         
), part_c AS (
--PART C-----------------------------------
 SELECT
  CAST('UNITID=' || unitid AS VARCHAR2(128))                 a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))             b,
  CAST('PART=C' AS VARCHAR2(128))                            c,
  CAST('LINE=' || ipeds_state_code AS VARCHAR2(128))         d,
  CAST('HS=' || '1' AS VARCHAR2(128))                        e,
  CAST('COUNT=' || COUNT(DISTINCT sd_pidm) AS VARCHAR2(128)) f,
  CAST(NULL AS VARCHAR2(128))                                g,
  CAST(NULL AS VARCHAR2(128))                                h,
  CAST(NULL AS VARCHAR2(128))                                i,
  CAST(NULL AS VARCHAR2(128))                                j,
  CAST(NULL AS VARCHAR2(128))                                k
 FROM
  dp1
 WHERE
  ia_student_type_code = 'F'
 GROUP BY
  CAST('UNITID=' || unitid AS VARCHAR2(128)),
  CAST('SURVSECT=' || survsect AS VARCHAR2(128)),
  CAST('PART=C' AS VARCHAR2(128)),
  CAST('LINE=' || ipeds_state_code AS VARCHAR2(128)),
  CAST('HS=' || '1' AS VARCHAR2(128))
), part_c_h2 AS (
--PART C_H2-----------------------------------
 SELECT
  CAST('UNITID=' || unitid AS VARCHAR2(128))                 a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))             b,
  CAST('PART=C' AS VARCHAR2(128))                            c,
  CAST('LINE=' || ipeds_state_code AS VARCHAR2(128))         d,
  CAST('HS=' || hs AS VARCHAR2(128))                         e,
  CAST('COUNT=' || COUNT(DISTINCT sd_pidm) AS VARCHAR2(128)) f,
  CAST(NULL AS VARCHAR2(128))                                g,
  CAST(NULL AS VARCHAR2(128))                                h,
  CAST(NULL AS VARCHAR2(128))                                i,
  CAST(NULL AS VARCHAR2(128))                                j,
  CAST(NULL AS VARCHAR2(128))                                k
 FROM
  dp1
 WHERE
  hs IS NOT NULL
 GROUP BY
  CAST('UNITID=' || unitid AS VARCHAR2(128)),
  CAST('SURVSECT=' || survsect AS VARCHAR2(128)),
  CAST('PART=C' AS VARCHAR2(128)),
  CAST('LINE=' || ipeds_state_code AS VARCHAR2(128)),
  CAST('HS=' || hs AS VARCHAR2(128))
), part_d AS (
--PART D-----------------------------------
 SELECT
  CAST('UNITID=' || unitid AS VARCHAR2(128))                 a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))             b,
  CAST('PART=D' AS VARCHAR2(128))                            c,
  CAST('COUNT=' || COUNT(DISTINCT sd_pidm) AS VARCHAR2(128)) d,
  CAST(NULL AS VARCHAR2(128))                                e,
  CAST(NULL AS VARCHAR2(128))                                f,
  CAST(NULL AS VARCHAR2(128))                                g,
  CAST(NULL AS VARCHAR2(128))                                h,
  CAST(NULL AS VARCHAR2(128))                                i,
  CAST(NULL AS VARCHAR2(128))                                j,
  CAST(NULL AS VARCHAR2(128))                                k
 FROM
  dp1
 WHERE
  slevel_part_d = 1
 GROUP BY
  CAST('UNITID=' || unitid AS VARCHAR2(128)),
  CAST('SURVSECT=' || survsect AS VARCHAR2(128)),
  CAST('PART=D' AS VARCHAR2(128))
), part_e AS (
--PART E-----------------------------------
 SELECT DISTINCT
  CAST('UNITID=' || unitid AS VARCHAR2(128))     a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128)) b,
  CAST('PART=E' AS VARCHAR2(128))                c,
  CAST('FT_PY_COHORT='
       ||(
   SELECT
    COUNT(DISTINCT co.co_pidm)
   FROM
    ia_cohort_persist_tbl_nsc co
   WHERE
     co.co_full_part_ind_umf = 'F'
    AND co.co_ia_student_type_code = 'F'
    AND co.co_term_code_key =(to_number(dp1.sd_term_code) - 100)
  ) AS VARCHAR2(128))                            d,
  CAST('FT_EXCLUSIONS='
       ||(
   SELECT
    COUNT(DISTINCT co.co_pidm)
   FROM
    ia_cohort_persist_tbl_nsc co
   WHERE
     co.co_full_part_ind_umf = 'F'
    AND co.co_ia_student_type_code = 'F'
    AND dp1.deceased_ind = 'Y'
    AND co.co_term_code_key =(to_number(dp1.sd_term_code) - 100)
  ) AS VARCHAR2(128))                            e,
  CAST('FT_CY_COHORT='
       ||(
   SELECT
    COUNT(DISTINCT co.co_pidm)
   FROM
    ia_cohort_persist_tbl_nsc co
   WHERE
     co.co_full_part_ind_umf = 'F'
    AND co.co_ia_student_type_code = 'F'
    AND co.scnd_fall_persist = 'UMF RETAINED'
    AND co.co_term_code_key =(to_number(dp1.sd_term_code) - 100)
  ) AS VARCHAR2(128))                            f,
  CAST('PT_PY_COHORT='
       ||(
   SELECT
    COUNT(DISTINCT co.co_pidm)
   FROM
    ia_cohort_persist_tbl_nsc co
   WHERE
     co.co_full_part_ind_umf = 'P'
    AND co.co_ia_student_type_code = 'F'
    AND co.co_term_code_key =(to_number(dp1.sd_term_code) - 100)
  ) AS VARCHAR2(128))                            g,
  CAST('PT_EXCLUSIONS='
       ||(
   SELECT
    COUNT(DISTINCT co.co_pidm)
   FROM
    ia_cohort_persist_tbl_nsc co
   WHERE
     co.co_full_part_ind_umf = 'P'
    AND co.co_ia_student_type_code = 'F'
    AND dp1.deceased_ind = 'Y'
    AND co.co_term_code_key =(to_number(dp1.sd_term_code) - 100)
  ) AS VARCHAR2(128))                            h,
  CAST('PT_CY_COHORT='
       ||(
   SELECT
    COUNT(DISTINCT co.co_pidm)
   FROM
    ia_cohort_persist_tbl_nsc co
   WHERE
     co.co_full_part_ind_umf = 'P'
    AND co.co_ia_student_type_code = 'F'
    AND co.scnd_fall_persist = 'UMF RETAINED'
    AND co.co_term_code_key =(to_number(dp1.sd_term_code) - 100)
  ) AS VARCHAR2(128))                            i,
  CAST(NULL AS VARCHAR2(128))                    j,
  CAST(NULL AS VARCHAR2(128))                    k
 FROM
  dp1
), part_f AS (
--PART F-----------------------------------
--UNITID=nnnnnn,SURVSECT=EF1,PART=F,ST_STAFF_RATIO=nnn
 SELECT DISTINCT
  CAST('UNITID=' || unitid AS VARCHAR2(128))       a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))   b,
  CAST('PART=F' AS VARCHAR2(128))                  c,
  CAST('ST_STAFF_RATIO=' || '14' AS VARCHAR2(128)) d,/*UPDATE THIS WITH STUDENT RATIO TO 1 FACULTY*/
  CAST(NULL AS VARCHAR2(128))                      e,
  CAST(NULL AS VARCHAR2(128))                      f,
  CAST(NULL AS VARCHAR2(128))                      g,
  CAST(NULL AS VARCHAR2(128))                      h,
  CAST(NULL AS VARCHAR2(128))                      i,
  CAST(NULL AS VARCHAR2(128))                      j,
  CAST(NULL AS VARCHAR2(128))                      k
 FROM
  dp1
), part_g AS (
--PART G-----------------------------------
 SELECT DISTINCT
  CAST('UNITID=' || unitid AS VARCHAR2(128))      a,
  CAST('SURVSECT=' || survsect AS VARCHAR2(128))  b,
  CAST('PART=G' AS VARCHAR2(128))                 c,
  CAST('LINE=' || slevel_part_g AS VARCHAR2(128)) d,
  CAST('ENROLL_EXCLUSIVE='
       ||(
   SELECT
    COUNT(DISTINCT sd_pidm)
   FROM
    dp1 t1
   WHERE
     online_courses_only_ind = 'Y'
    AND t1.slevel_part_g = dp1.slevel_part_g
  ) AS VARCHAR2(128))                             e,
  CAST('ENROLL_SOME='
       ||(
   SELECT
    COUNT(DISTINCT sd_pidm)
   FROM
    dp1 t1
   WHERE
     online_courses_only_ind = 'N'
    AND section_number LIKE 'W%'
    AND t1.slevel_part_g = dp1.slevel_part_g
  ) AS VARCHAR2(128))                             f,
  CAST('NOTENROLL=' AS VARCHAR2(128))                     g,
  CAST('INUS_PPS='
       ||(
   SELECT
    COUNT(DISTINCT sd_pidm)
   FROM
    dp1 t1
   WHERE
     online_courses_only_ind = 'Y'
    AND t1.slevel_part_g = dp1.slevel_part_g
    AND ipeds_state_code = '26'  --MI
  ) AS VARCHAR2(128))                             h,
--  CAST('INUS_PPS='
--       ||(
--   SELECT
--    COUNT(DISTINCT sd_pidm)
--   FROM
--    dp1 t1
--   WHERE
--     online_courses_only_ind = 'Y'
--    AND t1.slevel_part_g = dp1.slevel_part_g
--    AND ipeds_state_code_1 = 'INUS_PPS'  --MI
--  ) AS VARCHAR2(128))                             h,
  CAST('INUS_NOTPPS='
       ||(
   SELECT
    COUNT(DISTINCT sd_pidm)
   FROM
    dp1 t1
   WHERE
     online_courses_only_ind = 'Y'
    AND t1.slevel_part_g = dp1.slevel_part_g
    AND ipeds_state_code NOT IN('26', '57', '90') --NOT(MI,Unknown,Outside US)
  ) AS VARCHAR2(128))                             i,
--  CAST('INUS_NOTPPS='
--       ||(
--   SELECT
--    COUNT(DISTINCT sd_pidm)
--   FROM
--    dp1 t1
--   WHERE
--     online_courses_only_ind = 'Y'
--    AND t1.slevel_part_g = dp1.slevel_part_g
--    AND ipeds_state_code_1 = 'INUS_NOTPPS' --NOT(MI,Unknown,Outside US)
--  ) AS VARCHAR2(128))                             i,
  CAST('INUS_UNKNOWN_STATE='
       ||(
   SELECT
    COUNT(DISTINCT sd_pidm)
   FROM
    dp1 t1
   WHERE
     online_courses_only_ind = 'Y'
    AND t1.slevel_part_g = dp1.slevel_part_g
    AND ipeds_state_code = '57' --Unknown
  ) AS VARCHAR2(128))                             j,
--    CAST('INUS_UNKNOWN_STATE='
--  ) AS VARCHAR2(128))                             j,
  CAST('OUTSIDEUS='
       ||(
   SELECT
    COUNT(DISTINCT sd_pidm)
   FROM
    dp1 t1
   WHERE
     online_courses_only_ind = 'Y'
    AND t1.slevel_part_g = dp1.slevel_part_g
    AND ipeds_state_code = '90'-- Outside US
  ) AS VARCHAR2(128))                             k
--  CAST('OUTSIDEUS='
--       ||(
--   SELECT
--    COUNT(DISTINCT sd_pidm)
--   FROM
--    dp1 t1
--   WHERE
--     online_courses_only_ind = 'Y'
--    AND t1.slevel_part_g = dp1.slevel_part_g
--    AND ipeds_state_code_1 = 'OUTSIDEUS'-- Outside US
--  ) AS VARCHAR2(128))                             k
 FROM
  dp1
)
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_a
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_b
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_c
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_c_h2
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_d
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_e
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_f
UNION ALL
SELECT
 a,
 b,
 c,
 d,
 e,
 f,
 g,
 h,
 i,
 j,
 k
FROM
 part_g;