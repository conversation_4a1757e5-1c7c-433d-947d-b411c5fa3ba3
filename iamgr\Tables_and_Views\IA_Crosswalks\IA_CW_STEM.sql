/*
earth and resource science (CIP code 03)
computer science (CIP code 11)
engineering (CIP code 14)
biological sciences (CIP code 26) 
mathematics (CIP code 27) 
physical sciences (CIP code 40) 

*/
--create table ia_cw_stem_bac as select * from IA_CW_STEM;
truncate table ia_cw_stem_bac;

insert into ia_cw_stem_bac
select * from ia_cw_stem
;

truncate table IA_CW_STEM;

INSERT INTO IA_CW_STEM (
SELECT 
DISTINCT PRIMARY_MAJOR_1 MAJR_CODE, 
PRIMARY_MAJOR_1_DESC MAJR_DESC,
--substr(primary_major_1_cipc_code,1,2)cip_fam,
--primary_major_1_cipc_code,
'Y'  STEM_IND
FROM IA_TD_STUDENT_DATA
WHERE 
SD_TERM_CODE >= 202110
and (ia_cip_family in ('03','11','14','26','27','40')
OR
PRIMARY_MAJOR_1  IN ('AMTH'))
and 
PRIMARY_MAJOR_1  NOT IN ('URP')
and 
PRIMARY_MAJOR_1_DESC  NOT IN ('Engineering Science')

minus

select 
MAJR_CODE, 
MAJR_DESC, 
STEM_IND 
from ia_cw_stem_bac
)
;

select * from IA_CW_STEM
minus
select * from IA_CW_STEM_BAC;

commit;
