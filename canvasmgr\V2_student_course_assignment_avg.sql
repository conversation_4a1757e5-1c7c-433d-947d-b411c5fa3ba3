--------------------------------------------------------
--  File created - Wednesday-July-03-2024   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View V2_STUDENT_COURSE_ASSIGNMENT_AVG
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CANVASMGR"."V2_STUDENT_COURSE_ASSIGNMENT_AVG" ("COURSE_OFFERING_ID", "LEARNER_ACTIVITY_ID", "AVG_ASSIGNMENT_SCORE") AS 
  WITH
 lare AS (
 SELECT
   lare.learner_activity_id,
   lare.learner_activity_result_id,
   lare.score
 FROM
   udp_learner_activity_result_ext lare
 WHERE
   lare.gradebook_status = 'true'
   AND lare.grading_status = 'graded' )
   
SELECT
 lae.course_offering_id,
 lare.learner_activity_id,
 ROUND(AVG(lare.score),2) AS avg_assignment_score
from
 udp_learner_activity_ext lae
LEFT JOIN
 lare ON  lae.learner_activity_id = lare.learner_activity_id
LEFT JOIN
 udp_annotation_ext ae ON  lare.learner_activity_result_id = ae.learner_activity_result_id
WHERE
 lae.status = 'published'
GROUP BY
 lae.course_offering_id,
 lare.learner_activity_id
;
