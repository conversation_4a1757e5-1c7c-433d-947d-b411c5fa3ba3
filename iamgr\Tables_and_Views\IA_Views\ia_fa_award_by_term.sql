--------------------------------------------------------
--  DDL for View IA_FA_AWARD_BY_TERM
--------------------------------------------------------

CREATE OR REPLACE FORCE EDITIONABLE VIEW "IAMGR"."IA_FA_AWARD_BY_TERM" AS
  SELECT
    fa."PIDM",
    fa."UMID",
    fa."LAST_NAME",
    fa."FIRST_NAME",
    fa."MIDDLE_INITIAL",
    fa."AID_YEAR",
    fa."TERM_CODE",
    fy.cy,
    fy.fy,
    fy.fy_co_term_code,
    fa."FUND_CODE",
    fa."FUND_DESC",
    fa."AR_CODE",
    fa."FUND_SOURCE",
    fa."FUND_TYPE",
    fa."FUND_ACTIVE",
    fa."AWARD_STATUS",
    fa."AWARD_STATUS_DATE",
    fa."OFFER_AMT",
    fa."OFFER_DATE",
    fa."ACCEPT_AMT",
    fa."ACCEPT_DATE",
    fa."DECLINE_AMT",
    fa."DECLINE_DATE",
    fa."CANCEL_AMT",
    fa."CANCEL_DATE",
    fa."MEMO_AMT",
    fa."MEMO_DATE",
    fa."PAID_AMT",
    fa."PAID_DATE",
    fa."ACTIVITY_DATE",
    fa."PCKG_LOAD_IND",
    fa."ORIG_OFFER_DATE"
    
  FROM
         fa_award_by_term fa
    INNER JOIN ia_cw_fy_term fy ON fa.term_code = fy.fy_term_code;

GRANT SELECT ON "IAMGR"."IA_FA_AWARD_BY_TERM" TO "HEERFMGR";

GRANT SELECT ON "IAMGR"."IA_FA_AWARD_BY_TERM" TO "BO_FLINT_STUDENT";