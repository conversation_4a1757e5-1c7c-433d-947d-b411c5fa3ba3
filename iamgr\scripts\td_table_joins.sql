select 
td_student_data.sd_term_desc,
--td_demographic.a1_county_code,
--td_demographic.a1_county_desc,
count (*)

from td_student_data
inner join td_demographic on td_demographic.dm_pidm = td_student_data.sd_pidm and
                             td_demographic.td_term_code = td_student_data.sd_term_code
inner join TD_TEST_SCORE on TD_TEST_SCORE.pidm = td_student_data.sd_pidm and
                            TD_TEST_SCORE.td_term_code = td_student_data.sd_term_code

where --housing_ind = 'Y' and 
td_student_data.registered_ind = 'Y' and
td_student_data.sd_term_code in ('201110','201210','201310','201410','201510') and
td_demographic.a1_county_code in ('MI049','MI145','MI087','MI017','MI111')

-- ia_student_type_code = 'F'
--td_student_data.report_level_code = 'UG'
group by 
td_student_data.sd_term_desc
--td_demographic.a1_county_code,
--td_demographic.a1_county_desc
order by 
td_student_data.sd_term_desc asc
--td_demographic.a1_county_code,
--td_demographic.a1_county_desc asc

select 
co_term_code_key,
co_ia_student_type_code,
count (*)
from 
ia_cohort_pop_undup

where 
--co_ia_student_type_code = 'F' and
co_full_part_ind_umf = 'F'
group by
co_term_code_key,
co_ia_student_type_code
order by
co_term_code_key desc,
co_ia_student_type_code
