DROP TABLE CO_GR_PERSIST_TBL;
DROP TABLE CO_GR_PERSIST_TBL_BAC;
CREATE TABLE CO_GR_PERSIST_TBL_BAC AS SELECT * FROM CO_GR_PERSIST_TBL;
TRUNCATE TABLE CO_GR_PERSIST_TBL;

-- CREATE TABLE CO_GR_PERSIST_TBL AS
-- INSERT INTO CO_GR_PERSIST_TBL

  (SELECT CO_GR_TEN_YR_NSC_TBL.*,
      ----------------------------SECOND FALL PERSISTENCE-----------------------
      CASE
        WHEN SCND_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SCND_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC    IS NULL
        AND SCND_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ( '2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_NSC         = 'Y'
        AND SCND_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SCND_FALL_TERM_REG_IND IS NULL
        OR SCND_FALL_TERM_REG_IND      = 'N')
        AND SCND_FALL_GRAD_IND        IS NULL
        AND SCND_FALL_TERM_REG_NSC     = 'Y'
        AND SCND_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SCND_FALL_PERSIST,
      ----------------------------THIRD FALL PERSISTENCE------------------------
      CASE
        WHEN THRD_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN THRD_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC    IS NULL
        AND THRD_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_NSC         = 'Y'
        AND THRD_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((THRD_FALL_TERM_REG_IND IS NULL
        OR THRD_FALL_TERM_REG_IND      = 'N')
        AND THRD_FALL_GRAD_IND        IS NULL
        AND THRD_FALL_TERM_REG_NSC     = 'Y'
        AND THRD_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END THRD_FALL_PERSIST,
      ----------------------------FOURTH FALL PERSISTENCE-----------------------
      CASE
        WHEN FRTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FRTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC    IS NULL
        AND FRTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_NSC         = 'Y'
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FRTH_FALL_TERM_REG_IND IS NULL
        OR FRTH_FALL_TERM_REG_IND      = 'N')
        AND FRTH_FALL_GRAD_IND        IS NULL
        AND FRTH_FALL_TERM_REG_NSC     = 'Y'
        AND FRTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FRTH_FALL_PERSIST,
      ----------------------------FIFTH FALL PERSISTENCE------------------------
      CASE
        WHEN FFTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN FFTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC    IS NULL
        AND FFTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_NSC         = 'Y'
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC     = 'Y'
        AND FFTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((FFTH_FALL_TERM_REG_IND IS NULL
        OR FFTH_FALL_TERM_REG_IND      = 'N')
        AND FFTH_FALL_GRAD_IND        IS NULL
        AND FFTH_FALL_TERM_REG_NSC     = 'Y'
        AND FFTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END FFTH_FALL_PERSIST,
      ----------------------------SIXTH FALL PERSISTENCE------------------------
      CASE
        WHEN SXTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SXTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC    IS NULL
        AND SXTH_FALL_GRAD_NSC        IS NULL )
        THEN 'LOST'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE        IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_NSC         = 'Y'
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE         = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC     = 'Y'
        AND SXTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED      IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SXTH_FALL_TERM_REG_IND IS NULL
        OR SXTH_FALL_TERM_REG_IND      = 'N')
        AND SXTH_FALL_GRAD_IND        IS NULL
        AND SXTH_FALL_TERM_REG_NSC     = 'Y'
        AND SXTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SXTH_FALL_PERSIST,
      ----------------------------SEVENTH FALL PERSISTENCE----------------------
      CASE
        WHEN SVNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN SVNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC    IS NULL
        AND SVNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_NSC         = 'Y'
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND SVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((SVNTH_FALL_TERM_REG_IND IS NULL
        OR SVNTH_FALL_TERM_REG_IND      = 'N')
        AND SVNTH_FALL_GRAD_IND        IS NULL
        AND SVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND SVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END SVNTH_FALL_PERSIST,
      ----------------------------EIGTH FALL PERSISTENCE------------------------
      CASE
        WHEN EIGTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN EIGTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC    IS NULL
        AND EIGTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_NSC         = 'Y'
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC     = 'Y'
        AND EIGTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((EIGTH_FALL_TERM_REG_IND IS NULL
        OR EIGTH_FALL_TERM_REG_IND      = 'N')
        AND EIGTH_FALL_GRAD_IND        IS NULL
        AND EIGTH_FALL_TERM_REG_NSC     = 'Y'
        AND EIGTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END EIGTH_FALL_PERSIST,
      ----------------------------NINTH FALL PERSISTENCE------------------------
      CASE
        WHEN NNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN NNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_TERM_REG_NSC    IS NULL
        AND NNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_NSC         = 'Y'
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_TERM_REG_NSC     = 'Y'
        AND NNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((NNTH_FALL_TERM_REG_IND IS NULL
        OR NNTH_FALL_TERM_REG_IND      = 'N')
        AND NNTH_FALL_GRAD_IND        IS NULL
        AND NNTH_FALL_TERM_REG_NSC     = 'Y'
        AND NNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END NNTH_FALL_PERSIST,
      ----------------------------TENTH FALL PERSISTENCE------------------------
      CASE
        WHEN TNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN TNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_TERM_REG_NSC    IS NULL
        AND TNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_NSC         = 'Y'
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_TERM_REG_NSC     = 'Y'
        AND TNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((TNTH_FALL_TERM_REG_IND IS NULL
        OR TNTH_FALL_TERM_REG_IND      = 'N')
        AND TNTH_FALL_GRAD_IND        IS NULL
        AND TNTH_FALL_TERM_REG_NSC     = 'Y'
        AND TNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END TNTH_FALL_PERSIST,
      ----------------------------ELEVENTH FALL PERSISTENCE---------------------
      CASE
        WHEN ELVNTH_FALL_GRAD_IND = 'Y'
        THEN 'UMF GRADUATED'
        WHEN ELVNTH_FALL_TERM_REG_IND = 'Y'
        THEN 'UMF RETAINED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_TERM_REG_NSC    IS NULL
        AND ELVNTH_FALL_GRAD_NSC        IS NULL)
        THEN 'LOST'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_GRAD_NSC         = 'Y'
        AND TWOFOUR_NSC_DEGREE         IN ('2','L'))
        THEN 'OTHER 2 YEAR GRADUATED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_NSC         = 'Y'
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND TWOFOUR_NSC_DEGREE          = '4')
        THEN 'OTHER 4 YEAR GRADUATED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND ELVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED       IN ( '2','L'))
        THEN 'OTHER 2 YEAR RETAINED'
        WHEN ((ELVNTH_FALL_TERM_REG_IND IS NULL
        OR ELVNTH_FALL_TERM_REG_IND      = 'N')
        AND ELVNTH_FALL_GRAD_IND        IS NULL
        AND ELVNTH_FALL_TERM_REG_NSC     = 'Y'
        AND ELVNTH_FALL_GRAD_NSC        IS NULL
        AND TWOFOUR_NSC_ENROLLED        = '4')
        THEN 'OTHER 4 YEAR RETAINED'
      END ELVNTH_FALL_PERSIST
  FROM CO_GR_TEN_YR_NSC_TBL)
;
  DESC CO_GR_TEN_YR_NSC_TBL;