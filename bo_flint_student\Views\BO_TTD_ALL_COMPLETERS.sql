--------------------------------------------------------
--  DDL for View BO_TTD_ALL_COMPLETERS
--------------------------------------------------------

  CREATE OR REPLACE  VIEW "BO_FLINT_STUDENT"."BO_TTD_ALL_COMPLETERS" 
  AS
  WITH dp1 AS (
    SELECT
    ROW_NUMBER() OVER (PARTITION BY sd.sd_pidm ORDER BY sd_term_code) min_term,
    ROW_NUMBER() OVER (PARTITION BY sd.sd_pidm ORDER BY sd_term_code desc) max_term,
        sd.sd_pidm,
        dc.umid,
        sd.orig_student_type_code,
--        sd.student_type_code,
        sd.primary_level_code,
        sd.primary_college_code,
--        sd.report_level_code,
        sd.sd_term_code,
        sd.INST_HOURS_EARNED,
        STVTERM.STVTERM_START_DATE,
        STVTERM.STVTERM_FA_PROC_YR start_aidy_code,
        sd.primary_major_1 start_major,
        dc.grad_date,
        dc.fiscal_year grad_year,
        dc.grad_term_code,
        dc.degree_major_code,
        dc.degree_code,
        dc.nces_code,
        dc.completion_type
    FROM
             td_student_data sd
        INNER JOIN (--1038
                SELECT
                *
            FROM
                iamgr.ia_degree_completions               
            ) dc ON dc.pidm = sd.sd_pidm
        left join aimsmgr.stvterm_ext stvterm
        on sd.sd_term_code = stvterm.stvterm_code
       
    WHERE
            sd.registered_ind = 'Y'
        AND sd.primary_level_code = dc.primary_level_code    
--        AND sd.primary_degree_code = dc.degree_code
        AND sd.sd_term_code <= dc.grad_term_code 
)
 , start_date AS (
    SELECT
    distinct
        dp1.sd_pidm,
        dp1.sd_term_code,
        dp1.stvterm_start_date start_date
    FROM
        dp1
where dp1.min_term = 1
 )
, max_term_sel AS (
    SELECT
    distinct
        dp1.sd_pidm,
        dp1.INST_HOURS_EARNED,
        dp1.primary_college_code col_code
    FROM
        dp1 
where dp1.max_term = 1
)
SELECT
    dp1.grad_term_code,
    dp1.grad_year,
    dp1.sd_term_code start_term_code,
    dp1.start_aidy_code,
    dp1.sd_pidm,
    dp1.umid,
    dp1.orig_student_type_code,
--    dp1.student_type_code,
    dp1.primary_level_code,
    max_term_sel.col_code,
    dp1.start_major,
    dp1.degree_major_code,
    dp1.degree_code,
    dp1.completion_type,
    
    
    start_date.start_date,
    dp1.grad_date,
    round((dp1.grad_date - start_date.start_date) / 365, 2)     AS ttd_years,
--    round((dp1.grad_date - start_date.start_date) / 30.41, 2)   AS ttd_months,
    max_term_sel.INST_HOURS_EARNED
FROM
    dp1
    INNER JOIN start_date
      on dp1.sd_pidm = start_date.sd_pidm
      and dp1.sd_term_code = start_date.sd_term_code
    INNER JOIN max_term_sel ON max_term_sel.sd_pidm = dp1.sd_pidm
;

DESC AIMSMGR.stvterm_ext;
SELECT * FROM aimsmgr.stvterm_ext;