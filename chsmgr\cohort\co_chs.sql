--------------------------------------------------------
--  File created - Friday-August-18-2023   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View CO_CHS
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CHSMGR"."CO_CHS" ("CO_NUM", "CO_PIDM", "CO_FY", "UMID", "FIRST_NAME", "LAST_NAME", "CO_TERM_CODE", "CO_TERM_DESC", "CO_PRIMARY_LEVEL_CODE", "CO_DEGREE_CODE", "CO_MAJOR", "SECOND_FY", "THIRD_FY", "FORTH_FY", "FIFTH_FY", "SIXTH_FY", "SEVENTH_FY", "SECOND_YEAR_PERSIST", "THIRD_YEAR_PERSIST", "FORTH_YEAR_PERSIST", "FIFTH_YEAR_PERSIST", "SIXTH_YEAR_PERSIST", "SEVENTH_YEAR_PERSIST") AS 
  WITH co AS (
 SELECT
  ROW_NUMBER()
  OVER(PARTITION BY td.sd_pidm, td.primary_major_1
       ORDER BY td.sd_term_code ASC
  )                         row_id,
  td.sd_pidm                co_pidm,
  td.umid,
  dm.first_name,
  dm.last_name,
  fy.fy                     co_fy,
  td.sd_term_code           co_term_code,
  td.sd_term_desc           co_term_desc,
  td.primary_level_code     co_primary_level_code,
  td.primary_degree_code    co_degree_code,
  td.primary_major_1        co_major,
  CASE
   WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
    ( substr(td.sd_term_code, 1, 4) + 2
      || '10' )
   ELSE
    ( substr(td.sd_term_code, 1, 4) + 1
      || '10' )
  END                       sf_term_code,
  CASE
   WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
    ( substr(td.sd_term_code, 1, 4) + 3
      || '10' )
   ELSE
    ( substr(td.sd_term_code, 1, 4) + 2
      || '10' )
  END                       tf_term_code,
  CASE
   WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
    ( substr(td.sd_term_code, 1, 4) + 4
      || '10' )
   ELSE
    ( substr(td.sd_term_code, 1, 4) + 3
      || '10' )
  END                       fof_term_code,
  CASE
   WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
    ( substr(td.sd_term_code, 1, 4) + 5
      || '10' )
   ELSE
    ( substr(td.sd_term_code, 1, 4) + 4
      || '10' )
  END                       fif_term_code,
  CASE
   WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
    ( substr(td.sd_term_code, 1, 4) + 6
      || '10' )
   ELSE
    ( substr(td.sd_term_code, 1, 4) + 5
      || '10' )
  END                       sif_term_code,
  CASE
   WHEN ( substr(td.sd_term_code, 5, 6) = '40' ) THEN
    ( substr(td.sd_term_code, 1, 4) + 7
      || '10' )
   ELSE
    ( substr(td.sd_term_code, 1, 4) + 6
      || '10' )
  END                       sef_term_code
 FROM
  td_student_data  td
  LEFT OUTER JOIN td_demographic   dm ON dm.td_term_code = td.sd_term_code
                                       AND dm.dm_pidm = td.sd_pidm
  LEFT OUTER JOIN iamgr.ia_cw_fy_term    fy ON td.sd_term_code = fy.fy_term_code
 WHERE
   td.registered_ind = 'Y'
  AND td.sd_term_code >= '201140'
  AND td.primary_degree_code = 'BS'
  AND td.primary_major_1 IN ( 'HCAD', 'HLSC', 'PHS' )
  AND td.overall_hours_earned >= '75'
), co_reg AS (
 SELECT
  co.*,
--  sf_fy.fy second_fy,
--  tf.fy third_fy,
--  fof.fy forth_fy,
--  fif.fy fifth_fy,
--  sif.fy sixth_fy,
--  sef.fy seventh_fy,
    nvl(sf.registered_ind, 'N')         sf_reg_ind,
  nvl(tf.registered_ind, 'N')         tf_reg_ind,
  nvl(fof.registered_ind, 'N')        fof_reg_ind,
  nvl(fif.registered_ind, 'N')        fif_reg_ind,
  nvl(sif.registered_ind, 'N')        sif_reg_ind,
  nvl(sef.registered_ind, 'N')        sef_reg_ind,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.sf_term_code
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END                                 sf_grad_ind,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.tf_term_code
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END                                 tf_grad_ind,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.fof_term_code
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END                                 fof_grad_ind,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.fif_term_code
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END                                 fif_grad_ind,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.sif_term_code
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END                                 sif_grad_ind,
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = co.co_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = co_degree_code
     AND umd.level_code = co.co_primary_level_code
     AND grad_term_code >= co.co_term_code
     AND umd.grad_term_code <= co.sef_term_code
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END                                 sef_grad_ind
 FROM
  co
  LEFT OUTER JOIN td_student_data  sf ON co.co_pidm = sf.sd_pidm
                                        AND co.sf_term_code = sf.sd_term_code
                                        AND co.co_primary_level_code = sf.primary_level_code
                                        AND co.co_degree_code = sf.primary_degree_code
                                        AND co.co_major = sf.primary_major_1
  LEFT OUTER JOIN td_student_data  tf ON co.co_pidm = tf.sd_pidm
                                        AND co.tf_term_code = tf.sd_term_code
                                        AND co.co_primary_level_code = tf.primary_level_code
                                        AND co.co_degree_code = tf.primary_degree_code
                                        AND co.co_major = tf.primary_major_1
  LEFT OUTER JOIN td_student_data  fof ON co.co_pidm = fof.sd_pidm
                                         AND co.fof_term_code = fof.sd_term_code
                                         AND co.co_primary_level_code = fof.primary_level_code
                                         AND co.co_degree_code = fof.primary_degree_code
                                         AND co.co_major = fof.primary_major_1
  LEFT OUTER JOIN td_student_data  fif ON co.co_pidm = fif.sd_pidm
                                         AND co.fif_term_code = fif.sd_term_code
                                         AND co.co_primary_level_code = fif.primary_level_code
                                         AND co.co_degree_code = fif.primary_degree_code
                                         AND co.co_major = fif.primary_major_1
  LEFT OUTER JOIN td_student_data  sif ON co.co_pidm = sif.sd_pidm
                                         AND co.sif_term_code = sif.sd_term_code
                                         AND co.co_primary_level_code = sif.primary_level_code
                                         AND co.co_degree_code = sif.primary_degree_code
                                         AND co.co_major = sif.primary_major_1
  LEFT OUTER JOIN td_student_data  sef ON co.co_pidm = sef.sd_pidm
                                         AND co.sef_term_code = sef.sd_term_code
                                         AND co.co_primary_level_code = sef.primary_level_code
                                         AND co.co_degree_code = sef.primary_degree_code
                                         AND co.co_major = sef.primary_major_1
 WHERE
  row_id = 1
)
SELECT
 ROW_NUMBER()
 OVER(PARTITION BY co_pidm
      ORDER BY co_term_code ASC
 )            co_num,
 co_reg.co_pidm,
 co_reg.co_fy,
 co_reg.umid,
 co_reg.first_name,
 co_reg.last_name,
 co_reg.co_term_code,
 co_reg.co_term_desc,
 co_reg.co_primary_level_code,
 co_reg.co_degree_code,
 co_reg.co_major,
-- co_reg.sf_term_code,
-- co_reg.tf_term_code,
-- co_reg.fof_term_code,
-- co_reg.fif_term_code,
-- co_reg.sif_term_code,
-- co_reg.sef_term_code,
-- co_reg.sf_reg_ind,
-- co_reg.sf_grad_ind,
 sf_fy.fy     second_fy,
 tf_fy.fy     third_fy,
 fof_fy.fy    forth_fy,
 fif_fy.fy    fifth_fy,
 sif_fy.fy    sixth_fy,
 sef_fy.fy    seventh_fy,
 CASE
  WHEN co_reg.sf_grad_ind = 'Y'    THEN
   'GRAD'
  WHEN co_reg.sf_reg_ind = 'Y'     THEN
   'REG'
  WHEN co_reg.sf_reg_ind = 'N'
       AND co_reg.sf_grad_ind = 'N' THEN
   'LOST'
 END          second_year_persist,
-- co_reg.tf_reg_ind,
-- co_reg.tf_grad_ind,
  CASE
  WHEN co_reg.tf_grad_ind = 'Y'    THEN
   'GRAD'
  WHEN co_reg.tf_reg_ind = 'Y'     THEN
   'REG'
  WHEN co_reg.tf_reg_ind = 'N'
       AND co_reg.tf_grad_ind = 'N' THEN
   'LOST'
 END          third_year_persist,
-- co_reg.fof_reg_ind,
-- co_reg.fof_grad_ind,
  CASE
  WHEN co_reg.fof_grad_ind = 'Y'    THEN
   'GRAD'
  WHEN co_reg.fof_reg_ind = 'Y'     THEN
   'REG'
  WHEN co_reg.fof_reg_ind = 'N'
       AND co_reg.fof_grad_ind = 'N' THEN
   'LOST'
 END          forth_year_persist,
-- co_reg.fif_reg_ind,
-- co_reg.fif_grad_ind,
  CASE
  WHEN co_reg.fif_grad_ind = 'Y'    THEN
   'GRAD'
  WHEN co_reg.fif_reg_ind = 'Y'     THEN
   'REG'
  WHEN co_reg.fif_reg_ind = 'N'
       AND co_reg.fif_grad_ind = 'N' THEN
   'LOST'
 END          fifth_year_persist,
-- co_reg.sif_reg_ind,
-- co_reg.sif_grad_ind,
  CASE
  WHEN co_reg.sif_grad_ind = 'Y'    THEN
   'GRAD'
  WHEN co_reg.sif_reg_ind = 'Y'     THEN
   'REG'
  WHEN co_reg.sif_reg_ind = 'N'
       AND co_reg.sif_grad_ind = 'N' THEN
   'LOST'
 END          sixth_year_persist,
-- co_reg.sef_reg_ind,
-- co_reg.sef_grad_ind,
  CASE
  WHEN co_reg.sef_grad_ind = 'Y'    THEN
   'GRAD'
  WHEN co_reg.sef_reg_ind = 'Y'     THEN
   'REG'
  WHEN co_reg.sef_reg_ind = 'N'
       AND co_reg.sef_grad_ind = 'N' THEN
   'LOST'
 END          seventh_year_persist
FROM
 co_reg
 LEFT OUTER JOIN iamgr.ia_cw_fy_term  sf_fy ON co_reg.sf_term_code = sf_fy.fy_term_code
 LEFT OUTER JOIN iamgr.ia_cw_fy_term  tf_fy ON co_reg.tf_term_code = tf_fy.fy_term_code
 LEFT OUTER JOIN iamgr.ia_cw_fy_term  fof_fy ON co_reg.fof_term_code = fof_fy.fy_term_code
 LEFT OUTER JOIN iamgr.ia_cw_fy_term  fif_fy ON co_reg.fif_term_code = fif_fy.fy_term_code
 LEFT OUTER JOIN iamgr.ia_cw_fy_term  sif_fy ON co_reg.sif_term_code = sif_fy.fy_term_code
 LEFT OUTER JOIN iamgr.ia_cw_fy_term  sef_fy ON co_reg.sef_term_code = sef_fy.fy_term_code
;
