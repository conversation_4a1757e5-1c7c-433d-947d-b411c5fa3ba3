/*
This query is designed to create the IPEDS degreee Completions Upload. The table
being queried (IA_DEGREE_COMPLETIONS)will requre to be updated after census day
of the spring semester.  

Export data to comma dilimeted file and edit the file to remove trailing commas.
Upload to IPEDS as Key Value Pair.
Section D must be manually entered.  Use the Tableau Completions Dashboard
*/
with PS0 AS(
select 
PIDM,
AGE,
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
DECODE(NCES_CODE,'1b','9','5','4','7','5','17','6','18','6','6','7','8','7') COMPLETER_LEVEL,
IPEDS_RACE_CODE,
REPORT_ETHNICITY,
IPEDS_GENDER_CODE,
CASE
WHEN AGE <18 THEN 'AGE1'
WHEN AGE >=18 AND AGE <=24 THEN 'AGE2'
WHEN AGE >=25 AND AGE <=39 THEN 'AGE3'
WHEN AGE >=40 THEN 'AGE4'
ELSE 'AGE5'
END PARTD_AGE
FROM
IA_DEGREE_COMPLETIONS
where FISCAL_YEAR = '20-21'                --  Update before running
),
PS1 AS (
SELECT
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE,
COUNT(PIDM) AS DEG_COUNT
FROM
PS0
GROUP BY
DEGREE_CODE,
COMPLETION_TYPE_CODE,
CIP_CODE,
NCES_CODE,
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE
),
PS2 AS (
SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=A' C,
'MAJORNUM='||PS1.COMPLETION_TYPE_CODE D,
'CIPCODE='||SUBSTR(PS1.CIP_CODE, 1, 2) ||'.'||SUBSTR(PS1.CIP_CODE, 3, 6) E,
'AWLEVEL='||PS1.NCES_CODE F,
'RACE='||PS1.IPEDS_RACE_CODE  G,
'SEX='||PS1.IPEDS_GENDER_CODE H,
'COUNT='||PS1.DEG_COUNT I
FROM PS1
),
PS3 AS(
SELECT
PS2.*,
CASE
WHEN 
/*******************************************************************************
            Update Online Bachelor's Degrees check OEL website
*******************************************************************************/

(E = 'CIPCODE=52.0301' AND F = 'AWLEVEL=5')--UG Accounting 
OR (E = 'CIPCODE=13.1210' AND F = 'AWLEVEL=5')--UG Early Childhood Studies 
OR (E = 'CIPCODE=52.0701' AND F = 'AWLEVEL=5')--UG Entrepreneurship & Innovation Management 
OR (E = 'CIPCODE=52.0801' AND F = 'AWLEVEL=5')--UG Finance 
OR (E = 'CIPCODE=52.0201' AND F = 'AWLEVEL=5') --UG General Business: BBA BUS 
OR (E = 'CIPCODE=38.0101' AND F = 'AWLEVEL=5')--UG General Philosophy 
OR (E = 'CIPCODE=51.0701' AND F = 'AWLEVEL=5')--UG Health Care Administration 
OR (E = 'CIPCODE=54.0101' AND F = 'AWLEVEL=5')--UG History 
OR (E = 'CIPCODE=30.0000' AND F = 'AWLEVEL=5')--UG Interdiciplinary Studies 
OR (E = 'CIPCODE=52.1101' AND F = 'AWLEVEL=5')--UG International Business 
OR (E = 'CIPCODE=52.1401' AND F = 'AWLEVEL=5')--UG Marketing 
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=5')--UG Operations & Supply Chain Management
OR (E = 'CIPCODE=52.1001' AND F = 'AWLEVEL=5')--UG Organizational Behavior & Human Resources Management 
OR (E = 'CIPCODE=42.0101' AND F = 'AWLEVEL=5')--UG Psychology 
OR (E = 'CIPCODE=44.0701' AND F = 'AWLEVEL=5') --UG Social Work: BSW SWK,SUTI  
Or (E = 'CIPCODE=30.9999' AND F = 'AWLEVEL=5') --Applied Science:UG BAS APLS 
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=5') --UG Nursing RN to BSN: BSN NURR, NURG - 
OR (E = 'CIPCODE=51.0908' AND F = 'AWLEVEL=5') --UG Respiratory Therapy: BS BSRT 
OR (E = 'CIPCODE=51.0701' AND F = 'AWLEVEL=5') --UG Respiratory Therapy: BS RSP 
OR (E = 'CIPCODE=51.0908' AND F = 'AWLEVEL=5') --UG Respiratory Therapy: BS RSP 
/*******************************************************************************
            Update Online Master's Degrees check OEL website
*******************************************************************************/ 
OR (E = 'CIPCODE=52.0301' AND F = 'AWLEVEL=7') --GR Accounting: MSACC
OR (E = 'CIPCODE=501001' AND F = 'AWLEVEL=7')--Arts Administration: MA
OR (E = 'CIPCODE=52.0201' AND F = 'AWLEVEL=7')--GR Business Administration: MBA
OR (E = 'CIPCODE=11.0101' AND F = 'AWLEVEL=7') --GR Computer Science and Information Systems: MS CAIS
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=7')--Educational Administration: MA
OR (E = 'CIPCODE=51.0701' AND F = 'AWLEVEL=7')--GR Health Care Management: MS
OR (E = 'CIPCODE=24.0199' AND F = 'AWLEVEL=7') --GR Liberal Studies: MA LBS
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=7') --GR Nursing: MSN Family Nurse Practitioner MSN NURN 
OR (E = 'CIPCODE=44.0401' AND F = 'AWLEVEL=7')--GR Public Administration: MPA
OR (E = 'CIPCODE=51.2201' AND F = 'AWLEVEL=7')--Public Health (MPH)
/*******************************************************************************
             Update Online Specialist Programs
*******************************************************************************/  
OR (E = 'CIPCODE=13.0101' AND F = 'AWLEVEL=7')--Education Specialist: EdS
/*******************************************************************************
             Update Online Doctoral Degrees check OEL website
*******************************************************************************/                     
OR (E = 'CIPCODE=13.0101' AND F = 'AWLEVEL=17')--Education: EdD
OR (E = 'CIPCODE=51.2308' AND F = 'AWLEVEL=18') --DR Physical Therapy: Transitional DPT 
OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=18') --DR Nursing:DNP
/*******************************************************************************
             Update Online Undergrad Certs check OEL website
*******************************************************************************/                           
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Africana Studies
OR (E = 'CIPCODE=52.0213' AND F = 'AWLEVEL=1b')--UG Business Leadership
--OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Early Childhood Trauma & Education
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG International Business Development
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG International Market Analysis
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Nursing & Healthcare InformaticsNursing Case Management
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Nursing Case Management
OR (E = 'CIPCODE=51.3802' AND F = 'AWLEVEL=2')--UG Nursing Leadership & Management
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Specialist in Aging
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--UG Teacher’s Certificate Minor in Psychology
OR (E = 'CIPCODE=13.1401' AND F = 'AWLEVEL=2')--UG Teaching English to Speakers of Other Languages (TESOL)

--OR (E = 'CIPCODE=51.3801' AND F = 'AWLEVEL=6') --NUR POST BACH CERT
/*******************************************************************************
             Update Online Graduate Certs check OEL website
*******************************************************************************/                             
OR (E = 'CIPCODE=52.0301' AND F = 'AWLEVEL=8')--GR Accounting
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Adult Gerontology Acute Care Nurse Practitioner
OR (E = 'CIPCODE=52.0201' AND F = 'AWLEVEL=8')--GR Business
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Data Science
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Finance
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Healthcare Administration
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Healthcare Analytics
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Healthcare Simulation
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Information Security
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Intelligent Systems
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR International Business
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Long Term Care Administration
OR (E = 'CIPCODE=52.1401' AND F = 'AWLEVEL=8')--GR Marketing
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Nurse Educator
OR (E = 'CIPCODE=52.0213' AND F = 'AWLEVEL=8')--GR Organizational Leadership
OR (E = 'CIPCODE=' AND F = 'AWLEVEL=')--GR Psychiatric Mental Health Nurse Practitioner

THEN '1'
ELSE '2'
END DIS_ED
FROM PS2
),
PS4 AS(
SELECT DISTINCT PIDM,IPEDS_GENDER_CODE,IPEDS_RACE_CODE,COMPLETER_LEVEL, 
MAX(AGE) AGE
FROM PS0
GROUP BY PIDM,IPEDS_GENDER_CODE,IPEDS_RACE_CODE,COMPLETER_LEVEL
),
PART_C_A AS(
SELECT 
DISTINCT
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE, 
PIDM
FROM PS0
WHERE COMPLETION_TYPE_CODE = 1
),
PART_C AS(
SELECT 
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE, 
COUNT(*) DEMO_COUNT 
FROM PART_C_A
GROUP BY 
IPEDS_RACE_CODE,
IPEDS_GENDER_CODE
)
SELECT A,B,C,D,E,F,G,H,I,
'' j,'' K,'' L,'' M,'' N,'' O,'' P,'' Q,'' R,'' S,'' T
FROM PS3

UNION ALL

SELECT DISTINCT A,B,'PART=B' C,D,E,F,'DistanceED='||DIS_ED G,'' H,'' I,
'' j,'' K,'' L,'' M,'' N,'' O,'' P,'' Q,'' R,'' S,'' T
FROM PS3 --WHERE J = '1'

UNION ALL

SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=C' C,
'RACE='||PART_C.IPEDS_RACE_CODE  D,
'SEX='||PART_C.IPEDS_GENDER_CODE E,
'COUNT='||PART_C.DEMO_COUNT F,'' G,'' H,'' I,
'' j,'' K,'' L,'' M,'' N,'' O,'' P,'' Q,'' R,'' S,'' T
FROM PART_C

UNION ALL

SELECT
'UNITID=171146' A,
'SURVSECT=COM' B,
'PART=D' C,
'CTLEVEL='||COMPLETER_LEVEL D,
'CRACE15='||DECODE(IPEDS_GENDER_CODE,1,1,0) E,
'CRACE16='||DECODE(IPEDS_GENDER_CODE,2,1,0) F,
'CRACE17='||DECODE(IPEDS_RACE_CODE,1,1,0) G,
'CRACE41='||DECODE(IPEDS_RACE_CODE,2,1,0) H,
'CRACE42='||DECODE(IPEDS_RACE_CODE,3,1,0) I,
'CRACE43='||DECODE(IPEDS_RACE_CODE,4,1,0) J,
'CRACE44='||DECODE(IPEDS_RACE_CODE,5,1,0) K,
'CRACE45='||DECODE(IPEDS_RACE_CODE,6,1,0) L,
'CRACE46='||DECODE(IPEDS_RACE_CODE,7,1,0) M,
'CRACE47='||DECODE(IPEDS_RACE_CODE,8,1,0) N,
'CRACE23='||DECODE(IPEDS_RACE_CODE,9,1,0) O,
'AGE1='||CASE WHEN AGE<18 THEN 1 ELSE 0 END P,
'AGE2='||CASE WHEN AGE >= 18 AND AGE <= 24 THEN 1 ELSE 0 END Q,
'AGE3='||CASE WHEN AGE >= 25 AND AGE <= 39 THEN 1 ELSE 0 END R,
'AGE4='||CASE WHEN AGE >= 40 THEN 1 ELSE 0 END S,
'AGE5='||CASE WHEN AGE IS NULL THEN 1 ELSE 0 END T
from PS4
--select * from ps4
;

