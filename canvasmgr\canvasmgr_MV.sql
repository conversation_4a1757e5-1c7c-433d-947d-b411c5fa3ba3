/*******************************************************************************
Materialized View (MV) DBA
by <PERSON> on 042922
*******************************************************************************/
--Drop a MV
DROP MATERIALIZED VIEW v1_student_course_mv;

--Create MV with refresh schedule
CREATE MATERIALIZED VIEW v1_student_course_mv
  REFRESH
          ON DEMAND
          START WITH TO_DATE('02-28-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM')
        NEXT TO_DATE('02-29-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM') + 1
      WITH ROWID
AS
  SELECT
    *
  FROM
    v1_student_course;
  
  --Drop a MV
DROP MATERIALIZED VIEW v1_student_current_term_course_mv;
  
--Create MV with refresh schedule  
CREATE MATERIALIZED VIEW v1_student_current_term_course_mv
  REFRESH
          ON DEMAND
          START WITH TO_DATE('02-25-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM')
        NEXT TO_DATE('02-26-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM') + 1
      WITH ROWID
AS
  SELECT
    *
  FROM
    v1_student_current_term_course;

  --Drop a MV
DROP MATERIALIZED VIEW v2_student_course_assignment_avg_mv;
  
--Create MV with refresh schedule  
CREATE MATERIALIZED VIEW v2_student_course_assignment_avg_mv
  REFRESH
          ON DEMAND
          START WITH TO_DATE('02-25-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM')
        NEXT TO_DATE('02-26-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM') + 1
      WITH ROWID
AS
  SELECT
    *
  FROM
    v2_student_course_assignment_avg;

  --Drop a MV
DROP MATERIALIZED VIEW v3_term_course_activities_mv;
  
--Create MV with refresh schedule  
CREATE MATERIALIZED VIEW v3_term_course_activities_mv
  REFRESH
          ON DEMAND
          START WITH TO_DATE('02-25-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM')
        NEXT TO_DATE('02-26-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM') + 1
      WITH ROWID
AS
  SELECT
    *
  FROM
    v3_term_course_activities;

  --Drop a MV
DROP MATERIALIZED VIEW umf_dfw_all_courses_mv;
  
--Create MV with refresh schedule  
CREATE MATERIALIZED VIEW umf_dfw_all_courses_mv
  REFRESH
          ON DEMAND
          START WITH TO_DATE('06-08-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM')
        NEXT TO_DATE('06-08-2024 08:00 AM', 'MM-DD-YYYY HH:MI AM') + 1
      WITH ROWID
AS
  SELECT
    *
  FROM
    umf_dfw_all_courses;

--Force a refresh of MV

EXEC DBMS_MVIEW.refresh('v1_student_course_mv');
EXEC DBMS_MVIEW.refresh('v1_student_current_term_course_mv');
--EXEC DBMS_MVIEW.refresh('v2_student_course_assignment_avg_mv');
EXEC DBMS_MVIEW.refresh('v3_term_course_activities_mv');
EXEC DBMS_MVIEW.refresh('UMF_DFW_ALL_COURSES_MV');

EXEC dbms_mview.refresh_all_mviews;

--Identify last refresh of Materialized View
SELECT
  owner,
  mview_name,
  to_char(last_refresh_date, 'yyyymmddhh24miss')    last_refresh_date,
  to_char(last_refresh_date, 'MM-DD-YYYY HH:MI AM') last_refresh_day
FROM
  all_mviews
WHERE
    owner = 'REGMGR'
  AND mview_name = 'v1_student_course_mv';

--Alter refresh schedule MV
ALTER MATERIALIZED VIEW v1_student_course_mv REFRESH ON COMMIT --DEMAND
 START WITH TO_DATE('05-20-2022 06:00 AM', 'MM-DD-YYYY HH:MI AM') NEXT TO_DATE('05-20-2022 06:00 AM', 'MM-DD-YYYY HH:MI AM') + 1;

SELECT
  owner,
  object_name,
  object_type,
  status
FROM
  dba_objects
WHERE
  status = 'INVALID';

SELECT
  owner,
  object_name,
  object_type,
  status
FROM
  dba_objects
WHERE
    status = 'INVALID'
  AND object_type = 'SYNONYM';
 
alter public synonym v1_student_course_mv compile;


  
--Create MV with refresh schedule
CREATE MATERIALIZED VIEW <NAME_MV>
 REFRESH
ON DEMAND
  START WITH TO_DATE('01-01-2023 06:00 AM','MM-DD-YYYY HH:MI AM')
  NEXT TO_DATE('01-02-2023 06:00 AM','MM-DD-YYYY HH:MI AM') + 1 WITH ROWID
AS
 SELECT...;
  

  