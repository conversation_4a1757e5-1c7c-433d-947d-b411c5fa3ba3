/*******************************************************************************
The Purpose of this Query is to build a degree completions table for reporting
that will move duplicate degrees (ie 1 of 2BA's or 2BS's) from 1st degree to 2nd
degree.  The query will also need to identify if a degree is a STEM or Health 
field degree and give these degrees precedence when deciding which degree to 
keep as the first major completion and which to push to the second degree 
following State reporting methodologies.
Table Name: IA_DEGREE_COMP_TBL
Grain: 1 row/FY/Student
Variables:
FY,pidm,report_ethnicity,gender,degree_count,distinct_degree_count,
degree_1_code,degree_1_program,degree_1_mjr,degree_1_code_cip,degree_1_NCES,
degree_2_code,degree_2_program,degree_2_mjr,degree_2_code_cip,degree_2_NCES,
degree_3_code,degree_3_program,degree_3_mjr,degree_3_code_cip,degree_3_NCES,
degree_4_code,degree_4_program,degree_4_mjr,degree_4_code_cip,degree_4_NCES
*******************************************************************************/
with 
raw_data as (
  select
  row_number() over(partition by pidm order by HP_DEGREE_IND desc,stem_mjr_ind_1 desc, DEGREE_SEQNO ) order_num,
  pidm,
  GRAD_TERM_CODE,
  REPORT_ETHNICITY,
  GENDER,
  degree_code,
  PRIMARY_PROGRAM,
  IA_NCES_CODE,
  LEVEL_CODE,
  HP_DEGREE_IND,
  primary_major_1,
  PRIMARY_MAJOR_1_CIPC_CODE,
  PRIMARY_MAJOR_1_DESC,
  STEM_MJR_IND_1,
  primary_major_2,
  PRIMARY_MAJOR_2_CIPC_CODE,
  PRIMARY_MAJOR_2_DESC,
  STEM_MJR_IND_2
  from IA_UM_DEGREE
  where
  degree_status = 'AW' and
  grad_term_code in ('201440','201510','201520','201530')
--  order by pidm
)
--,
--
--completion_counts as (
--  select 
--  pidm,
--  count (pidm) as pidm_count,
--  count (degree_code) as completions_count,
--  count (distinct degree_code) as first_major_completions,
--  count (degree_code) - count (distinct degree_code) as move_to_second_major,
--  count (PRIMARY_MAJOR_2) as second_majors,
--  count (degree_code) - count (distinct degree_code) + count (PRIMARY_MAJOR_2) as total_second_majors
--  from raw_data
--  group by pidm --,GRAD_TERM_CODE
--  order by 
--  pidm_count desc, 
--  completions_count desc, 
--  first_major_completions desc,
--  move_to_second_major desc,
--  second_majors desc,
--  total_second_majors desc  
--)--,
select 
completion_counts.pidm
from raw_data
left outer join(
  select 
  pidm,
  count (pidm) as pidm_count,
  count (degree_code) as completions_count,
  count (distinct degree_code) as first_major_completions,
  count (degree_code) - count (distinct degree_code) as move_to_second_major,
  count (PRIMARY_MAJOR_2) as second_majors,
  count (degree_code) - count (distinct degree_code) + count (PRIMARY_MAJOR_2) as total_second_majors
  from (
    select
    row_number() over(partition by pidm order by HP_DEGREE_IND desc,stem_mjr_ind_1 desc, DEGREE_SEQNO ) order_num,
    pidm,
    GRAD_TERM_CODE,
    REPORT_ETHNICITY,
    GENDER,
    degree_code,
    PRIMARY_PROGRAM,
    IA_NCES_CODE,
    LEVEL_CODE,
    HP_DEGREE_IND,
    primary_major_1,
    PRIMARY_MAJOR_1_CIPC_CODE,
    PRIMARY_MAJOR_1_DESC,
    STEM_MJR_IND_1,
    primary_major_2,
    PRIMARY_MAJOR_2_CIPC_CODE,
    PRIMARY_MAJOR_2_DESC,
    STEM_MJR_IND_2
    from IA_UM_DEGREE
    where
    degree_status = 'AW' and
    grad_term_code in ('201440','201510','201520','201530')
  )raw_data_2
  group by pidm --,GRAD_TERM_CODE
)completion_counts on completion_counts.pidm = raw_data.pidm


order by 1

from stvterm


--degree_1 as(
--  select
--  pidm as degree_1_pidm,
--  grad_term_code as degree_1_term_code,
--  DEGREE_CODE as degree_1_code,
--  PRIMARY_PROGRAM as degree_1_program,
--  PRIMARY_MAJOR_1 as degree_1_mjr,
--  PRIMARY_MAJOR_1_CIPC_CODE as degree_1_code_cip,
--  IA_NCES_CODE as degree_1_NCES
--  from raw_data
--  where order_num = 1
--),
--degree_2 as(
--  select
--  pidm as degree_2_pidm,
--  grad_term_code as degree_2_term_code,
--  DEGREE_CODE as degree_2_code,
--  PRIMARY_PROGRAM as degree_2_program,
--  PRIMARY_MAJOR_1 as degree_2_mjr,
--  PRIMARY_MAJOR_1_CIPC_CODE as degree_2_code_cip,
--  IA_NCES_CODE as degree_2_NCES
--  from raw_data
--  where order_num = 2
--),
--degree_3 as(
--  select
--  pidm as degree_3_pidm,
--  grad_term_code as degree_3_term_code,
--  DEGREE_CODE as degree_3_code,
--  PRIMARY_PROGRAM as degree_3_program,
--  PRIMARY_MAJOR_1 as degree_3_mjr,
--  PRIMARY_MAJOR_1_CIPC_CODE as degree_3_code_cip,
--  IA_NCES_CODE as degree_3_NCES
--  from raw_data
--  where order_num = 3
--),
--degree_4 as(
--  select
--  pidm as degree_4_pidm,
--  grad_term_code as degree_4_term_code,
--  DEGREE_CODE as degree_4_code,
--  PRIMARY_PROGRAM as degree_4_program,
--  PRIMARY_MAJOR_2 as degree_4_mjr,
--  PRIMARY_MAJOR_2_CIPC_CODE as degree_4_code_cip,
--  IA_NCES_CODE as degree_4_NCES
--  from raw_data
--  --where PRIMARY_MAJOR_2 is not NULL
--)

--select 
--distinct raw_data.pidm,
--raw_data.REPORT_ETHNICITY,
--raw_data.GENDER
--degree_1_code,
--degree_1_program,
--degree_1_mjr,
--degree_1_code_cip,
--degree_1_NCES,
--degree_2_code,
--degree_2_program,
--degree_2_mjr,
--degree_2_code_cip,
--degree_2_NCES
--degree_3_code,
--degree_3_program,
--degree_3_mjr,
--degree_3_code_cip,
--degree_3_NCES,
--degree_4_code,
--degree_4_program,
--degree_4_mjr,
--degree_4_code_cip,
--degree_4_NCES
--from raw_data
--left outer join  degree_1 on degree_1.degree_1_pidm = raw_data.pidm
--left outer join  degree_2 on degree_2.degree_2_pidm = raw_data.pidm
--left outer join  degree_3 on degree_3.degree_3_pidm = raw_data.pidm
--left outer join  degree_4 on degree_4.degree_4_pidm = raw_data.pidm

--select * from completion_counts
