--------------------------------------------------------
--  File created - Thursday-May-19-2022   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View DAP_REQ_MST
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DWRKSMGR"."DAP_REQ_MST" ("DAP_REQ_ID", "DAP_BLOCK_TYPE", "DAP_BLOCK_VALUE", "DAP_REQ_TITLE", "DAP_CAT_YR_START", "DAP_CAT_YR_STOP", "DAP_SCHOOL", "DAP_DEGREE", "DAP_COLLEGE", "DAP_MAJOR1", "DAP_MAJOR2", "DAP_CONC", "DAP_MINOR", "DAP_LIBL", "DAP_SPEC", "DAP_PROGRAM", "DAP_STU_ID", "DAP_PARSE_STATUS", "DAP_PARSE_DATE", "DAP_USER_LAST", "DAP_LOCK_ID", "DAP_LOCK_STAMP", "DAP_LOCK_JOBNUM", "DAP_CREATE_DATE", "DAP_CREATE_WHO", "DAP_MOD_DATE", "DAP_WHO", "DAP_WHAT", "DAP_TIMESTAMP1", "DAP_TIMESTAMP2", "UNIQUE_ID", "UNIQUE_KEY") AS 
  select "DAP_REQ_ID","DAP_BLOCK_TYPE","DAP_BLOCK_VALUE","DAP_REQ_TITLE","DAP_CAT_YR_START","DAP_CAT_YR_STOP","DAP_SCHOOL","DAP_DEGREE","DAP_COLLEGE","DAP_MAJOR1","DAP_MAJOR2","DAP_CONC","DAP_MINOR","DAP_LIBL","DAP_SPEC","DAP_PROGRAM","DAP_STU_ID","DAP_PARSE_STATUS","DAP_PARSE_DATE","DAP_USER_LAST","DAP_LOCK_ID","DAP_LOCK_STAMP","DAP_LOCK_JOBNUM","DAP_CREATE_DATE","DAP_CREATE_WHO","DAP_MOD_DATE","DAP_WHO","DAP_WHAT","DAP_TIMESTAMP1","DAP_TIMESTAMP2","UNIQUE_ID","UNIQUE_KEY"
from <EMAIL>
;
