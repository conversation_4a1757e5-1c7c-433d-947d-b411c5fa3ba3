/*******************************************************************************
This script pulls the Graduate Student Cohorts going back to Fall 10
with anamoulous student records removed
pidm    term code
90306   201310
*******************************************************************************/
CREATE OR REPLACE VIEW CO_GR_POP AS
  (SELECT sd.sd_pidm AS CO_GR_PIDM,
    dm.UMID,
    sd.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
--    to_date('01-SEP-' ||(to_number(SUBSTR(sd.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
(select stvterm.STVTERM_START_DATE from aimsmgr.stvterm_ext stvterm 
where sd.sd_term_code = STVTERM.STVTERM_CODE ) START_DATE,
    sd.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
    sd.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
    dm.report_ethnicity AS CO_GR_ETHNICITY,
    dm.gender           AS CO_GR_GENDER,
    sd.report_level_code,
    sd.primary_level_code,
    CASE
      WHEN (sd.PRIMARY_DEGREE_CODE = 'MLS'
      AND sd.sd_term_code         IN ('201310'))
      THEN 'MA'
      ELSE sd.PRIMARY_DEGREE_CODE
    END PRIMARY_DEGREE_CODE,
    sd.primary_major_1_CIPC_CODE,
    dm.CITIZENSHIP_CODE,
    dm.CITIZENSHIP_DESC,
    dm.PCOL_GPA_TRANSFERRED_1 trans_gpa,
    dm.PCOL_DESC_1,
    sd.PRIMARY_MAJOR_1,
    sd.PRIMARY_PROGRAM,
    sd.PRIMARY_CONC_1
  FROM td_student_data sd
  inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
  WHERE registered_ind      = 'Y'
  AND ia_student_type_code IN ('N')
--  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '201110'
  
  
  
  /* Students record anommolies removed*/
  MINUS
SELECT sd.sd_pidm AS CO_GR_PIDM,
    dm.UMID,
    sd.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
--    to_date('01-SEP-' ||(to_number(SUBSTR(sd.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
(select stvterm.STVTERM_START_DATE from aimsmgr.stvterm_ext stvterm 
where sd.sd_term_code = STVTERM.STVTERM_CODE ) START_DATE,
    sd.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
    sd.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
    dm.report_ethnicity AS CO_GR_ETHNICITY,
    dm.gender           AS CO_GR_GENDER,
    sd.report_level_code,
    sd.primary_level_code,
    CASE
      WHEN (sd.PRIMARY_DEGREE_CODE = 'MLS'
      AND sd.sd_term_code         IN ('201310'))
      THEN 'MA'
      ELSE sd.PRIMARY_DEGREE_CODE
    END PRIMARY_DEGREE_CODE,
    sd.primary_major_1_CIPC_CODE,
    dm.CITIZENSHIP_CODE,
    dm.CITIZENSHIP_DESC,
    dm.PCOL_GPA_TRANSFERRED_1 trans_gpa,
    dm.PCOL_DESC_1,
    sd.PRIMARY_MAJOR_1,
    sd.PRIMARY_PROGRAM,
    sd.PRIMARY_CONC_1
  FROM td_student_data sd
  inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
  WHERE registered_ind      = 'Y'
  AND ia_student_type_code IN ('N')
--  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '201110'
    --/******************************************************************************
    --Anamalous student records removed Start
    --******************************************************************************/
  AND (sd.sd_pidm     = 90306
  AND sd.sd_term_code = '201110')
    --
  );
desc um_demographic;