CREATE OR REPLACE package zdwgrdr as 

procedure p_load_predictive_enroll;

end zdwgrdr;
/


CREATE OR REPLACE package body zdwgrdr as

--grant select on stvdegc_ext to grmgr;
--grant execute on f_distance_by_zips to grmgr;

  procedure p_load_predictive_enroll as

  cursor pe_cur is 
  select
  row_number() over(order by td_admissions_applicant.term_code_entry desc, td_admissions_applicant.appl_date) row_number,
  td_admissions_applicant.ad_pidm,
  td_demographic.umid,
  td_admissions_applicant.term_code_entry,
  (select 
   stvterm.stvterm_fa_proc_yr
   from aimsmgr.stvterm_ext stvterm
   where stvterm.stvterm_code = td_admissions_applicant.term_code_entry) aidy_code,
  td_admissions_applicant.appl_no,
  td_admissions_applicant.appl_date,
  td_admissions_applicant.apdc_code_1,
  (select min(ever_admitted_date)
   from (
      select
      td.ad_pidm,
      td.appl_no,
      td.apdc_date_1 ever_admitted_date
      from td_admissions_applicant td
      where td.apdc_code_1 in ('AD', 'A2')
      union
      select
      td.ad_pidm,
      td.appl_no,
      td.apdc_date_2
      from td_admissions_applicant td
      where td.apdc_code_2 in ('AD', 'A2')
      union
      select
      td.ad_pidm,
      td.appl_no,
      td.apdc_date_3
      from td_admissions_applicant td
      where td.apdc_code_3 in ('AD', 'A2')
      union
      select
      td.ad_pidm,
      td.appl_no,
      td.apdc_date_4
      from td_admissions_applicant td
      where td.apdc_code_4 in ('AD', 'A2')
      union
      select
      td.ad_pidm,
      td.appl_no,
      td.apdc_date_5
      from td_admissions_applicant td
      where td.apdc_code_5 in ('AD', 'A2')
    ) td_cur
    where td_cur.ad_pidm = td_admissions_applicant.ad_pidm
    and td_cur.appl_no = td_admissions_applicant.appl_no
  ) ever_admitted_date,
  
  td_admissions_applicant.apdc_date_1,
  
  td_admissions_applicant.admt_code,
  td_admissions_applicant.styp_code,

  td_admissions_applicant.primary_program,
  td_admissions_applicant.primary_major_1,
  td_admissions_applicant.primary_conc_1,

  case
    when td_admissions_applicant.primary_conc_1 is not null then td_admissions_applicant.primary_program || ':' || td_admissions_applicant.primary_major_1 || ':' || td_admissions_applicant.primary_conc_1
    else td_admissions_applicant.primary_program || ':' || td_admissions_applicant.primary_major_1
  end gr_program,
  
--  td_admissions_applicant.primary_college_code,
  td_demographic.pcol_code_1,
  td_demographic.pcol_desc_1,  
  td_admissions_applicant.resd_code,
  td_demographic.a1_state_code,
  td_demographic.a1_zip,
  td_demographic.a1_county_desc,
  td_demographic.a1_nation_desc,
  td_demographic.pr_nation_desc,
  td_demographic.nation_citizen_desc,
  td_demographic.gender,
  td_demographic.report_ethnicity,
  td_demographic.intl_ind,
  td_demographic.veteran_ind,
  
  -- used for debugging
  --td_demographic.pcol_code_1 td_pcol_code_1,
  --td_demographic.pcol_desc_1 td_pcol_desc_1,
  --td_demographic.pcol_degc_code_1 td_degc_code_1,
  primary_college_gpa.sordegr_gpa_transferred,
  
  td_admissions_applicant.enroll_deposit_ind,
  td_demographic.age,
  case
    when td_demographic.a1_zip is not null and 
         nvl(td_demographic.intl_ind, 'N') != 'Y' then 
  --       nvl(td_demographic.intl_ind, 'N') != 'Y' and
  --       nvl(substr(td_demographic.a1_zip, 1, 1), 'X') != '0' then 
         round(aimsmgr.f_distance_by_zips('48502', substr(td_demographic.a1_zip, 1, 5)), 2) 
    else null
  end miles_from_48502,
  
  highest_sordegr.sordegr_degc_code,
  highest_sordegr.stvdegc_acat_code,
  
  td_test_score.gre_rev_gen_verbal,
  td_test_score.gre_rev_gen_verbal_percentile,
  td_test_score.gre_rev_gen_quantitative,
  td_test_score.gre_rev_gen_quant_percentile,
  td_test_score.gre_rev_gen_writing,
  td_test_score.gre_rev_gen_writing_percentile,
  td_test_score.gmat_total_score,
  td_test_score.gmat_total_percentile,
  td_test_score.gmat_verbal_score,
  td_test_score.gmat_verbal_percentile,
  td_test_score.gmat_quantitative_score,
  td_test_score.gmat_quantitative_percentile,
  td_test_score.gmat_writing_score,
  td_test_score.gmat_writing_percentile,
  td_test_score.ielts_overall,
  td_test_score.toefl_ibt_total toefl,
  
  case
    when (select count(*)
          from td_student_data
          where td_student_data.sd_pidm = td_admissions_applicant.ad_pidm
          and ((td_student_data.primary_program = td_admissions_applicant.primary_program and td_student_data.primary_major_1 = td_admissions_applicant.primary_major_1) or
               (td_student_data.second_program = td_admissions_applicant.primary_program and td_student_data.second_major_1 = td_admissions_applicant.primary_major_1))
          and td_student_data.registered_ind = 'Y'
          and td_student_data.sd_term_code = td_admissions_applicant.term_code_entry) > 0 then 'Y'
    else 'N'
  end registered_ind_appl_term,
  
  case
    when (select count(*)
          from td_student_data
          where td_student_data.sd_pidm = td_admissions_applicant.ad_pidm
          and ((td_student_data.primary_program = td_admissions_applicant.primary_program and td_student_data.primary_major_1 = td_admissions_applicant.primary_major_1) or
               (td_student_data.second_program = td_admissions_applicant.primary_program and td_student_data.second_major_1 = td_admissions_applicant.primary_major_1))
          and td_student_data.registered_ind = 'Y'
          and td_student_data.sd_term_code >= to_char(to_number(td_admissions_applicant.term_code_entry) + 100)) > 0 then 'Y'
    else 'N'
  end registered_ind_one_year_later
  
  from aimsmgr.td_admissions_applicant
  inner join td_demographic on td_demographic.dm_pidm = td_admissions_applicant.ad_pidm
                            and td_demographic.td_term_code = td_admissions_applicant.term_code_entry
  inner join td_test_score on td_test_score.td_term_code = td_admissions_applicant.term_code_entry
                           and td_test_score.pidm = td_admissions_applicant.ad_pidm
  left outer join(
    select 
    sordegr.sordegr_pidm,
    sordegr.sordegr_degc_code,
    stvdegc.stvdegc_acat_code,
    row_number() over(partition by sordegr.sordegr_pidm order by stvdegc.stvdegc_acat_code desc) order_num
    from aimsmgr.sordegr_ext sordegr
    inner join aimsmgr.stvdegc_ext stvdegc on stvdegc.stvdegc_code = sordegr.sordegr_degc_code 
    where sordegr.sordegr_degc_code != '000000'
  )highest_sordegr on highest_sordegr.sordegr_pidm = td_admissions_applicant.ad_pidm
                   and highest_sordegr.order_num = 1
  left outer join(
    select
    sordegr.sordegr_pidm,
    sordegr.sordegr_sbgi_code,
    sordegr.sordegr_degc_code,
    sordegr.sordegr_gpa_transferred,
    row_number() over(partition by sordegr.sordegr_pidm, sordegr.sordegr_sbgi_code, sordegr.sordegr_degc_code order by sordegr.sordegr_degc_code ) order_num
    from aimsmgr.sordegr_ext sordegr
  )primary_college_gpa on primary_college_gpa.sordegr_degc_code = td_demographic.pcol_degc_code_1
                       and primary_college_gpa.sordegr_sbgi_code = td_demographic.pcol_code_1
                       and primary_college_gpa.sordegr_pidm = td_demographic.dm_pidm
                       and primary_college_gpa.order_num = 1
  
  where td_admissions_applicant.term_code_entry >= '201430'
  and td_admissions_applicant.term_code_entry <= '201720'
  and td_admissions_applicant.primary_level_code not like 'U%'
  and td_admissions_applicant.primary_level_code not like 'N%'
  and (td_admissions_applicant.apdc_code_1 in ('AD', 'A2') or
       td_admissions_applicant.apdc_code_2 in ('AD', 'A2') or
       td_admissions_applicant.apdc_code_3 in ('AD', 'A2') or
       td_admissions_applicant.apdc_code_4 in ('AD', 'A2') or
       td_admissions_applicant.apdc_code_5 in ('AD', 'A2'))
  
--  and td_admissions_applicant.ad_pidm = 149453
  --and td_admissions_applicant.ad_pidm = 171990
  order by td_admissions_applicant.term_code_entry desc
  ;
  
  type pe_array is table of pe_cur%rowtype index by binary_integer;
  pe_rec pe_array;

  begin

-- drop table predictive_enrollment;
  execute immediate('truncate table predictive_enrollment');

  open pe_cur;
  fetch pe_cur bulk collect into pe_rec;
  close pe_cur;
  
  for i in 1..pe_rec.count
  loop
    insert into predictive_enrollment(
    predictive_enrollment.row_number,
    predictive_enrollment.ad_pidm,
    predictive_enrollment.umid,
    predictive_enrollment.hash_id,
    predictive_enrollment.term_code_entry,
    predictive_enrollment.aidy_code,
    predictive_enrollment.appl_no,
    predictive_enrollment.appl_date,
    predictive_enrollment.apdc_code_1,
    predictive_enrollment.ever_admitted_date,
    predictive_enrollment.apdc_date_1,
    predictive_enrollment.admt_code,
    predictive_enrollment.styp_code,
    predictive_enrollment.primary_program,
    predictive_enrollment.primary_major_1,
    predictive_enrollment.primary_conc_1,
    predictive_enrollment.gr_program,
    predictive_enrollment.pcol_code_1,
    predictive_enrollment.pcol_desc_1,  
    predictive_enrollment.resd_code,
    predictive_enrollment.a1_state_code,
    predictive_enrollment.a1_zip,
    predictive_enrollment.a1_county_desc,
    predictive_enrollment.a1_nation_desc,
    predictive_enrollment.pr_nation_desc,
    predictive_enrollment.nation_citizen_desc,
    predictive_enrollment.gender,
    predictive_enrollment.report_ethnicity,
    predictive_enrollment.intl_ind,
    predictive_enrollment.veteran_ind,
    predictive_enrollment.sordegr_gpa_transferred,
    predictive_enrollment.enroll_deposit_ind,
    predictive_enrollment.age,
    predictive_enrollment.miles_from_48502,
    predictive_enrollment.sordegr_degc_code,
    predictive_enrollment.stvdegc_acat_code,
    predictive_enrollment.gre_rev_gen_verbal,
    predictive_enrollment.gre_rev_gen_verbal_percentile,
    predictive_enrollment.gre_rev_gen_quantitative,
    predictive_enrollment.gre_rev_gen_quant_percentile,
    predictive_enrollment.gre_rev_gen_writing,
    predictive_enrollment.gre_rev_gen_writing_percentile,
    predictive_enrollment.gmat_total_score,
    predictive_enrollment.gmat_total_percentile,
    predictive_enrollment.gmat_verbal_score,
    predictive_enrollment.gmat_verbal_percentile,
    predictive_enrollment.gmat_quantitative_score,
    predictive_enrollment.gmat_quantitative_percentile,
    predictive_enrollment.gmat_writing_score,
    predictive_enrollment.gmat_writing_percentile,
    predictive_enrollment.ielts_overall,
    predictive_enrollment.toefl,
    predictive_enrollment.registered_ind_appl_term,
    predictive_enrollment.registered_ind_one_year_later
    )values(
    pe_rec(i).row_number,
    pe_rec(i).ad_pidm,
    pe_rec(i).umid,
    to_char(ora_hash(pe_rec(i).umid, 99998888), '00000000'),
    pe_rec(i).term_code_entry,
    pe_rec(i).aidy_code,
    pe_rec(i).appl_no,
    pe_rec(i).appl_date,
    pe_rec(i).apdc_code_1,
    pe_rec(i).ever_admitted_date,
    pe_rec(i).apdc_date_1,
    pe_rec(i).admt_code,
    pe_rec(i).styp_code,
    pe_rec(i).primary_program,
    pe_rec(i).primary_major_1,
    pe_rec(i).primary_conc_1,
    pe_rec(i).gr_program,
    pe_rec(i).pcol_code_1,
    pe_rec(i).pcol_desc_1,  
    pe_rec(i).resd_code,
    pe_rec(i).a1_state_code,
    pe_rec(i).a1_zip,
    pe_rec(i).a1_county_desc,
    pe_rec(i).a1_nation_desc,
    pe_rec(i).pr_nation_desc,
    pe_rec(i).nation_citizen_desc,
    pe_rec(i).gender,
    pe_rec(i).report_ethnicity,
    pe_rec(i).intl_ind,
    pe_rec(i).veteran_ind,
    pe_rec(i).sordegr_gpa_transferred,
    pe_rec(i).enroll_deposit_ind,
    pe_rec(i).age,
    pe_rec(i).miles_from_48502,
    pe_rec(i).sordegr_degc_code,
    pe_rec(i).stvdegc_acat_code,
    pe_rec(i).gre_rev_gen_verbal,
    pe_rec(i).gre_rev_gen_verbal_percentile,
    pe_rec(i).gre_rev_gen_quantitative,
    pe_rec(i).gre_rev_gen_quant_percentile,
    pe_rec(i).gre_rev_gen_writing,
    pe_rec(i).gre_rev_gen_writing_percentile,
    pe_rec(i).gmat_total_score,
    pe_rec(i).gmat_total_percentile,
    pe_rec(i).gmat_verbal_score,
    pe_rec(i).gmat_verbal_percentile,
    pe_rec(i).gmat_quantitative_score,
    pe_rec(i).gmat_quantitative_percentile,
    pe_rec(i).gmat_writing_score,
    pe_rec(i).gmat_writing_percentile,
    pe_rec(i).ielts_overall,
    pe_rec(i).toefl,
    pe_rec(i).registered_ind_appl_term,
    pe_rec(i).registered_ind_one_year_later
    );
  
    commit;

  end loop;
  

--create table predictive_enrollment as 
  
  commit;

  end p_load_predictive_enroll;

end zdwgrdr;
/
