select distinct
td.primary_major_1,
td.primary_minor_1,
td.primary_conc_1

 FROM
  td_student_data  td
  LEFT OUTER JOIN td_demographic   dm ON td.sd_term_code = dm.td_term_code
                                       AND td.sd_pidm = dm.dm_pidm
  LEFT OUTER JOIN ia_cw_fy_term    fy ON td.sd_term_code = fy.fy_term_code
  LEFT OUTER JOIN um_test_score    ts ON td.sd_pidm = ts.pidm
 WHERE
   td.registered_ind = 'Y'
  AND substr(td.primary_major_1_cipc_code, 1, 2) = '13'
  AND td.primary_major_1 != 'INED'
  AND td.primary_major_1 != 'TESL'
  AND td.primary_major_1 != 'PEDU'
  AND td.primary_major_1 != 'PHST'
  OR ( td.registered_ind = 'Y'
       AND substr(td.primary_major_1_cipc_code, 1, 2) != '13'
       AND td.primary_minor_1_desc LIKE '%TCP' )