--***UPDATE THE FY AT THE END OF THE QUERY***

TRUNCATE TABLE IA_FY_COHORT_FA_BAC;


INSERT INTO IA_FY_COHORT_FA
SELECT * FROM IA_FY_COHORT_FA_BAC;

INSERT INTO IA_FY_COHORT_FA_BAC
SELECT * FROM IA_FY_COHORT_FA;
COMMIT;

SELECT COUNT (*) FROM IA_FY_COHORT_POP_UNDUP_TBL;
SELECT COUNT (*) FROM IA_FY_COHORT_FA;
SELECT COUNT (*) FROM IA_FY_COHORT_FA_BAC;

-- TRUNCATE TABLE IA_FY_COHORT_FA;

INSERT INTO IA_FY_COHORT_FA

SELECT 
FY,
CO_PIDM,
FY_CO_TERM_CODE,
FY_TERM_CODE,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM          = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_TYPE IN ('SCHL','GRNT')
    AND FB.FUND_SOURCE    IN ('INST','OTHR')
    AND FB.FUND_CODE NOT LIKE ('PRIV%')
    AND FB.PAID_AMT >0
    AND PAID_AMT   IS NOT NULL
    )
  ), 'N') INS_GRANT_IND,--INCLUDES GRANTS AND SCHOLORSHIPS
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM          = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_TYPE IN ('SCHL','GRNT')
  AND FB.FUND_SOURCE    IN ('INST','OTHR')
  AND FB.FUND_CODE NOT LIKE ('PRIV%')
  ),0) INS_GRANT_AMT,--INCLUDES GRANTS AND SCHOLORSHIPS
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM     = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_CODE 
    IN ('2FMS','2FMS3','2FMS5','2FMS7','UMFSAW')
    --CHECK MERIT FUND CODES FOR CHANGES IN THE FUTURE
    AND FB.PAID_AMT   >0
    AND PAID_AMT     IS NOT NULL
    )
  ), 'N') FRESHMAN_MERIT_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM     = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_CODE 
  IN ('2FMS','2FMS3','2FMS5','2FMS7','UMFSAW')
  --CHECK MERIT FUND CODES FOR CHANGES IN THE FUTURE
  ),0) FRESHMAN_MERIT_AMT,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM          = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_TYPE IN ('SCHL','GRNT')
    AND FB.FUND_SOURCE    IN ('FDRL','STAT')
      OR (FB.PIDM          = CO.CO_PIDM
        AND FB.FY = CO.FY
        AND FB.FUND_TYPE  = 'SCHL'
        AND FB.FUND_SOURCE     = 'OTHR'
        AND FB.FUND_CODE LIKE ('PRIV%'))
        AND FB.PAID_AMT >0
        AND PAID_AMT   IS NOT NULL
    )
  ), 'N') OUTSIDE_GRANT_IND,--INCLUDES GRANTS AND SCHOLORSHIPS
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM          = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_TYPE IN ('SCHL','GRNT')
  AND FB.FUND_SOURCE    IN ('FDRL','STAT')
    OR (FB.PIDM          = CO.CO_PIDM
        AND FB.FY = CO.FY
        AND FB.FUND_TYPE  = 'SCHL'
        AND FB.FUND_SOURCE     = 'OTHR'
        AND FB.FUND_CODE LIKE ('PRIV%'))
      ),0) OUTSIDE_GRANT_AMT,--INCLUDES GRANTS AND SCHOLORSHIPS
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM    = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_CODE = '1PELL'
    AND FB.PAID_AMT  >0
    AND PAID_AMT    IS NOT NULL
    )
  ), 'N') PELL_GRANT_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM    = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_CODE = '1PELL'
  ),0) PELL_GRANT_AMT,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM    = CO.CO_PIDM
    AND FB.FY        = CO.FY
    AND FB.FUND_CODE = '1SUBLN'
    AND FB.PAID_AMT  >0
    AND PAID_AMT    IS NOT NULL
    )
  ), 'N') FED_SUBSIDIZED_LOAN_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM    = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_CODE = '1SUBLN'
  ),0) FED_SUBSIDIZED_LOAN_AMT,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_TYPE = 'LOAN'
    AND FB.PAID_AMT       >0
    AND PAID_AMT         IS NOT NULL
    )
  ), 'N') LOAN_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM         = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_TYPE = 'LOAN'
  ),0) LOAN_AMT,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_TYPE = 'WORK'
    AND FB.PAID_AMT       >0
    AND PAID_AMT         IS NOT NULL
    )
  ), 'N') WORK_STUDY_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM         = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_TYPE = 'WORK'
  ),0) WORK_STUDY_AMT,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_TYPE = 'GRNT'
    AND FB.PAID_AMT       >0
    AND PAID_AMT         IS NOT NULL
    )
  ), 'N') GRANT_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM         = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_TYPE = 'GRNT'
  ),0) GRANT_AMT,
  NVL(
  (SELECT 'Y'
  FROM DUAL
  WHERE EXISTS
    (SELECT FB.PIDM
    FROM IA_FA_AWARD_BY_TERM FB
    WHERE FB.PIDM         = CO.CO_PIDM
    AND FB.FY = CO.FY
    AND FB.FUND_TYPE = 'SCHL'
    AND FB.PAID_AMT       >0
    AND PAID_AMT         IS NOT NULL
    )
  ), 'N') SCHOLORSHIP_IND,
  NVL(
  (SELECT SUM(OFFER_AMT)
  FROM IA_FA_AWARD_BY_TERM FB
  WHERE FB.PIDM         = CO.CO_PIDM
  AND FB.FY = CO.FY
  AND FB.FUND_TYPE = 'SCHL'
  ),0) SCHOLORSHIP_AMT
FROM IA_FY_COHORT_POP_UNDUP_TBL CO
WHERE CO.FY = '23-24'                                             --UPDATE THIS
ORDER BY 1,2 
;