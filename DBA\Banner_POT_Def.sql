select distinct ptm.sobptrm_ptrm_code, sobptrm_desc
from aimsmgr.sobptrm_ext ptm
where ptm.sobptrm_term_code >= 202010
--inner join aimsmgr.stvterm_ext stv on ptm.stvptrm_code = stv.stvter

;

select *
from aimsmgr.stvterm_ext stvterm

left outer join aimsmgr.sobptrm_ext sobptrm on sobptrm.sobptrm_term_code = stvterm.stvterm_code

where stvterm.stvterm_code <= (select max(sobptrm.sobptrm_term_code) from aimsmgr.sobptrm_ext sobptrm)
and stvterm.stvterm_code >= '202010'

order by
stvterm.stvterm_code,
sobptrm.sobptrm_ptrm_code
;

desc aimsmgr.sobptrm_ext;