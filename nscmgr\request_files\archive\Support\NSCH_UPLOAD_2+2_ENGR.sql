/*******************************************************************************
-- Author:		<PERSON>, University of Michigan-Flint
-- Create date: 5/9/16
-- Description:	Querie to gather Banner data for upload to National Student 
   Clearinhouse to identify students in 2+2 and 3+2 program.
-- Notes:       EXPORT DATA AS TAB DELIMETED TXT WITH NONE ENCLOSURE
-- Known issue(s):	
*******************************************************************************/

--set basic selection criteria here
WITH Data_pull AS
  (SELECT ia_td_student_data.sd_pidm,
    MAX (ia_td_student_data.sd_term_code)AS last_term
  FROM ia_td_student_data
  WHERE sd_term_code  >= 200810
  AND primary_major_1 IN ('MEGN','EGR')
  AND educ_goal       IN ('E2','E3')
  GROUP BY ia_td_student_data.sd_pidm
  ),
  base_sel AS
  ( SELECT DISTINCT ia_td_student_data.first_name,
    ia_td_student_data.MIDDLE_INITIAL AS middle_name,
    ia_td_student_data.last_name,
    ia_td_student_data.name_suffix,
    ia_td_student_data.birthdate,
    ia_td_student_data.sd_term_code AS term_code_entry,
    ia_td_student_data.sd_pidm      AS dm_pidm
  FROM data_pull
  INNER JOIN ia_td_student_data
  ON ia_td_student_data.sd_pidm       = data_pull.sd_pidm
  AND ia_td_student_data.sd_term_code = data_pull.last_term
  ),
--End basic selection criteria here

pop_sel as (
  --This section creates a header file.
  select
  1 order_num,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  to_char(sysdate, 'YYYYMMDD') "E",
  'SE' "F",
  'I' "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from dual
  
  union
  --This section pulls the student records for the payload.
  select
  2 order_num,
  'D1' "A",
  null "B",
  substr(first_name,1,20) "C",
  substr(middle_name, 1 ,1) "D",
  substr(last_name,1,20) "E",
  substr(name_suffix,1,5) "F",
  to_char(birthdate, 'YYYYMMDD') "G",
  to_char(to_number(SUBSTR(term_code_entry,1,4))-1 ||'0901') "H", 
  null "I",
  '002327' "J",
  '00' "K",
  to_char(dm_pidm) "L"
  from base_sel
      
  union
    --This is to count the number of records and append a trailer record
  select
  3 order_num,
  'T1' "A",
  to_char(count(base_sel.dm_pidm)+2) "B",
  null "C",
  null "D",
  null "E",
  null "F",
  null "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from base_sel
  
  order by order_num
)

select 
pop_sel.A,
pop_sel.B,
pop_sel.C,
pop_sel.D,
pop_sel.E,
pop_sel.F,
pop_sel.G,
pop_sel.H,
pop_sel.I,
pop_sel.J,
pop_sel.K,
pop_sel.L
from pop_sel
;
