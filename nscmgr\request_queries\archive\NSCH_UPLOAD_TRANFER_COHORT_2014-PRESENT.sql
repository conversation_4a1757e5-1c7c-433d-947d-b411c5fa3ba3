/*
This query is designed to pull the Transfer cohort and format the file for upload to
the NSCH.

The rules to be in the cohort are:
  Student is a full time, registered, Transfer, UG in the FAll term
  or the student is a full time or part time, registered, Transfer, UG in the prior Summer term 
  and the student returned in the Fall term as a full time, registered, continuing, UG, in Fall term
  
This must be used between 2014 to present becaue Transfer students starting in summer starting
fall 2014 were rolled to Fall as ia_student_type_code = 'T'
*/

with pop_sel as(
  --This section creates a header file.
  select
  1 order_num,
  'H1' "A",
  '002327' "B",
  '00' "C",
  'UNIVERSITY OF MICHIGAN FLINT' "D",
  to_char(sysdate, 'YYYYMMDD') "E",
  'CO' "F",
  'I' "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from dual
  
  union
  --This section pulls the student records for the payload.
  select
  2 order_num,
  'D1' "A",
  demo_join.ssn "B",
  demo_join.first_name "C",
  substr(demo_join.middle_name, 1 ,1) "D",
  demo_join.last_name "E",
  demo_join.name_suffix "F",
  to_char(demo_join.birthdate, 'YYYYMMDD') "G",
  '20090901' "H",  --Instert Cohort Term YYYYMMDD
  '' "I",
  '002327' "J",
  '00' "K",
  demo_join.dm_pidm "L"
  
  from td_student_data
  inner join(
    select
    *
    from um_demographic
   )demo_join on demo_join.dm_pidm = td_student_data.sd_pidm
  where sd_term_code = '201510'  --Instert Cohort term ie 201010
  and td_student_data.ia_student_type_code = 'T'
  and td_student_data.primary_level_code = 'UG'
  and td_student_data.registered_ind = 'Y'
  and td_student_data.full_part_time_ind_umf = 'F'
    
  union
    --This is to count the number of records and append a trailer record
  select
  3 order_num,
  'T1' "A",
  to_char(count(demo_join.dm_pidm)) "B",
  null "C",
  null "D",
  null "E",
  null "F",
  null "G",
  null "H",
  null "I",
  null "J",
  null "K",
  null "L"
  from td_student_data
  inner join(
    select
    dm_pidm
    from um_demographic
  )demo_join on demo_join.dm_pidm = td_student_data.sd_pidm
  where sd_term_code = '201510'  --Instert Cohort term ie 201010
  and td_student_data.ia_student_type_code = 'T'
  and td_student_data.primary_level_code = 'UG'
  and td_student_data.registered_ind = 'Y'
  and td_student_data.full_part_time_ind_umf = 'F'
  order by order_num
)
select 
pop_sel.A,
pop_sel.B,
pop_sel.C,
pop_sel.D,
pop_sel.E,
pop_sel.F,
pop_sel.G,
pop_sel.H,
pop_sel.I,
pop_sel.J,
pop_sel.K,
pop_sel.L
from pop_sel