
--Transfer students removed from Cohort
with dp1 as (
select
sd_pidm pidm,
sd_term_code term
from ia_td_student_data
where registered_ind = 'Y'
and sd_term_code = 201810
and ia_student_type_code = 'T'

minus

select
co_pidm pidm,
co_term_code_key term
from ia_cohort_pop_undup
where co_term_code_key = 201810
and co_ia_student_type_code = 'T'
)
select dp1.*,sd.primary_level_code from dp1
left join ia_td_student_data sd on dp1.pidm = sd.sd_pidm
and dp1.term = sd.sd_term_code

;
--Full Time Part Time Transfers by primary level 
select
primary_level_code,
full_part_time_ind_umf,
count (*)
from ia_td_student_data
where registered_ind = 'Y'
and sd_term_code = 201910
and ia_student_type_code = 'T'
group by
primary_level_code,
full_part_time_ind_umf
--minus
--
--select
--co_pidm pidm,
--co_term_code_key term,
--co_ia_student_type_code stype,
--
--from ia_cohort_pop_undup
--where co_term_code_key = 201910
--and co_ia_student_type_code = 'T'
;
--Transfers starting in summer
with dp1 as (
select
td.sd_pidm pidm,
td.sd_term_code term,
td.ia_student_type_code stype,
td.primary_level_code,
td.full_part_time_ind_umf
from ia_td_student_data td
where td.registered_ind = 'Y'
and td.sd_term_code = 201840
and td.ia_student_type_code = 'T'
)
select count (*) from dp1 

;
--Transfers starting in summer and continued to Fall
with dp1 as (
select
td.sd_pidm pidm,
td.sd_term_code term,
td.ia_student_type_code stype,
td.primary_level_code,
td.full_part_time_ind_umf
from ia_td_student_data td
where td.registered_ind = 'Y'
and td.sd_term_code = 201910
and td.ia_student_type_code = 'T'
)
select count (*) from dp1 
inner join ia_td_student_data sd 
on dp1.pidm = sd.sd_pidm
where sd.registered_ind = 'Y'
and sd.sd_term_code = 201840
and sd.ia_student_type_code = 'T'
;
--Trasnfers with prior TD record
with dp1 as (
select
sd_pidm,
umid,
primary_level_code,
sd_term_code,
ia_student_type_code
from td_student_data
where registered_ind = 'Y'
and ia_student_type_code in ('T')
and report_level_code = 'UG'
and sd_term_code = 201910 -- change to audit term
)

select
dp1.umid,
sd.sd_pidm pidm,
nvl(sd.INTL_IND,'N')intl_ind,
dp1.ia_student_type_code current_stype,
dp1.primary_level_code current_level,
dp1.sd_term_code current_term_code,
sd.ia_student_type_code first_term_stype,
sd.primary_level_code first_term_level,
total_credit_hours_umf first_term_CH,
min(sd.sd_term_code) first_term
from dp1 left join ia_td_student_data sd on sd.sd_pidm = dp1.sd_pidm
where sd.sd_term_code < dp1.sd_term_code 
and sd.ia_student_type_code in ('F','T')
and sd.registered_ind = 'Y'
group by 
dp1.umid,
sd.sd_pidm,
nvl(sd.INTL_IND,'N'),
dp1.ia_student_type_code,
dp1.primary_level_code,
dp1.sd_term_code,
sd.ia_student_type_code,
sd.primary_level_code,
total_credit_hours_umf 

minus

select 
dp1.umid,
sd.sd_pidm pidm,
nvl(sd.INTL_IND,'N')intl_ind,
dp1.ia_student_type_code current_stype,
dp1.primary_level_code current_level,
dp1.sd_term_code current_term_code,
sd.ia_student_type_code first_term_stype,
sd.primary_level_code first_term_level,
total_credit_hours_umf first_term_CH,
min(sd.sd_term_code) first_term
from dp1 left join ia_td_student_data sd on sd.sd_pidm = dp1.sd_pidm
where 
--sd.sd_term_code < dp1.sd_term_code
--and 
sd.sd_term_code = (dp1.sd_term_code - 70)-- change to audit term
and sd.ia_student_type_code = dp1.ia_student_type_code
and sd.registered_ind = 'Y'
group by 
dp1.umid,
sd.sd_pidm,
nvl(sd.INTL_IND,'N'),
dp1.ia_student_type_code,
dp1.primary_level_code,
dp1.sd_term_code,
sd.ia_student_type_code,
sd.primary_level_code,
total_credit_hours_umf 
;