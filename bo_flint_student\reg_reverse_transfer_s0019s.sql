--------------------------------------------------------
--  DDL for View REG_REVERSE_TRANSFER_S0019S
--------------------------------------------------------

--  CREATE OR REPLACE FORCE VIEW "AIMSMGR"."REG_REVERSE_TRANSFER_S0019S" ("STVSBGI_DESC", "LAST_NAME", "FIRST_NAME", "UMID", "TRANSFER_HOURS", "A1_STREET_LINE1", "A1_CITY", "A1_STATE_CODE", "A1_AREA_CODE", "A1_PHONE_NUMBER", "CA_EMAIL", "TVRAUTH_TYPE_CODE", "TVVAUTH_DESC") AS 
  with sbgi_cur as(
    select 
    shrtrit.shrtrit_pidm,
    stvsbgi.stvsbgi_desc,
    sum(shrtgpa.shrtgpa_hours_earned) shrtgpa_hours_earned
    from shrtrit shrtrit
    inner join shrtram shrtram on shrtram.shrtram_pidm = shrtrit.shrtrit_pidm
                                   and shrtram.shrtram_trit_seq_no = shrtrit.shrtrit_seq_no
    inner join shrtgpa shrtgpa on shrtgpa.shrtgpa_pidm = shrtrit.shrtrit_pidm
                                   and shrtgpa.shrtgpa_trit_seq_no = shrtram.shrtram_trit_seq_no
                                   and shrtgpa.shrtgpa_tram_seq_no = shrtram.shrtram_seq_no
    inner join stvsbgi on stvsbgi.stvsbgi_code = shrtrit.shrtrit_sbgi_code
    where shrtrit.shrtrit_sbgi_code in ('001225', '001628', '001816', '001378')

    group by
    shrtrit.shrtrit_pidm,
    stvsbgi.stvsbgi_desc
)
select
sbgi_cur.stvsbgi_desc,
um_demographic.last_name,
um_demographic.first_name,
um_demographic.umid,
sbgi_cur.shrtgpa_hours_earned transfer_hours,
um_demographic.a1_street_line1,
um_demographic.a1_city,
um_demographic.a1_state_code,
um_demographic.a1_area_code,

um_demographic.a1_phone_number,
um_demographic.ca_email,

--listagg(tvrauth.tvrauth_type_code, ', ') within group (order by tvrauth.tvrauth_type_code) tvrauth_type_code
tvrauth.tvrauth_type_code,
(select tvvauth.tvvauth_desc from tvvauth where tvvauth.tvvauth_type_code = tvrauth.tvrauth_type_code) tvvauth_desc
from um_student_data
inner join um_demographic on um_demographic.dm_pidm = um_student_data.sd_pidm
inner join sbgi_cur on sbgi_cur.shrtrit_pidm = um_student_data.sd_pidm

left outer join tvrauth on tvrauth.tvrauth_pidm = um_student_data.sd_pidm
                       and tvrauth.tvrauth_start_date <= trunc(sysdate)
                       and tvrauth.tvrauth_end_date >= trunc(sysdate)
                       and tvrauth.tvrauth_status = 'A'
                       and tvrauth.tvrauth_type_code = 'RT'
where um_student_data.student_status_code = 'AS'
and um_student_data.inst_hours_earned >= 24
and um_student_data.transfer_hours_earned >= 45
and sbgi_cur.shrtgpa_hours_earned >= 45
and um_student_data.primary_level_code like 'U%'
and um_student_data.sd_term_code = (select um_current_term.current_term from um_current_term)

--and um_demographic.umid = '83774174'

--group by
--sbgi_cur.stvsbgi_desc,
--um_demographic.last_name,
--um_demographic.first_name,
--um_demographic.umid,
--sbgi_cur.shrtgpa_hours_earned,
--um_demographic.a1_street_line1,
--um_demographic.a1_city,
--um_demographic.a1_state_code,
--um_demographic.a1_area_code,
--
--um_demographic.a1_phone_number,
--um_demographic.ca_email

order by 
stvsbgi_desc,
um_demographic.last_name,
um_demographic.first_name
;
