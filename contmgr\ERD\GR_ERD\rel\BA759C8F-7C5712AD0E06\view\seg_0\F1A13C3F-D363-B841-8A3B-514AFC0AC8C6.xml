<?xml version = '1.0' encoding = 'UTF-8'?>
<TableView class="oracle.dbtools.crest.model.design.relational.TableView" directorySegmentName="seg_0" id="F1A13C3F-D363-B841-8A3B-514AFC0AC8C6" schemaObject="9DE458CD-949D-3DB7-13CC-F0BD30E45418" name="CO_GR_POP">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CONTMGR</sourceObjSchema>
<sourceObjName>CO_GR_POP</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<userDefined>true</userDefined>
<userDefinedSQL><![CDATA[CREATE OR REPLACE VIEW CO_GR_POP
AS 
SELECT sd.sd_pidm AS CO_GR_PIDM,
    dm.UMID,
    sd.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
--    to_date('01-SEP-' ||(to_number(SUBSTR(sd.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
(select stvterm.STVTERM_START_DATE from aimsmgr.stvterm_ext stvterm 
where sd.sd_term_code = STVTERM.STVTERM_CODE ) START_DATE,
    sd.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
    sd.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
    dm.report_ethnicity AS CO_GR_ETHNICITY,
    dm.gender           AS CO_GR_GENDER,
    sd.report_level_code,
    sd.primary_level_code,
    CASE
      WHEN (sd.PRIMARY_DEGREE_CODE = 'MLS'
      AND sd.sd_term_code         IN ('201310'))
      THEN 'MA'
      ELSE sd.PRIMARY_DEGREE_CODE
    END PRIMARY_DEGREE_CODE,
    sd.primary_major_1_CIPC_CODE,
    dm.CITIZENSHIP_CODE,
    dm.CITIZENSHIP_DESC,
    dm.PCOL_GPA_TRANSFERRED_1 trans_gpa,
    dm.PCOL_DESC_1,
    sd.PRIMARY_MAJOR_1,
    sd.PRIMARY_PROGRAM,
    sd.PRIMARY_CONC_1
  FROM td_student_data sd
  inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
  WHERE registered_ind      = 'Y'
  AND ia_student_type_code IN ('N')
--  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '200740'
  
  
  
  /* Students record anommolies removed*/
  MINUS
SELECT sd.sd_pidm AS CO_GR_PIDM,
    dm.UMID,
    sd.sd_term_code CO_GR_TERM_CODE_KEY, -- fall term code
--    to_date('01-SEP-' ||(to_number(SUBSTR(sd.sd_term_code,3,2))-1),'DD-MON-YY') start_date,
(select stvterm.STVTERM_START_DATE from aimsmgr.stvterm_ext stvterm 
where sd.sd_term_code = STVTERM.STVTERM_CODE ) START_DATE,
    sd.ia_student_type_code CO_GR_IA_STUDENT_TYPE_CODE,
    sd.full_part_time_ind_umf CO_GR_FULL_PART_IND_UMF,
    dm.report_ethnicity AS CO_GR_ETHNICITY,
    dm.gender           AS CO_GR_GENDER,
    sd.report_level_code,
    sd.primary_level_code,
    CASE
      WHEN (sd.PRIMARY_DEGREE_CODE = 'MLS'
      AND sd.sd_term_code         IN ('201310'))
      THEN 'MA'
      ELSE sd.PRIMARY_DEGREE_CODE
    END PRIMARY_DEGREE_CODE,
    sd.primary_major_1_CIPC_CODE,
    dm.CITIZENSHIP_CODE,
    dm.CITIZENSHIP_DESC,
    dm.PCOL_GPA_TRANSFERRED_1 trans_gpa,
    dm.PCOL_DESC_1,
    sd.PRIMARY_MAJOR_1,
    sd.PRIMARY_PROGRAM,
    sd.PRIMARY_CONC_1
  FROM td_student_data sd
  inner join um_demographic dm on sd.sd_pidm = dm.dm_pidm
  WHERE registered_ind      = 'Y'
  AND ia_student_type_code IN ('N')
--  AND sd_term_code LIKE '%10'
  AND sd_term_code >= '200740'
    --/******************************************************************************
    --Anamalous student records removed Start
    --******************************************************************************/
  AND (sd.sd_pidm     = 90306
  AND sd.sd_term_code = '201110')
    --]]></userDefinedSQL>
<parsed>true</parsed>
<viewElements>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CO_GR_PIDM" id="3092AA80-BC30-8DC8-8E1B-40EDCD8FE52F">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CO_GR_PIDM</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CO_GR_PIDM</alias>
<dataType>NUMBER (8)</dataType>
<headerAlias>CO_GR_PIDM</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="UMID" id="F5CC8446-DDC2-CA79-7572-7647F91C2E01">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>UMID</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>UMID</alias>
<dataType>VARCHAR2 (9 BYTE)</dataType>
<headerAlias>UMID</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CO_GR_TERM_CODE_KEY" id="39F73833-D9F6-631F-C127-52FDB292D570">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CO_GR_TERM_CODE_KEY</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CO_GR_TERM_CODE_KEY</alias>
<dataType>VARCHAR2 (6 BYTE)</dataType>
<headerAlias>CO_GR_TERM_CODE_KEY</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="START_DATE" id="8F029523-24A2-42DC-6531-5905C9F2EB7E">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>START_DATE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>START_DATE</alias>
<dataType>DATE</dataType>
<headerAlias>START_DATE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CO_GR_IA_STUDENT_TYPE_CODE" id="1664F7DE-BF0D-E504-A983-0A6E284D2DA7">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CO_GR_IA_STUDENT_TYPE_CODE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CO_GR_IA_STUDENT_TYPE_CODE</alias>
<dataType>VARCHAR2 (1 BYTE)</dataType>
<headerAlias>CO_GR_IA_STUDENT_TYPE_CODE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CO_GR_FULL_PART_IND_UMF" id="0F536337-10E5-68D0-E988-8FEDE5B737DF">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CO_GR_FULL_PART_IND_UMF</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CO_GR_FULL_PART_IND_UMF</alias>
<dataType>VARCHAR2 (1 BYTE)</dataType>
<headerAlias>CO_GR_FULL_PART_IND_UMF</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CO_GR_ETHNICITY" id="84ADCB2E-BBB9-22DB-B6C4-EFAF7A0F382B">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CO_GR_ETHNICITY</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CO_GR_ETHNICITY</alias>
<dataType>VARCHAR2 (96 BYTE)</dataType>
<headerAlias>CO_GR_ETHNICITY</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CO_GR_GENDER" id="971B1C4D-EC53-DF9E-39EA-BDC9A4E8A43A">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CO_GR_GENDER</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CO_GR_GENDER</alias>
<dataType>VARCHAR2 (1 BYTE)</dataType>
<headerAlias>CO_GR_GENDER</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="REPORT_LEVEL_CODE" id="CB8FF044-C3DB-D560-F36C-33FDA631D621">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>REPORT_LEVEL_CODE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>REPORT_LEVEL_CODE</alias>
<dataType>VARCHAR2 (2 BYTE)</dataType>
<headerAlias>REPORT_LEVEL_CODE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PRIMARY_LEVEL_CODE" id="3D307644-5FE2-31B8-2225-F5D28357C302">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PRIMARY_LEVEL_CODE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PRIMARY_LEVEL_CODE</alias>
<dataType>VARCHAR2 (2 BYTE)</dataType>
<headerAlias>PRIMARY_LEVEL_CODE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PRIMARY_DEGREE_CODE" id="0F178E46-34EC-4973-E699-58E0751731BB">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PRIMARY_DEGREE_CODE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PRIMARY_DEGREE_CODE</alias>
<dataType>VARCHAR2 (6 BYTE)</dataType>
<headerAlias>PRIMARY_DEGREE_CODE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PRIMARY_MAJOR_1_CIPC_CODE" id="5DD8E5C6-8C45-6453-1717-0E7D6B61BB34">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PRIMARY_MAJOR_1_CIPC_CODE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PRIMARY_MAJOR_1_CIPC_CODE</alias>
<dataType>VARCHAR2 (6 BYTE)</dataType>
<headerAlias>PRIMARY_MAJOR_1_CIPC_CODE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CITIZENSHIP_CODE" id="B991DBA5-47B4-F280-B25F-70D90E0AC2EB">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CITIZENSHIP_CODE</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CITIZENSHIP_CODE</alias>
<dataType>VARCHAR2 (2 BYTE)</dataType>
<headerAlias>CITIZENSHIP_CODE</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="CITIZENSHIP_DESC" id="816617E3-C7A1-5121-0A7A-0936202E1AA0">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>CITIZENSHIP_DESC</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>CITIZENSHIP_DESC</alias>
<dataType>VARCHAR2 (30 BYTE)</dataType>
<headerAlias>CITIZENSHIP_DESC</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="TRANS_GPA" id="E5ADA568-1814-BFD6-7B90-7CEA75E50A66">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>TRANS_GPA</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>TRANS_GPA</alias>
<dataType>NUMBER (23, 9)</dataType>
<headerAlias>TRANS_GPA</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PCOL_DESC_1" id="5599BAB4-83CB-2AF6-B46F-B1720C926F7F">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PCOL_DESC_1</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PCOL_DESC_1</alias>
<dataType>VARCHAR2 (30 BYTE)</dataType>
<headerAlias>PCOL_DESC_1</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PRIMARY_MAJOR_1" id="0040B426-E951-21E2-36C7-34F1F9D5089F">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PRIMARY_MAJOR_1</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PRIMARY_MAJOR_1</alias>
<dataType>VARCHAR2 (4 BYTE)</dataType>
<headerAlias>PRIMARY_MAJOR_1</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PRIMARY_PROGRAM" id="68A75F93-D8DC-D0C3-5DB7-7F0EFC5A8D2C">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PRIMARY_PROGRAM</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PRIMARY_PROGRAM</alias>
<dataType>VARCHAR2 (12 BYTE)</dataType>
<headerAlias>PRIMARY_PROGRAM</headerAlias>
<reference>false</reference>
</viewElement>
<viewElement class="oracle.dbtools.crest.model.design.relational.ColumnView" name="PRIMARY_CONC_1" id="A24CD759-FD8E-D8A1-CAFE-10E153866785">
<sourceConnName>2_IAPROD_contmgr</sourceConnName>
<sourceObjSchema>CO_GR_POP</sourceObjSchema>
<sourceObjName>PRIMARY_CONC_1</sourceObjName>
<createdBy>dgetty</createdBy>
<createdTime>2023-07-10 18:55:19 UTC</createdTime>
<ownerDesignName>GR_ERD</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<alias>PRIMARY_CONC_1</alias>
<dataType>VARCHAR2 (4 BYTE)</dataType>
<headerAlias>PRIMARY_CONC_1</headerAlias>
<reference>false</reference>
</viewElement>
</viewElements>
</TableView>