/*******************************************************************************
This query identifies and transforms the level code, major, and program in the 
first fall term that do not match the second fall term.  Then replaces the first
fall term variables to the second fall term variables.
*******************************************************************************/

--n= 174 OF 2641

create or replace view IA_GR_COHORT_CUR2 AS
select popsel.*
from(

WITH cur1 AS
  (SELECT IA_GR_COHORT_CUR1.*
  FROM IA_GR_COHORT_CUR1
  --WHERE CO_GR_PIDM = '108601'
  ),
  scnd_fall_to_frst_fall AS
  (SELECT CO_GR_PIDM,
    FRST_FALL_TERM_CODE,
    FRST_FALL_REG_IND,
    SCND_FALL_LVL_CODE FRST_FALL_LVL_CODE,
    SCND_FALL_MAJOR FRST_FALL_MAJOR,
    SCND_FALL_PROGRAM FRST_FALL_PROGRAM
  FROM cur1
  WHERE FRST_FALL_REG_IND    = 'Y'
  AND SCND_FALL_TERM_REG_IND = 'Y'
  AND (FRST_FALL_LVL_CODE    <> SCND_FALL_LVL_CODE
  or FRST_FALL_MAJOR <> SCND_FALL_MAJOR
  or FRST_FALL_PROGRAM <> SCND_FALL_PROGRAM )
  )
SELECT 
scnd_fall_to_frst_fall.CO_GR_PIDM,
scnd_fall_to_frst_fall.FRST_FALL_TERM_CODE,
scnd_fall_to_frst_fall.FRST_FALL_REG_IND,
scnd_fall_to_frst_fall.FRST_FALL_LVL_CODE,
scnd_fall_to_frst_fall.FRST_FALL_MAJOR,
scnd_fall_to_frst_fall.FRST_FALL_PROGRAM,
cur1.SCND_FALL_TERM_CODE,
cur1.SCND_FALL_TERM_REG_IND,
cur1.SCND_FALL_LVL_CODE,
cur1.SCND_FALL_MAJOR,
cur1.SCND_FALL_PROGRAM,
cur1.THRD_FALL_CODE,
cur1.THRD_FALL_TERM_REG_IND,
cur1.THRD_FALL_LVL_CODE,
cur1.THRD_FALL_MAJOR,
cur1.THRD_FALL_PROGRAM,
cur1.FRTH_FALL_CODE,
cur1.FRTH_FALL_TERM_REG_IND,
cur1.FRTH_FALL_LVL_CODE,
cur1.FRTH_FALL_MAJOR,
cur1.FRTH_FALL_PROGRAM,
cur1.FFTH_FALL_CODE,
cur1.FFTH_FALL_TERM_REG_IND,
cur1.FFTH_FALL_LVL_CODE,
cur1.FFTH_FALL_MAJOR,
cur1.FFTH_FALL_PROGRAM,
cur1.SXTH_FALL_CODE,
cur1.SXTH_FALL_TERM_REG_IND,
cur1.SXTH_FALL_LVL_CODE,
cur1.SXTH_FALL_MAJOR,
cur1.SXTH_FALL_PROGRAM,
cur1.SVNTH_FALL_CODE,
cur1.SVNTH_FALL_TERM_REG_IND,
cur1.SVNTH_FALL_LVL_CODE,
cur1.SVNTH_FALL_MAJOR,
cur1.SVNTH_FALL_PROGRAM,
cur1.EIGTH_FALL_CODE,
cur1.EIGTH_FALL_TERM_REG_IND,
cur1.EIGTH_FALL_LVL_CODE,
cur1.EIGTH_FALL_MAJOR,
cur1.EIGTH_FALL_PROGRAM
FROM cur1
inner JOIN scnd_fall_to_frst_fall
ON scnd_fall_to_frst_fall.CO_GR_PIDM           = cur1.CO_GR_PIDM
AND scnd_fall_to_frst_fall.FRST_FALL_TERM_CODE = cur1.FRST_FALL_TERM_CODE
)popsel;