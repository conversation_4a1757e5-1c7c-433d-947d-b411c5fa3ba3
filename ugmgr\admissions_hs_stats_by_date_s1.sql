with run_date_cur as(
    select trunc(sysdate - 1) sel_date from dual
),
term_cur as(
    select
    stvterm.stvterm_code,
    stvterm.stvterm_desc,
    row_number() over(order by stvterm.stvterm_code desc) order_num
    from aimsmgr.stvterm_ext stvterm
    where stvterm.stvterm_code <= 202110 --'&TERM_CODE_IN'
),
applied_td_cur as(
    select
    hsch_code,
	count(td_admissions_applicant.ad_pidm) td_counter
	from td_admissions_applicant
	inner join td_demographic on td_demographic.dm_pidm = td_admissions_applicant.ad_pidm
	                          and td_demographic.td_term_code = td_admissions_applicant.term_code_entry
	where td_admissions_applicant.term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
    and primary_level_code like 'U%'
    and intl_ind = 'N'
    and td_admissions_applicant.styp_code = 'F'
    group by hsch_code
),
admitted_td_cur as(
    select
    hsch_code,
	count(td_admissions_applicant.ad_pidm) td_counter
	from td_admissions_applicant
	inner join td_demographic on td_demographic.dm_pidm = td_admissions_applicant.ad_pidm
	                          and td_demographic.td_term_code = td_admissions_applicant.term_code_entry
	where td_admissions_applicant.term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
    and primary_level_code like 'U%'
    and intl_ind = 'N'
    and td_admissions_applicant.styp_code = 'F'
    and inst_accepted_app_any_date_ind = 'Y'
    group by hsch_code
    ),
confirmed_td_cur as (
	select
    hsch_code,
	count(td_admissions_applicant.ad_pidm) td_counter
	from td_admissions_applicant
	inner join td_demographic on td_demographic.dm_pidm = td_admissions_applicant.ad_pidm
	                          and td_demographic.td_term_code = td_admissions_applicant.term_code_entry
	where td_admissions_applicant.term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
    and primary_level_code like 'U%'
    and intl_ind = 'N'
    and td_admissions_applicant.styp_code = 'F'
    and (
        td_admissions_applicant.housing_app_ind = 'Y' or
        td_admissions_applicant.orientation_session_ind = 'Y' or
        td_admissions_applicant.enroll_deposit_ind = 'Y' or
        td_admissions_applicant.confirm_admission_card_ind = 'Y' or
        td_admissions_applicant.reg_ind_with_nonacademic = 'Y'
    )
    group by hsch_code
 ),
enrolled_td_cur as (
    select
    hsch_code,
	count(td_admissions_applicant.ad_pidm) td_counter
	from td_admissions_applicant
	inner join td_demographic on td_demographic.dm_pidm = td_admissions_applicant.ad_pidm
	                          and td_demographic.td_term_code = td_admissions_applicant.term_code_entry
	where td_admissions_applicant.term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
    and primary_level_code like 'U%'
    and intl_ind = 'N'
    and td_admissions_applicant.styp_code = 'F'
    and reg_ind_with_nonacademic = 'Y'
    group by hsch_code
    ),
pop_sel as(
select
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
(select term_cur.stvterm_desc from term_cur where term_cur.stvterm_code = t1.saradap_term_code_entry) term_code_entry_desc,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
'300' stage,
sum(t1.stage_300_counter) current_term,
sum(t2.stage_300_counter) previous_term,
sum(t3.stage_300_counter) previous_term_2,
sum(t4.stage_300_counter) previous_term_3,
nvl(applied_td_cur.td_counter, 0) td_counter
from ugmgr.um_sem_hsch_funnel t1
inner join ugmgr.um_sem_hsch_funnel t2 on t2.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
                            and t2.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 10000, 'YYYYMMDD')
                            and t2.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t3 on t3.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 9)
                            and t3.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 20000, 'YYYYMMDD')
                            and t3.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t4 on t4.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 13)
                            and t4.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 30000, 'YYYYMMDD')
                            and t4.sbgi_code = t1.sbgi_code
left outer join applied_td_cur on applied_td_cur.hsch_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_area on um_sem_hsch_area.sbgi_code = t1.sbgi_code
where t1.run_date = (select run_date_cur.sel_date from run_date_cur)
and t1.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 1)
group by
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
nvl(applied_td_cur.td_counter, 0)
union all
select
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
(select term_cur.stvterm_desc from term_cur where term_cur.stvterm_code = t1.saradap_term_code_entry) term_code_entry_desc,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
'400' stage,
sum(t1.stage_400_counter),
sum(t2.stage_400_counter),
sum(t3.stage_400_counter),
sum(t4.stage_400_counter),
nvl(admitted_td_cur.td_counter, 0) td_counter
from ugmgr.um_sem_hsch_funnel t1
inner join ugmgr.um_sem_hsch_funnel t2 on t2.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
                            and t2.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 10000, 'YYYYMMDD')
                            and t2.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t3 on t3.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 9)
                            and t3.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 20000, 'YYYYMMDD')
                            and t3.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t4 on t4.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 13)
                            and t4.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 30000, 'YYYYMMDD')
                            and t4.sbgi_code = t1.sbgi_code
left outer join admitted_td_cur on admitted_td_cur.hsch_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_area on um_sem_hsch_area.sbgi_code = t1.sbgi_code
where t1.run_date = (select run_date_cur.sel_date from run_date_cur)
and t1.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 1)
group by
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
nvl(admitted_td_cur.td_counter, 0)
union all
select
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
(select term_cur.stvterm_desc from term_cur where term_cur.stvterm_code = t1.saradap_term_code_entry) term_code_entry_desc,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
'500' stage,
sum(t1.stage_500_counter),
sum(t2.stage_500_counter),
sum(t3.stage_500_counter),
sum(t4.stage_500_counter),
nvl(confirmed_td_cur.td_counter, 0) td_counter
from ugmgr.um_sem_hsch_funnel t1
inner join ugmgr.um_sem_hsch_funnel t2 on t2.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
                            and t2.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 10000, 'YYYYMMDD')
                            and t2.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t3 on t3.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 9)
                            and t3.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 20000, 'YYYYMMDD')
                            and t3.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t4 on t4.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 13)
                            and t4.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 30000, 'YYYYMMDD')
                            and t4.sbgi_code = t1.sbgi_code
left outer join confirmed_td_cur on confirmed_td_cur.hsch_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_area on um_sem_hsch_area.sbgi_code = t1.sbgi_code
where t1.run_date = (select run_date_cur.sel_date from run_date_cur)
and t1.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 1)
group by
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
nvl(confirmed_td_cur.td_counter, 0)
union all
select
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
(select term_cur.stvterm_desc from term_cur where term_cur.stvterm_code = t1.saradap_term_code_entry) term_code_entry_desc,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
'600' stage,
sum(t1.stage_600_counter),
sum(t2.stage_600_counter),
sum(t3.stage_600_counter),
sum(t4.stage_600_counter),
nvl(enrolled_td_cur.td_counter, 0) td_counter
from ugmgr.um_sem_hsch_funnel t1
inner join ugmgr.um_sem_hsch_funnel t2 on t2.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 5)
                            and t2.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 10000, 'YYYYMMDD')
                            and t2.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t3 on t3.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 9)
                            and t3.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 20000, 'YYYYMMDD')
                            and t3.sbgi_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_funnel t4 on t4.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 13)
                            and t4.run_date = to_date((to_char(t1.run_date, 'YYYYMMDD')) - 30000, 'YYYYMMDD')
                            and t4.sbgi_code = t1.sbgi_code
left outer join enrolled_td_cur on enrolled_td_cur.hsch_code = t1.sbgi_code
inner join ugmgr.um_sem_hsch_area on um_sem_hsch_area.sbgi_code = t1.sbgi_code
where t1.run_date = (select run_date_cur.sel_date from run_date_cur)
and t1.saradap_term_code_entry = (select term_cur.stvterm_code from term_cur where term_cur.order_num = 1)
group by
t1.run_date,
t1.day_of_term_wkends,
t1.saradap_term_code_entry,
sbgi_desc,
t1.sbgi_code,
um_sem_hsch_area.sem_area,
nvl(enrolled_td_cur.td_counter, 0)
order by
stage, sem_area, sbgi_desc asc
)




select
run_date,
day_of_term_wkends,
saradap_term_code_entry,
term_code_entry_desc,
stage,
sem_area,
sum(current_term) current_term,
sum(previous_term) previous_term,
sum(previous_term_2) previous_term_2,
sum(previous_term_3) previous_term_3,
sum(td_counter) td_counter
from pop_sel
group by
run_date,
day_of_term_wkends,
saradap_term_code_entry,
term_code_entry_desc,
stage,
sem_area
order by sem_area, stage asc
