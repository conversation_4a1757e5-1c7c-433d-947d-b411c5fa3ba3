create or replace view scholarship_dinner_list_win as
select 
FB.FUND_CODE,
FB.FUND_DESC,
FB.PIDM,
FB.AID_YEAR,
FB.TERM_CODE,
stv.stvterm_desc term_desc,
--DM.TERM_DESC,
DM.UMID,
DM.LAST_NAME,
DM.FIRST_NAME,
DM.MIDDLE_INITIAL,
DM.A1_STREET_LINE1,
DM.A1_STREET_LINE2,
DM.A1_CITY,
DM.A1_STATE_CODE state,
DM.A1_ZIP,
DM.A1_AREA_CODE,
DM.A1_PHONE_NUMBER,
DM.CA_EMAIL

from
famgr.FA_AWARD_BY_TERM fb
inner join aimsmgr.stvterm_ext stv on fb.term_code = stv.stvterm_code
inner join um_demographic dm on dm.dm_pidm = fb.pidm 
where
FB.FUND_TYPE = 'SCHL'
AND FB.FUND_CODE not in ('SOMSAS','3GUPCD','STVETS','2GMATS'
,'2MAEMS','2BASCH','1SDSUN','1SDSRT','1SDSMT','1NURAN'
,'1MNPG','1BYRD','1EBYRD','1PDOUG','3MCS','3MIPRO','3MMAP'
,'3PL174','ADMLDR','ADMMRT','ADMREC','ADMSCH','ALLEN','B,LBK'
,'CHNSAS','CISNUR','CTS','CTXTBK','DEANGR','EADMMR','ECHANC'
,'ECTS','EFLALU','EHERIT','EHON,','EISP','EMFEOS','EMFNUR','EMICHS'
,'EMINSC','ENGGBA','EPRSCH','ESCHOL','ESOM','EUMFDS','EUNSCH'
,'FRANF','GENNET','GRADMS','GRDEFE','HERITG','HLTHPR','HONOFF','HON,2'
,'HON,S','HONSCD','INTNUR','ISP','ISPEST','MAC/FL','MAC/PR','MCUMCP'
,'MFEOS','MFEOS2','MICHSC','MINLAP','MINLEA','MINSCH','MINURS','MOTTUM' 
,'MUDEPT','MUSASF','MUSENR','MUSTIP','NMERIT','NRTHUP','NURSEF','ONLINE' 
,'PHEAA','PRIV1','PRIV2','PRIV3','PRIV4','PRIV5','PRIV6','PRIV7','PRIV8' 
,'PTDEPT','PTEMER','PTGIFT','PTKAPA','RCKHMF','RT2LRN','RVERFT','SGCLDR' 
,'SOMGIF','SOMIST','SOMMRT','SOMMTG','STUGOV','SWKGFT','T10SCH','THEATR' 
,'TRBLTS','UAWDC','UAWDL','UAWEST','UAWFC','UAWGM','UMFSDS','UNSCHL' 
,'WOLVER','DPTOSS','NURGRA','DIVSCH','AFBOOK','GENPRO','BSUCBK','ALICK' 
,'ANSELM','BENNET','COLLIN','CIVAUX','FC1962','PTRECL','SMITH','WELCH' 
,'UMFEMR','2UNVSC','PRIV9','PRIV10','PRIV11','PRIV12','2ADHOU','2BAS' 
,'2DDGSS','2DETPR','2DIPLS','2FMS3','2FMS5','2FMS7','2GBEC','2GFCLS' 
,'2HAIL','2UMFBL','3FFS','3GUPMI','CEO','CEWSCH','CHANCS','CHSA15' 
,'CHSCAW','GECSCH','ICGDS','ICGMS','MOTTDE','MOTTFH','PRIV5','SCDUMF' 
,'SOMIES','SOMMRT','UMFSAW','UMFSDS','UNSCHL') 
AND FB.PAID_AMT > 0
AND FB.TERM_CODE LIKE '%20'
and fb.term_code >= (select last_4_term from um_current_term)
ORDER BY
FB.FUND_CODE,
DM.LAST_NAME
;