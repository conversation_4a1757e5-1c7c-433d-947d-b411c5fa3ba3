/******************************************************************************* 
This Query is designed to build a view to support a continuation rate dashboard 
it will run on the current term and pull in continuation varaiables for the 
following three semesters with open enrollment.  See query below for availible 
terms
Written by: Dan Getty Institutional Analisys
Date: 040521
*******************************************************************************/  
create or replace view ia_term_cont as
 SELECT
  td.sd_pidm pidm,
  td.sd_term_code cur_term_code,
  td.sd_term_desc cur_term_desc,
  td.primary_college_code cur_col_code,
  td.primary_major_1 cur_major_1,
/******************************************************************************* 
Current Reg Term Set
*******************************************************************************/  
    (
   SELECT
    um.sd_term_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    cur_reg_term_code, 
      (
   SELECT
    um.sd_term_desc
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    cur_reg_term_desc, 
  (
   SELECT
    um.registered_ind
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    cur_reg_term_enrolled_ind, 
  (
   SELECT
    um.primary_college_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    cur_reg_term_col_code, 
 
   CASE
   WHEN (
    SELECT
     um.primary_college_code
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        bridge_term
       FROM
        um_current_term
      )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
   ) = td.primary_college_code THEN
    'Y'
   ELSE
    'N'
  END  cur_reg_term_col_code_cont,
  
   (
   SELECT
    um.primary_major_1
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    cur_reg_term_major_1, 
 
   CASE
   WHEN (
    SELECT
     um.primary_major_1
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        bridge_term
       FROM
        um_current_term
      )
     AND um.sd_pidm = td.sd_pidm
     AND um.registered_ind = 'Y'
   ) = td.primary_major_1 THEN
    'Y'
   ELSE
    'N'
  END  cur_reg_term_major_1_cont,
  
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = td.sd_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = td.primary_degree_code
     AND umd.level_code = td.primary_level_code
     AND umd.grad_term_code < (
      SELECT
       bridge_term
      FROM
       um_current_term
     )
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END  cur_reg_term_grad_ind,
/******************************************************************************* 
Next Reg Term Set
*******************************************************************************/  
      (
   SELECT
    um.sd_term_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    next_reg_term_code, 
      (
   SELECT
    um.sd_term_desc
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    next_reg_term_desc, 
  (
   SELECT
    um.registered_ind
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    next_reg_term_enrolled_ind, 
  (
   SELECT
    um.primary_college_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    next_reg_term_col_code, 
 
   CASE
   WHEN (
    SELECT
     um.primary_college_code
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        next_bridge_term
       FROM
        um_current_term
      )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
   ) = td.primary_college_code THEN
    'Y'
   ELSE
    'N'
  END  next_reg_term_col_code_cont,
    
   (
   SELECT
    um.primary_major_1
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    next_reg_term_major_1, 
 
   CASE
   WHEN (
    SELECT
     um.primary_major_1
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        next_bridge_term
       FROM
        um_current_term
      )
     AND um.sd_pidm = td.sd_pidm
     AND um.registered_ind = 'Y'
   ) = td.primary_major_1 THEN
    'Y'
   ELSE
    'N'
  END  next_reg_term_major_1_cont,
    
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = td.sd_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = td.primary_degree_code
     AND umd.level_code = td.primary_level_code
     AND umd.grad_term_code < (
      SELECT
       next_bridge_term
      FROM
       um_current_term
     )
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END  next_reg_term_grad_ind,
/******************************************************************************* 
Next 2 Reg Term Set
*******************************************************************************/  
      (
   SELECT
    um.sd_term_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_2_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    next_2_reg_term_code, 
      (
   SELECT
    um.sd_term_desc
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_2_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    next_2_reg_term_desc, 
  (
   SELECT
    um.registered_ind
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_2_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
  )    next_2_reg_term_enrolled_ind, 
  (
   SELECT
    um.primary_college_code
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_2_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    next_2_reg_term_col_code, 
 
   CASE
   WHEN (
    SELECT
     um.primary_college_code
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        next_2_bridge_term
       FROM
        um_current_term
      )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
   ) = td.primary_college_code THEN
    'Y'
   ELSE
    'N'
  END  next_2_reg_term_col_code_cont,
  
     (
   SELECT
    um.primary_major_1
   FROM
    um_student_data um
   WHERE
     um.sd_term_code = (
      SELECT
       next_2_bridge_term
      FROM
       um_current_term
     )
    AND um.sd_pidm = td.sd_pidm
    AND um.registered_ind = 'Y'
  )    next_2_reg_term_major_1, 
 
   CASE
   WHEN (
    SELECT
     um.primary_major_1
    FROM
     um_student_data um
    WHERE
      um.sd_term_code = (
       SELECT
        next_2_bridge_term
       FROM
        um_current_term
      )
     AND um.sd_pidm = td.sd_pidm
     AND um.registered_ind = 'Y'
   ) = td.primary_major_1 THEN
    'Y'
   ELSE
    'N'
  END  next_2_reg_term_major_1_cont,
    
  CASE
   WHEN (
    SELECT
     COUNT(*)
    FROM
     um_degree umd
    WHERE
      umd.pidm = td.sd_pidm
     AND umd.degree_status = 'AW'
     AND umd.degree_code = td.primary_degree_code
     AND umd.level_code = td.primary_level_code
     AND umd.grad_term_code < (
      SELECT
       next_2_bridge_term
      FROM
       um_current_term
     )
   ) > 0 THEN
    'Y'
   ELSE
    'N'
  END  next_2_reg_term_grad_ind
  
 FROM
  td_student_data  td
  LEFT OUTER JOIN td_demographic   dm ON dm.td_term_code = td.sd_term_code
                                       AND dm.dm_pidm = td.sd_pidm
 WHERE
   sd_term_code = (
    SELECT
     current_term
    FROM
     um_current_term
   )
   AND td.registered_ind = 'Y'
;
--Terms to build out
