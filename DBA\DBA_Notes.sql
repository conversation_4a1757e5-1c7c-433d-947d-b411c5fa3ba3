/*******************************************************************************
                               DBA Statistics
*******************************************************************************/
--Query all Invalid Objects
select
   owner       ,
   object_type ,
   object_name 
from
   dba_objects
where
   status = 'INVALID'
order by
   owner,
   object_type;
   
--Query All Objects for owner
select 
owner
     , object_name
     , object_type
  from ALL_OBJECTS
 where object_name = 'SPRCMNT'
 ;
 
--find table with specific column
select owner, table_name from all_tab_columns where column_name LIKE '%ACAT_CODE';

--Query All users of an object
select Grantee,'Granted Through Role' as Grant_Type, role, table_name
from role_tab_privs rtp, dba_role_privs drp
where rtp.role = drp.granted_role
and table_name = 'UM_SCHL_APPLICANTS' 
union
select Grantee,'Direct Grant' as Grant_type, null as role, table_name
from dba_tab_privs
where table_name = 'UM_SCHL_APPLICANTS' ;

grant select on UM_TRANSCRIPT_DETAIL to BO_FLINT_STUDENT;
grant select on UM_TRANSCRIPT_DETAIL to famgr;
DESC SPRCMNT;
SELECT * FROM SPRCMNT WHERE SPRCMNT_PIDM = 126975;

--Count Tables:
SELECT
SYS.database_name,
 'Table'      asset_type,
 owner,
 COUNT(*)     asset_count
FROM
 dba_tables
WHERE
 owner LIKE '%MGR'
 OR owner LIKE 'BO%'
 OR owner IN ( 'RCASTER', 'WEBFOCUS' )
GROUP BY
 owner
UNION
SELECT
SYS.database_name,
 'View'       asset_type,
 owner,
 COUNT(*)     asset_count
FROM
 dba_views
WHERE
 owner LIKE '%MGR'
 OR owner LIKE 'BO%'
 OR owner IN ( 'RCASTER', 'WEBFOCUS' )
GROUP BY
 owner
ORDER BY
 1,
 2,
 3,
 4 DESC;
 
--Count Tables, views and schemas:
with dp1 as (
SELECT
SYS.database_name db,
 'Table'      asset_type,
 COUNT (distinct owner) owner_count,
 COUNT(*)     asset_count
FROM
 dba_tables
WHERE
 owner LIKE '%MGR'
 OR owner LIKE 'BO%'
 OR owner IN ( 'RCASTER', 'WEBFOCUS' )
GROUP BY
SYS.database_name,
'Table'
UNION
SELECT
SYS.database_name db,
 'View'       asset_type,
  COUNT (distinct owner) owner_count,
 COUNT(*)     asset_count
FROM
 dba_views
WHERE
 owner LIKE '%MGR'
 OR owner LIKE 'BO%'
 OR owner IN ( 'RCASTER', 'WEBFOCUS' )
GROUP BY
SYS.database_name,
'View'

)
select * from dp1
union all
select
'Total' DB,
'All' asset_type,
sum(owner_count),
sum(asset_count)
from dp1
; 
 
--drop schema
DROP USER RBESSACMGR CASCADE; 

--Count Sequences
SELECT
 COUNT(*)
FROM
 dba_sequences;

--Count Indexes
SELECT
 COUNT(*)
FROM
 dba_indexes;
 
 SELECT
*
--object_name
-- COUNT(*)

FROM
 dba_procedures
 where owner = 'AIMSMGR'
-- and object_type in ('FUNCTION','PROCEDURE')
 ;
  --Check user account status
SELECT
 username,
 account_status
FROM
 dba_users
WHERE
 username = 'IAMGR';
 
--unlock accounts
ALTER USER canvasmgr ACCOUNT UNLOCK;
--Change user account password
ALTER USER canvasmgr IDENTIFIED BY lms4eva;
--Set password to never expire
ALTER USER emasmgr PROFILE topgun;

/*******************************************************************************
NOTES SECTION
*******************************************************************************/
--count number of schemas in database
--Count Tables:
SELECT
 username,
 created,
 COUNT(DISTINCT dba_tables.table_name)       tbl_count,
 COUNT(DISTINCT dba_views.view_name)         view_count
FROM
 all_users au
 LEFT JOIN dba_tables ON au.username = dba_tables.owner
 LEFT JOIN dba_views ON au.username = dba_views.owner
WHERE
 au.username LIKE 'BO_FLINT_STUDENT'
GROUP BY
 username,
 created
ORDER BY
 3 DESC;

SELECT
 *
FROM
 dba_tables
WHERE
 owner LIKE 'BO_FLINT_STUDENT'
--group by owner
ORDER BY
 2 DESC;

SELECT
 *
FROM
 user_role_privs
WHERE
 username = 'BO_FLINT_STUDENT';

SELECT
 *
FROM
 user_tab_privs
WHERE
 grantee = 'BO_FLINT_STUDENT';

SELECT
 *
FROM
 user_sys_privs
WHERE
 username = 'BO_FLINT_STUDENT';

--Create user and grant create table, and tablespace
CREATE USER bo_flint_fa IDENTIFIED BY password;

GRANT
 CREATE TABLE
TO bo_flint_fa;

GRANT
 CREATE VIEW
TO bo_flint_fa;

ALTER USER bo_flint_fa
 QUOTA 100M ON development;

GRANT
 UNLIMITED TABLESPACE
TO bo_flint_fa;

ALTER USER bo_flint_fa DEFAULT ROLE ALL;

GRANT
 CREATE SESSION
TO bo_flint_fa;

GRANT
 CREATE PROCEDURE
TO bo_flint_fa;

GRANT
 CREATE ANY DIRECTORY
TO bo_flint_fa;

-- create a profile, assign parameters, and alter user profile
CREATE PROFILE topgun LIMIT
 PASSWORD_REUSE_TIME UNLIMITED;

ALTER PROFILE topgun LIMIT
 PASSWORD_LIFE_TIME UNLIMITED;

ALTER USER dwrksmgr
 PROFILE topgun;
 
 ALTER USER webfocus
 PROFILE topgun;

--Check user account status
SELECT
 username,
 account_status
FROM
 dba_users
WHERE
 username = 'HEDAMGR';
--unlock accounts
ALTER USER dwrksmgr
 ACCOUNT UNLOCK;
--Change user account password
ALTER USER dwrksmgr IDENTIFIED BY password;
ALTER USER  IDENTIFIED BY umf4eva;


--query the priveleges of a user
SELECT
 *
--username, account_status, EXPIRY_DATE 
FROM
 dba_users
WHERE
 username = 'IAMGR';

--count number of schemas in database
--Count Tables:
SELECT
 username,
 created,
 COUNT(DISTINCT dba_tables.table_name)       tbl_count,
 COUNT(DISTINCT dba_views.view_name)         view_count
FROM
 all_users au
 LEFT JOIN dba_tables ON au.username = dba_tables.owner
 LEFT JOIN dba_views ON au.username = dba_views.owner
WHERE
 au.username LIKE '%MGR'

GROUP BY
 username,
 created
ORDER BY
 3 DESC;

SELECT
 *
FROM
 dba_tables
--where owner like '%MGR'
--group by owner
--ORDER BY 2 DESC
 ;
select * from dba_views
where owner = 'INOWMGR'
;
/*******************************************************************************
                          Locked User Accounts
*******************************************************************************/

SELECT username, account_status, created, lock_date, expiry_date
  FROM dba_users
 WHERE account_status != 'OPEN'
 order by
 1,2
 ;
/*******************************************************************************
                          User table grants Direct and Indirect
*******************************************************************************/
--Direct
SELECT owner, table_name, select_priv, insert_priv, delete_priv, update_priv, 
references_priv, alter_priv, index_priv 
  FROM table_privileges
 WHERE grantee = 'OELMGR'
 ORDER BY owner, table_name;
 
--Indirect
 SELECT DISTINCT owner, table_name, PRIVILEGE 
  FROM dba_role_privs rp JOIN role_tab_privs rtp ON (rp.granted_role = rtp.role)
 WHERE rp.grantee = 'IAMGR'
 ORDER BY owner, table_name;

--Privileges 
SELECT * FROM dba_sys_privs
WHERE grantee = 'IAMGR';

/*******************************************************************************
                          Kill user connecton
*******************************************************************************/

select s.sid, s.serial#, s.status, p.*
from v$session s, v$process p
where s.username = 'UGMGR'
and p.addr (+) = s.paddr;

alter system kill session '<sid>,<serial>';
--alter system kill session '751,37105';
--alter system kill session '266,59079';
--alter system kill session '761,54262';
--alter system kill session '868,49621';
--alter system kill session '875,55875';
--alter system kill session '862,47481';
--alter system kill session '132,29704';
--alter system kill session '256,60471';
--alter system kill session '635,11810';
--alter system kill session '391,45061';
--alter system kill session '511,42279';

DESC V$session;
desc v$process;

/*******************************************************************************
                          Tablespace
*******************************************************************************/

select b.tablespace_name, tbs_size Tablespcace_SizeGb, a.free_space Tablespace_FreeGb
from  (select tablespace_name, round(sum(bytes)/1024/1024/1024 ,2) as free_space
       from dba_free_space
       group by tablespace_name) a,
      (select tablespace_name, round(sum(bytes)/1024/1024/1024,2) as tbs_size
       from dba_data_files
       group by tablespace_name) b
where a.tablespace_name(+)=b.tablespace_name;


/*******************************************************************************
                Assest that did not get moved between databases
*******************************************************************************/
SELECT
 'Table' asset_type,
 owner,
 table_name
FROM
 <EMAIL>
WHERE
 owner NOT LIKE '%SYS%'
 AND owner NOT LIKE '%AIMSMGR%'
 AND owner NOT LIKE '%APEX%'
 AND owner NOT LIKE '%AAAMGR%'
 AND owner NOT LIKE '%AIMSADMS%'
 AND owner NOT LIKE '%DBSNMP%'
 AND owner NOT LIKE '%HR%'
 AND owner NOT LIKE '%IX%'
 AND owner NOT LIKE '%OE%'
 AND owner NOT LIKE '%PM%'
 AND owner NOT LIKE '%SCOTT%'
 AND owner NOT LIKE '%SEM%'
 AND owner NOT LIKE '%WECMGR%'
 AND owner NOT LIKE '%XDB%'
 AND owner NOT LIKE '%SH%'
MINUS
SELECT
 'Table' asset_type,
 owner,
 table_name
FROM
 dba_tables
UNION
SELECT
 'View' asset_type,
 owner,
 view_name
FROM
 <EMAIL>
WHERE
 owner NOT LIKE '%SYS%'
 AND owner NOT LIKE '%AIMSMGR%'
 AND owner NOT LIKE '%APEX%'
 AND owner NOT LIKE '%AAAMGR%'
 AND owner NOT LIKE '%AIMSADMS%'
 AND owner NOT LIKE '%DBSNMP%'
 AND owner NOT LIKE '%HR%'
 AND owner NOT LIKE '%IX%'
 AND owner NOT LIKE '%OE%'
 AND owner NOT LIKE '%PM%'
 AND owner NOT LIKE '%SCOTT%'
 AND owner NOT LIKE '%SEM%'
 AND owner NOT LIKE '%WECMGR%'
 AND owner NOT LIKE '%XDB%'
 AND owner NOT LIKE '%SH%'
MINUS
SELECT
 'View' asset_type,
 owner,
 view_name
FROM
 dba_views;