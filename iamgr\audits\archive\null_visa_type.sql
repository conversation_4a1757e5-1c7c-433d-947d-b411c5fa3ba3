select 
UMID,
SD_PIDM,
SD_TERM_CODE,
REPORT_LEVEL_CODE,
ia_student_type_code,
VISA_TYPE_CODE,
citizenship_code,
CITIZENSHIP_DESC

from
ia_um_student_data
where
sd_term_code = (select current_term from um_current_term) --'202030' 
and registered_ind = 'Y'
and INTL_IND = 'Y' 
and VISA_TYPE_CODE IS NULL
and citizenship_code != 'NO'
;

select distinct 
visa_type_desc,
visa_type_code
from ia_um_student_data
where sd_term_code >= (select current_term from um_current_term)
--and intl_ind = 'Y'
;

select current_term, 
current_term_start_date_1,
current_term_census_date_1,
current_term_end_date_1
from um_current_term;