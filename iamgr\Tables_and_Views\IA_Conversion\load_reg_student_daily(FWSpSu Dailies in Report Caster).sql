/*******************************************************************************
Underlying data for the Daily Enrollment Reports Dashboard
Replaces the followiong Report Caster Jobs in Webfocus:
Daily Enrollment Reports (Fall, Winter, Spring, Summer)
AKA FWSS Dailies
*******************************************************************************/

with key_sel as(
    select
    (select distinct um_reg_student_daily_ia.day_of_term_wkends from um_reg_student_daily_ia where um_reg_student_daily_ia.run_date = trunc(sysdate - 1) and um_reg_student_daily_ia.term_code = stvterm.stvterm_code) day_of_term_wkends,
    stvterm.stvterm_code,
    stvterm.stvterm_desc,
    stvlevl.stvlevl_code,
    stvlevl.stvlevl_desc,
    stvstyp.stvstyp_code,
    stvstyp.stvstyp_desc
    from aimsmgr.stvterm_ext stvterm
    inner join aimsmgr.stvlevl_ext stvlevl on stvlevl_code in ('UG', 'GR')
    inner join aimsmgr.stvstyp_ext stvstyp on stvstyp.stvstyp_code in ('C', 'G', 'N', 'S', 'R', 'D', 'E', 'F', 'T')
    where stvterm.stvterm_code in (select distinct um_reg_student_daily_ia.term_code from um_reg_student_daily_ia where um_reg_student_daily_ia.run_date = trunc(sysdate))
    and not (stvlevl_code = 'GR' and stvstyp_code in ('T', 'E', 'F', 'D'))
    and not (stvlevl_code = 'UG' and stvstyp_code in ('N'))
)
, pop_sel as(
    select 
    run_date,
    day_of_term_wkends,
    term_code,
    rpt_levl_code,
    rpt_student_type_code,
    sum(head_count) head_count,
    sum(total_credit_hours) credit_hours
    from um_reg_student_daily_ia
    group by
    run_date,
    day_of_term_wkends,
    term_code,
    rpt_levl_code,
    rpt_student_type_code
)
, td_cur as(
    select
    td_student_data.sd_term_code,
    td_student_data.report_level_code,
    td_student_data.student_type_code,
    
    count(td_student_data.sd_pidm) head_count
    from td_student_data
    
    group by
    td_student_data.sd_term_code,
    td_student_data.report_level_code,
    td_student_data.student_type_code
)
select
pop_sel.run_date,
key_sel.stvterm_code,
key_sel.day_of_term_wkends,
key_sel.stvlevl_code,
key_sel.stvstyp_code,
nvl(pop_sel.head_count, 0) current_head_count,
nvl(pop_sel.credit_hours, 0) current_credit_hours,

--nvl(p1.head_count, 0) prev_head_count,
--nvl(p1.credit_hours, 0) prev_credit_hours,
--
--nvl(p2.head_count, 0) prev2_head_count,
--nvl(p2.credit_hours, 0) prev2_credit_hours,
--
--nvl(p3.head_count, 0) prev3_head_count,
--nvl(p3.credit_hours, 0) prev3_credit_hours,


pop_sel.* --,
--p1.*
from key_sel
left outer join pop_sel on pop_sel.term_code = key_sel.stvterm_code
                   and pop_sel.rpt_levl_code = key_sel.stvlevl_code
                   and pop_sel.rpt_student_type_code = key_sel.stvstyp_code
                   and pop_sel.day_of_term_wkends = key_sel.day_of_term_wkends
--left outer join pop_sel p1 on p1.term_code = (key_sel.stvterm_code - 100)
--                           and p1.rpt_levl_code = key_sel.stvlevl_code
--                           and p1.rpt_student_type_code = key_sel.stvstyp_code
--                           and p1.day_of_term_wkends = key_sel.day_of_term_wkends
--left outer join pop_sel p2 on p2.term_code = (key_sel.stvterm_code - 200)
--                           and p2.rpt_levl_code = key_sel.stvlevl_code
--                           and p2.rpt_student_type_code = key_sel.stvstyp_code
--                           and p2.day_of_term_wkends = key_sel.day_of_term_wkends
--left outer join pop_sel p3 on p3.term_code = (key_sel.stvterm_code - 300)
--                           and p3.rpt_levl_code = key_sel.stvlevl_code
--                           and p3.rpt_student_type_code = key_sel.stvstyp_code
--                           and p3.day_of_term_wkends = key_sel.day_of_term_wkends

where key_sel.stvterm_code = '202110'
order by
key_sel.stvlevl_code,
key_sel.stvstyp_code


;


--create or replace view 
with pop_sel as(
    select 
    run_date,
    day_of_term_wkends,
    term_code,
    rpt_levl_code,
    rpt_student_type_code,
    sum(head_count) head_count,
    sum(total_credit_hours) credit_hours
    from um_reg_student_daily_ia
    group by
    run_date,
    day_of_term_wkends,
    term_code,
    rpt_levl_code,
    rpt_student_type_code
)
, td_cur as(
    select
    td_student_data.sd_term_code,
    td_student_data.report_level_code,
    td_student_data.student_type_code,
    
    count(td_student_data.sd_pidm) head_count
    from td_student_data
    
    group by
    td_student_data.sd_term_code,
    td_student_data.report_level_code,
    td_student_data.student_type_code
)
select
pop_sel.run_date,
pop_sel.day_of_term_wkends,
pop_sel.term_code,
(select stvterm.stvterm_desc from aimsmgr.stvterm_ext stvterm where stvterm.stvterm_code = pop_sel.term_code) term_desc,
pop_sel.rpt_student_type_code,
pop_sel.rpt_levl_code,

pop_sel.head_count,
pop_sel.credit_hours,

(pop_sel.term_code - 100) p1_term_code,
(select stvterm.stvterm_desc from aimsmgr.stvterm_ext stvterm where stvterm.stvterm_code = (pop_sel.term_code - 100)) p1_term_desc,
nvl(pop_sel_1.head_count, 0) p1_head_count,
nvl(pop_sel_1.credit_hours, 0) p1_credit_hours,
(pop_sel.head_count - nvl(pop_sel_1.head_count, 0)) p1_diff,

(pop_sel.term_code - 200) p2_term_code,
(select stvterm.stvterm_desc from aimsmgr.stvterm_ext stvterm where stvterm.stvterm_code = (pop_sel.term_code - 200)) p2_term_desc,
nvl(pop_sel_2.head_count, 0) p2_head_count,
nvl(pop_sel_2.credit_hours, 0) p2_credit_hours,

(pop_sel.term_code - 300) p2_term_code,
(select stvterm.stvterm_desc from aimsmgr.stvterm_ext stvterm where stvterm.stvterm_code = (pop_sel.term_code - 300)) p3_term_desc,
nvl(pop_sel_3.head_count, 0) p3_head_count,
nvl(pop_sel_3.credit_hours, 0) p3_credit_hours,
td_cur.head_count td_head_count

from pop_sel
left outer join pop_sel pop_sel_1 on pop_sel_1.term_code = pop_sel.term_code - 100
                                  and pop_sel_1.day_of_term_wkends = pop_sel.day_of_term_wkends
                                  and pop_sel_1.rpt_levl_code = pop_sel.rpt_levl_code
                                  and pop_sel_1.rpt_student_type_code = pop_sel.rpt_student_type_code
left outer join pop_sel pop_sel_2 on pop_sel_2.term_code = pop_sel.term_code - 200
                                  and pop_sel_2.day_of_term_wkends = pop_sel.day_of_term_wkends
                                  and pop_sel_2.rpt_levl_code = pop_sel.rpt_levl_code
                                  and pop_sel_2.rpt_student_type_code = pop_sel.rpt_student_type_code
left outer join pop_sel pop_sel_3 on pop_sel_3.term_code = pop_sel.term_code - 300
                                  and pop_sel_3.day_of_term_wkends = pop_sel.day_of_term_wkends
                                  and pop_sel_3.rpt_levl_code = pop_sel.rpt_levl_code
                                  and pop_sel_3.rpt_student_type_code = pop_sel.rpt_student_type_code
left outer join td_cur on td_cur.sd_term_code = (pop_sel.term_code - 100)
                       and td_cur.report_level_code = pop_sel.rpt_levl_code
                       and td_cur.student_type_code = pop_sel.rpt_student_type_code
where pop_sel.run_date = trunc(sysdate - 1) --(select max(run_date) from um_reg_student_daily_ia) -- trunc(sysdate - 1)
and pop_sel.term_code = '202110'


;



--left outer join(
--
--
--)
--where um_reg_student_daily_ia.run_date = trunc(sysdate - 1)
--and term_code = '202020'
--group by
--term_code,
--rpt_student_type_code
--;

--select *
--from um_current_term
--
--;
--select 
--run_date,
----term_code,
--count(*)
--from um_reg_student_daily_ia
--group by 
--run_date --,
----term_code
--
--order by
--run_date desc
--;
