/*******************************************************************************
primary degree from primary_major_1
*******************************************************************************/
create or replace view IA_DC_POP as( 
 
  select 
  pidm,
   case
    when ia_cw_hp.degree_code is not null then 'Health'
    when ia_cw_stem.majr_code is not null then 'STEM'
    else 'Standard'
  end hp_stem_order_desc,
  case
    when ia_cw_hp.degree_code is not null then 1 
    when ia_cw_stem.majr_code is not null then 2
    else 3
  end hp_stem_order_num, -- first priority for degree
  ia_um_degree.primary_major_1 degree_major_code,  
  'primary degree' degree_source_desc,
  1 degree_source--,
  --ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
  
    union all
/*******************************************************************************
second degree from primary_major_2
*******************************************************************************/
  select 
  pidm,
  'Standard' hp_stem_order_desc,
  3 hp_stem_order_num,
  ia_um_degree.primary_major_2 degree_major_code,
  'second major' degree_source_desc,
  2 degree_source--,
  --ia_um_degree.*
  from ia_um_degree
  left join iamgr.ia_cw_hp on ia_cw_hp.degree_code = ia_um_degree.degree_code 
                            and ia_cw_hp.primary_program = ia_um_degree.primary_program
  left join iamgr.ia_cw_stem on ia_cw_stem.majr_code = ia_um_degree.primary_major_1
  where 
  ia_um_degree.primary_major_2 is not null

)
--,
--
--
--select 
--pop_sel.pidm,
--pop_sel.grad_term_code,
--pop_sel.DEGREE_CODE,
--pop_sel.degree_major_code,
--pop_sel.hp_stem_order_desc,
--pop_sel.hp_stem_order_num,
--pop_sel.degree_source_desc,
--pop_sel.degree_source
--
--from 
--pop_sel
----left outer join pidm_count.pidm on  pidm_count.pidm = pop_sel.pidm
--where 
--pop_sel.grad_term_code in ('201440', '201510', '201520', '201530') and
--pop_sel.degree_status = 'AW'
--)
--order by 
--pop_sel.hp_stem_order_num, pop_sel.degree_seqno
