-- percent of all students live in Genesee County
create or replace FUNCTION "F_GEN_COUNTY_STUDS_HC" 
   (
--   HEADCOUNT number,
--   GENESEE_COUNTY varchar2,
   TERM_CODE_IN varchar2)
   return VARCHAR2
as

REC_VAL VARCHAR2(10);

cursor sel_cur is
SELECT -- GENESEE RESIDENTS 3307
COUNT (*) HEADCOUNT 
FROM IA_TD_STUDENT_DATA
WHERE SD_TERM_CODE = TERM_CODE_IN
AND A1_COUNTY_CODE = 'MI049'  --GENESEE COUNTY
AND REGISTERED_IND = 'Y'
;

sel_rec sel_cur%rowtype;


BEGIN
  open sel_cur;
  fetch sel_cur into sel_rec;
  if sel_cur%found  then 
    rec_val := sel_rec.headcount;
  else 
    rec_val := 0;
  end if;


  close sel_cur;


RETURN REC_VAL;
END;
