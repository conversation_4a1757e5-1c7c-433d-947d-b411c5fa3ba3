--------------------------------------------------------
--  File created - Wednesday-February-21-2024   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for View UDP_COURSE_SECTION_ENROLLMENT
--------------------------------------------------------

  CREATE OR REPLACE FORCE EDITIONABLE VIEW "CANVASMGR"."UDP_COURSE_SECTION_ENROLLMENT" ("COURSE_OFFERING_ID", "ACADEMIC_TERM_ID", "LEARNING_ENVIRONMENT_ORGANIZATION_ID", "COURSE_SECTION_ID", "LE_CURRENT_COURSE_OFFERING_ID", "LE_ORIGINAL_COURSE_OFFERING_ID", "START_DATE", "END_DATE", "LE_CODE", "LE_STATUS", "COURSE_NUM", "COURSE_SUBJ", "TITLE", "CLASS_NUMBER", "CREATED_DATE", "LE_NAME", "MAX_ENROLLMENT", "SECTION_NUMBER", "STATUS", "TYPE", "PERSON_ID", "ROLE", "ROLE_STATUS", "ENROLLMENT_STATUS", "ENTRY_DATE", "EXIT_DATE", "UPDATED_DATE") AS 
  select
UCOE.COURSE_OFFERING_ID, 
UCOE.ACADEMIC_TERM_ID, 
UCOE.LEARNING_ENVIRONMENT_ORGANIZATION_ID, 
UCSE.COURSE_SECTION_ID, 
UCSE.LE_CURRENT_COURSE_OFFERING_ID, 
UCSE.LE_ORIGINAL_COURSE_OFFERING_ID, 
UCOE.START_DATE, 
UCOE.END_DATE, 
UCOE.LE_CODE, 
UCOE.LE_STATUS, 
UCOE.COURSE_NUM, 
UCOE.COURSE_SUBJ, 
UCOE.TITLE,
UCSE.CLASS_NUMBER, 
UCSE.CREATED_DATE, 
UCSE.LE_NAME, 
UCSE.MAX_ENROLLMENT, 
UCSE.SECTION_NUMBER, 
UCSE.STATUS, 
UCSE.TYPE, 
CSEE.PERSON_ID,
CSEE.ROLE, 
CSEE.ROLE_STATUS, 
CSEE.ENROLLMENT_STATUS, 
CSEE.ENTRY_DATE, 
CSEE.EXIT_DATE, 
CSEE.UPDATED_DATE
from UDP_COURSE_OFFERING_EXT UCOE
inner join UDP_COURSE_SECTION_EXT UCSE ON UCOE.course_offering_id = UCSE.COURSE_OFFERING_ID
inner join UDP_COURSE_SECTION_ENROLLMENT_EXT CSEE on UCSE.COURSE_SECTION_ID = csee.COURSE_SECTION_ID
where academic_term_id is not null
and le_status is not null
--and entry_date is not null
;
