
with IA_GRADES AS (
select 
UM_STUDENT_TRANSCRIPT.pidm, 
UM_STUDENT_TRANSCRIPT.term, 
UM_DEMOGRAPHIC.HSCH_DESC,
UM_DEMOGRAPHIC.HSCH_GPA,
UM_DEMOGRAPHIC.hsch_code,
UM_STUDENT_TRANSCRIPT.subject, 
UM_STUDENT_TRANSCRIPT.course , 
UM_STUDENT_TRANSCRIPT.section, 
UM_STUDENT_TRANSCRIPT.CRN, 
UM_STUDENT_TRANSCRIPT.Title, 
UM_STUDENT_TRANSCRIPT.Grade,
(case UM_STUDENT_TRANSCRIPT.Grade
when 'A+' then 'Pass'
when 'A' then 'Pass'
when 'A-' then 'Pass'
when 'B+' then 'Pass'
when 'B' then 'Pass'
when 'B-' then 'Pass'
when 'C+' then 'Pass'
when 'C' then 'Pass'
when 'C-' then 'Pass'
when 'IA+' then 'Pass'
when 'IA' then 'Pass'
when 'IA-' then 'Pass'
when 'IB+' then 'Pass'
when 'IB' then 'Pass'
when 'IB-' then 'Pass'
when 'IC+' then 'Pass'
when 'IC' then 'Pass'
when 'IC-' then 'Pass'
when 'P' then 'Pass'
when 'W' then 'withdraw'
when 'IN' then 'NoGrade'
when 'N' then 'NoGrade'
Else 'Fail'
end)pass_fail,

UM_STUDENT_TRANSCRIPT.COMPLETED_IND,
UM_CATALOG_SCHEDULE.PRIMARY_INSTRUCTOR_ID,
UM_CATALOG_SCHEDULE.PRIMARY_INSTRUCTOR_LAST_NAME || ', ' || UM_CATALOG_SCHEDULE.PRIMARY_INSTRUCTOR_FIRST_NAME as INS_NAME, 
UM_STUDENT_TRANSCRIPT.subject || ' ' ||UM_STUDENT_TRANSCRIPT.course as sub_crs

from UM_STUDENT_TRANSCRIPT 
inner join UM_CATALOG_SCHEDULE on UM_STUDENT_TRANSCRIPT.term = UM_CATALOG_SCHEDULE.term_code_key AND 
                                  UM_STUDENT_TRANSCRIPT.CRN = UM_CATALOG_SCHEDULE.CRN_KEY
inner join um_demographic on UM_STUDENT_TRANSCRIPT.pidm = um_demographic.dm_pidm
inner join um_student_data on UM_STUDENT_TRANSCRIPT.term = um_student_data.sd_term_code and
                              UM_STUDENT_TRANSCRIPT.pidm = um_student_data.sd_pidm
where UM_STUDENT_TRANSCRIPT.TYPE = 'I' and 
  UM_STUDENT_TRANSCRIPT.term >= '201310' and 
  UM_STUDENT_TRANSCRIPT.term < '201610' and
  HSCH_CODE is not NULL and
  --UM_STUDENT_TRANSCRIPT.COURSE like '1%' and
  um_student_data.ORIG_STUDENT_TYPE_CODE = 'F'
)

select distinct Grade
from ia_grades
order by grade asc
--hsch_code = '231473'--Carmon Ainsworth

