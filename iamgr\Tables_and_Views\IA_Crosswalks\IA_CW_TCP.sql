TRUNCATE TABLE IA_CW_TCP_BAC;

INSERT INTO IA_CW_TCP_BAC 
SELECT * from IA_CW_TCP;

COMMIT;

INSERT INTO IA_CW_TCP
SELECT 
DISTINCT PRIMARY_MAJOR_1 MAJR_CODE, 
PRIMARY_MAJOR_1_DESC MAJR_DESC,
'Y' TCP_IND
FROM IA_TD_STUDENT_DATA
WHERE SD_TERM_CODE >= '201110'
AND (PRIMARY_MAJOR_1 
IN (
'EDUT',
'CHMT',
'BIOT',
'ERTH',
'FRNT',
'ENGT',
'IST',
'PHST',
'SPNT',
'PSYT',
'MTHT',
'MUST',
'MUSE',
'HIST',
'SOCS',
'PHYT',
'POLT',
'ARTE',
'SPE')
OR PRIMARY_MAJOR_1_DESC LIKE '%TCP%'
)
minus
select * from IA_CW_TCP_BAC
--)
;
commit;
