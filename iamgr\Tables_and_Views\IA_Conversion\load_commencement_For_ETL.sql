/*
  University of Michigan-Flint
  Commencement Program List
  winter 2020
  Primary College Desc
  Degree Desc
  * Honors          ** High Honors          *** Academic Distinction          + Multiple Degrees
  
College of Arts & Sciences	AS
College of Health Sciences	HS
Rackham	RK
Sch of Education & Human Srvcs	EH
Sch of Health Prof & Studies	HP
School of Management	MG
School of Nursing	NR
*/
create table temp_commencement
as select * from
load_commencement;

select
'University of Michigan-Flint' clist
from dual
union all
select
'Commencement Program List' clist
from dual
union all
select distinct
grad_term_desc clist
from temp_commencement
where grad_term_code = 202020
union all
select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'AS'
union all

select distinct
degree_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'AS'
order by degree_code

union all

select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'AS'
union all

select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'EH'
union all
select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'HP'
union all
select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'HS'
union all
select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'MG'
union all
select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'NR'
union all
select distinct
primary_college_desc clist
from temp_commencement
where grad_term_code = 202020
and primary_college_code = 'RK'


union all
select
'* Honors          ** High Honors          *** Academic Distinction          + Multiple Degrees' clist
from dual;

select distinct
primary_college_desc,
primary_college_code
from temp_commencement
order by
primary_college_desc 
;




