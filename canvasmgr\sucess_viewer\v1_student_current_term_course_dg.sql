--v1_student_current_term_course
WITH term_info AS (
    SELECT
        *
--   academic_term_id,
--   name AS term_name
    FROM
        UDP_UMF_TERM_KEY uty
    WHERE
            sysdate < uty.le_term_end_date
        AND sysdate > uty.le_term_begin_date

--), courses AS (
--    SELECT
--        coe.course_offering_id,
--        coe.le_code           AS canvas_course_title,
----        coe.available_credits AS credits,
--        coe.academic_term_id,
--        term_info.term_name
--    FROM
--             udp_course_offering_ext coe
--        JOIN term_info ON coe.academic_term_id = term_info.academic_term_id
--    WHERE
--        coe.le_status = 'available'

--), sections AS (
--    SELECT
--        cse.course_section_id,
----   cs2.lms_ext_id AS canvas_section_id,
--        cse.le_current_course_offering_id AS course_offering_id,
----   co4.lms_ext_id AS canvas_course_id,
--        cse.le_name                       AS section_name,
--        cse.section_number                AS section
--    FROM
--        udp_course_section_ext cse
-- JOIN
--   keymap.course_section cs2
-- ON
--   cse.course_section_id = cs2.id
-- JOIN
--   keymap.course_offering co4
-- ON
--   co4.id = cse.le_current_course_offering_id
--   AND cse.status='active'
), courses_sections_of_current_term AS (
    SELECT
        coe.le_code AS canvas_course_title,
        coe.title,
        ti.term_name,
        cse.*,
        coe.academic_term_id
--        ,
--        coe.credits
    FROM
             udp_course_offering_ext coe
        JOIN udp_course_section_ext cse ON coe.course_offering_id = cse.course_offering_id
        JOIN term_info              ti ON coe.academic_term_id = ti.academic_term_id
), courses_enrollment AS (
    SELECT
        csct.*,
        csee.person_id,
        p.name      AS person_name,
        CASE
            WHEN p.first_name IS NULL THEN
                p.name
            ELSE
                p.first_name
        END         first_name,
        CASE
            WHEN p.last_name IS NULL THEN
                p.name
            ELSE
                p.last_name
        END         last_name,
        replace(lower(pe.email_address),
                '@umich.edu',
                '') AS uniqname
    FROM
             courses_sections_of_current_term csct
        JOIN udp_course_section_enrollment_ext csee ON csee.course_section_id = csct.course_section_id
        JOIN udp_person_ext                    p ON csee.person_id = p.person_id
        JOIN udp_person_email_ext              pe ON pe.person_id = p.person_id
    WHERE
            csee.role = 'Student'
        AND csee.role_status = 'Enrolled'
        AND csee.enrollment_status = 'Active'

), course_grades AS (
    SELECT
        ce.*,
        cge.le_current_score current_grade,
        cge.le_final_score   final_grade
--   ,
--   cge.gpa_cumulative_excluding_course_grade AS cumulative_gpa
    FROM
             udp_course_grade_ext cge
        JOIN courses_enrollment ce ON ce.course_section_id = cge.course_section_id
                                      AND ce.person_id = cge.person_id
)
   
--, aca_prog_major AS (                                            --Continue Here
-- SELECT
--   am.academic_program_id,
--   am.description AS academic_major,
--   ap.description AS academic_program,
--   ap.educational_level,
--   am.academic_major_id
-- FROM
--   entity.academic_major am
-- JOIN
--   entity.academic_program ap
-- ON
--   ap.academic_program_id = am.academic_program_id)
   
, average_course_grade as (
  select 
    course_section_id, 
    round(AVG(current_grade),2) as avg_course_grade 
  from (
    select 
      distinct uniqname, 
      course_section_id,
      current_grade 
    from course_grades
    ) course_unique_student_avg_grade 
    group by course_section_id
    )
    
, courses_grade_average as (
  select 
    cg.*, 
    acg.avg_course_grade  
  from 
    average_course_grade acg 
  join 
    course_grades cg 
  on 
    cg.course_section_id  = acg.course_section_id)
    
--,person_term_major_academic AS (
--
-- SELECT
--   pamat.academic_major_id,
--   pat.athletic_participant_sport,
--   pat.cen_academic_level,
--   pat.gpa_credits_units_hours,
--   pamat.person_id,
--   pat.academic_term_id
-- FROM
--   entity.person__academic_term pat
-- JOIN
--   entity.person__academic_major__academic_term pamat
-- ON
--   pamat.person_id = pat .person_id
--   AND pat.academic_term_id = pamat.academic_term_id
--   AND pat.eot_academic_load LIKE '%Time')

--, person_full_academic_term_major AS (
-- SELECT
--   *
-- FROM
--   aca_prog_major a
-- JOIN
--   person_term_major_academic b
-- ON
--   a.academic_major_id = b.academic_major_id )
   
, courses_enrollment_major AS (
 SELECT
   a.*
--,
--   b.academic_major,
--   b.academic_program,
--   b.educational_level,
--   b.athletic_participant_sport,
--   b.cen_academic_level,
--   b.gpa_credits_units_hours
 FROM
   courses_grade_average a
-- left outer JOIN
--   person_full_academic_term_major b
-- ON
--   a.person_id = b.person_id
--   AND a.academic_term_id = b.academic_term_id
)
 SELECT
    term_name,
--    term_code,
    course_section_id,
    course_offering_id,
    canvas_course_title,
--    section,
--    section_name,
--    credits,
    person_id,
    uniqname,
    person_name,
    first_name,
    last_name,
    current_grade,
    final_grade,
    avg_course_grade
-- ,
-- gpa_credits_units_hours,
-- cumulative_gpa,

 

-- cen_academic_level,
-- academic_major,
-- academic_program,
-- athletic_participant_sport,
-- educational_level,

-- ,
-- canvas_section_id
FROM
    courses_enrollment_major
ORDER BY
    2,
    uniqname