--------------------------------------------------------
--  Grad Programs un-pushed apps
--------------------------------------------------------
SELECT sarhead.sarhead_aidm aidm,
  sarhead.sarhead_appl_seqno appl_seqno,
  sarctrl_join.spriden_pidm pidm,
  sarctrl_join.sarctrl_appl_no_saradap,
  
--  sarhead.sarhead_process_date appl_date,
  sarhead_activity_date activity_date,
  sarctrl_join.spriden_id umid,
  (SELECT stvterm.stvterm_desc
  FROM stvterm
  WHERE stvterm.stvterm_code = sarhead.sarhead_term_code_entry
  ) term_entry,
  sarhead.sarhead_prev_applied_resp prev_um_appl,
  sarhead.sarhead_prev_attend_resp prev_um_course,
  sarpers_join.sarpers_prefix,
  sarpers_join.sarpers_first_name,
  sarpers_join.sarpers_middle_name1,
  sarpers_join.sarpers_last_name,
  sarpers_join.sarpers_suffix,
  sarpers_join.sarpers_former_name,
  sarpers_join.sarpers_nickname,
  sarpers_join.saraddr_street_line1,
  sarpers_join.saraddr_street_line2,
  sarpers_join.saraddr_city,
  sarpers_join.saraddr_stat_cde,
  sarpers_join.saraddr_zip,
  (SELECT stvnatn.stvnatn_nation
  FROM stvnatn
  WHERE stvnatn.stvnatn_code = sarpers_join.saraddr_natn_cde
  ) nation,
  sarpers_join.sarpers_gender,
  sarpers_join.sarpers_birth_dte,
  CASE
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', '')), 0) > 7
    THEN '('
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 1, 3)
      || ')'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 4, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 7, 4)
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', '')), 0) <= 7
    THEN SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 1, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.phone_number, '*WEB*', ''), ' ', ''), 4, 4)
  END phone_number,
  CASE
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', '')), 0) > 7
    THEN '('
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 1, 3)
      || ')'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 4, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 7, 4)
    WHEN NVL(LENGTH(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', '')), 0) <= 7
    THEN SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 1, 3)
      || '-'
      || SUBSTR(REPLACE(REPLACE(sarpers_join.cell_phone, '*WEB*', ''), ' ', ''), 4, 4)
  END cell_phone,
  sarpers_join.email_address,
  CASE
    WHEN sarpers_join.sarpers_ethn_category = '1'
    THEN 'Not Hispanic or Latino'
    WHEN sarpers_join.sarpers_ethn_category = '2'
    THEN 'Hispanic or Latino'
    ELSE 'None'
  END new_ethnicity,
  (SELECT stvlang.stvlang_desc
  FROM stvlang
  WHERE stvlang.stvlang_code = sarpers_join.sarpers_lang_cde_native
  ) language_native,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_wudq_no  = '16'
  AND sarrqst.sarrqst_aidm       = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  ) birthplace,
  (SELECT stvnatn.stvnatn_nation
  FROM stvnatn
  WHERE stvnatn.stvnatn_edi_equiv = sarpers_join.sarpers_natn_cde_birth
  ) birth_country,
  (SELECT stvnatn.stvnatn_nation
  FROM stvnatn
  WHERE stvnatn.stvnatn_edi_equiv = sarpers_join.sarpers_natn_cde_citz
  ) country_of_citizenship,
  CASE
    WHEN sarpers_join.sarpers_citz_cde = '1'
    THEN 'U.S. Citizen'
    WHEN sarpers_join.sarpers_citz_cde = '3'
    THEN 'U.S. Permanent Resident'
    WHEN sarpers_join.sarpers_citz_cde = '9'
    THEN 'Non U.S. Citizen-SV'
    WHEN sarpers_join.sarpers_citz_cde = '10'
    THEN 'Non U.S. Citizen-OV'
    ELSE NULL
  END citizenship_status,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_wudq_no  = 60
  AND sarrqst.sarrqst_aidm       = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  ) pr_number,
--  (select
--  'American Indian or Alaska Native'
--  from sarprac
--  where sarprac.sarprac_aidm = sarhead.sarhead_aidm
--  and sarprac.sarprac_race_cde = '1') race_1,
--  (select
--  'Asian'
--  from sarprac
--  where sarprac.sarprac_aidm = sarhead.sarhead_aidm
--  and sarprac.sarprac_race_cde = '2') race_2,
--  (select
--  'Black or African American'
--  from sarprac
--  where sarprac.sarprac_aidm = sarhead.sarhead_aidm
--  and sarprac.sarprac_race_cde = '3') race_3,
--  (select
--  'Native Hawiian or Other Pacific Islander'
--  from sarprac
--  where sarprac.sarprac_aidm = sarhead.sarhead_aidm
--  and sarprac.sarprac_race_cde = '4') race_4,
--  (select
--  'White'
--  from sarprac
--  where sarprac.sarprac_aidm = sarhead.sarhead_aidm
--  and sarprac.sarprac_race_cde = '5') race_5,
  (
  SELECT sarrqst.sarrqst_resp_flag
  FROM sarrqst
  WHERE sarrqst.sarrqst_qstn_cde = 'RS'
  AND sarrqst.sarrqst_aidm       = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  ) michigan_residency,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '8'
  ) michigan_residency_since,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '11'
  ) expelled_conduct_question,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '12'
  ) criminal_conduct_question,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '13'
  ) conduct_question_explanation,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '17'
  ) utica_cohort,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '21'
  ) how_did_you_find_out_ansr_desc,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '24'
  ) emp1_name_address,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '25'
  ) emp1_to_from_date,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '26'
  ) emp1_title,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '27'
  ) emp2_name_address,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '28'
  ) emp3_to_from_date,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '29'
  ) emp2_to_from_date,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '30'
  ) emp2_title,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '31'
  ) emp3_name_address,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '32'
  ) emp3_title,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '33'
  ) mba_gre_gmat,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '34'
  ) gre_date,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '35'
  ) english_proficiency,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '44'
  ) recommend1_name,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '45'
  ) recommend1_title,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '46'
  ) recommend1_company,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '47'
  ) recommend1_city_state_phone,
  (SELECT REPLACE( REPLACE( REPLACE( REPLACE( REPLACE( REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') , chr(3), '') , chr(4), '') , chr(11), '') , chr(18), '') , chr(25), '') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '48'
  ) recommend1_email,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '49'
  ) recommend2_name,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '50'
  ) recommend2_title,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '51'
  ) recommend2_company,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '52'
  ) recommend2_city_state_phone,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '53'
  ) recommend2_email,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '54'
  ) recommend3_name,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '55'
  ) recommend3_title,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '56'
  ) recommend3_company,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '57'
  ) recommend3_city_state_phone,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '58'
  ) recommend3_email,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '67'
  ) mil_active_duty,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '68'
  ) mil_active_reserves,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '69'
  ) mil_veteran,
  --(select replace(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  -- from sarrqst
  -- where sarrqst.sarrqst_aidm = sarhead.sarhead_aidm
  -- and sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  -- and sarrqst.sarrqst_wudq_no = '66') mil_dependent,
  NULL mil_dependent,
  (SELECT REPLACE(sarrqst.sarrqst_ansr_desc, chr(2), ' ') sarrqst_ansr_desc
  FROM sarrqst
  WHERE sarrqst.sarrqst_aidm     = sarhead.sarhead_aidm
  AND sarrqst.sarrqst_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrqst.sarrqst_wudq_no    = '59'
  ) ferpa_question,
  saretry_join.sorcmjr_majr_code major_code,
  CASE
    WHEN NVL(LENGTH(saretry_join.sorcmjr_desc), 0) > 0
    THEN saretry_join.sorcmjr_desc
    ELSE
      (SELECT stvmajr.stvmajr_desc
      FROM stvmajr
      WHERE stvmajr.stvmajr_code = saretry_join.sorcmjr_majr_code
      )
  END major,
  saretry_join.sorccon_majr_code_conc concentration_code,
  (SELECT stvmajr.stvmajr_desc
  FROM stvmajr
  WHERE stvmajr.stvmajr_code = saretry_join.sorccon_majr_code_conc
  ) concentration,
  TO_CHAR(sarhead.sarhead_complete_date, 'MM/DD/YYYY HH12:MI AM') complete_date,
  (SELECT stvvtyp_join.stvvtyp_desc
  FROM sarrfno
  INNER JOIN
    ( SELECT stvvtyp.stvvtyp_code, stvvtyp.stvvtyp_desc FROM stvvtyp
    )stvvtyp_join
  ON stvvtyp_join.stvvtyp_code   = sarrfno.sarrfno_ref_no
  WHERE sarrfno.sarrfno_aidm     = sarhead.sarhead_aidm
  AND sarrfno.sarrfno_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrfno.sarrfno_seqno      = 1
  AND sarrfno.sarrfno_rfql_cde   = 'V2'
  ) visa_type,
  (SELECT sarrfno.sarrfno_ref_no
  FROM sarrfno
  WHERE sarrfno.sarrfno_aidm     = sarhead.sarhead_aidm
  AND sarrfno.sarrfno_appl_seqno = sarhead.sarhead_appl_seqno
  AND sarrfno.sarrfno_seqno      = 2
  AND sarrfno.sarrfno_rfql_cde   = 30
  ) sevis_id_number,
  (SELECT sabnstu.sabnstu_id
  FROM sabnstu
  WHERE sabnstu.sabnstu_aidm = sarhead.sarhead_aidm
  ) web_app_id
FROM sarhead
INNER JOIN
  (SELECT saretry.saretry_aidm,
    saretry.saretry_appl_seqno,
    sarefos_m_join.sorcmjr_majr_code,
    sarefos_m_join.sorcmjr_desc,
    sarefos_c_join.sorccon_majr_code_conc
  FROM saretry
  INNER JOIN
    (SELECT sarefos.sarefos_aidm,
      sarefos.sarefos_appl_seqno,
      sarefos.sarefos_etry_seqno,
      sarefos.sarefos_flvl_cde,
      sorcmjr_join.sorcmjr_majr_code,
      sorcmjr_join.sorcmjr_desc
    FROM sarefos
    INNER JOIN
      (SELECT sorcmjr.sorcmjr_cmjr_rule,
        sorcmjr.sorcmjr_majr_code,
        sorcmjr.sorcmjr_desc
      FROM sorcmjr
      )sorcmjr_join
    ON sorcmjr_join.sorcmjr_cmjr_rule = sarefos.sarefos_lfos_rule
    WHERE sarefos.sarefos_seqno       =
      (SELECT MAX(s7.sarefos_seqno)
      FROM sarefos s7
      WHERE s7.sarefos_aidm     = sarefos.sarefos_aidm
      AND s7.sarefos_appl_seqno = sarefos.sarefos_appl_seqno
      AND s7.sarefos_etry_seqno = sarefos.sarefos_etry_seqno
      AND s7.sarefos_flvl_cde   = sarefos.sarefos_flvl_cde
      )
    )sarefos_m_join ON sarefos_m_join.sarefos_aidm = saretry.saretry_aidm
  AND sarefos_m_join.sarefos_appl_seqno            = saretry.saretry_appl_seqno
  AND sarefos_m_join.sarefos_flvl_cde              = 'M'
  LEFT OUTER JOIN
    (SELECT sarefos.sarefos_aidm,
      sarefos.sarefos_appl_seqno,
      sarefos.sarefos_etry_seqno,
      sarefos.sarefos_flvl_cde,
      sorccon_join.sorccon_majr_code_conc
    FROM sarefos
    INNER JOIN
      (SELECT sorccon.sorccon_ccon_rule,
        sorccon.sorccon_majr_code_conc
      FROM sorccon
      )sorccon_join
    ON sorccon_join.sorccon_ccon_rule = sarefos.sarefos_lfos_rule
    WHERE sarefos.sarefos_seqno       =
      (SELECT MAX(s7.sarefos_seqno)
      FROM sarefos s7
      WHERE s7.sarefos_aidm     = sarefos.sarefos_aidm
      AND s7.sarefos_appl_seqno = sarefos.sarefos_appl_seqno
      AND s7.sarefos_etry_seqno = sarefos.sarefos_etry_seqno
      AND s7.sarefos_flvl_cde   = sarefos.sarefos_flvl_cde
      )
    )sarefos_c_join ON sarefos_c_join.sarefos_aidm = saretry.saretry_aidm
  AND sarefos_c_join.sarefos_appl_seqno            = saretry.saretry_appl_seqno
  AND sarefos_c_join.sarefos_flvl_cde              = 'C'
  )saretry_join ON saretry_join.saretry_aidm       = sarhead.sarhead_aidm
AND saretry_join.saretry_appl_seqno                = sarhead.sarhead_appl_seqno
INNER JOIN
  (SELECT sarpers.sarpers_aidm,
    sarpers.sarpers_appl_seqno,
    sarpers.sarpers_prefix,
    sarpers.sarpers_first_name,
    sarpers.sarpers_middle_name1,
    sarpers.sarpers_last_name,
    sarpers.sarpers_suffix,
    sarpers.sarpers_former_name,
    sarpers.sarpers_nickname,
    sarpers.sarpers_gender,
    sarpers.sarpers_birth_dte,
    sarpers.sarpers_city_birth,
    sarpers.sarpers_stat_cde_birth,
    sarpers.sarpers_natn_cde_birth,
    sarpers.sarpers_ethn_category,
    sarpers.sarpers_lang_cde_native,
    sarpers.sarpers_natn_cde_citz,
    sarpers.sarpers_citz_cde,
    sarphon_phon_join.sarphon_phone phone_number,
    sarphon_cell_phon_join.sarphon_phone cell_phone,
    sarphon_email_join.sarphon_phone email_address,
    saraddr_join.saraddr_street_line1,
    saraddr_join.saraddr_street_line2,
    saraddr_join.saraddr_city,
    saraddr_join.saraddr_stat_cde,
    saraddr_join.saraddr_zip,
    saraddr_join.saraddr_natn_cde
  FROM sarpers
  INNER JOIN
    (SELECT saraddr.saraddr_aidm,
      saraddr.saraddr_appl_seqno,
      saraddr.saraddr_pers_seqno,
      saraddr.saraddr_street_line1,
      saraddr.saraddr_street_line2,
      saraddr.saraddr_city,
      saraddr.saraddr_stat_cde,
      saraddr.saraddr_zip,
      saraddr.saraddr_natn_cde
    FROM saraddr
    WHERE saraddr.saraddr_seqno =
      (SELECT MAX(s5.saraddr_seqno)
      FROM saraddr s5
      WHERE s5.saraddr_aidm     = saraddr.saraddr_aidm
      AND s5.saraddr_appl_seqno = saraddr.saraddr_appl_seqno
      AND s5.saraddr_pers_seqno = saraddr.saraddr_pers_seqno
      AND s5.saraddr_lcql_cde   = saraddr.saraddr_lcql_cde
      )
    AND saraddr.saraddr_lcql_cde = 'F'
    )saraddr_join
  ON saraddr_join.saraddr_aidm        = sarpers.sarpers_aidm
  AND saraddr_join.saraddr_appl_seqno = sarpers.sarpers_appl_seqno
  AND saraddr_join.saraddr_pers_seqno = sarpers.sarpers_seqno
  LEFT OUTER JOIN
    (SELECT sarphon.sarphon_aidm,
      sarphon.sarphon_appl_seqno,
      sarphon.sarphon_pers_seqno,
      sarphon.sarphon_pqlf_cde,
      sarphon.sarphon_phone
    FROM sarphon
    WHERE sarphon.sarphon_seqno =
      (SELECT MAX(s6.sarphon_seqno)
      FROM sarphon s6
      WHERE s6.sarphon_aidm     = sarphon.sarphon_aidm
      AND s6.sarphon_appl_seqno = sarphon.sarphon_appl_seqno
      AND s6.sarphon_pers_seqno = sarphon.sarphon_pers_seqno
      AND s6.sarphon_pqlf_cde   = sarphon.sarphon_pqlf_cde
      )
    )sarphon_phon_join
  ON sarphon_phon_join.sarphon_aidm        = sarpers.sarpers_aidm
  AND sarphon_phon_join.sarphon_appl_seqno = sarpers.sarpers_appl_seqno
  AND sarphon_phon_join.sarphon_pers_seqno = sarpers.sarpers_seqno
  AND sarphon_phon_join.sarphon_pqlf_cde   = 'HP' -- phone number
  LEFT OUTER JOIN
    (SELECT sarphon.sarphon_aidm,
      sarphon.sarphon_appl_seqno,
      sarphon.sarphon_pers_seqno,
      sarphon.sarphon_pqlf_cde,
      sarphon.sarphon_phone
    FROM sarphon
    WHERE sarphon.sarphon_seqno =
      (SELECT MAX(s6.sarphon_seqno)
      FROM sarphon s6
      WHERE s6.sarphon_aidm     = sarphon.sarphon_aidm
      AND s6.sarphon_appl_seqno = sarphon.sarphon_appl_seqno
      AND s6.sarphon_pers_seqno = sarphon.sarphon_pers_seqno
      AND s6.sarphon_pqlf_cde   = sarphon.sarphon_pqlf_cde
      )
    )sarphon_cell_phon_join
  ON sarphon_cell_phon_join.sarphon_aidm        = sarpers.sarpers_aidm
  AND sarphon_cell_phon_join.sarphon_appl_seqno = sarpers.sarpers_appl_seqno
  AND sarphon_cell_phon_join.sarphon_pers_seqno = sarpers.sarpers_seqno
  AND sarphon_cell_phon_join.sarphon_pqlf_cde   = 'MO' -- cell phone number
  LEFT OUTER JOIN
    (SELECT sarphon.sarphon_aidm,
      sarphon.sarphon_appl_seqno,
      sarphon.sarphon_pers_seqno,
      sarphon.sarphon_pqlf_cde,
      sarphon.sarphon_phone
    FROM sarphon
    WHERE sarphon.sarphon_seqno =
      (SELECT MAX(s6.sarphon_seqno)
      FROM sarphon s6
      WHERE s6.sarphon_aidm     = sarphon.sarphon_aidm
      AND s6.sarphon_appl_seqno = sarphon.sarphon_appl_seqno
      AND s6.sarphon_pers_seqno = sarphon.sarphon_pers_seqno
      AND s6.sarphon_pqlf_cde   = sarphon.sarphon_pqlf_cde
      )
    )sarphon_email_join
  ON sarphon_email_join.sarphon_aidm        = sarpers.sarpers_aidm
  AND sarphon_email_join.sarphon_appl_seqno = sarpers.sarpers_appl_seqno
  AND sarphon_email_join.sarphon_pers_seqno = sarpers.sarpers_seqno
  AND sarphon_email_join.sarphon_pqlf_cde   = 'EM' -- email address
  WHERE sarpers.sarpers_seqno               =
    (SELECT MAX(s4.sarpers_seqno)
    FROM sarpers s4
    WHERE s4.sarpers_aidm     = sarpers.sarpers_aidm
    AND s4.sarpers_appl_seqno = sarpers.sarpers_appl_seqno
    )
  )sarpers_join ON sarpers_join.sarpers_aidm = sarhead.sarhead_aidm
AND sarpers_join.sarpers_appl_seqno          = sarhead.sarhead_appl_seqno
LEFT OUTER JOIN
  (SELECT sarctrl.sarctrl_aidm,
    sarctrl.sarctrl_appl_seqno,
    sarctrl.sarctrl_appl_no_saradap,
    spriden_join.spriden_pidm,
    spriden_join.spriden_id
  FROM sarctrl
  INNER JOIN
    (SELECT spriden.spriden_pidm,
      spriden.spriden_id,
      spriden.spriden_change_ind
    FROM spriden
    )spriden_join
  ON spriden_join.spriden_pidm               = sarctrl.sarctrl_pidm
  AND spriden_join.spriden_change_ind       IS NULL
  )sarctrl_join ON sarctrl_join.sarctrl_aidm = sarhead.sarhead_aidm
AND sarctrl_join.sarctrl_appl_seqno          = sarhead.sarhead_appl_seqno
WHERE sarhead.sarhead_process_ind           != 'P'
AND (sarhead.sarhead_wapp_code               = 'W4'
OR sarhead.sarhead_wapp_code                 = 'W5')
AND sarhead.sarhead_term_code_entry         >= '201910'
ORDER BY sarpers_last_name,
  sarpers_first_name,
  appl_seqno DESC ;
