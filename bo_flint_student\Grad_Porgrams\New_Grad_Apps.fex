ENGINE SQLORA SET DEFAULT_CONNECTION IAPROD_webfocus
SQL SQLORA PREPARE SQLOUT FOR
select
distinct saradap_pidm,
um_demographic.umid,
um_demographic.last_name,
um_demographic.first_name,
zrbadms_v2.saradap_levl_code,
zrbadms_v2.saradap_appl_no,
zrbadms_v2.saradap_term_code_entry,
um_emas.entry_term || '0' as "new_term",
um_emas.entry_term,
um_emas.entry_term_desc,
zrbadms_v2.saradap_program_1,
zrbadms_v2.saradap_majr_code_1,
um_emas.isource_cd,
um_emas.isource_desc,
um_emas.st_int_lvl,
um_emas.st_int_lvl_desc,
um_emas.stage,
um_emas.create_dt
from zrbadms_v2
inner join um_demographic on um_demographic.dm_pidm = zrbadms_v2.saradap_pidm
inner join emasmgr.um_emas on um_emas.studentno_pidm = zrbadms_v2.saradap_pidm
                           and um_emas.entry_term || '0'= zrbadms_v2.saradap_term_code_entry
where saradap_styp_code ='N'
order by saradap_pidm
END
TABLE FILE SQLOUT
PRINT
     SARADAP_PIDM AS 'Pidm'
     UMID
     LAST_NAME AS 'Last Name'
     FIRST_NAME AS 'First Name'
     SARADAP_PROGRAM_1 AS 'Program'
     SARADAP_MAJR_CODE_1 AS 'Major'
     SARADAP_LEVL_CODE NOPRINT
     SARADAP_TERM_CODE_ENTRY AS 'Entry Term'
     ENTRY_TERM_DESC AS 'Entry Term Desc'
     SARADAP_APPL_NO AS 'Application Number'
     ISOURCE_CD AS 'Source Code'
     ISOURCE_DESC AS 'Source Desc'
     ST_INT_LVL AS 'Referred By Code'
     ST_INT_LVL_DESC AS 'Referred By Description'
     STAGE NOPRINT
     CREATE_DT/HMDYY AS 'Inquiry Date'
WHERE SARADAP_TERM_CODE_ENTRY EQ &SARADAP_TERM_CODE_ENTRY.(OR(FIND STVTERM.STVTERM.STVTERM_CODE,STVTERM.STVTERM.STVTERM_DESC IN stvterm)).SARADAP_TERM_CODE_ENTRY:.;
ON TABLE SET PAGE-NUM NOLEAD
ON TABLE NOTOTAL
ON TABLE PCHOLD FORMAT &WFFMT.(<Excel,XLSX>,<HTML,HTML>,<PDF,PDF>).Select type of display output.
ON TABLE SET HTMLCSS ON
ON TABLE SET STYLE *
     INCLUDE = IBFS:/EDA/EDASERVE/_EDAHOME/ETC/endeflt.sty,
$
TYPE=REPORT,
     COLUMN=N9,
     SQUEEZE=2.055556,
$
TYPE=REPORT,
     COLUMN=N12,
     SQUEEZE=4.805556,
$
TYPE=REPORT,
     COLUMN=N14,
     WRAP=6.000000,
$
ENDSTYLE
END
 