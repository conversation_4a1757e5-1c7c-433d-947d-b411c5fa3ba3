--New FTIAC/Transfers with prior student records
with dp1 as (
select 
sd_pidm pidm, 
sds.SD_TERM_code term_code,
ia_student_type_code stype,
primary_level_code
from ia_um_student_data sds
where sds.registered_ind = 'Y'
and sds.sd_term_code = 202210 
AND sds.STUDENT_TYPE_CODE in ('T','F')
and sds.primary_level_code = 'UG'
)
select 
dp1.pidm,
sd.UMID,
student_type_code,
dp1.primary_level_code,
min(sd.sd_term_code) prior_term
from dp1
inner join td_student_data sd on dp1.pidm = sd.sd_pidm
where sd.student_type_code in ('T','F')
and sd.registered_ind = 'Y'
and sd.sd_term_code < 202210
and sd.primary_level_code = 'UG'
group by 
dp1.pidm,
sd.umid,
student_type_code,
dp1.primary_level_code
;

--compare differences between FULL_PART_TIME_IND and FULL_PART_TIME_IND_UMF
select 
FULL_PART_TIME_IND_UMF,
full_part_time_ind,
count (*) headcount
from ia_UM_student_data
where registered_ind = 'Y'
and sd_term_code = 202210 -- update fall term
group by
FULL_PART_TIME_IND_UMF,
full_part_time_ind
;
--Summer Transfers stype and ia_stype in Fall
with dp1 as(
select 
sd_pidm, 
sds.SD_TERM_code,
sds.SD_TERM_DESC,
student_type_code
from ia_um_student_data sds
where sds.registered_ind = 'Y'
and sds.sd_term_code = 202040 -- update summer term
AND sds.STUDENT_TYPE_CODE = 'T'
)
select 
dp1.sd_pidm,
dp1.SD_TERM_DESC summer_term,
dp1.student_type_code summer_stype,
sd.sd_term_desc fall_term,
sd.student_type_code fall_stype,
sd.ia_student_type_code fall_ia_stype
from dp1
inner join ia_td_student_data sd on dp1.sd_pidm = sd.sd_pidm
and sd.sd_term_code = (to_number (dp1.sd_term_code) + 70)
;
--New Transfers with prior student records - current level
with dp1 as (
select 
sd_pidm pidm, 
sds.SD_TERM_code term_code,
ia_student_type_code stype
from ia_td_student_data sds
where sds.registered_ind = 'Y'
and sds.sd_term_code = 202210 
AND sds.student_type_code = 'T'
minus
select co_pidm pidm,
co_term_code_key term_code,
co_ia_student_type_code stype
from ia_cohort_pop_undup_tbl
where co_ia_student_type_code = 'T'
and co_term_code_key = 202210
), cur_level as(
select 
sd.sd_pidm,
sd.sd_term_desc,
sd.primary_level_code
from dp1 
left join ia_td_student_data sd 
on dp1.pidm = sd.sd_pidm
and dp1.term_code = sd.sd_term_code
)
select * from cur_level
;
-- New Transfers with prior student records - min term
with dp1 as (
select 
sd_pidm pidm, 
sds.SD_TERM_code term_code,
ia_student_type_code stype
from ia_td_student_data sds
where sds.registered_ind = 'Y'
and sds.sd_term_code = 202210 
AND sds.STUDENT_TYPE_CODE = 'T'
minus
select co_pidm pidm,
co_term_code_key term_code,
co_ia_student_type_code stype
from IA_COHORT_POP_UNDUP_TBL
where CO_IA_STUDENT_TYPE_CODE = 'T'
and co_term_code_key = 202210
), min_term as (
select 
sd.sd_pidm,
min (sd_term_code)
from dp1 
left join ia_td_student_data sd 
on dp1.pidm = sd.sd_pidm
group by
sd.sd_pidm
)
select * from min_term
;

select sd_pidm, sd_term_code,ia_student_type_code, TOTAL_CREDIT_HOURS_UMF from ia_td_student_data
where registered_ind = 'Y'
and sd_pidm = 132748
;
















