--------------------------------------------------------
--  DDL for View IA_EFC_ABILITY_MATRIX
--------------------------------------------------------

  CREATE OR REPLACE VIEW IA_EFC_ABILITY_MATRIX  AS 
  (SELECT PIDM_KEY,
    TERM_CODE_KEY,
    TERM_DESC,
    EFC_LEVEL_CODE_DESC,
    EFC_LEVEL_CODE,
    REGISTERED_IND,
    HSCH_GPA,
    ACT_COMPOSITE,
    TFC EFC,
    NEED,
    SUCCESS_ACT_HSCH_GPA,
    SUCCESS_DISTRIBUTION_DESC,
    SUCCESS_DISTRIBUTION,
--    STATE_GIFT_AMT,
    NVL(FED_GIFT_AMT,0)FED_GIFT_AMT,
    NVL(INST_GIFT_AMT,0)INST_GIFT_AMT,
    NVL (OUT_GIFT_AMT,0)OUT_GIFT_AMT,
--    TOTAL_GIFT,
    LOAN_WORK_AMT,
    Model_variables,
    x,
    success_distribution + EFC_LEVEL_CODE MATRIX_POSITION  --matrix position
  FROM--pull data from IA_AR_AWARD_DETAL_BY_TERM_EXT update after Fall 10th day
    ( 
	WITH DP1 AS
    (SELECT 
--    DP1.*,
      DP1.PIDM_KEY,
      DP1.TERM_CODE_KEY,
      DP1.TERM_DESC,
      DP1.REGISTERED_IND,
      DP1.FA_FUND_SOURCE,
      DP1.FA_FUND_TYPE_CODE,
      DP1.FUND_CODE_KEY,
      DP1.AWARD_TERM_OFFER_AMOUNT,
      DP1.FM_TFC,
      DP1.FM_GROSS_NEED /2 FM_GROSS_NEED,
      ROUND(DM.HSCH_GPA,2) HSCH_GPA,
      TS.ACT_COMPOSITE ACT_COMPOSITE,
      /* Create full time, part time, gone indicator must keep
      students who left as not to loose the unregistered students */
      CASE
        WHEN DP1.STUDENT_TOTAL_CREDIT_HOURS >= 12
        THEN 'FULL'
        WHEN DP1.STUDENT_TOTAL_CREDIT_HOURS IS NULL
        THEN 'GONE'
        ELSE 'PART'
      END FULL_PART_GONE,
        CASE
        WHEN DP1.fm_tfc >= 18001
        THEN '18001 and above'
        WHEN DP1.fm_tfc >= 12001
        AND DP1.fm_tfc  <= 18000
        THEN '12001-18000'
        WHEN DP1.fm_tfc >= 6001
        AND DP1.fm_tfc  <= 12000
        THEN '6001-12000'
        WHEN DP1.fm_tfc >= 1
        AND DP1.fm_tfc  <= 6000
        THEN '1-6000'
        WHEN DP1.fm_tfc =0
        THEN 'Zero EFC'
        WHEN DP1.fm_tfc IS NULL
        THEN 'NO FASFA'
      END EFC_LEVEL_CODE_DESC,
      CASE
        WHEN DP1.fm_tfc >= 18001
        THEN 1
        WHEN DP1.fm_tfc >= 12001
        AND DP1.fm_tfc  <= 18000
        THEN 2
        WHEN DP1.fm_tfc >= 6001
        AND DP1.fm_tfc  <= 12000
        THEN 3
        WHEN DP1.fm_tfc >= 1
        AND DP1.fm_tfc  <= 6000
        THEN 4
        WHEN DP1.fm_tfc =0
        THEN 5
        WHEN DP1.fm_tfc IS NULL
        THEN 0
      END EFC_LEVEL_CODE,              --calculate second digit in matrix postion
	  2.71828 E,                  --EXP(x) = e^x where e is a universal constant 2.71828
  /*************************************************************************************************
--	  calculation of variables used in the success_act_hsch_gpa variable:
--	  probability of first year retention = e^x/(1+e^x)
--	
--   Using the Astin model published by the Higher Education Research Institute.
--	  Astin, A.W. and Oseguera, L. (2005). Degree Attainment Rates at American Colleges and Universities.
--	  Revised Edition Los Angeles: Higher Education Research Institute, UCLA.
--	  
--	  A previous study conducted by Institutional Analysis and the Admissions department developed a 
--	  1st year success predictive model.  The following coefficients were derived using FTIAC student
--	  data from Fall 2009, 10, 11 
--	 (see \\deptdrive\dept\BUDGET\Admissions Standards\Predictive Model\1st_year_success_predictive_models.xlsx)
--											             X1	  X2	   X3
--		HSCH_GPA(B1)	    					1.843	1.821	 1.843
--		ACT_COMPOSITE(B2)					.041	
--		ACT_MATH (B3)								 .046
--		Constant(a)	    	   -5.265  -5.261	-5.265
--
--	  X1,X2,X3 is calculated using a case depending on what variables are present for the student.  The
--	  astin model does not account for the absence of an ACT score and in the case a student does not 
--	  have an ACT score in X3 we are using coefficients for HSCH_GPA and the Constant used in the X1 calculation
--	 
--	  X1 = a + hsch_gpa * B1 + act_composite * B2
--	  or
--	  X2 = a + hsch_gpa * B1 + act_math * B3
--	  or
--	  X3 = a + hsch_gpa * B1
	  
	  *************************************************************************************************/ 
	 CASE
        WHEN DM.HSCH_GPA     IS NOT NULL
        AND TS.ACT_COMPOSITE IS NOT NULL
        THEN 'HS GPA and ACT Composite'
        WHEN DM.HSCH_GPA     IS NOT NULL
        AND TS.ACT_COMPOSITE IS NULL
        AND TS.ACT_MATH      IS NOT NULL
        THEN 'HS GPA and ACT Math'
        WHEN DM.HSCH_GPA     IS NOT NULL
        AND TS.ACT_COMPOSITE IS NULL
        AND TS.ACT_MATH      IS NULL
        THEN 'HS GPA Only'
        WHEN DM.HSCH_GPA IS NULL
        THEN 'No HS GPA Availible'
      END Model_variables, -- variables used to solve for X in formaula e^x/(1+e^x)
	 CASE
        WHEN DM.HSCH_GPA     IS NOT NULL
        AND TS.ACT_COMPOSITE IS NOT NULL
        THEN (- 5.26528572841609 + DM.HSCH_GPA * 1.84343107849639 + TS.ACT_COMPOSITE * 0.041436431427755)
        WHEN DM.HSCH_GPA     IS NOT NULL
        AND TS.ACT_COMPOSITE IS NULL
        AND TS.ACT_MATH      IS NOT NULL
        THEN (-5.2610494059369 + DM.HSCH_GPA * 1.82130779272212 + TS.ACT_MATH * 0.0460110852710893)
        WHEN DM.HSCH_GPA     IS NOT NULL
        AND TS.ACT_COMPOSITE IS NULL
        AND TS.ACT_MATH      IS NULL
        THEN (- 5.26528572841609 + DM.HSCH_GPA * 1.84343107849639)
        WHEN DM.HSCH_GPA IS NULL
        THEN NULL
      END X 
	 /*************************************************************************************************
	  -- end of x calculation
	 *************************************************************************************************/ 
	  
	FROM IA_AR_AWARD_DETAIL_BY_TERM_EXT DP1
    LEFT JOIN TD_TEST_SCORE TS
    ON TS.PIDM = DP1.PIDM_KEY
    AND TS.TD_TERM_CODE = DP1.TERM_CODE_KEY
    LEFT JOIN TD_DEMOGRAPHIC DM
    ON DM.DM_PIDM = DP1.PIDM_KEY
    AND DM.TD_TERM_CODE = DP1.TERM_CODE_KEY
--    LEFT JOIN IA_FA_FUND_BASE FB
--    ON FB.FA_PIDM       = DP1.PIDM_KEY
--    AND FB.FA_TERM_CODE = DP1.TERM_CODE_KEY
--    AND FB.FA_FUND_CODE = DP1.FUND_CODE_KEY
    WHERE
      DP1.STUDENT_TYPE_CODE = 'F'
    ),
    DP2 AS
    ( SELECT DISTINCT pidm_key,
      term_code_key,
      TERM_DESC,
      EFC_LEVEL_CODE_DESC,
      EFC_LEVEL_CODE,
      REGISTERED_IND,
      HSCH_GPA,
      ACT_COMPOSITE,
      DP1.fm_tfc tfc,
      fm_gross_need NEED,
      Model_variables,
      x,
	 /*************************************************************************************************
	  probability of first year retention = e^x/(1+e^x)
	  This uses the variable calculated for the value of X where e is a universal constant
	 *************************************************************************************************/ 
      ROUND((power(e,x))/(1+power(e,x)) * 100,2) success_act_hsch_gpa -- success variable
	 /*************************************************************************************************
	  -- end of probability of first year retention
	 *************************************************************************************************/ 
    FROM dp1
    WHERE FULL_PART_GONE IN ('FULL','GONE')
    ),
    DP3 AS
    (SELECT dp2.*,
      CASE
        WHEN success_act_hsch_gpa >= 81
        THEN '81+'
        WHEN success_act_hsch_gpa >=74
        AND success_act_hsch_gpa  <=80.99
        THEN '74-80.9'
        WHEN success_act_hsch_gpa >=68
        AND success_act_hsch_gpa  <=73.99
        THEN '68-73.9'
        WHEN success_act_hsch_gpa >=64
        AND success_act_hsch_gpa  <=67.99
        THEN '64-67.9'
        WHEN success_act_hsch_gpa <=63.99
        THEN '63.9 and below'
        WHEN success_act_hsch_gpa IS NULL
        THEN 'no act or hsch_gpa'
      END success_distribution_desc,
      CASE
        WHEN success_act_hsch_gpa >= 81
        THEN 10
        WHEN success_act_hsch_gpa >=74
        AND success_act_hsch_gpa  <=80.99
        THEN 20
        WHEN success_act_hsch_gpa >=68
        AND success_act_hsch_gpa  <=73.99
        THEN 30
        WHEN success_act_hsch_gpa >=64
        AND success_act_hsch_gpa  <=67.99
        THEN 40
        WHEN success_act_hsch_gpa <=63.99
        THEN 50
        ELSE success_act_hsch_gpa
      END success_distribution -- dustrubytuib if syccess variable
    FROM dp2
    ),
--    STAT AS
--    (SELECT pidm_key,
--      term_code_key,
--      NVL(SUM(AWARD_TERM_OFFER_AMOUNT),0) STATE_GIFT_AMT
--    FROM dp1
--    WHERE 
--    dp1.FA_FUND_SOURCE = 'STAT' AND DP1.FA_FUND_TYPE_CODE = 'GRNT'
--    GROUP BY PIDM_KEY,
--      TERM_CODE_KEY
--    ),
     FED AS
    (SELECT PIDM_KEY,
      term_code_key,
      NVL(SUM(AWARD_TERM_OFFER_AMOUNT),0) FED_GIFT_AMT
    FROM dp1
    WHERE 
    dp1.FA_FUND_SOURCE = 'FDRL' AND DP1.FA_FUND_TYPE_CODE = 'GRNT'
    GROUP BY PIDM_KEY,
      TERM_CODE_KEY
    ),
    IGA AS --616
    (SELECT pidm_key,
      term_code_key,
      NVL(SUM(AWARD_TERM_OFFER_AMOUNT),0) INST_GIFT_AMT
    FROM dp1
    WHERE 
     (dp1.FA_FUND_SOURCE in ('INST','OTHR','DONR') AND   DP1.FA_FUND_TYPE_CODE IN ('SCHL','GRNT') 
     AND DP1.FUND_CODE_KEY NOT LIKE ('PRIV%'))
--    dp1.FA_FUND_SOURCE = 'INST' AND DP1.FA_FUND_TYPE_CODE = 'GRNT'
    GROUP BY PIDM_KEY,
      TERM_CODE_KEY
    ),
    OGA AS --1283
    (SELECT pidm_key,
      term_code_key,
      NVL(SUM(AWARD_TERM_OFFER_AMOUNT),0) OUT_GIFT_AMT
    FROM dp1
    WHERE
     (dp1.FA_FUND_SOURCE in ('FDRL','STAT','EXTN') AND   DP1.FA_FUND_TYPE_CODE IN ('SCHL','GRNT'))
      OR DP1.FUND_CODE_KEY LIKE ('PRIV%')
      GROUP BY PIDM_KEY,
      TERM_CODE_KEY
    ),
    LWA AS --1096
    (SELECT pidm_key,
      term_code_key,
      NVL(SUM(AWARD_TERM_OFFER_AMOUNT),0) LOAN_WORK_AMT
    FROM dp1
    WHERE   
    DP1.FA_FUND_TYPE_CODE IN ('LOAN','WORK')                                                                                                                                                                                                                                                                                                                          
    GROUP BY PIDM_KEY,
      TERM_CODE_KEY
    )
  SELECT DP3.*,
--    STAT.STATE_GIFT_AMT,
    FED.FED_GIFT_AMT,
    IGA.INST_GIFT_AMT,
    OGA.OUT_GIFT_AMT,
--    IGA.INST_GIFT_AMT + OGA.OUT_GIFT_AMT TOTAL_GIFT,
    LWA.LOAN_WORK_AMT
  FROM DP3
--  LEFT JOIN STAT
--  ON DP3.PIDM_KEY = STAT.PIDM_KEY       
--  AND DP3.TERM_CODE_KEY = STAT.TERM_CODE_KEY 
  LEFT JOIN FED
  ON DP3.PIDM_KEY = FED.PIDM_KEY       
  AND DP3.TERM_CODE_KEY = FED.TERM_CODE_KEY 
  LEFT JOIN IGA
  ON DP3.PIDM_KEY = IGA.PIDM_KEY       
  AND DP3.TERM_CODE_KEY = IGA.TERM_CODE_KEY 
  LEFT JOIN OGA
  ON DP3.PIDM_KEY = OGA.PIDM_KEY       
  AND DP3.TERM_CODE_KEY = OGA.TERM_CODE_KEY 
  LEFT JOIN LWA
  ON DP3.PIDM_KEY = LWA.PIDM_KEY       
  AND DP3.TERM_CODE_KEY = LWA.TERM_CODE_KEY 
    )POPSEL
  );
