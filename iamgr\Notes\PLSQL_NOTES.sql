--basic structure of PL/SQL code block
DECLARE
  VAR2 VARCHAR2(10);
  --HERE WE CAN DECLARE VARIABLES
BEGIN
  VAR2 := 'WORLD';
  DBMS_OUTPUT.PUT_LINE ('HELLO ' || VAR2);
  --THIS IS AN EXECUTABLE SECTION
END;
--varaible types
DECLARE
  NUM_VAR       NUMBER (4,2)  := 11.25;
  INT_VAR       INTEGER       := 5;
  DATE_VAR      DATE          := TO_DATE ('11/03/2012','DD/MM/YYYY');
  STRING_VAR    VARCHAR2 (50) := 'STRING 1';
  STRING_NO_VAR VARCHAR2(50)  := '5.30';
  CHAR_VAR      CHAR (50)     := 'STRING 2';
BEGIN
  DBMS_OUTPUT.PUT_LINE ('NUM_VAR VALUE ' || NUM_VAR);
  DBMS_OUTPUT.PUT_LINE ('INT_VAR VALUE ' || INT_VAR);
  DBMS_OUTPUT.PUT_LINE ('DATE_VAR VALUE ' || DATE_VAR);
  DBMS_OUTPUT.PUT_LINE ('STRING_VAR VALUE ' || STRING_VAR);
  DBMS_OUTPUT.PUT_LINE ('CHAR_VAR VALUE ' || CHAR_VAR);
  DBMS_OUTPUT.PUT_LINE ('CONVERT NUMBER TO STRING ' || TO_CHAR (NUM_VAR));
  DBMS_OUTPUT.PUT_LINE ('CONVERT STRING TO NUMBER ' || TO_NUMBER (STRING_NO_VAR,'9.99'));
END;
-- using variable type function
DECLARE
  stud_first_name IA_TD_STUDENT_DATA.FIRST_NAME%type;
  stud_last_name IA_TD_STUDENT_DATA.LAST_NAME%type;
BEGIN
  SELECT DISTINCT FIRST_NAME,
    LAST_NAME
  INTO stud_first_name,
    stud_last_name
  FROM ia_td_student_data
  WHERE umid = '12805441';
  DBMS_OUTPUT.PUT_LINE (stud_first_name ||' ' || stud_last_name);
END;
--IF-THEN-ELSE statement
DECLARE
  var1 INTEGER :=10;
BEGIN
  IF var1 = 5 THEN
    DBMS_OUTPUT.PUT_LINE ('VAR1 IS EQUAL TO 5');
  ELSE
    DBMS_OUTPUT.PUT_LINE ('VAR1 IS NOT EQUAL TO 5');
  END IF;
END;
--CASE statement
DECLARE
  var1 INTEGER :=10;
BEGIN
  DBMS_OUTPUT.PUT_LINE ('BEGIN CHECK FOR 5');
  CASE
  WHEN var1 < 5 THEN
    DBMS_OUTPUT.PUT_LINE ('VAR1 LESS THAN 5');
  WHEN VAR1 = 5 THEN
    DBMS_OUTPUT.PUT_LINE ('VAR1 IS EQUAL TO 5');
  WHEN var1 > 5 THEN
    DBMS_OUTPUT.PUT_LINE ('VAR1 GREATER THAN 5');
  END CASE;
  DBMS_OUTPUT.PUT_LINE ('VAR IS '||VAR1);
END;
--SINPLE LOOP
DECLARE
  I INTEGER :=0;
BEGIN
  LOOP
    I := I +1;
    DBMS_OUTPUT.PUT_LINE ('INDEX VALUE IS '||I);
    EXIT
  WHEN I >= 10;
  END LOOP;
END;
--WHILE LOOP AKA CONDITIONAL LOOP
DECLARE
  I INTEGER :=1;
BEGIN
  WHILE I <= 10
  LOOP
    DBMS_OUTPUT.PUT_LINE ('INDEX VALUE IS '||I);
    I := I +1;
  END LOOP;
END;
--FOR LOOP
BEGIN
  FOR I IN 1 .. 10
  LOOP
    DBMS_OUTPUT.PUT_LINE ('INDEX VALUE IS '||I);
  END LOOP;
END;
--FOR LOOP WITH DATA
BEGIN
  FOR I IN
  (SELECT SD_TERM_DESC
  FROM IA_TD_STUDENT_DATA
  WHERE UMID         = '12805441'
  AND REGISTERED_IND = 'Y'
  )
  LOOP
    DBMS_OUTPUT.PUT_LINE ('REGISTERED FOR TERM '|| I.SD_TERM_DESC);
  END LOOP;
END;
